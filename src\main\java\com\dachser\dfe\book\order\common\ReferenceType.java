package com.dachser.dfe.book.order.common;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@ToString
public enum ReferenceType {
	EKAER_NUMBER("ekaer_number", "312"),

	DELIVERY_NOTE_NUMBER("delivery_note_number", "003"),

	PURCHASE_ORDER_NUMBER("purchase_order_number", "007"),

	DAILY_PRICE_REFERENCE("daily_price_reference", "060"),

	ORDER_NUMBER("order_number", "100"),

	IDENTIFICATION_CODE_TRANSPORT("identification_code_transport", "337"),

	BOOKING_REFERENCE("booking_reference", "077"),

	BOOKING_REFERENCE_LABEL("booking_reference_label", "077");

	private String dfeValue;

	private String coreSystemsValue;
}
