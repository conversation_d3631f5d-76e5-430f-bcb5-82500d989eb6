package com.dachser.dfe.book.order.sea;

import com.dachser.dfe.book.order.common.fcl.BaseFullContainerLoad;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.OffsetDateTime;
import java.util.List;

public interface BaseFullContainerLoadRepository extends JpaRepository<BaseFullContainerLoad, Long> {
	
	//@formatter:off
	@Query(value ="SELECT TOP(3) container_type "
				+ "FROM (SELECT TOP(250) container_type, ob.order_id, customer_number "
						+ "FROM full_container_load fcl "
						+ "JOIN order_base ob ON (ob.customer_number = :customerNumber AND fcl.order_id = ob.order_id AND ob.last_modified > :lastValidDate) "
							+ ") AS sub_select "
				+ "GROUP BY container_type ORDER BY MAX(order_id) DESC", nativeQuery = true)
	//@formatter:on
	List<String> getMostFrequentlyUsedContainerTypes(@Param("customerNumber") final String customerNumber, @Param("lastValidDate") OffsetDateTime lastValidDate);
	
    default List<String> getMostFrequentlyUsedContainerTypes(String customerNumber) {
        return getMostFrequentlyUsedContainerTypes(customerNumber, OffsetDateTime.now().minusDays(30));
    }

}