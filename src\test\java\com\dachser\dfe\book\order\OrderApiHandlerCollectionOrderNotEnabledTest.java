package com.dachser.dfe.book.order;

import com.dachser.dfe.book.BaseOpenApiTest;
import com.dachser.dfe.book.model.RoadCollectionOrderDto;
import com.fasterxml.jackson.core.type.TypeReference;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import io.restassured.module.mockmvc.specification.MockMvcRequestSpecification;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.TestPropertySource;

import java.io.IOException;
import java.time.LocalDate;
import java.time.OffsetDateTime;

import static com.dachser.dfe.book.order.OrderApiTestUtil.CUSTOMER_NUMBER_ROAD;
import static com.dachser.dfe.book.order.OrderApiTestUtil.resourceLoader;
import static org.junit.jupiter.api.Assertions.assertEquals;

class OrderApiHandlerCollectionOrderNotEnabledTest extends BaseOpenApiTest {

	@Test
	void shouldThrowExceptionWhenCollectionOrderIsNotEnabled() throws IOException {
		final RoadCollectionOrderDto order = resourceLoader.loadResourceAndConvert("orders/new-collection-order-complete-valid.json", new TypeReference<>() {
		});
		order.setCustomerNumber(CUSTOMER_NUMBER_ROAD);
		order.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
		order.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
		order.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));
		MockMvcRequestSpecification request = givenWriteRequest().body(order);
		MockMvcResponse put = request.put(buildUrl("/orders/validate"));
		assertEquals(400, put.statusCode());
	}

}
