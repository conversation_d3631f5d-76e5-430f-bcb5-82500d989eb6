package com.dachser.dfe.book.order.validation.road;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ ElementType.TYPE, ElementType.ANNOTATION_TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = { ForwardingOrderConstraintValidator.class })
@Documented
public @interface ValidForwardingOrder {

	String message() default "{com.dachser.dfe.book.validation." + "ValidForwardingOrder.message}";

	Class<?>[] groups() default {};

	Class<? extends Payload>[] payload() default {};
}