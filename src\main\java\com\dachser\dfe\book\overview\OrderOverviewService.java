package com.dachser.dfe.book.overview;

import com.dachser.dfe.book.document.DocumentType;
import com.dachser.dfe.book.exception.ErrorIdExternalServiceNotAvailableException;
import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.jpa.AirOrderReferenceRepository;
import com.dachser.dfe.book.jpa.OrderReferenceRepository;
import com.dachser.dfe.book.jpa.SeaOrderReferenceRepository;
import com.dachser.dfe.book.jpa.VOrderOverviewRepository;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.jpa.entity.VOrderOverview;
import com.dachser.dfe.book.jpa.spec.AdditionalLikeFilterSpec;
import com.dachser.dfe.book.jpa.spec.AirProductFilterSpec;
import com.dachser.dfe.book.jpa.spec.CollectionDateSortSpec;
import com.dachser.dfe.book.jpa.spec.DeletedOrderSpec;
import com.dachser.dfe.book.mapper.OrderOverviewMapper;
import com.dachser.dfe.book.mapper.SortFilterQueryMapper;
import com.dachser.dfe.book.mapper.custom.TranslationMapper;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.model.overview.ReferenceTranslation;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.order.air.AirOrderReference;
import com.dachser.dfe.book.order.exception.OrderOverviewDatabaseException;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.sea.SeaOrderReference;
import com.dachser.dfe.book.service.FurtherAddressTypeService;
import com.dachser.dfe.book.user.UserContextService;
import com.dachser.dfe.dfefiltersorthelper.jpa.ProductAndDivisionSpec;
import com.dachser.dfe.dfefiltersorthelper.model.ProductDivisionPair;
import com.dachser.dfe.dfefiltersorthelper.service.SortAndFilterService;
import com.dachser.dfe.trackandtrace.model.TTBasicOrderOverview;
import com.dachser.dfe.trackandtrace.model.TTFilter;
import com.dachser.dfe.trackandtrace.model.TTKeys;
import com.dachser.dfe.trackandtrace.model.TTOrderOverviewItem;
import com.dachser.dfe.trackandtrace.model.TTOrderReference;
import com.dachser.dfe.trackandtrace.model.TTQueryObject;
import com.dachser.dfe.trackandtrace.model.TTResponsePage;
import com.dachser.dfe.trackandtrace.model.TTSorting;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.InputMismatchException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
@Log4j2
public class OrderOverviewService {

	private static final String SEPARATOR = ",";

	private static final String PRODUCT = "product";

	private static final String DIVISION = "division";

	private static final String ERROR = "ERROR";

	private static final List<Integer> ORDER_TYPES_ROAD = Arrays.stream(OrderType.values()).filter(type -> type.getSegment().equals(Segment.ROAD)).map(OrderType::getId).toList();

	private static final List<Integer> ORDER_TYPES_SEA = Arrays.stream(OrderType.values()).filter(type -> type.getSegment().equals(Segment.SEA)).map(OrderType::getId).toList();

	private final VOrderOverviewRepository orderOverviewRepository;

	private final OrderOverviewMapper mapper;

	private final FurtherAddressTypeService furtherAddressTypeService;

	private final SortAndFilterService<VOrderOverview> sortAndFilterService;

	private final SortFilterQueryMapper sortFilterQueryMapper;

	private final Translator translator;

	private final OrderReferenceRepository orderReferenceRepository;

	private final AirOrderReferenceRepository airOrderReferenceRepository;

	private final SeaOrderReferenceRepository seaOrderReferenceRepository;

	private final CachedOrderOverviewService cachedOrderOverviewService;

	private final UserContextService userContextService;

	private final TranslationMapper translationMapper;

	@Retryable(retryFor = { OrderOverviewDatabaseException.class }, maxAttempts = 2, listeners = "loggerRetryListener")
	public TTResponsePage getOverviewResponsePage(Pageable page, TTQueryObject queryObject) {
		List<String> customerNumbersWithSegment =  userContextService.getAllBookPermittedCustomerIdsWithSegment();
		Optional<AdditionalLikeFilterSpec> additionalLikeFilterSpec = extractShipmentNumberForAdditionalFilter(queryObject).map(AdditionalLikeFilterSpec::new);
		Specification<VOrderOverview> collectionDateSortSpec = new CollectionDateSortSpec(getSortWhenPresent(TTKeys.PICKUP_DATE_TIME, queryObject));
		Specification<VOrderOverview> finalSpecification = sortAndFilterService.createCustomerNumbersWithSegmentQuerySpecification(customerNumbersWithSegment, sortFilterQueryMapper.map(queryObject))
				.and(createSpecForProductAndDivisionFilters(queryObject))
				.and(createSpecForAirProductFilter(queryObject))
				.and(new DeletedOrderSpec()).and(collectionDateSortSpec);
		if (additionalLikeFilterSpec.isPresent()) {
			finalSpecification = finalSpecification.and(additionalLikeFilterSpec.get());
		}
		Page<VOrderOverview> orderOverviewItems;
		try {
			orderOverviewItems = orderOverviewRepository.findAll(finalSpecification, page);
		} catch (Exception ex) {
			String errorMsg = String.format("Order Overview: Error while retrieving data from database: %s.", ex.getMessage());
			log.error(errorMsg, ex);
			throw new OrderOverviewDatabaseException(errorMsg);
		}

		return createResponsePage(orderOverviewItems);
	}

	private Optional<String> extractShipmentNumberForAdditionalFilter(TTQueryObject queryObject) {
		if (queryObject.getFilter() == null) {
			return Optional.empty();
		}
		return queryObject.getFilter().stream().filter(filter -> Objects.equals(filter.getKey(), TTKeys.SHIPMENT_NUMBER_LIKE)).map(TTFilter::getValue).findFirst();
	}

	private TTSorting getSortWhenPresent(TTKeys sortKey, TTQueryObject queryObject) {
		if (queryObject.getSort() == null || !sortKey.equals(queryObject.getSort().getKey())) {
			return null;
		} else {
			return queryObject.getSort();
		}
	}

	private Specification<VOrderOverview> createSpecForProductAndDivisionFilters(TTQueryObject queryObject) {
		if (queryObject.getFilter() == null) {
			return new ProductAndDivisionSpec<>(DIVISION, PRODUCT, List.of());
		}
		List<ProductDivisionPair> productDivisionPairs = queryObject.getFilter().stream()
				.filter(filter -> List.of(TTKeys.PRODUCT_EUROPEAN, TTKeys.PRODUCT_FOOD).contains(filter.getKey())).map(filter -> {
					if (TTKeys.PRODUCT_EUROPEAN.equals(filter.getKey())) {
						return new ProductDivisionPair(Division.T.getKey(), filter.getValueList());
					}
					return new ProductDivisionPair(Division.F.toString(), filter.getValueList());
				}).toList();
		return new ProductAndDivisionSpec<>(DIVISION, PRODUCT, productDivisionPairs);
	}

	private Specification<VOrderOverview> createSpecForAirProductFilter(TTQueryObject queryObject) {
		if (queryObject.getFilter() == null) {
			return new AirProductFilterSpec(Collections.emptyList());
		}
		List<String> airProductCodes = queryObject.getFilter().stream().filter(filter -> TTKeys.PRODUCT_AIR.equals(filter.getKey())).map(TTFilter::getValueList).findFirst()
				.orElse(Collections.emptyList());
		return new AirProductFilterSpec(airProductCodes);
	}

	private TTResponsePage createResponsePage(Page<VOrderOverview> orderOverviewItems) {
		TTResponsePage responsePage = new TTResponsePage();
		responsePage.setContent(createResultSet(orderOverviewItems.getContent()));
		responsePage.setTotalPages(orderOverviewItems.getTotalPages());
		responsePage.setTotalElements((int) orderOverviewItems.getTotalElements());
		return responsePage;
	}

	private List<TTOrderOverviewItem> createResultSet(List<VOrderOverview> overviews) {
		return overviews.stream().map(overview -> {
			OrderType orderType = OrderType.getById(overview.getOrderType());
			finalizeOverview(overview);
			return mapByOrderType(overview, orderType);
		}).toList();

	}

	private void finalizeOverview(VOrderOverview overview) {
		mapToFurtherAddressType(overview);
		getDocumentCategoryTranslationByDocType(overview);
		getReferencesById(overview);
		mapProductName(overview);
		getOrderStatusTranslation(overview);
	}

	public void getDocumentCategoryTranslationByDocType(VOrderOverview overview) {
		List<String> translatedDocumentCategories = new ArrayList<>();
		List<String> ids = Arrays.stream(Objects.requireNonNullElse(overview.getIncludedDocuments(), "").split(SEPARATOR)).toList();
		if (StringUtils.isNotBlank(ids.get(0))) {
			translatedDocumentCategories = getDocumentCategoryTranslationByDocType(ids);
		}
		overview.setIncludedDocuments(StringUtils.join(translatedDocumentCategories, SEPARATOR));
	}

	public void getReferencesById(VOrderOverview overview) {
		List<String> referenceStrings = new ArrayList<>(Arrays.stream(Objects.requireNonNullElse(overview.getOrderReferences(), "").split(SEPARATOR)).toList());
		referenceStrings.removeIf(StringUtils::isBlank);

		List<RoadOrderReference> referencesRoad = new ArrayList<>();
		List<AirOrderReference> referencesAir = new ArrayList<>();
		List<SeaOrderReference> referencesSea = new ArrayList<>();
		if (ORDER_TYPES_ROAD.contains(overview.getOrderType())) {
			referencesRoad = orderReferenceRepository.findOrderReferencesByReferenceAndOrderId(referenceStrings, overview.getOrderId());
		} else if (ORDER_TYPES_SEA.contains(overview.getOrderType())) {
			referencesSea = seaOrderReferenceRepository.findSeaOrderReferencesByReferenceAndOrderId(referenceStrings, overview.getOrderId());
		} else {
			referencesAir = airOrderReferenceRepository.findAirOrderReferencesByReferenceAndOrderId(referenceStrings, overview.getOrderId());
		}

		overview.setAllReferencesRoad(referencesRoad);
		overview.setAllReferencesAir(referencesAir);
		overview.setAllReferencesSea(referencesSea);
	}

	private List<String> getDocumentCategoryTranslationByDocType(List<String> docTypeIds) {
		List<DocumentType> documentTypes = new ArrayList<>();
		for (String docTypeId : docTypeIds) {
			// We avoid a single call with multiple docTypeIds in order to cache each result
			// This should improve overall performance and reduce DB load, as we can cache the results of each docType for multiple requests
			cachedOrderOverviewService.loadDocumentTypeById(docTypeId).ifPresent(documentTypes::add);
		}
		return documentTypes.stream().map(dt -> translator.toLocale(dt.getCategory())).toList();
	}

	public void mapToFurtherAddressType(VOrderOverview overview) {
		List<String> furtherAddressType = Stream.of(Objects.requireNonNullElse(overview.getFurtherCodes(), "").split(SEPARATOR))
				.filter(ref -> (StringUtils.isNotEmpty(ref) && (StringUtils.isNotBlank(ref)))).toList();
		try {
			List<OptionDto> furtherTypes = furtherAddressTypeService.getFurtherAddressTypes(userContextService.getBusinessDomain());
			overview.setFurtherTypes(furtherTypes.stream().filter(acct -> furtherAddressType.contains(acct.getCode())).toList());
		} catch (ErrorIdExternalServiceNotAvailableException ex) {
			OptionDto errorOption = new OptionDto();
			errorOption.setCode(ERROR);
			errorOption.setDescription(ERROR);
			overview.setFurtherTypes(List.of(errorOption));
		}
	}

	protected void mapProductName(VOrderOverview overview) {
		if (!OrderType.isAirOrderType(OrderType.getById(overview.getOrderType()))) {
			String productName = cachedOrderOverviewService.getProductNameOrError(overview.getProduct(), overview.getDivision());
			overview.setProductName(productName);
		} else if (OrderType.isAirOrderType(OrderType.getById(overview.getOrderType()))) {
			String productName = cachedOrderOverviewService.getAirProductNameOrError(overview.getProduct());
			overview.setProductName(productName);
		} else {
			overview.setProductName(overview.getProduct());
		}
	}

	public void getOrderStatusTranslation(VOrderOverview overview) {
		if (overview.getStatus() == null) {
			return;
		}
		overview.setStatusTranslated(translationMapper.translatedLabel(overview.getStatus().getLabel()));
	}

	private TTOrderOverviewItem mapByOrderType(VOrderOverview overview, OrderType orderType) {
		TTOrderOverviewItem overviewItem;
		switch (orderType) {
		case AIRIMPORTORDER, AIREXPORTORDER, SEAEXPORTORDER, SEAIMPORTORDER -> overviewItem = mapper.mapASL(overview);
		case ROADFORWARDINGORDER -> overviewItem = mapper.mapRoadForward(overview);
		case ROADCOLLECTIONORDER -> overviewItem = mapper.mapRoadCollection(overview);
		default -> throw new InputMismatchException("No ordertype with id " + orderType + " defined for Overview");
		}
		finalizeOverviewItem((TTBasicOrderOverview) overviewItem);
		return overviewItem;
	}

	private void finalizeOverviewItem(TTBasicOrderOverview orderOverview) {
		orderOverview.setReferences(translateReferences(orderOverview.getReferences()));
	}

	private List<TTOrderReference> translateReferences(List<TTOrderReference> references) {
		return references.stream().map(this::translateReference).toList();
	}

	private TTOrderReference translateReference(TTOrderReference ref) {
		if (ref.getReferenceCode() == null && ref.getReferenceCodeASL() == null) {
			return ref;
		}

		String code = ref.getReferenceCode() != null ? ref.getReferenceCode().name() : ref.getReferenceCodeASL().name();
		String value = ref.getReferenceCode() != null ? ref.getReferenceCode().getValue() : ref.getReferenceCodeASL().getValue();

		String refCodeLabel = ReferenceTranslation.getByName(code).map(ReferenceTranslation::getLabel).orElse(value);
		ref.setText(translationMapper.translatedLabel(refCodeLabel));
		return ref;
	}
}
