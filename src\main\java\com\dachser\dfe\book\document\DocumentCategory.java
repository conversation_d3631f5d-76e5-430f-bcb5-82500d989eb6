package com.dachser.dfe.book.document;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@AllArgsConstructor
@Getter
public enum DocumentCategory {
    //@formatter:off
    CUSTOMS("label.text.customs_documents_label", 2),
    ORDER("label.text.order_label", 7),
    COLLECTION_ORDER("label.text.collection_order_documents_label", 1),
    DANGEROUS_GOOD("label.text.dangerous_goods_documents",3),

    UNKNOWN("unknown", 8 );
    //@formatter:on

    private final String label;
    private final int rank;

    public static DocumentCategory getByLabel(String label) {
        return Arrays.stream(DocumentCategory.values())
                       .filter(Objects::nonNull)
                       .filter(documentCategory -> Objects.equals(documentCategory.getLabel(), label))
                       .findFirst().orElse(UNKNOWN);
    }
}