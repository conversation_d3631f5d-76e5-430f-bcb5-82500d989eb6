package com.dachser.dfe.book.order.validation.road;

import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.model.ValidationResultEntryDto;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.validation.Messages;
import com.dachser.dfe.book.order.validation.PayloadProvidingValidator;
import com.dachser.dfe.book.service.freightterm.FreightTermService;
import com.dachser.dfe.book.user.BusinessDomainProvider;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraintvalidation.HibernateConstraintViolationBuilder;

@RequiredArgsConstructor
public class FreightTermValidator implements ConstraintValidator<FreightTermValid, ForwardingOrder>, PayloadProvidingValidator {

	private final FreightTermService freightTermService;

	private final BusinessDomainProvider businessDomainProvider;

	private final Translator translator;

	@Override
	public boolean isValid(ForwardingOrder roadOrder, ConstraintValidatorContext context) {

		context.disableDefaultConstraintViolation();
		final boolean valid = freightTermService.validateFreightTermForOrder(roadOrder, businessDomainProvider.getBusinessDomain());
		if (!valid) {
			final HibernateConstraintViolationBuilder invalidFreightTerm = createBuilderWithErrorType(context, translator.toLocale(Messages.INVALID_INPUT),
					ValidationResultEntryDto.ErrorTypeEnum.CONSTRAINT_VIOLATION);
			invalidFreightTerm.addPropertyNode("freightTerm").addConstraintViolation();
		}
		return valid;
	}
}
