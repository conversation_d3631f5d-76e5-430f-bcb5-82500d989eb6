package com.dachser.dfe.book.customer;

import com.dachser.dfe.book.user.model.OrderOptions;
import com.dachser.dfe.book.user.model.RoadCustomer;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.dachser.dfe.book.model.FurtherAddressType.CONSIGNEE_EXPORT_CERTIFICATION;
import static com.dachser.dfe.book.model.FurtherAddressType.COVER_ADDRESS;
import static com.dachser.dfe.book.model.FurtherAddressType.DECONSOLIDATOR_ADDRESS;
import static com.dachser.dfe.book.model.FurtherAddressType.DIFFERING_INVOICE_ADDRESS;
import static com.dachser.dfe.book.model.FurtherAddressType.FINAL_DELIVERY_ADDRESS;

public class AddressTypesFilterUtil {

	private AddressTypesFilterUtil() {
		// hidden constructor
	}

	public static List<String> getFilterForRoadCustomer(Optional<RoadCustomer> roadCustomerOptional) {
		List<String> keysToFilter = new ArrayList<>();
		roadCustomerOptional.ifPresent(customer -> {
			final OrderOptions customerOrderOptions = customer.getOrderOptions();
			if (Boolean.FALSE.equals(customerOrderOptions.getCoverAddress())) {
				keysToFilter.add(COVER_ADDRESS.getKey());
			}
			if (Boolean.FALSE.equals(customerOrderOptions.getDeconsolidatorAddress())) {
				keysToFilter.add(DECONSOLIDATOR_ADDRESS.getKey());
			}
			if (Boolean.FALSE.equals(customerOrderOptions.getDifferingInvoiceAddress())) {
				keysToFilter.add(DIFFERING_INVOICE_ADDRESS.getKey());
			}
			if (Boolean.FALSE.equals(customerOrderOptions.getFinalDeliveryAddress())) {
				keysToFilter.add(FINAL_DELIVERY_ADDRESS.getKey());
			}
			if (Boolean.FALSE.equals(customerOrderOptions.getConsigneeExportCertification())) {
				keysToFilter.add(CONSIGNEE_EXPORT_CERTIFICATION.getKey());
			}
		});

		return keysToFilter;
	}
}
