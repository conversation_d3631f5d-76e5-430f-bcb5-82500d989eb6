package com.dachser.dfe.book.document;

import com.dachser.bi.common.validation.service.pri.api.file.bean.FileWrapperBean;
import com.dachser.dfe.book.BaseOpenApiTest;
import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.model.BasicDocumentDto;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.BooleanResultDto;
import com.dachser.dfe.book.model.DocumentDownloadDto;
import com.dachser.dfe.book.model.DocumentGroupDto;
import com.dachser.dfe.book.model.DocumentInfoDto;
import com.dachser.dfe.book.model.DocumentResponseDto;
import com.dachser.dfe.book.model.DocumentTypeDto;
import com.dachser.dfe.book.model.ExtensionDto;
import com.dachser.dfe.book.model.GenericErrorDto;
import com.dachser.dfe.book.model.OrderTypeDto;
import com.dachser.dfe.book.model.RoadForwardingOrderDto;
import com.dachser.dfe.book.model.SegmentDto;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderService;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.Header;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import io.restassured.module.mockmvc.specification.MockMvcRequestSpecification;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import java.util.Random;
import java.util.UUID;

import static io.restassured.module.mockmvc.RestAssuredMockMvc.given;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class DocumentsApiHandlerTest extends BaseOpenApiTest implements ResourceLoadingTest {
	private static final String CUSTOMER_NUMBER = "00012345";

	private final ObjectMapper objectMapper = new ObjectMapper();

	private final Random random = new Random();

	@Autowired
	OrderMapper orderMapper;

	@Autowired
	private WebApplicationContext webApplicationContext;

	@Autowired
	private DocumentService documentService;

	@Autowired
	private DocumentTypeRepository documentTypeRepository;

	@Autowired
	private DocumentRepositoryFacade documentRepository;

	@Autowired
	private OrderService orderService;

	@Autowired
	private FileStorageCleanup fileStorageCleanup;

	@BeforeEach
	public void setUp() {
	}

	@AfterEach
	public void cleanUp() {
		fileStorageCleanup.deleteAllFoldersInBasePath();
	}

	private Order loadForwardingOrder(String customerNumber, String resourceName) throws IOException {
		RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert(resourceName, new TypeReference<>() {
		});
		final Order order = orderMapper.map(forwardingOrder);
		if (order.getShipmentNumber() != null) {
			setRandomShipmentNumber(order);
		}
		order.setCustomerNumber(customerNumber);
		return order;
	}

	private void setRandomShipmentNumber(Order order) {
		order.setShipmentNumber(Math.abs(Long.valueOf(random.nextInt())));
	}

	private Order createOrder() throws IOException {
		final Order order = loadForwardingOrder(CUSTOMER_NUMBER, "orders/new-forwarding-order-complete-valid.json");
		return orderService.createNewTestOrder(order);
	}

	private MockHttpServletRequestBuilder createRequest(Long orderId, String documentTypeId) {
		return createRequest(orderId, documentTypeId, CUSTOMER_NUMBER);
	}

	private MockHttpServletRequestBuilder createRequest(Long orderId, String documentTypeId, String customerNumber) {
		return createRequest(orderId, documentTypeId, customerNumber, "edn.pdf");
	}

	private MockHttpServletRequestBuilder createRequest(Long orderId, String documentTypeId, String customerNumber, String documentName) {
		// @formatter:off
		final MockHttpServletRequestBuilder request = MockMvcRequestBuilders.multipart("/documents/")
				.file(new MockMultipartFile("file", "orig.pdf", "application/pdf", "test".getBytes()))
				.param("orderId", orderId!=null ? orderId.toString() : null)
				.param("documentTypeId", documentTypeId)
				.param("documentName", documentName)
				.param("mimeType", "application/pdf");
		// @formatter:on

		addValidCustomerNumberParam(request, customerNumber);
		addRoadCustomerSegment(request);
		Header validAccessTokenHeader = getValidAccessTokenHeader();
		request.header(validAccessTokenHeader.getName(), validAccessTokenHeader.getValue());
		// @formatter:on
		return request;
	}

	@Nested
	class Preferences {
		@Nested
		class WithAccessToken {
			@Nested
			class ExtensionsRequest {
				@Test
				void shouldReturn200() {
					MockMvcRequestSpecification request = givenRequest();

					final MockMvcResponse response = request.get(buildUrl("/extensions"));
					assertEquals(200, response.statusCode());
					final List<ExtensionDto> content = response.getBody().as(new TypeRef<>() {
					});
					content.stream().findFirst().ifPresent(extension -> {
						assertTrue(StringUtils.isNotEmpty(extension.getLabel()));
						assertTrue(StringUtils.isNotEmpty(extension.getDescription()));
					});
				}
			}

			@Nested
			class DocumentTypesRequest {
				@Test
				void shouldReturn200ForRoadDocumentTypes() {
					MockMvcRequestSpecification request = givenRequest();

					addRoadOrderTypeParam(request);

					final MockMvcResponse response = request.get(buildUrl("/documents/document-types"));
					assertEquals(200, response.statusCode());
					final List<DocumentTypeDto> content = response.getBody().as(new TypeRef<>() {
					});

					content.stream().findFirst().ifPresent(type -> {
						assertNotNull(type.getTypeId());
						assertTrue(StringUtils.isNotEmpty(type.getCategory()));
						assertTrue(StringUtils.isNotEmpty(type.getDescription()));
					});

					// a road order has the customs types at the start(if lang = eng) and requires to have these to on position 1 and 2
					assertEquals("Commercial invoice", content.get(0).getDescription());
					assertEquals("Proforma invoice", content.get(1).getDescription());
					content.stream().filter(documentTypeDto -> documentTypeDto.getType().equals("GGDGN")).findFirst().ifPresent(type -> {
						assertEquals("GGDGN", type.getType());
						assertEquals("Dangerous Goods Note (DGN)", type.getDescription());
					});

					content.stream().filter(documentTypeDto -> documentTypeDto.getType().equals("GGETC")).findFirst().ifPresent(type -> {
						assertEquals("GGETC", type.getType());
						assertEquals("Other dangerous goods documents", type.getDescription());
					});
				}

				@Test
				void shouldReturn200ForAirDocumentTypes() {
					MockMvcRequestSpecification request = givenRequest();

					addAirOrderTypeParam(request);

					final MockMvcResponse response = request.get(buildUrl("/documents/document-types"));
					assertEquals(200, response.statusCode());
					final List<DocumentTypeDto> content = response.getBody().as(new TypeRef<>() {
					});
					content.stream().findFirst().ifPresent(type -> {
						assertNotNull(type.getTypeId());
						assertTrue(StringUtils.isNotEmpty(type.getCategory()));
						assertTrue(StringUtils.isNotEmpty(type.getDescription()));
					});

					// an air order has the customs types at the start(if lang = eng) and requires to have commercial invoice on first
					// and the rest in alphabetical order
					assertEquals("Commercial invoice*", content.get(0).getDescription());
					assertEquals("Certificate of origin", content.get(1).getDescription());
				}

				@Test
				void shouldReturn200ForSeaDocumentTypes() {
					MockMvcRequestSpecification request = givenRequest();

					addSeaOrderTypeParam(request);

					final MockMvcResponse response = request.get(buildUrl("/documents/document-types"));
					assertEquals(200, response.statusCode());
					final List<DocumentTypeDto> content = response.getBody().as(new TypeRef<>() {
					});
					content.stream().findFirst().ifPresent(type -> {
						assertNotNull(type.getTypeId());
						assertTrue(StringUtils.isNotEmpty(type.getCategory()));
						assertTrue(StringUtils.isNotEmpty(type.getDescription()));
					});

					// an air order has the customs types at the start(if lang = eng) and requires to have commercial invoice on first
					// and the rest in alphabetical order
					assertEquals("Certificate", content.get(0).getDescription());
					assertEquals("Commercial invoice", content.get(2).getDescription());
				}

				@Test
				void shouldReturn200ForAirDocumentTypesAndHaveSortingInGerman() {
					MockMvcRequestSpecification request = givenRequest().header(HttpHeaders.ACCEPT_LANGUAGE, new Locale("de", "DE").toLanguageTag()).body("{}");

					addAirOrderTypeParam(request);

					final MockMvcResponse response = request.get(buildUrl("/documents/document-types"));
					assertEquals(200, response.statusCode());
					final List<DocumentTypeDto> content = response.getBody().as(new TypeRef<>() {
					});
					content.stream().findFirst().ifPresent(type -> {
						assertNotNull(type.getTypeId());
						assertTrue(StringUtils.isNotEmpty(type.getCategory()));
						assertTrue(StringUtils.isNotEmpty(type.getDescription()));
					});

					// in german the customs types are not on first, therefore the position is changed
					// at first Handelsrechnung then other documents in alphabetical order
					assertEquals("Handelsrechnung*", content.get(2).getDescription());
					assertEquals("Ausfuhrbegleitdokument", content.get(3).getDescription());
				}
			}
		}
	}

	@Nested
	class uploadDocument {
		@Nested
		class GivenValidAccessToken {
			MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

			@Nested
			class ValidRequest {

				@Test
				void uploadDocument() throws Exception {

					final Order order = createOrder();

					MockHttpServletRequestBuilder request = createRequest(order.getOrderId(), null);
					MvcResult response = mockMvc.perform(request).andExpect(status().is(200)).andReturn();
					DocumentResponseDto uploadResponseObject = objectMapper.readValue(response.getResponse().getContentAsString(), DocumentResponseDto.class);

					assertThat(uploadResponseObject.getUploadStatus()).isEqualTo(DocumentResponseDto.UploadStatusEnum.SUCCESS);
				}

				@Test
				void update() throws Exception {

					Order order = createOrder();

					MockHttpServletRequestBuilder request = createRequest(order.getOrderId(), null);

					MvcResult uploadResponse = mockMvc.perform(request).andExpect(status().is(200)).andReturn();
					DocumentResponseDto uploadResponseObject = objectMapper.readValue(uploadResponse.getResponse().getContentAsString(), DocumentResponseDto.class);

					BasicDocumentDto updateObject = new BasicDocumentDto();

					updateObject.setOrderId(order.getOrderId());
					updateObject.setStartProcessing(false);
					updateObject.setDocumentTypeId(1);
					updateObject.setExtension("PDF");

					MockMvcRequestSpecification updateRequest = givenWriteRequest().body(updateObject);
					addValidCustomerNumberParam(updateRequest);
					addRoadCustomerSegment(updateRequest);
					final MockMvcResponse updateResponse =updateRequest.put(buildUrl("/documents/{documentId}"), uploadResponseObject.getDocumentId().toString());
					assertEquals(200, updateResponse.statusCode());
					assertTrue(updateResponse.getBody().as(BooleanResultDto.class).getResult());
					final List<Document> documentsOfOrder = documentService.getDocumentsOfOrder(order);
					assertEquals(1, documentsOfOrder.size());
				}

				@Test
				void delete() throws Exception {
					Order order = createOrder();

					MockHttpServletRequestBuilder request = createRequest(order.getOrderId(), null);

					MvcResult uploadResponse = mockMvc.perform(request).andExpect(status().is(200)).andReturn();
					ObjectMapper objectMapper = new ObjectMapper();
					DocumentResponseDto uploadResponseObject = objectMapper.readValue(uploadResponse.getResponse().getContentAsString(), DocumentResponseDto.class);

					MockMvcRequestSpecification deleteRequest = givenWriteRequest();
					addValidCustomerNumberParam(deleteRequest);
					addRoadCustomerSegment(deleteRequest);
					final MockMvcResponse deleteResponse = deleteRequest.delete(buildUrl("/documents/{documentId}"), uploadResponseObject.getDocumentId().toString());
					assertEquals(200, deleteResponse.statusCode());
					assertTrue(deleteResponse.getBody().as(BooleanResultDto.class).getResult());
				}

			}

			@Nested
			class InvalidRequest {
				@Test
				void shouldReturn404() {
					MockMvcRequestSpecification updateRequest = givenWriteRequest().body("{}");
					addValidCustomerNumberParam(updateRequest);
					addRoadCustomerSegment(updateRequest);
					final MockMvcResponse updateResponse = updateRequest.put(buildUrl("/documents/42000"));
					assertEquals(404, updateResponse.statusCode());
				}

				@Test
				void updateToMissingOrderId() throws Exception {
					Order order = createOrder();

					MockHttpServletRequestBuilder request = createRequest(order.getOrderId(), null);

					MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
					MvcResult uploadResponse = mockMvc.perform(request).andExpect(status().is(200)).andReturn();
					ObjectMapper objectMapper = new ObjectMapper();
					DocumentResponseDto uploadResponseObject = objectMapper.readValue(uploadResponse.getResponse().getContentAsString(), DocumentResponseDto.class);

					BasicDocumentDto updateObject = new BasicDocumentDto();
					final long orderId = 1000L;
					updateObject.setOrderId(orderId);
					updateObject.setDocumentTypeId(1);
					updateObject.setStartProcessing(true);

					MockMvcRequestSpecification updateRequest = givenWriteRequest().body(updateObject);
					addValidCustomerNumberParam(updateRequest);
					addRoadCustomerSegment(updateRequest);
					final MockMvcResponse updateResponse = updateRequest.put(buildUrl("/documents/{documentId}"), uploadResponseObject.getDocumentId().toString());
					assertEquals(404, updateResponse.statusCode());

				}

				@Test
				void shouldNotAllowUploadForDifferentCustomerNumber() throws Exception {
					MockHttpServletRequestBuilder request = createRequest(null, null, VALID_CUST_NO_AIR);
					ResultActions perform = mockMvc.perform(request);
					perform.andExpect(status().is(404));
				}

				@Test
				void shouldReturn400OOnTooLongDocumentName() throws Exception {
					Order order = createOrder();

					MockHttpServletRequestBuilder request = createRequest(order.getOrderId(), null);

					MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
					MvcResult uploadResponse = mockMvc.perform(request).andExpect(status().is(200)).andReturn();

					DocumentResponseDto uploadResponseObject = objectMapper.readValue(uploadResponse.getResponse().getContentAsString(), DocumentResponseDto.class);

					// Create an order
					BasicDocumentDto updateObject = new BasicDocumentDto();

					updateObject.setOrderId(order.getOrderId());
					updateObject.setStartProcessing(false);
					updateObject.setDocumentTypeId(1);
					updateObject.setDocumentName(StringUtils.repeat("a", 258));

					MockMvcRequestSpecification updateRequest = givenWriteRequest().body(updateObject);
					addValidCustomerNumberParam(updateRequest);
					addRoadCustomerSegment(updateRequest);
					final MockMvcResponse updateResponse = updateRequest.put(buildUrl("/documents/{documentId}"), uploadResponseObject.getDocumentId().toString());
					assertEquals(400, updateResponse.statusCode());
					List<GenericErrorDto> errors = updateResponse.getBody().as(new TypeRef<>() {
					});
					assertEquals(1, errors.size());
					assertEquals("documentName", errors.get(0).getField());

				}
			}

		}

		@Nested
		class GivenNoAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given().header("Content-Type", "application/json").body("{}");

					final MockMvcResponse response = request.get(buildUrl("/documents/"));
					assertEquals(401, response.statusCode());
				}
			}
		}
	}

	@Nested
	class updateDocument {

		@Nested
		class ValidRequest {

			@Test
			void upload() throws Exception {

				Order order = createOrder();

				MockHttpServletRequestBuilder request = createRequest(order.getOrderId(), null);

				MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
				MvcResult response = mockMvc.perform(request).andExpect(status().is(200)).andReturn();

			}

			@Test
			void update() throws Exception {
				Order order = createOrder();				
				
				MockHttpServletRequestBuilder request = createRequest(order.getOrderId(), null);

				MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
				MvcResult uploadResponse = mockMvc.perform(request).andExpect(status().is(200)).andReturn();

				DocumentResponseDto uploadResponseObject = objectMapper.readValue(uploadResponse.getResponse().getContentAsString(), DocumentResponseDto.class);

				// Create an order
				BasicDocumentDto updateObject = new BasicDocumentDto();

				updateObject.setOrderId(order.getOrderId());
				updateObject.setStartProcessing(false);
				updateObject.setDocumentTypeId(1);
				updateObject.setExtension("PDF");

				MockMvcRequestSpecification updateRequest = givenWriteRequest().body(updateObject);
				addValidCustomerNumberParam(updateRequest);
				addRoadCustomerSegment(updateRequest);
				final MockMvcResponse updateResponse = updateRequest.put(buildUrl("/documents/{documentId}"), uploadResponseObject.getDocumentId().toString());
				assertEquals(200, updateResponse.statusCode());
				assertTrue(updateResponse.getBody().as(BooleanResultDto.class).getResult());
				final List<Document> documentsOfOrder = documentService.getDocumentsOfOrder(order);
				assertEquals(1, documentsOfOrder.size());
			}

			@Test
			void delete() throws Exception {
				Order order = createOrder();

				MockHttpServletRequestBuilder request = createRequest(order.getOrderId(), null);

				MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
				MvcResult uploadResponse = mockMvc.perform(request).andExpect(status().is(200)).andReturn();
				ObjectMapper objectMapper = new ObjectMapper();
				DocumentResponseDto uploadResponseObject = objectMapper.readValue(uploadResponse.getResponse().getContentAsString(), DocumentResponseDto.class);

				MockMvcRequestSpecification deleteRequest = givenWriteRequest();
				addValidCustomerNumberParam(deleteRequest);
				addRoadCustomerSegment(deleteRequest);
				final MockMvcResponse deleteResponse = deleteRequest.delete(buildUrl("/documents/{documentId}"), uploadResponseObject.getDocumentId().toString());
				assertEquals(200, deleteResponse.statusCode());
				assertTrue(deleteResponse.getBody().as(BooleanResultDto.class).getResult());
			}

		}

		@Nested
		class InvalidRequest {
			@Test
			void shouldReturn404() {
				MockMvcRequestSpecification updateRequest = givenWriteRequest();
				addValidCustomerNumberParam(updateRequest);
				addRoadCustomerSegment(updateRequest);
				final MockMvcResponse updateResponse = updateRequest.put(buildUrl("/documents/-1"));
				assertEquals(404, updateResponse.statusCode());
			}

			@Test
			void updateToMissingOrderId() throws Exception {
				Order order = createOrder();

				MockHttpServletRequestBuilder request = createRequest(order.getOrderId(), null);

				MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
				MvcResult uploadResponse = mockMvc.perform(request).andExpect(status().is(200)).andReturn();
				ObjectMapper objectMapper = new ObjectMapper();
				DocumentResponseDto uploadResponseObject = objectMapper.readValue(uploadResponse.getResponse().getContentAsString(), DocumentResponseDto.class);

				BasicDocumentDto updateObject = new BasicDocumentDto();
				final long orderId = 1000L;
				updateObject.setOrderId(orderId);
				updateObject.setDocumentTypeId(1);
				updateObject.setStartProcessing(true);

				MockMvcRequestSpecification updateRequest = givenWriteRequest().body(updateObject);
				addValidCustomerNumberParam(updateRequest);
				addRoadCustomerSegment(updateRequest);
				final MockMvcResponse updateResponse = given().postProcessors(csrf()).spec(updateRequest)
						.put(buildUrl("/documents/{documentId}"), uploadResponseObject.getDocumentId().toString());
				assertEquals(404, updateResponse.statusCode());

			}

			@Test
			void shouldReturn404IfUserNotAllowedToUpdateDocument() {
				String customerNumber = "12345678";
				DocumentResponseDto documentResponseDto = documentService.uploadDocument(customerNumber, null, "TEST", null, "harmless", MediaType.APPLICATION_PDF,
						new MockMultipartFile("file", "orig_harmless", "application/pdf", "test".getBytes()));
				BasicDocumentDto basicDocumentDto = new BasicDocumentDto();
				basicDocumentDto.setDocumentName("Test2");
				basicDocumentDto.setDocumentId(documentResponseDto.getDocumentId());
				final MockMvcRequestSpecification documentsRequest = givenWriteRequest().body(basicDocumentDto).queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.AIR);
				addValidCustomerNumberParam(documentsRequest, VALID_CUST_NO_AIR);
				final MockMvcResponse response = given().spec(documentsRequest).put(buildUrl("/documents/{documentId}"), documentResponseDto.getDocumentId().toString());
				assertEquals(404, response.statusCode());
			}

			@Test
			void shouldReturn404IfUserNotAllowedToUpdateOrder() throws IOException {
				RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
				});
				String customerNumber = "12345678";
				forwardingOrder.setCustomerNumber(customerNumber);
				BasicOrderDto newDraftOrder = (BasicOrderDto) orderService.createNewDraftOrder(forwardingOrder);
				DocumentResponseDto documentResponseDto = documentService.uploadDocument(VALID_CUST_NO_AIR, null, "TEST", null, "harmless", MediaType.APPLICATION_PDF,
						new MockMultipartFile("file", "orig_harmless", "application/pdf", "test".getBytes()));
				BasicDocumentDto basicDocumentDto = new BasicDocumentDto();
				basicDocumentDto.setDocumentName("Test");
				basicDocumentDto.setOrderId(newDraftOrder.getOrderId());
				final MockMvcRequestSpecification documentsRequest = givenWriteRequest().body(basicDocumentDto).queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.AIR);
				addValidCustomerNumberParam(documentsRequest, VALID_CUST_NO_AIR);
				final MockMvcResponse response = given().spec(documentsRequest).put(buildUrl("/documents/{documentId}"), documentResponseDto.getDocumentId().toString());
				assertEquals(404, response.statusCode());
			}

		}

	}

	@Nested
	class getDocumentTypes {
		@Nested
		class GivenValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void getRecentlyUsedDocumentTypes() throws Exception {
					Order order = createOrder();

					MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

					// Document 1
					final DocumentType documentType = new DocumentType();
					documentType.setDocumentTypeId(1);
					MockHttpServletRequestBuilder request = createRequest(order.getOrderId(), documentType.getDocumentTypeId().toString());
					mockMvc.perform(request).andExpect(status().is(200)).andReturn();

					// Document 2
					final DocumentType documentType2 = new DocumentType();
					documentType2.setDocumentTypeId(2);
					MockHttpServletRequestBuilder request2 = createRequest(order.getOrderId(), documentType2.getDocumentTypeId().toString());
					mockMvc.perform(request2).andExpect(status().is(200)).andReturn();

					// Document 3
					final DocumentType documentType3 = new DocumentType();
					documentType3.setDocumentTypeId(3);
					MockHttpServletRequestBuilder request3 = createRequest(order.getOrderId(), documentType3.getDocumentTypeId().toString());
					mockMvc.perform(request3).andExpect(status().is(200)).andReturn();

					// Document 4
					final DocumentType documentType4 = new DocumentType();
					documentType4.setDocumentTypeId(4);
					MockHttpServletRequestBuilder request4 = createRequest(order.getOrderId(), documentType4.getDocumentTypeId().toString());
					mockMvc.perform(request4).andExpect(status().is(200)).andReturn();

					MockMvcRequestSpecification typesRequest = givenRequest();
					addValidCustomerNumberParam(typesRequest);
					addRoadCustomerSegment(typesRequest);
					final MockMvcResponse response = typesRequest.get(buildUrl("/documents/recently-used-document-types"));
					assertEquals(200, response.statusCode());
					final List<DocumentTypeDto> content = response.getBody().as(new TypeRef<>() {
					});
					assertFalse(content.isEmpty());
					assertEquals(3, content.size());
				}
			}
		}
	}

	@Nested
	class uploadInvalidDocument {
		@Test
		void uploadFileWithVirusReturnsErrorInResponse() throws Exception {

			// eicar test string. Is not dangerous but will be recognised as virus
			String eicar = "X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*";

			// @formatter:off
			MockHttpServletRequestBuilder request = MockMvcRequestBuilders.multipart("/documents/")
					.file(new MockMultipartFile("file", "orig", "application/pdf", eicar.getBytes()))
					.param("document-type", "edn")
					.param("document-category", "edn")
					.param("document-name", "edn.pdf")
					.param("mimeType", "application/pdf")
					.param("extension", "application/pdf");

			addValidCustomerNumberParam(request);
			addRoadCustomerSegment(request);
			// @formatter:on

			MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
			MvcResult result = mockMvc.perform(request).andExpect(status().is(400)).andReturn();
			// TODO: should we check that we did not save the file locally?

			DocumentResponseDto actual = objectMapper.readValue(result.getResponse().getContentAsString(), DocumentResponseDto.class);
			assertThat(actual.getUploadStatus()).isEqualTo(DocumentResponseDto.UploadStatusEnum.FILE_MALICIOUS);
		}

		@Test
		void uploadFakePdfFile() throws Exception {
			when(fileCheckService.isFileExtensionCorrect(any(FileWrapperBean.class), any(String.class))).thenReturn(false);
			// @formatter:off
			MockHttpServletRequestBuilder request = MockMvcRequestBuilders.multipart("/documents/")
					.file(new MockMultipartFile("file", "orig", "application/pdf", "test".getBytes()))
					.param("document-type", "edn")
					.param("document-category", "edn")
					.param("document-name", "invalid.pdf")
					.param("mimeType", "application/pdf")
					.param("extension", "application/pdf");

			addValidCustomerNumberParam(request);
			addRoadCustomerSegment(request);
			// @formatter:on

			MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
			MvcResult result = mockMvc.perform(request).andExpect(status().is(400)).andReturn();
			DocumentResponseDto actual = objectMapper.readValue(result.getResponse().getContentAsString(), DocumentResponseDto.class);
			assertThat(actual.getUploadStatus()).isEqualTo(DocumentResponseDto.UploadStatusEnum.ERROR_INVALID_EXTENSION);
		}

	}

	@Nested
	class getDocuments {
		@Nested
		class GivenValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void getDocuments() throws Exception {
					final String documentName = "test.pdf";
					Order order = createOrder();
					final DocumentType documentType = new DocumentType();
					documentType.setType("edn");
					documentType.setCategory("label.text.customs_documents_label");
					documentType.setLabel("label");
					documentType.setOrderType(0);
					final DocumentType savedDocType = documentTypeRepository.save(documentType);
					Document doc = new Document();
					doc.setCustomerNumber("12345678");
					doc.setOrderId(order.getOrderId());
					doc.setDocumentType(savedDocType);
					doc.setDocumentName(documentName);
					doc.setFileType(FileType.PDF);
					doc.setSize(12L);
					doc.setStatus(DocumentStatus.NEW);
					doc.setCreator(UUID.randomUUID().toString());
					doc.setCreatedAt(Instant.now());
					doc.setLastEditor(UUID.randomUUID().toString());
					doc.setLastModified(Instant.now());
					final Document savedDocument = documentRepository.save(doc);

					final MockMvcRequestSpecification getDocumentsRequest = givenRequest().queryParam("orderId", order.getOrderId());
					final MockMvcResponse response = getDocumentsRequest.get(buildUrl("/v1/documents-grouped"));
					assertEquals(200, response.statusCode());
					final List<DocumentGroupDto> content = response.getBody().as(new TypeRef<>() {
					});
					final DocumentGroupDto documentGroup = content.get(0);
					DocumentInfoDto documentInfo = documentGroup.getDocumentInfo().get(0);
					assertEquals(documentName, documentInfo.getDocumentName());
					assertEquals("edn", documentInfo.getDocumentType());
					assertEquals(savedDocument.getDocumentId(), documentInfo.getDocumentId());
					assertEquals(savedDocument.getDocumentType().getLabel(), documentInfo.getDocumentClass());
					assertEquals(DocumentCategory.CUSTOMS.toString(), documentInfo.getDocumentCategoryEnum().toString());
					assertEquals(savedDocument.getDocumentType().getCategory(), documentInfo.getDocumentCategory());
				}
			}

			@Nested
			class InvalidRequest {
				@Test
				void shouldReturn404IfOrderNotFound() {
					final Long invalidOrderId = 10345L;
					final MockMvcRequestSpecification getDocumentsRequest = givenRequest().queryParam("orderId", invalidOrderId);
					final MockMvcResponse response = getDocumentsRequest.get(buildUrl("/v1/documents/"));
					assertEquals(404, response.statusCode());
				}

				@Test
				void shouldReturn404IfUserNotAllowedToSeeOrder() throws IOException {
					RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
					});
					String customerNumber = "12345678";
					forwardingOrder.setCustomerNumber(customerNumber);
					BasicOrderDto newDraftOrder = (BasicOrderDto) orderService.createNewDraftOrder(forwardingOrder);
					DocumentResponseDto documentResponseDto = documentService.uploadDocument(customerNumber, forwardingOrder.getOrderId(), "TEST", null, "harmless",
							MediaType.APPLICATION_PDF, new MockMultipartFile("file", "orig_harmless", "application/pdf", "test".getBytes()));
					final MockMvcRequestSpecification documentsRequest = givenRequest().queryParam("orderId", newDraftOrder.getOrderId());
					final MockMvcResponse response = given().spec(documentsRequest).get(buildUrl("/v1/documents"));
					assertEquals(404, response.statusCode());
				}

			}
		}
	}

	@Nested
	class downloadDocument {
		@Nested
		class ValidRequests {
			@Test
			void shouldDownloadDocument() throws Exception {
				Order order = createOrder();
				MockHttpServletRequestBuilder uploadDocumentRequest = createRequest(order.getOrderId(), null);
				MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
				MvcResult uploadResponse = mockMvc.perform(uploadDocumentRequest).andExpect(status().is(200)).andReturn();
				Long documentId = objectMapper.readValue(uploadResponse.getResponse().getContentAsString(), DocumentResponseDto.class).getDocumentId();
				String documentName = objectMapper.readValue(uploadResponse.getResponse().getContentAsString(), DocumentResponseDto.class).getDocumentName();

				final MockMvcRequestSpecification request = givenRequest();
				final MockMvcResponse downloadResponse = request.get(buildUrl("/v1/documents/{documentId}/download"), documentId.toString());
				final DocumentDownloadDto content = downloadResponse.getBody().as(new TypeRef<>() {
				});
				assertEquals(200, downloadResponse.statusCode());
				assertEquals("application/json", downloadResponse.getContentType());
				assertEquals(documentName, content.getDocumentName());
				assertEquals("dGVzdA==", content.getContent()); //base64 encoded
			}

			@Test
			void shouldReturnStatusNotFoundIfDocumentIsNotAvailable() {
				MockMvcRequestSpecification request = givenRequest();
				final MockMvcResponse downloadResponse = request.get(buildUrl("/v1/documents/{documentId}/download"), "167868");

				assertEquals(404, downloadResponse.statusCode());
			}
		}

		@Nested
		class InvalidRequests {

			@Test
			void shouldReturn404IfUserNotAllowedToSeeOrder() throws IOException {
				RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
				});
				String customerNumber = "12345678";
				forwardingOrder.setCustomerNumber(customerNumber);
				BasicOrderDto newDraftOrder = (BasicOrderDto) orderService.createNewDraftOrder(forwardingOrder);
				DocumentResponseDto documentResponseDto = documentService.uploadDocument(customerNumber, forwardingOrder.getOrderId(), "TEST", null, "harmless",
						MediaType.APPLICATION_PDF, new MockMultipartFile("file", "orig_harmless", "application/pdf", "test".getBytes()));
				final MockMvcRequestSpecification documentsRequest = givenRequest().queryParam("orderId", newDraftOrder.getOrderId());
				final MockMvcResponse response = documentsRequest.get(buildUrl("/v1/documents/{documentId}/download"), documentResponseDto.getDocumentId().toString());
				assertEquals(404, response.statusCode());
			}
		}
	}

	// region Helper

	private void addValidCustomerNumberParam(MockHttpServletRequestBuilder req) {
		addValidCustomerNumberParam(req, CUSTOMER_NUMBER);
	}

	private void addValidCustomerNumberParam(MockHttpServletRequestBuilder req, String customerNumber) {
		req.queryParam(CUSTOMER_NUMBER_PARAM, customerNumber);
	}

	private void addValidCustomerNumberParam(MockMvcRequestSpecification req, String customerNumber) {
		req.queryParam(CUSTOMER_NUMBER_PARAM, customerNumber);
	}

	private void addValidCustomerNumberParam(MockMvcRequestSpecification req) {
		addValidCustomerNumberParam(req, CUSTOMER_NUMBER);
	}

	private void addRoadOrderTypeParam(MockMvcRequestSpecification req) {
		req.queryParam(ORDER_TYPE_PARAM, OrderTypeDto.ROAD_FORWARDING_ORDER.getValue());
	}

	private void addAirOrderTypeParam(MockMvcRequestSpecification req) {
		req.queryParam(ORDER_TYPE_PARAM, OrderTypeDto.AIR_EXPORT_ORDER.getValue());
	}

	private void addSeaOrderTypeParam(MockMvcRequestSpecification req) {
		req.queryParam(ORDER_TYPE_PARAM, OrderTypeDto.SEA_EXPORT_ORDER.getValue());
	}

	private void addRoadCustomerSegment(MockHttpServletRequestBuilder req) {
		req.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.ROAD.getValue());
	}

	private void addRoadCustomerSegment(MockMvcRequestSpecification req) {
		req.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.ROAD.getValue());
	}

	// endregion
}
