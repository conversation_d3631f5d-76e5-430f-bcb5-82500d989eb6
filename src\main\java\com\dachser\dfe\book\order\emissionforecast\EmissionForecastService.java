package com.dachser.dfe.book.order.emissionforecast;

import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.user.UserContextService;
import com.dachser.dfe.emissionforecast.model.EFForecastRequestDto;
import com.dachser.dfe.emissionforecast.model.EFForecastResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmissionForecastService {

	private final EmissionForecastAdapter emissionForecastAdapter;

	private final EmissionForecastMapper emissionForecastMapper;

	private final UserContextService userContextService;

	public Optional<Double> getEmissionForecastForOrder(final Order order) {
		EFForecastRequestDto request = buildForecastRequest(order);
		return fetchEmissionForecast(request);
	}

	private EFForecastRequestDto buildForecastRequest(Order order) {
		String divisionKey = getDivisionKey(order);
		return emissionForecastMapper.mapToEFForecastRequestDto(order, divisionKey, order.getOrderType().getSegment());
	}

	private String getDivisionKey(Order order) {
		// If the order is a RoadOrder, try to get the division from the order itself
		if (order instanceof RoadOrder roadOrder) {
			Division division = roadOrder.getDivision();
			// If the division and its alternative key are available, use it
			if (division != null && division.getAlternativeDivisionKey() != null) {
				return division.getAlternativeDivisionKey();
			}
			// Otherwise, fallback to looking up the division via the customer number and order segment
			return userContextService.getFirstFoundDivision(order.getCustomerNumber(), order.getOrderType().getSegment()).getAlternativeDivisionKey();
		}
		// Default to Division.T if the order is not a RoadOrder
		return Division.T.getAlternativeDivisionKey();
	}

	private Optional<Double> fetchEmissionForecast(final EFForecastRequestDto request) {
		try {
			return emissionForecastAdapter.getEmissionForecast(request).map(EFForecastResponseDto::getCo2Equivalents);
		} catch (final RestClientException ex) {
			log.warn("No forecast received, Emission Forecast Service Call failed", ex);
			return Optional.empty();
		}
	}

}
