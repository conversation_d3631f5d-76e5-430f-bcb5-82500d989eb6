package com.dachser.dfe.book.mapper;

import com.dachser.dfe.book.model.FreightTermDto;
import com.dachser.dfe.book.order.mapper.FreightTermCodeMapperImpl;
import com.dachser.dfe.book.service.freightterm.FreightTermService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FreightTermCodeMapperTest {

	private final List<FreightTermDto> freightTerms = new ArrayList<>();

	@Mock
	private FreightTermService freightTermService;

	@InjectMocks
	private FreightTermCodeMapperImpl mapper;

	@BeforeEach
	void setUp() {
		FreightTermDto freightTerm01 = constructFreightTermWithSuffix("01");
		FreightTermDto freightTerm02 = constructFreightTermWithSuffix("02");
		freightTerms.add(freightTerm01);
		freightTerms.add(freightTerm02);
	}

	@Test
	void shouldMapKey() {
		FreightTermDto freightTermDto1 = freightTerms.get(0);
		when(freightTermService.getFreightTermByDachserKey(freightTermDto1.getDachserTermKey())).thenReturn(freightTermDto1);
		FreightTermDto freightTermDto = mapper.map(freightTermDto1.getDachserTermKey());
		assertNotNull(freightTermDto);
		assertThat(freightTermDto.getDachserTermKey()).isEqualTo("MOC");
		assertThat(freightTermDto.getHeadline()).isEqualTo("MOC01");
	}

	private FreightTermDto constructFreightTermWithSuffix(String suffix) {
		FreightTermDto freightTerm = new FreightTermDto();
		freightTerm.setDachserTermKey("MOC");
		freightTerm.setIncoTermKey("MOC");
		freightTerm.setHeadline("MOC" + suffix);
		return freightTerm;
	}
}
