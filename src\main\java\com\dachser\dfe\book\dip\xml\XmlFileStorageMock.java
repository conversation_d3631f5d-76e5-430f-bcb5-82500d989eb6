package com.dachser.dfe.book.dip.xml;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "true")
public class XmlFileStorageMock implements XmlFileStorageProvider {

	private final Map<String, String> storage = new HashMap<>();

	@Override
	public void saveXml(String order, Long shipmentNumber) {
		storage.put(String.valueOf(shipmentNumber), order);
	}

	@Override
	public String getXml(Long shipmentNumber) {
		return storage.getOrDefault(String.valueOf(shipmentNumber), "");
	}

	@Override
	public void cleanUpXmlFiles(int ageThresholdToDelete) {
		// Mock implementation does not need to delete old orders
	}
}
