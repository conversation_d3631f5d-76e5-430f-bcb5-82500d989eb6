package com.dachser.dfe.book.dangerousgoods;

import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.masterdata.thirdparty.model.MDTCalculationPointResult;
import com.dachser.masterdata.thirdparty.model.MDTDangerousGoodInformation;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface DangerousGoodsMasterDataMapper {

	@Mapping(source = "dgItemADR", target = ".")
	@Mapping(source = "dgItemADR.psn", target = "description")
	@Mapping(source = "dgItemADR.dgClass", target = "mainDanger")
	@Mapping(source = "dgItemADR.needTechChemName", target = "nosRequired")
	@Mapping(source = "dgItemADR.tunnelCode", target = "tunnelCode")
	@Mapping(source = "dgItemADR.subrisk", target = "subsidiaryHazardOne", qualifiedByName = "mapSubrisk1")
	@Mapping(source = "dgItemADR.subrisk", target = "subsidiaryHazardTwo", qualifiedByName = "mapSubrisk2")
	@Mapping(source = "dgItemADR.subrisk", target = "subsidiaryHazardThree", qualifiedByName = "mapSubrisk3")
	DangerousGoodDataItemDto mapRoad(MDTDangerousGoodInformation dangerousGoods);

	@Mapping(source = "resultValue", target = "resultValue", qualifiedByName = "mapPointResult")
	DangerousGoodsTransferListService.DgmIdAndQuantity mapDgmIdAndQuantity(MDTCalculationPointResult pointResult);

	private String mapSubrisks(String subrisk, int index) {
		if (subrisk == null || subrisk.isEmpty()) {
			return null;
		}
		String[] subrisks = StringUtils.strip(StringUtils.defaultString(subrisk), "()").split(",");
		if (subrisks.length > index) {
			return subrisks[index].trim();
		}
		return null;
	}

	@Named("mapSubrisk1")
	default String mapSubrisk1(String subrisk) {
		return mapSubrisks(subrisk, 0);
	}

	@Named("mapSubrisk2")
	default String mapSubrisk2(String subrisk) {
		return mapSubrisks(subrisk, 1);
	}

	@Named("mapSubrisk3")
	default String mapSubrisk3(String subrisk) {
		return mapSubrisks(subrisk, 2);
	}

	@Named("mapPointResult")
	default Integer mapPointResult(String resultValue) {
	    if (resultValue == null || !resultValue.contains(":")) {
	        return null;
	    }
	    try {
	        String[] parts = resultValue.split(":");
	        return Integer.parseInt(parts[1].trim());
	    } catch (NumberFormatException e) {
	        return null;
	    }
	}
}
