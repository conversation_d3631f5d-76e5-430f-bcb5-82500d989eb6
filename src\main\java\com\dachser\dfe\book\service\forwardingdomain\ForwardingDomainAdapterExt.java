package com.dachser.dfe.book.service.forwardingdomain;

import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.road.masterdata.api.ForwardingDomainControllerApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;

@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "false", matchIfMissing = true)
class ForwardingDomainAdapterExt implements ForwardingDomainAdapter {

	private final ForwardingDomainControllerApi forwardingDomainApi;

	private final CountryService countryService;

	@Override
	public String getForwardingDomain(int businessDomain, int branchId, String consigneeCountryCode, String shipperCountryCode) {
		try {
			String consigneeDachserCountryCode = countryService.mapToDachserCountryCode(consigneeCountryCode);
			String shipperDachserCountryCode = countryService.mapToDachserCountryCode(shipperCountryCode);
			return forwardingDomainApi.getForwardingDomain(businessDomain, branchId, consigneeDachserCountryCode, shipperDachserCountryCode);
		} catch (RestClientException exception) {
			throw NOT_AVAILABLE_EXCEPTION;
		}
	}
}
