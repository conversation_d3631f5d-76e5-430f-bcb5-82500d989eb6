package com.dachser.dfe.book.order.common.orderline;

import com.dachser.dfe.book.order.air.AirOrderLineHsCode;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AirOrderLineHsCodeRepository extends JpaRepository<AirOrderLineHsCode, Long> {

	List<AirOrderLineHsCode> findTop10ByOrderLine_Order_CreatorAndGoodsIsNotNullOrderByOrderLineHsCodeIdDesc(@NotNull String orderCreator);
}
