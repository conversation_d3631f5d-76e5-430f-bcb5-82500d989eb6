package com.dachser.dfe.book.customer;

import com.dachser.dfe.book.api.CustomerListApiDelegate;
import com.dachser.dfe.book.exception.DivisionUnresolvableException;
import com.dachser.dfe.book.mapper.DivisionMapper;
import com.dachser.dfe.book.model.CustomerDto;
import com.dachser.dfe.book.model.DivisionDto;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.model.SegmentDto;
import com.dachser.dfe.book.user.UserContextService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@RequiredArgsConstructor
@Component
@Slf4j
public class CustomerListApiHandler implements CustomerListApiDelegate {

	private final SegmentMapper segmentMapper;

	private final DivisionMapper divisionMapper;

	private final UserContextService userContextService;

	@Override
	public ResponseEntity<List<CustomerDto>> getCustomers(SegmentDto customerSegment) {
		final Segment segment = segmentMapper.map(customerSegment);
		final List<CustomerDto> customerDTOs = userContextService.getCustomers(segment);

		for (CustomerDto customerDto : customerDTOs) {
			enrichWithDivision(customerDto, segment);
		}
		List<CustomerDto> returnList = customerDTOs.stream().filter(c -> !Objects.isNull(c.getDivision())).toList();

		return ResponseEntity.ok(returnList);
	}


	private void enrichWithDivision(CustomerDto c, Segment segment) {
		try {
			DivisionDto division = divisionMapper.map(userContextService.getFirstFoundDivision(c.getCustomerNumber(), segment));
			c.setDivision(division);
		} catch (DivisionUnresolvableException exception) {
			log.error("Could not resolve division for customer {}, removing customer from customer-list afterwards", c.getCustomerNumber());
		}
	}

}
