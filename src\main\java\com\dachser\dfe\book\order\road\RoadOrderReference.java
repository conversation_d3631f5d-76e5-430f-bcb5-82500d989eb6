package com.dachser.dfe.book.order.road;

import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.validation.CompleteOrderValidation;
import com.dachser.dfe.book.order.validation.road.OrderReferenceValid;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Entity
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@OrderReferenceValid(groups = CompleteOrderValidation.class)
@Table(name = "order_reference_road")
public class RoadOrderReference {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long orderReferenceId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "order_id", nullable = false)
	@ToString.Exclude
	@EqualsAndHashCode.Exclude
	private Order order;

	@NotNull
	@Enumerated(EnumType.STRING)
	private ReferenceType referenceType;

	@Enumerated(EnumType.STRING)
	private RoadOrderReferenceSubtype referenceSubtype;

	@Size(max = 35)
	private String reference;
}
