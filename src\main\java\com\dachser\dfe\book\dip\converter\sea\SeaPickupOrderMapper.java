package com.dachser.dfe.book.dip.converter.sea;

import com.dachser.dfe.book.dip.converter.shared.AirSeaAddressMappingConfig;
import com.dachser.dfe.book.dip.converter.shared.AirSeaGoodsDescriptionMapper;
import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderContactCommunicationsMapper;
import com.dachser.dfe.book.dip.converter.shared.AirSeaPickupOrderMapper;
import com.dachser.dfe.book.dip.converter.shared.DateMapper;
import com.dachser.dfe.book.dip.converter.shared.StringNotEmptyConditionMapper;
import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.model.jaxb.order.asl.Address;
import com.dachser.dfe.book.model.jaxb.order.asl.Contact;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.sea.SeaOrder;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper(config = AirSeaAddressMappingConfig.class, componentModel = "spring", uses = { DateMapper.class, AirSeaOrderContactCommunicationsMapper.class, SeaOrderLineMapper.class,
		SeaOrderReferenceMapper.class, StringNotEmptyConditionMapper.class, AirSeaGoodsDescriptionMapper.class, SeaOrderCategoryMapper.class })
public interface SeaPickupOrderMapper extends AirSeaPickupOrderMapper<SeaOrder> {

	default List<ForwardingOrder.SeaFreightShipment.PickupOrder> mapPickupOrders(SeaOrder order) {
		// We need to map an delivery order in that case
		if (order.isDeliverToPort()) {
			return List.of();
		}
		final ForwardingOrder.SeaFreightShipment.PickupOrder pickupOrder = mapPickupOrder(order);
		return List.of(pickupOrder);
	}

	@Mapping(target = "loadingInstruction", source = ".", qualifiedByName = "mapLoadingInstruction")
	@Mapping(target = "orderPosition", source = ".")
	@Mapping(target = "pickUpDate", source = ".")
	@Mapping(target = "goodsDescription", source = ".", qualifiedByName = "mapGoodsDescriptionSea")
	@Mapping(target = "orderAddress", source = ".", qualifiedByName = "mapPickupAddress")
	@Mapping(target = "orderReference", source = "orderReferences")
	@Mapping(target = "customsGoods", constant = Types.CustomsGoods.NON_CUSTOMS_GOODS)
	@Mapping(target = "typeOfPickup", source = ".", qualifiedByName = "mapOrderCategory")
	@Mapping(target = "additionalOrderInformation", source = "allOrderLinesOrFCLOrderLines")
	ForwardingOrder.SeaFreightShipment.PickupOrder mapPickupOrder(SeaOrder order);

	@Named("mapLoadingInstruction")
	default List<String> mapLoadingInstruction(SeaOrder order) {
		if (order.isTailLiftCollection()) {
			return List.of(Types.LoadingInstruction.TLF);
		}
		return List.of();
	}

	@Named("mapPickupAddress")
	@Mapping(target = "address", source = ".", qualifiedByName = "mapPickupOrShipperAddress")
	@Mapping(target = "contact", source = "orderContact")
	@Mapping(target = "typeOfAddress", constant = Types.AddressTypes.PICKUP)
	ForwardingOrder.SeaFreightShipment.PickupOrder.OrderAddress mapPickupAddress(SeaOrder airOrder);

	@Named("mapPickupOrShipperAddress")
	default Address mapPickupOrShipperAddress(SeaOrder airOrder) {
		if (airOrder.getPickupAddress() != null) {
			return mapOrderAddress(airOrder.getPickupAddress());
		} else {
			return mapOrderAddress(airOrder.getShipperAddress());
		}
	}

	@InheritConfiguration(name = "mapAddress")
	Address mapOrderAddress(OrderAddress orderAddress);

	@InheritConfiguration(name = "mapContact")
	Contact mapOrderContact(OrderContact orderContact);

}
