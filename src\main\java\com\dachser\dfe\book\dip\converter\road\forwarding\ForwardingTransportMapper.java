package com.dachser.dfe.book.dip.converter.road.forwarding;

import com.dachser.dfe.book.dip.converter.road.GeneralTransportDataMapper;
import com.dachser.dfe.book.dip.converter.road.RoadTransportMapper;
import com.dachser.dfe.book.dip.converter.road.ShipmentAddressBuilder;
import com.dachser.dfe.book.dip.converter.shared.DateMapper;
import com.dachser.dfe.book.dip.converter.shared.SharedEdiReference;
import com.dachser.dfe.book.jpa.entity.CustomsType;
import com.dachser.dfe.book.model.SourceOfOrder;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.AdditionalReference;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.COD;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.GoodsValue;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.PackageIdentification;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.PartnerInformation;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentAddress;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentHeader;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentText;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.StoringPositions;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.Transport;
import com.dachser.dfe.book.order.common.OrderSscc;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static org.apache.commons.lang3.StringUtils.isEmpty;

@Mapper(componentModel = "spring", uses = { DateMapper.class, ForwardingShipmentLineMapper.class, ForwardingShipmentAddressMapper.class, GeneralTransportDataMapper.class,
		ForwardingPackingPositionMapper.class })
public interface ForwardingTransportMapper extends RoadTransportMapper<ShipmentText, PartnerInformation, GoodsValue, AdditionalReference, ShipmentAddress, ForwardingOrder> {

	Logger log = LoggerFactory.getLogger(ForwardingTransportMapper.class);

	String ADDRESS_TYPE_SHIPPER = "CZ";
	String ADDRESS_TYPE_CONSIGNEE = "CN";
	String ADDRESS_TYPE_FORWARDER = "FW";

	String SHIPMENT_NUMBER_TEXTTYPE = "MI";
	String FROST_PROTECTION_TEXTTYPE = "TH";

	String SHIPMENT_NUMBER_ADDITION = " W";
	String PALLET_LOCATIONS_TYPE = "INVOIC";
	String CUSTOMS_REFERENCETYPE = "EXD";

	@Mapping(target = "number", constant = "")
	@Mapping(target = "shipmentHeader", expression = "java(List.of(mapShipmentHeader(order)))")
	Transport map(ForwardingOrder order);

	default List<Transport> mapList(ForwardingOrder order) {
		return Collections.singletonList(map(order));
	}

	@Mapping(target = "ADRflag", source="dangerousGoodsOrder", qualifiedByName = "mapBoolean")
	@Mapping(target = "customsIndicator", source = "customsGoods", qualifiedByName = "mapBoolean")
	@Mapping(target = "consigneeCollectionIndicator", source = "order.selfCollection", qualifiedByName = "mapBoolean")
	@Mapping(target = "customerShipmentReference", expression = "java( order.getOrderNumber() != null? order.getOrderNumber() : \"\")")
	@Mapping(target = "additionalReference", source = "orderReferences", qualifiedByName = "mapReferences")
	@Mapping(target = "dachserProduct", source = "product")
	@Mapping(target = "shipmentLine", source = ".", qualifiedByName = "mapShipmentLines")
	@Mapping(target = "deliveryDateFixed", source = "order.fixDate")
	@Mapping(target = "division", source = "division")
	@Mapping(target = "goodsValue", source = ".")
	@Mapping(target = "orderGroup", source = "orderGroup")
	@Mapping(target = "originalTerm", source = "freightTerm")
	@Mapping(target = "packageIdentification", source = "ssccs")
	@Mapping(target = "shipmentAddress", source = ".", qualifiedByName = "customShipmentAddressMapping")
	@Mapping(target = "shipmentDate.date", source = "sendAt", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
	@Mapping(target = "shipmentText", source = "orderTexts")
	@Mapping(target = "tailLiftRequired", source = "order.tailLiftDelivery", qualifiedByName = "mapBoolean")
	@Mapping(target = "storingPositions", source = "palletLocations", qualifiedByName = "mapPalletLocations")
	@Mapping(target = "packagingAids", source = ".", conditionExpression = "java( order.getPackingPositions() != null && !order.getPackingPositions().isEmpty() )")
	@Mapping(target = "COD", source = ".")
	ShipmentHeader mapShipmentHeader(ForwardingOrder order);

	@Mapping(target = "SSCCBarCode", source = "sscc")
	PackageIdentification mapBarcode(OrderSscc orderSscc);

	List<PackageIdentification> mapBarcodes(List<OrderSscc> ssccs);

	default COD mapCOD(ForwardingOrder order) {
		if (order.isCashOnDelivery() && order.getCashOnDeliveryAmount() != null) {
			COD cod = new COD();
			cod.setAmount(order.getCashOnDeliveryAmount().floatValue());
			cod.setCurrency("EUR");
			cod.setCode("02");
			return cod;
		}
		return null;
	}

	@Named("mapPalletLocations")
	default List<StoringPositions> mapStoringPositions(BigDecimal palletLocations) {
		if (Objects.nonNull(palletLocations) && palletLocations.doubleValue() > 0) {
			StoringPositions storingPositions = new StoringPositions();
			storingPositions.setQuantity(palletLocations.floatValue());
			storingPositions.setCode(PALLET_LOCATIONS_TYPE);
			return List.of(storingPositions);
		}
		return new ArrayList<>();
	}

	default AdditionalReference createAdditionalReference(String code, String value) {
		AdditionalReference additionalReference = new AdditionalReference();
		additionalReference.setCode(code);
		additionalReference.setReference(value);
		return additionalReference;
	}

	default ShipmentAddress buildShipmentAddressFromOrder(ForwardingOrder order, String addressType) {
		ShipmentAddressBuilder<ShipmentAddress, ForwardingOrder, PartnerInformation> shipmentAddressBuilder = new ForwardingShipmentAddressBuilder();
		return shipmentAddressBuilder.buildShipmentAddressFromOrder(order, addressType, this::mapPartnerInformationForConsignee);

	}

	// Workaround - We don't have any information except the branchID for the forwarder, so we use the branchId as partnerID
	default ShipmentAddress buildShipmentAddressForForwarder(Integer branchId, String addressTypeForwarder) {
		ShipmentAddress shipmentAddress = new ShipmentAddress();
		shipmentAddress.setAddressType(addressTypeForwarder);
		PartnerInformation partnerInformation = new PartnerInformation();
		partnerInformation.setPartnerID("%08d".formatted(branchId));
		shipmentAddress.setPartnerInformation(partnerInformation);
		return shipmentAddress;
	}

	/**
	 * we create reduced address for shipper. As we have the shippers address in masterdata, we only need the customernumber
	 */
	default ShipmentAddress buildShipmentAddressForShipper(ForwardingOrder order, String addressTypeShipper) {
		ShipmentAddress shippersAddress = new ShipmentAddress();
		shippersAddress.setAddressType(addressTypeShipper);

		PartnerInformation partnerInformation = new PartnerInformation();
		partnerInformation.setPartnerID(order.getCustomerNumber());
		shippersAddress.setPartnerInformation(partnerInformation);
		return shippersAddress;
	}

	@AfterMapping
	default void mapShipmentNumberAsText(@MappingTarget ShipmentHeader mappedOrder, ForwardingOrder order) {
		ShipmentText shipmentNumberAsText = new ShipmentText();
		shipmentNumberAsText.setTextType(SHIPMENT_NUMBER_TEXTTYPE);
		shipmentNumberAsText.getText().add(String.format("%011d", order.getShipmentNumber()) + SHIPMENT_NUMBER_ADDITION);

		mappedOrder.getShipmentText().add(shipmentNumberAsText);
	}

	/**
	 * Mapping is done here, because of the complex mapping of the ID and GLN See functions: mapValidGlnOrNull, mapInvalidGlnAsIdOrNull If after the mapping shipper partnerID is
	 * not set, the principal ID will be used
	 */
	@AfterMapping
	default void mapPrincipalNumberAsPartnerID(@MappingTarget ShipmentHeader mappedOrder, ForwardingOrder order) {
		for (ShipmentAddress address : mappedOrder.getShipmentAddress()) {
			if (ADDRESS_TYPE_SHIPPER.equals(address.getAddressType()) && address.getPartnerInformation() != null && (address.getPartnerInformation().getPartnerID() == null
					|| address.getPartnerInformation().getPartnerID().isEmpty())) {
				address.getPartnerInformation().setPartnerID(order.getCustomerNumber());
				break;
			}
		}
	}

	@AfterMapping
	default void mapFrostProtectionAsText(@MappingTarget ShipmentHeader mappedOrder, ForwardingOrder order) {
		if (Objects.nonNull(order.getFrostProtection()) && Boolean.TRUE.equals(order.getFrostProtection())) {
			ShipmentText shipmentText = new ShipmentText();
			shipmentText.setTextType(FROST_PROTECTION_TEXTTYPE);
			shipmentText.getText().add("Frost Protected");
			mappedOrder.getShipmentText().add(shipmentText);
		}
	}

	@AfterMapping
	default void mapCustomsInformationAsReference(@MappingTarget ShipmentHeader mappedOrder, ForwardingOrder order) {
		if (order.isCustomsGoods() && Objects.nonNull(order.getCustomsType())) {
			AdditionalReference customsReference = new AdditionalReference();
			customsReference.setCode(CUSTOMS_REFERENCETYPE);
			customsReference.setReference(order.getCustomsType().equals(CustomsType.DACHSER) ? "J" : "N");
			mappedOrder.getAdditionalReference().add(customsReference);
		}
	}

	@AfterMapping
	default void mapOrderOriginInformationAsReference(@MappingTarget ShipmentHeader mappedOrder, ForwardingOrder order) {
		final AdditionalReference orderOriginReference = new AdditionalReference();
		orderOriginReference.setCode(SharedEdiReference.ORDER_ORIGIN_REFERENCE.getReferenceCode());
		if (order.getDatasource() != null) {
			orderOriginReference.setReference(order.getDatasource().getEdiReferenceValue());
		} else {
			orderOriginReference.setReference(SourceOfOrder.BOOK.getEdiReferenceValue());
		}
		mappedOrder.getAdditionalReference().add(orderOriginReference);
	}

	@AfterMapping
	default void mapDropOfLocationToShipmentText(@MappingTarget ShipmentHeader mappedOrder, ForwardingOrder order) {
		if (order.getConsigneeAddress() != null && !isEmpty(order.getConsigneeAddress().getDropOfLocation())) {
			ShipmentText dropOffLocationText = new ShipmentText();
			dropOffLocationText.setTextType("AG");
			dropOffLocationText.getText().add(order.getConsigneeAddress().getDropOfLocation());
			mappedOrder.getShipmentText().add(dropOffLocationText);
		}
	}

	@Named("mapBoolean")
	default String mapBoolean(Boolean value) {
		return Boolean.TRUE == value ? "Y" : "N";
	}

	class ForwardingShipmentAddressBuilder implements ShipmentAddressBuilder<ShipmentAddress, ForwardingOrder, PartnerInformation> {
		@Override
		public ShipmentAddress newShipmentAddress() {
			return new ShipmentAddress();
		}

		@Override
		public void setAddressType(ShipmentAddress shipmentAddress, String addressType) {
			shipmentAddress.setAddressType(addressType);
		}

		@Override
		public void setServiceContactType(ShipmentAddress shipmentAddress, String serviceContactType) {
			shipmentAddress.setServiceContactType(serviceContactType);
		}

		@Override
		public void setPartnerInformation(ShipmentAddress shipmentAddress, PartnerInformation partnerInformation) {
			shipmentAddress.setPartnerInformation(partnerInformation);
		}
	}
}
