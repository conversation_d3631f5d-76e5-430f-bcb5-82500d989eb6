package com.dachser.dfe.book.overview;

import com.dachser.dfe.book.SpringBasedLocalMockTest;
import com.dachser.dfe.book.UseCacheInTest;
import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.document.DocumentType;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.product.air.AirProductService;
import com.dachser.dfe.book.product.road.ShipperMasterdataService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.cache.CacheManager;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import java.util.Objects;
import java.util.Optional;

import static com.dachser.dfe.book.cache.CacheNames.OVERVIEW_AIR_PRODUCT_NAMES;
import static com.dachser.dfe.book.cache.CacheNames.OVERVIEW_DOCUMENT_TYPES;
import static com.dachser.dfe.book.cache.CacheNames.OVERVIEW_PRODUCT_NAMES;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBasedLocalMockTest
@TestPropertySource(properties = { "whitelist-config.delivery-products.road.european-logistics= E", "whitelist-config.delivery-products.road.food-logistics= E" })
@UseCacheInTest
@DirtiesContext
class CachedOrderOverviewServiceTest {

	@Autowired
	private CacheManager cacheManager;

	@SpyBean
	private CachedOrderOverviewService cachedOrderOverviewService;

	@MockBean
	private DocumentService documentService;

	@MockBean
	private ShipperMasterdataService shipperMasterdataService;

	@MockBean
	private AirProductService airProductService;

	@BeforeEach
	void setUp() {
		when(shipperMasterdataService.isOrderNumberMandatoryForCustomerNumber(anyString())).thenReturn(Boolean.TRUE);
	}

	@Nested
	class GetProductName {

		@Test
		void getProductName() {
			String productCode = "E";
			String productDescription = "targospeed 12";
			String productName = cachedOrderOverviewService.getProductNameOrError(productCode, Division.T);

			assertThat(productName).isEqualTo(productDescription);
		}

		@Test
		void getErrorForProductNameWhenProductServiceFails() {
			String productCode = "123";
			String productName = cachedOrderOverviewService.getProductNameOrError(productCode, Division.T);

			assertThat(productName).isEqualTo("ERROR");
		}

		@Test
		void getNullForProductNameWhenProductCodeIsNull() {
			String productCode = null;
			String productName = cachedOrderOverviewService.getProductNameOrError(productCode, Division.T);

			assertThat(productName).isNull();
		}

		@Test
		void shouldNotUseCacheForProductName() {
			String productCode = null;
			Division division = null;
			// clear cache if exists
			Objects.requireNonNull(cacheManager.getCache(OVERVIEW_PRODUCT_NAMES)).invalidate();

			String productName = cachedOrderOverviewService.getProductNameOrError(productCode, division);
			cachedOrderOverviewService.getProductNameOrError(productCode, division);

			verify(cachedOrderOverviewService, times(2)).getProductNameOrError(productCode, division);

			assertThat(productName).isNull();
		}

		@Test
		void shouldUseCacheForProductName() {
			final String productCode = "E";
			// clear cache if exists
			Objects.requireNonNull(cacheManager.getCache(OVERVIEW_PRODUCT_NAMES)).invalidate();

			String responseEL1 = cachedOrderOverviewService.getProductNameOrError(productCode, Division.T);
			String responseEL2 = cachedOrderOverviewService.getProductNameOrError(productCode, Division.T);
			String responseFL1 = cachedOrderOverviewService.getProductNameOrError(productCode, Division.F);
			String responseFL2 = cachedOrderOverviewService.getProductNameOrError(productCode, Division.F);

			verify(cachedOrderOverviewService, times(1)).getProductNameOrError(productCode, Division.T);
			verify(cachedOrderOverviewService, times(1)).getProductNameOrError(productCode, Division.F);

			assertThat(responseEL1).isEqualTo(responseEL2).isEqualTo("targospeed 12");
			assertThat(responseFL1).isEqualTo(responseFL2).isEqualTo("vengospeed");
		}
	}

	@Nested
	class GetAirProductName {

		@Test
		void getProductName() {
			String productCode = "1";
			String productDescription = "Product";
			when(airProductService.getAirProductByProductCode("1")).thenReturn(Optional.of(new AirProductDto("1", productDescription).hint("description").active(true)));

			String productName = cachedOrderOverviewService.getAirProductNameOrError(productCode);

			assertThat(productName).isEqualTo(productDescription);
		}

		@Test
		void getErrorForProductNameWhenProductServiceFails() {
			String productCode = "123";
			when(airProductService.getAirProductByProductCode("123")).thenReturn(Optional.empty());

			String productName = cachedOrderOverviewService.getAirProductNameOrError(productCode);

			assertThat(productName).isEqualTo("ERROR");
		}

		@Test
		void getNullForProductNameWhenProductCodeIsNull() {
			String productCode = null;

			String productName = cachedOrderOverviewService.getAirProductNameOrError(productCode);

			assertThat(productName).isNull();
		}

		@Test
		void shouldUseCacheForProductName() {
			final String productCode = "1";
			when(airProductService.getAirProductByProductCode("1")).thenReturn(Optional.of(new AirProductDto("1", "some air product").hint("description").active(true)));
			// clear cache if exists
			Objects.requireNonNull(cacheManager.getCache(OVERVIEW_AIR_PRODUCT_NAMES)).invalidate();

			String response1 = cachedOrderOverviewService.getAirProductNameOrError(productCode);
			String response2 = cachedOrderOverviewService.getAirProductNameOrError(productCode);

			verify(cachedOrderOverviewService, times(1)).getAirProductNameOrError(productCode);

			assertEquals(response1, response2);
		}
	}

	@Nested
	class LoadDocumentTypeById {

		@Test
		void shouldLoadDocumentTypeById() {
			String docTypeId = "1";
			when(documentService.findDocumentTypeById(docTypeId)).thenReturn(Optional.of(new DocumentType()));
			Optional<DocumentType> document = cachedOrderOverviewService.loadDocumentTypeById(docTypeId);

			assertTrue(document.isPresent());
		}

		@Test
		void shouldReturnEmtpyOptional() {
			String docTypeId = "X";
			when(documentService.findDocumentTypeById(docTypeId)).thenReturn(Optional.empty());
			Optional<DocumentType> document = cachedOrderOverviewService.loadDocumentTypeById(docTypeId);

			assertTrue(document.isEmpty());
		}

		@Test
		void shouldLoadDocumentTypeFromCache() {
			// clear cache if exists
			Objects.requireNonNull(cacheManager.getCache(OVERVIEW_DOCUMENT_TYPES)).invalidate();

			DocumentType documentType = new DocumentType();
			documentType.setType("DocumentType");
			documentType.setDocumentTypeId(1);
			documentType.setCategory("Category");

			when(documentService.findDocumentTypeById("1")).thenReturn(Optional.of(documentType));

			String documentTypeId = "1";
			Optional<DocumentType> response1 = cachedOrderOverviewService.loadDocumentTypeById(documentTypeId);
			Optional<DocumentType> response2 = cachedOrderOverviewService.loadDocumentTypeById(documentTypeId);

			verify(cachedOrderOverviewService, times(1)).loadDocumentTypeById(documentTypeId);

			assertTrue(response1.isPresent());
			assertTrue(response2.isPresent());
			assertEquals(response1.get(), response2.get());
		}

	}

}