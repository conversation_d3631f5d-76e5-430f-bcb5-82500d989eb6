package com.dachser.dfe.book.order.sea;

import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.common.AirSeaOrder;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.common.fcl.FullContainerLoad;
import com.dachser.dfe.book.order.validation.common.ValidOrder;
import com.dachser.dfe.book.order.validation.sea.CompleteOrderValidationSea;
import com.dachser.dfe.book.order.validation.sea.ValidSeaCollectionTimeSlot;
import com.dachser.dfe.book.order.validation.sea.ValidSeaOrder;
import com.dachser.dfe.book.order.validation.sea.ValidSeaOrderLine;
import com.dachser.dfe.book.quote.SeaQuoteInformation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.SQLRestriction;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "order_sea")
@ValidSeaOrder(groups = CompleteOrderValidationSea.class)
@ValidOrder(groups = CompleteOrderValidationSea.class)
@ValidSeaCollectionTimeSlot(groups = CompleteOrderValidationSea.class)
@ValidSeaOrderLine(groups = CompleteOrderValidationSea.class)
public abstract class SeaOrder extends Order implements AirSeaOrder<SeaOrderReference>, OrderLineCombiner<SeaOrderLine, SeaOrderLineHsCode> {

	@Size(max = 5)
	@Column(name = "from_port")
	@NotNull(groups = CompleteOrderValidationSea.class, message = "{label.text.invalid_input}")
	private String fromPort;

	@Size(max = 5)
	@Column(name = "to_port")
	@NotNull(groups = CompleteOrderValidationSea.class, message = "{label.text.invalid_input}")
	private String toPort;

	private boolean deliverToPort;

	private boolean collectFromPort;

	@Size(max = 3)
	@NotNull(groups = CompleteOrderValidationSea.class, message = "{label.text.invalid_input}")
	private String incoTerm;

	@JoinColumn(name = "customer_contact_id")
	@OneToOne(cascade = CascadeType.ALL)
	@Valid
	private OrderContact orderContact;

	@JoinColumn(name = "pickup_address_id")
	@OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
	@Valid
	private OrderAddress pickupAddress;

	@JoinColumn(name = "delivery_address_id")
	@OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
	@Valid
	private OrderAddress deliveryAddress;

	@OneToMany(mappedBy = "order", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@ToString.Exclude
	@Size(groups = CompleteOrderValidationSea.class, message = "{label.text.invalid_input}")
	@Valid
	@OrderBy("number")
	@SQLRestriction("full_container_load_id IS NULL")
	private List<SeaOrderLine> orderLines;

	@OneToMany(mappedBy = "order", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@ToString.Exclude
	@Valid
	private List<SeaOrderReference> orderReferences;

	@Size(max = 35)
	@NotNull(groups = CompleteOrderValidationSea.class, message = "{label.text.invalid_input}")
	private String shipperReference;

	private boolean requestArrangement;

	private boolean tailLiftCollection;

	// Business default is true
	private boolean stackable = true;

	private boolean shockSensitive;

	@ToString.Exclude
	@OneToMany(mappedBy = "order", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@OrderBy("sortingPosition")
	@Size(min = 0, max = 999, groups = CompleteOrderValidationSea.class, message = "{label.invalid_input}")
	@Valid
	private List<FullContainerLoad> fullContainerLoads;

	private boolean fullContainerLoad;

	public void addOrderLine(SeaOrderLine orderLine) {
		if (orderLines == null) {
			orderLines = new ArrayList<>();
		}
		orderLines.add(orderLine);
		orderLine.setOrder(this);
	}

	public void addOrderReference(SeaOrderReference orderReference) {
		if (orderReferences == null) {
			orderReferences = new ArrayList<>();
		}
		orderReferences.add(orderReference);
		orderReference.setOrder(this);
	}

	public Double getTotalOrderWeight() {
		if (!fullContainerLoad && orderLines != null) {
			return orderLines.stream().map(SeaOrderLine::getWeight).filter(Objects::nonNull).mapToDouble(BigDecimal::doubleValue).sum();
		}
		if (fullContainerLoad && fullContainerLoads != null) {
			return fullContainerLoads.stream().map(FullContainerLoad::getOrderLines).filter(Objects::nonNull).flatMap(List::stream).map(SeaOrderLine::getWeight)
					.filter(Objects::nonNull).mapToDouble(BigDecimal::doubleValue).sum();
		}
		return 0.0;
	}

	public double getTotalOrderVolume() {
		if (!fullContainerLoad && orderLines != null) {
			return orderLines.stream().map(SeaOrderLine::getVolume).filter(Objects::nonNull).mapToDouble(BigDecimal::doubleValue).sum();
		}
		if (fullContainerLoad && fullContainerLoads != null) {
			return fullContainerLoads.stream().map(FullContainerLoad::getOrderLines).filter(Objects::nonNull).flatMap(List::stream).map(SeaOrderLine::getVolume)
					.filter(Objects::nonNull).mapToDouble(BigDecimal::doubleValue).sum();
		}
		return 0.0;
	}

	public int getTotalAmountPackages() {
		if (!fullContainerLoad && orderLines != null) {
			return orderLines.stream().map(SeaOrderLine::getQuantity).mapToInt(Integer::intValue).sum();
		}
		if (fullContainerLoad && fullContainerLoads != null) {
			return fullContainerLoads.stream().map(FullContainerLoad::getOrderLines).filter(Objects::nonNull).flatMap(List::stream).map(SeaOrderLine::getQuantity)
					.mapToInt(Integer::intValue).sum();
		}
		return 0;
	}

	public int getTotalContainerQuantity() {
		if (fullContainerLoad && fullContainerLoads != null) {
			return fullContainerLoads.size();
		}
		return 0;
	}

	public int getTotalVerifiedGrossMass() {
		if (fullContainerLoad && fullContainerLoads != null) {
			return fullContainerLoads.stream().map(FullContainerLoad::getVerifiedGrossMass).filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
		}
		return 0;
	}

	@Transient
	public String getCombinedOrderLineGoods() {
		return combineGoods(getAllOrderLinesOrFCLOrderLines(), SeaOrderLine::getHsCodes);
	}

	@Transient
	private List<SeaOrderLine> getAllOrderLinesForFCL() {
		if (getFullContainerLoads() != null) {
			return getFullContainerLoads().stream().flatMap(containerLoad -> containerLoad.getOrderLines().stream()).toList();
		} else {
			return List.of();
		}
	}

	@Transient
	public List<SeaOrderLine> getAllOrderLinesOrFCLOrderLines() {
		if (isFullContainerLoad()) {
			return getAllOrderLinesForFCL();
		} else {
			return getOrderLines();
		}
	}

	// Provide a getter for the quote information of an sea order - this is absolutely necessary for mapper to determine types correctly
	@Override
	public SeaQuoteInformation getQuoteInformation() {
		return (SeaQuoteInformation) super.getQuoteInformation();
	}

	@Override
	public List<SeaOrderReference> getOrderReferences() {
		return orderReferences;
	}

}
