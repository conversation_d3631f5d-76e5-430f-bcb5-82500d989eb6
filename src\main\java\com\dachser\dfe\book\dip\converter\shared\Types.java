package com.dachser.dfe.book.dip.converter.shared;

import lombok.NoArgsConstructor;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class Types {
	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class LocationTypes {

		public static final String DEPARTURE_AIRPORT = "DepartureAirport";

		public static final String DESTINATION_AIRPORT = "DestinationAirport";

		public static final String PORT_OF_LOADING = "PortOfLoading";

		public static final String PORT_OF_UNLOADING = "PortOfUnloading";

		public static final String PLACE_OF_RECIEPT = "PlaceOfReciept";

		public static final String PLACE_OF_DELIVERY = "PlaceOfDelivery";
	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class AddressTypes {
		public static final String FORWARDER = "Forwarder";

		public static final String ORDERER = "Orderer";

		public static final String CONSIGNOR = "Consignor";

		public static final String CONSIGNEE = "Consignee";

		public static final String PICKUP = "Pickup";

		public static final String DELIVERER = "Deliverer";

		public static final String IMPORTER = "Importer";

		public static final String ULTIMATE_CONSIGNEE = "UltimateConsignee";

		public static final String MAIN_NOTIFY = "MainNotify";

		public static final String CUSTOMS_AGENT = "CustomsAgent";
	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class ContactTypes {
		public static final String INFORMATION = "Information";
	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class DimensionUnit {
		public static final String MTR = "MTR";

		public static final String CMT = "CMT";

		public static final String FET = "FET";

		public static final String INH = "INH";
	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class VolumeUnits {
		public static final String MTQ = "MTQ";

		public static final String FTQ = "FTQ";
	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class WeightUnit {
		public static final String LBR = "LBR";

		public static final String LTR = "LTR";

		public static final String KGM = "KGM";
	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class PickupDateType {
		public static final String FROM = "From";

		public static final String TO = "To";

		public static final String FIX = "Fix";
	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class LoadingInstruction {
		public static final String TLF = "TLF";
	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class ShipmentReferenceFlagLoadUnload {

		public static final String LOAD = "Load";

		public static final String UNLOAD = "Unload";

		public static final String LOAD_AND_UNLOAD = "LoadAndUnload";
	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class Function {
		public static final String ORIGINAL = "Original";

		public static final String UPDATE = "Update";

		public static final String CANCEL = "Cancel";
	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class KindOfShipment {
		public static final String FINAL = "Final";

		public static final String ADVICE = "Advice";
	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class CustomsGoods {
		public static final String CUSTOMS_GOODS = "CustomsGoods";

		public static final String NON_CUSTOMS_GOODS = "NonCustomsGoods";

		public static final String UNKNOWN = "Unknown";
	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class TypeOfCommunications {
		public static final String PHONE = "Phone";

		public static final String EMAIL = "eMail";

		public static final String FAX = "Fax";

		public static final String MOBILE = "Mobile";

	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class HandlingInstructions {
		public static final String NOT_STACKABLE = "NST";

		public static final String SHOCK_SENSITIVE = "SSE";

	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class SeaOrderCategory {
		public static final String LCL = "LCL";

		public static final String FCL = "FCL";
	}

	@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
	public static class AdditionalOrderInformation {
		public static final String MARKS_AND_NUMBERS = "MKS";
	}
}
