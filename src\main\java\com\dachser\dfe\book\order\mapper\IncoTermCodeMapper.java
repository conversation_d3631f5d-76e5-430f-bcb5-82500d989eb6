package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.model.IncoTermDto;
import com.dachser.dfe.book.term.IncoTermService;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public abstract class IncoTermCodeMapper {

	@Autowired
	private IncoTermService incoTermService;

	public IncoTermDto mapByDachserKey(String dachserKey) {
		return incoTermService.getIncoTermByDachserCode(dachserKey);
	}
}
