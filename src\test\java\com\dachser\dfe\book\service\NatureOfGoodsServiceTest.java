package com.dachser.dfe.book.service;

import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.order.air.AirOrderLineHsCode;
import com.dachser.dfe.book.order.common.orderline.AirOrderLineHsCodeRepository;
import com.dachser.dfe.book.order.common.orderline.RoadOrderLineRepository;
import com.dachser.dfe.book.order.common.orderline.SeaOrderLineHsCodeRepository;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.sea.SeaOrderLineHsCode;
import com.dachser.dfe.book.user.UserContextService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static com.dachser.dfe.book.TestUtil.generateRoadOrderLine;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class NatureOfGoodsServiceTest {

	@InjectMocks
	private NatureOfGoodsService natureOfGoodsService;

	@Mock
	private AirOrderLineHsCodeRepository airOrderLineHsCodeRepository;

	@Mock
	private SeaOrderLineHsCodeRepository seaOrderLineHsCodeRepository;

	@Mock
	private RoadOrderLineRepository roadOrderLineRepository;

	@Mock
	private UserContextService userContextService;

	@BeforeEach
	void setUp() {
		when(userContextService.getCurrentUserId()).thenReturn("PA");
	}

	@Nested
	class Road {
		@Test
		void shouldReturnDistinctNatureOfGoodsForRoad() {
			when(roadOrderLineRepository.findTop10ByOrder_CreatorAndContentIsNotNullOrderByOrderLineIdDesc(any())).thenReturn(generateRoadOrderLines());
			List<String> result = natureOfGoodsService.getLastUsedNatureOfGoods(Segment.ROAD);
			assertEquals(1, result.size());
			assertEquals("CAR SPARE", result.get(0));
		}
	}

	@Nested
	class Sea {
		@Test
		void shouldReturnLastUsedNatureOfGoodsForSea() {
			when(seaOrderLineHsCodeRepository.findTop10ByOrderLine_Order_CreatorAndGoodsIsNotNullOrderByOrderLineHsCodeIdDesc(any())).thenReturn(generateSeaOrderLineHsCodes());
			List<String> result = natureOfGoodsService.getLastUsedNatureOfGoods(Segment.SEA);
			List<String> expected = List.of("good5", "good4", "good3", "good2", "good1");
			assertEquals(expected, result);
		}
	}

	@Nested
	class Air {
		@Test
		void shouldReturnLastUsedNatureOfGoodsForAir() {
			when(airOrderLineHsCodeRepository.findTop10ByOrderLine_Order_CreatorAndGoodsIsNotNullOrderByOrderLineHsCodeIdDesc(any())).thenReturn(generateAirOrderLineHsCodes());
			List<String> result = natureOfGoodsService.getLastUsedNatureOfGoods(Segment.AIR);
			List<String> expected = List.of("good5", "good4", "good3", "good2", "good1");
			assertEquals(expected, result);
		}
	}

	private List<RoadOrderLine> generateRoadOrderLines() {
		// @formatter:off
		return List.of(
				generateRoadOrderLine("PA", "test", 1),
				generateRoadOrderLine("PB", "test", 2),
				generateRoadOrderLine("PC", "test", 3),
				generateRoadOrderLine("PD", "test", 4),
				generateRoadOrderLine("PE", "test", 5)
		);
		// @formatter:on
	}

	private List<SeaOrderLineHsCode> generateSeaOrderLineHsCodes() {
		// @formatter:off
		return List.of(
				generateSeaOrderLineHsCode("good1", 1L),
				generateSeaOrderLineHsCode("good2", 2L),
				generateSeaOrderLineHsCode("good3", 3L),
				generateSeaOrderLineHsCode("good4", 4L),
				generateSeaOrderLineHsCode("good5", 5L)
		);
		// @formatter:on
	}

	private SeaOrderLineHsCode generateSeaOrderLineHsCode(String good, Long id) {
		SeaOrderLineHsCode lineHsCode = new SeaOrderLineHsCode();
		lineHsCode.setGoods(good);
		lineHsCode.setOrderLineHsCodeId(id);
		return lineHsCode;
	}

	private List<AirOrderLineHsCode> generateAirOrderLineHsCodes() {
		// @formatter:off
		return List.of(
				generateAirOrderLineHsCode("good1", 1L),
				generateAirOrderLineHsCode("good2", 2L),
				generateAirOrderLineHsCode("good3", 3L),
				generateAirOrderLineHsCode("good4", 4L),
				generateAirOrderLineHsCode("good5", 5L)
		);
		// @formatter:on
	}

	private AirOrderLineHsCode generateAirOrderLineHsCode(String good, Long id) {
		AirOrderLineHsCode lineHsCode = new AirOrderLineHsCode();
		lineHsCode.setGoods(good);
		lineHsCode.setOrderLineHsCodeId(id);
		return lineHsCode;
	}
}