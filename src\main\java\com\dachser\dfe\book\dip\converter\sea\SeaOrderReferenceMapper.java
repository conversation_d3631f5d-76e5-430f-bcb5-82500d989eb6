package com.dachser.dfe.book.dip.converter.sea;

import com.dachser.dfe.book.dip.converter.shared.SharedEdiReference;
import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.model.jaxb.order.asl.OrderReference;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.book.order.sea.SeaOrderLine;
import com.dachser.dfe.book.order.sea.SeaOrderReference;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

@Mapper(componentModel = "spring")
public interface SeaOrderReferenceMapper {

	@Mapping(target = "typeOfReference", source = "referenceType.ediReferenceType")
	@Mapping(target = "value", source = "referenceValue")
	OrderReference mapReference(SeaOrderReference reference);

	default List<OrderReference> mapReferences(List<SeaOrderReference> references) {
		return references.stream().filter(ref -> ref.getReferenceType().isOrderPositionReference()).map(this::mapReference).toList();
	}

	default List<OrderReference> mapMarksAndNumbers(SeaOrder order) {
		return order.getAllOrderLinesOrFCLOrderLines().stream().map(SeaOrderLine::getMarkAndNumbers).filter(Objects::nonNull).filter(Predicate.not(String::isEmpty)).map(content -> {
			final OrderReference orderReference = new OrderReference();
			orderReference.setTypeOfReference(AirSeaOrderReferenceType.MARKS_AND_NUMBERS.getEdiReferenceType());
			orderReference.setValue(content);
			return orderReference;
		}).toList();
	}

	default List<OrderReference> mapShippersReference(final SeaOrder seaOrder) {
		List<String> handlingInstructions = new ArrayList<>();

		if (!seaOrder.isFullContainerLoad()) {
			if (!seaOrder.isStackable()) {
				handlingInstructions.add(Types.HandlingInstructions.NOT_STACKABLE);
			}
			if (seaOrder.isShockSensitive()) {
				handlingInstructions.add(Types.HandlingInstructions.SHOCK_SENSITIVE);
			}
		}

		// If there is no handling instruction, we return the shipper reference as a shippers reference, else we generate a shippers reference for each handling instruction
		if (handlingInstructions.isEmpty()) {
			final OrderReference orderReference = new OrderReference();
			orderReference.setTypeOfReference(AirSeaOrderReferenceType.SHIPPERS_REFERENCE.getEdiReferenceType());
			orderReference.setValue(seaOrder.getShipperReference());
			return List.of(orderReference);
		} else {
			return handlingInstructions.stream().map(handlingInstruction -> {
				final OrderReference orderReference = new OrderReference();
				orderReference.setTypeOfReference(AirSeaOrderReferenceType.SHIPPERS_REFERENCE.getEdiReferenceType());
				orderReference.setValue(seaOrder.getShipperReference());
				orderReference.setHandlingInstruction(handlingInstruction);
				return orderReference;
			}).toList();
		}

	}

	@AfterMapping
	default void addAdditionalReferences(@MappingTarget ForwardingOrder.SeaFreightShipment.PickupOrder pickupOrder, SeaOrder order) {
		final List<OrderReference> orderReferences = pickupOrder.getOrderReference();
		orderReferences.addAll(mapMarksAndNumbers(order));
		orderReferences.addAll(mapShippersReference(order));

		addShipmentNumberAsReference(orderReferences, order);
		addOrderOriginAsReference(orderReferences, order);
	}

	@AfterMapping
	default void addAdditionalReferences(@MappingTarget ForwardingOrder.SeaFreightShipment.DeliveryOrder deliveryOrder, SeaOrder order) {
		final List<OrderReference> orderReferences = deliveryOrder.getOrderReference();
		orderReferences.addAll(mapMarksAndNumbers(order));
		orderReferences.addAll(mapShippersReference(order));

		addShipmentNumberAsReference(orderReferences, order);
		addOrderOriginAsReference(orderReferences, order);
	}

	private void addShipmentNumberAsReference(List<OrderReference> orderReferences, SeaOrder order) {
		OrderReference shipmentNumberReference = new OrderReference();
		shipmentNumberReference.setTypeOfReference(AirSeaOrderReferenceType.SHIPPING_ORDER_NUMBER.getEdiReferenceType());
		shipmentNumberReference.setValue(String.format("%011d", order.getShipmentNumber()));

		orderReferences.add(shipmentNumberReference);
	}

	private void addOrderOriginAsReference(List<OrderReference> orderReferences, SeaOrder order) {
		OrderReference orderOriginReference = new OrderReference();
		orderOriginReference.setTypeOfReference(SharedEdiReference.ORDER_ORIGIN_REFERENCE.getReferenceCode());
		orderOriginReference.setValue(order.getDatasource().getEdiReferenceValue());

		orderReferences.add(orderOriginReference);
	}
}
