package com.dachser.dfe.book.masterdata.airsea;

import com.dachser.dfe.airsea.masterdata.api.AirportApi;
import com.dachser.dfe.airsea.masterdata.api.AirportRoutingApi;
import com.dachser.dfe.airsea.masterdata.api.EmbargoApi;
import com.dachser.dfe.airsea.masterdata.api.SeaportApi;
import com.dachser.dfe.airsea.masterdata.api.SeaportRoutingApi;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class AirSeaMasterDataApiWrapperTest {

	@Mock
	AirportApi airportApi;

	@Mock
	EmbargoApi embargoApi;

	@Mock
	AirportRoutingApi airportRoutingApi;
	@Mock
	SeaportRoutingApi seaportRoutingApi;

	@Mock
	SeaportApi seaportApi;

	@Test
	void shouldInvokeEmbargoApi(){
		// given
		AirSeaMasterDataApiWrapper airSeaMasterDataApiWrapper = new AirSeaMasterDataApiWrapper(airportApi, airportRoutingApi, embargoApi, seaportApi, seaportRoutingApi);
		// when
		airSeaMasterDataApiWrapper.findEmbargo("DE", "US");
		// then
		// verify that the method was called
		Mockito.verify(embargoApi).findEmbargo("DE", "US");
	}

	@Test
	void shouldInvokeAirportApi(){
		// given
		AirSeaMasterDataApiWrapper airSeaMasterDataApiWrapper = new AirSeaMasterDataApiWrapper(airportApi, airportRoutingApi, embargoApi, seaportApi, seaportRoutingApi);
		// when
		airSeaMasterDataApiWrapper.findAllAirports(0, 10, List.of("name"));
		// then
		// verify that the method was called
		Mockito.verify(airportApi).findAllAirports(0, 10, List.of("name"));
	}

	@Test
	void shouldInvokeAirportRoutingApi(){
		// given
		AirSeaMasterDataApiWrapper airSeaMasterDataApiWrapper = new AirSeaMasterDataApiWrapper(airportApi, airportRoutingApi, embargoApi, seaportApi, seaportRoutingApi);
		lenient().when(airportRoutingApi.findAirportRoutingsForCountryAndPostcodeWithHttpInfo("DE", "12345", 0, 10, List.of("name"))).thenReturn(ResponseEntity.ok().build());
		// when
		airSeaMasterDataApiWrapper.findAirportRoutingsForCountryAndPostcode("DE", "12345", 0, 10, List.of("name"));
		// then
		// verify that the method was called
		Mockito.verify(airportRoutingApi).findAirportRoutingsForCountryAndPostcodeWithHttpInfo("DE", "12345", 0, 10, List.of("name"));
	}

	@Test
	void shouldInvokeAirportRoutingApi204(){
		// given
		AirSeaMasterDataApiWrapper airSeaMasterDataApiWrapper = new AirSeaMasterDataApiWrapper(airportApi, airportRoutingApi, embargoApi, seaportApi, seaportRoutingApi);
		lenient().when(airportRoutingApi.findAirportRoutingsForCountryAndPostcodeWithHttpInfo("DE", "12345", 0, 10, List.of("name"))).thenReturn(ResponseEntity.noContent().build());
		// when
		airSeaMasterDataApiWrapper.findAirportRoutingsForCountryAndPostcode("DE", "12345", 0, 10, List.of("name"));
		// then
		// verify that the method was called
		Mockito.verify(airportRoutingApi).findAirportRoutingsForCountryAndPostcodeWithHttpInfo("DE", "12345", 0, 10, List.of("name"));
	}

	@Test
	void shouldInvokeSeaportApi(){
		// given
		AirSeaMasterDataApiWrapper airSeaMasterDataApiWrapper = new AirSeaMasterDataApiWrapper(airportApi, airportRoutingApi, embargoApi, seaportApi, seaportRoutingApi);
		lenient().when(seaportRoutingApi.findSeaportRoutingsByPostcodeAndCountryWithHttpInfo("DE", "12345", 0, 10, List.of("name"))).thenReturn(ResponseEntity.ok().build());
		// when
		airSeaMasterDataApiWrapper.getAllSeaports(0, 10, List.of("name"));
		// then
		// verify that the method was called
		Mockito.verify(seaportApi).getAllSeaports(0, 10, List.of("name"));
	}

	@Test
	void shouldInvokeSeaportApiNoContent(){
		// given
		AirSeaMasterDataApiWrapper airSeaMasterDataApiWrapper = new AirSeaMasterDataApiWrapper(airportApi, airportRoutingApi, embargoApi, seaportApi, seaportRoutingApi);
		lenient().when(seaportRoutingApi.findSeaportRoutingsByPostcodeAndCountryWithHttpInfo("DE", "12345", 0, 10, List.of("name"))).thenReturn(ResponseEntity.noContent().build());
		// when
		airSeaMasterDataApiWrapper.findSeaportRoutingsForCountryAndPostcode("DE", "12345", 0, 10, List.of("name"));
		// then
		// verify that the method was called
		Mockito.verify(seaportRoutingApi).findSeaportRoutingsByPostcodeAndCountryWithHttpInfo("DE", "12345", 0, 10, List.of("name"));
	}

}