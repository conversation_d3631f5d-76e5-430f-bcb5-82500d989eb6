databaseChangeLog:
  - include:
      file: db/changelog/all/2023-04-25_10-30_init_db.sql
  - include:
      file: db/changelog/h2/2023-04-25_11-50_add_unique_constraint.sql
      context: h2
  - include:
      file: db/changelog/h2/2023-05-25_migrate_delivery_contact_to_road_order.sql
      context: h2
  - include:
      file: db/changelog/mssql/2023-04-25_11-50_add_unique_constraint.sql
      context: mssql
  - include:
      file: db/changelog/mssql/2023-04-27_import_hs_codes.sql
      context: mssql
  - include:
      file: db/changelog/all/2023-05-03_add_avis_hash_column.sql
  - include:
      file: db/changelog/mssql/2023-05-03_insert_freight_terms.sql
      context: mssql
  - include:
      file: db/changelog/all/2023-05-05_add_column_quoterequestid.sql
  - include:
      file: db/changelog/all/2023-05-10_change_avis_hash_column.sql
  - include:
      file: db/changelog/mssql/2023-05-11_import-incoterms.sql
      context: mssql
  - include:
      file: db/changelog/all/2023-05-12_adjust_air_order_table.sql
  - include:
      file: db/changelog/all/2023-05-18-rename-order-line-and-order-reference.yaml
  - include:
      file: db/changelog/all/2023-05-19_recreate_view.sql
  - include:
      file: db/changelog/h2/2023-05-22_migrate_collection_data.sql
      context: h2
  - include:
      file: db/changelog/h2/2023-05-22_migrate_inco_term_dachser_code_to_air_order.sql
      context: h2
  - include:
      file: db/changelog/mssql/2023-05-22_migrate_collection_data.sql
      context: mssql
  - include:
      file: db/changelog/mssql/2023-05-22_migrate_inco_term_dachser_code_to_air_order.sql
      context: mssql
  - include:
      file: db/changelog/h2/2023-05-25-add-default-values-to-boolean.sql
      context: h2
  - include:
      file: db/changelog/mssql/2023-05-25-add-default-values-to-boolean.sql
      context: mssql
  - include:
      file: db/changelog/mssql/2023-05-25_migrate_delivery_contact_to_road_order.sql
      context: mssql
  - include:
      file: db/changelog/all/2023-05-26_update_document_types.sql
  - include:
      file: db/changelog/h2/2023-05-26_insert_document_types.sql
      context: h2
  - include:
      file: db/changelog/mssql/2023-05-26_update_document_types.sql
      context: mssql
  - include:
      file: db/changelog/all/2023-05-24_add-document-id-order-air.sql
  - include:
      file: db/changelog/all/2023-05-30_add_missing_mandatory_fields_order_air.sql
  - include:
      file: db/changelog/mssql/2023-06-01_migrate_data_for_validation.sql
      context: mssql
  - include:
      file: db/changelog/mssql/2023-06-01_migrate_data_for_validation.sql
      context: h2
  - include:
      file: db/changelog/all/2023-06-01_change_columns_for_air_order.sql
  - include:
      file: db/changelog/all/2023-06-07_add_missing_fields_air_order.sql
  - include:
      file: db/changelog/all/2023-06-15_fix_fields_air.sql
  - include:
      file: db/changelog/all/2023-06-20_add_routing_iata_to_order_air_table.sql
  - include:
      file: db/changelog/all/2023-06-22_add_customer_number_to_order_address.sql
  - include:
      file: db/changelog/all/2023-06-23_add_column_quote_expiration_time.sql
  - include:
      file: db/changelog/all/2023-06-28_add_column_collection_date.sql
  - include:
      file: db/changelog/all/2023-06-29_add_deliver_and_collect_to_airport.sql
  - include:
      file: db/changelog/all/2023-06-30_change_decimal_precision_orderline.sql
  - include:
      file: db/changelog/all/2023-07-04_recreate_view.sql
  - include:
      file: db/changelog/all/2023-07-04_add_locked_by_quote.sql
  - include:
      file: db/changelog/all/2023-07-10_adjust_document_types.sql
  - include:
      file: db/changelog/all/2023-07-10_migrate_collection_date.sql
  - include:
      file: db/changelog/all/2023-07-12_recreate_view_update_asl.sql
  - include:
      file: db/changelog/all/2023-07-13_create_indices_for_smart_proposals.sql
  - include:
      file: db/changelog/all/2023-07-14_change_weight_column_air.sql
  - include:
      file: db/changelog/all/2023-07-19_add_filesize_to_document.sql
  - include:
      file: db/changelog/all/2023-07-20_rename_column_extension_to_file_type.yaml
  - include:
      file: db/changelog/all/2023-07-24_update_file_type_data.sql
  - include:
      file: db/changelog/all/2023-07-24_delete_old_inco_term.sql
  - include:
      file: db/changelog/all/2023-07-25_air_shock_sensitive.sql
  - include:
      file: db/changelog/all/2023-07-26_delete_consignee_customer_number.sql
  - include:
      file: db/changelog/all/2023-07-27_add_street2_to_address.sql
  - include:
      context: mssql
      file: db/changelog/mssql/2023-07-31-update-hs-codes.sql
  - include:
      file: db/changelog/all/2023-08-03_add_previous_order_status.sql
  - include:
      file: db/changelog/all/2023-08-04_create_sea_order_tables.sql
  - include:
      file: db/changelog/all/2023-08-16_alter_sea_order_tables.sql
  - include:
      file: db/changelog/all/2023-08-16_alter_sea_order_table_constraints.sql
  - include:
      file: db/changelog/all/2023-09-04-add-port-info-to-view.sql
  - include:
      file: db/changelog/all/2023-09-26-add-air-product-table.sql
  - include:
      file: db/changelog/all/2023-10-03-add-sea-orders.sql
  - include:
      file: db/changelog/all/2023-10-04-add-active-field-incoterm.sql
  - include:
      file: db/changelog/all/2023-10-04-recreate-view-add-request-arrangement.sql
  - include:
      file: db/changelog/all/2023-10-11-rename-incoterms.sql
  - include:
      file: db/changelog/all/2023-10-16-recreate-view-add-join-for-product.sql
  - include:
      file: db/changelog/all/2023-10-18-deactivate-avigoflex-per-default.sql
  - include:
      file: db/changelog/all/2023-12-12-add-collection-date-as-fallback.sql
  - include:
      file: db/changelog/all/2023-11-09_update_ref_set_not_null.sql
  - include:
      file: db/changelog/all/2023-11-15-alter-sea-order-line-weight-and-volume.sql
  - include:
      file: db/changelog/all/2024-01-04-add-individualId.sql
  - include:
      file: db/changelog/all/2024-01-08-add-principal-address-to-order.sql
  - include:
      file: db/changelog/all/2024-01-16-add-originalPackagingType-field-for-air.sql
  - include:
      file: db/changelog/all/2024-01-29-adapt-goods-value.sql
  - include:
      file: db/changelog/all/2024-03-07-delete-order-number-from-order-base.sql
  - include:
      context: mssql
      file: db/changelog/mssql/2024-03-27-adapt-shippers-reference_remove_default_1.sql
  - include:
      context: mssql
      file: db/changelog/mssql/2024-03-27-adapt-shippers-reference_remove_default_2.sql
  - include:
      file: db/changelog/all/2024-03-27-adapt-shippers-reference.sql
  - include:
      file: db/changelog/mssql/2024-04-08-create-transfer-list-view.sql
      context: mssql
  - include:
      file: db/changelog/h2/2024-04-08-create-transfer-list-view.sql
      context: h2
  - include:
      file: db/changelog/all/2024-04-09-recreate-overview-view-remove-order-number.sql
  - include:
      file: db/changelog/all/2024-04-16-add-quote-information-tables.sql
  - include:
      file: db/changelog/mssql/2024-04-29-create-transfer-list-view.sql
      context: mssql
  - include:
      file: db/changelog/h2/2024-04-29-alter-transfer-list-view.sql
      context: h2
  - include:
      file: db/changelog/all/2024-05-03-alter-quote-address-table-postcode-constraint.sql
  - include:
      file: db/changelog/all/2024-05-13-alter-road-quote-information-table.sql
  - include:
      file: db/changelog/all/2024-05-15-recreate-overview-view-add-last-modified.sql
  - include:
      file: db/changelog/all/2024-05-24-alter-road-quote-information-table.sql
  - include:
      file: db/changelog/mssql/2024-05-29-set-shippers-name-to-35-chars.sql
      context: mssql
  - include:
      file: db/changelog/h2/2024-05-29-set-shippers-name-to-35-chars.sql
      context: h2
  - include:
      file: db/changelog/all/2024-05-31-refactor-quote-information-expiry-date.sql
  - include:
      file: db/changelog/all/2024-06-13-add-index-to-order-status.sql
  - include:
      file: db/changelog/all/2024-06-12-add-drop-of-location-to-order-address.sql
  - include:
      file: db/changelog/all/2024-06-18-add-index-to-order-references-and-others.sql
  - include:
      file: db/changelog/all/2024-06-19-introduce-sorting-for-order-status.sql
  - include:
      file: db/changelog/all/2024-06-20-add-collection-date-index.sql
  - include:
      file: db/changelog/mssql/2024-06-25-alter-order-references-collation.sql
      context: mssql
  - include:
      file: db/changelog/all/2024-06-26-introduce-address-columns-in-view.sql
  - include:
      file: db/changelog/mssql/2024-07-03-add-index-to-further-address-type.sql
      context: mssql
  - include:
      file: db/changelog/mssql/2024-07-04-create-transfer-list-view.sql
      context: mssql
  - include:
      file: db/changelog/all/2024-07-10-adapt-fields-regarding-advice.sql
  - include:
      file: db/changelog/all/2024-07-10_change_decimal_precision_orderline_ldm.sql
  - include:
      file: db/changelog/all/2024-07-17-change-pallet-locations.sql
  - include:
      file: db/changelog/mssql/2024-07-22-alter-order-references-collation.sql
      context: mssql
  - include:
      file: db/changelog/all/2024-07-31-principal-lock.sql
  - include:
      file: db/changelog/all/2024-08-02-add-customer-number-with-segment.sql
  - include:
      file: db/changelog/mssql/2024-08-05-create-transfer-list-view.sql
      context: mssql
  - include:
      file: db/changelog/h2/2024-08-05-alter-transfer-list-view.sql
      context: h2
  - include:
      file: db/changelog/mssql/2024-09-03-collection-order-fields.sql
      context: mssql
  - include:
      file: db/changelog/h2/2024-09-03-collection-order-fields.sql
      context: h2
  - include:
      file: db/changelog/mssql/2024-09-11-define-number-orderline-not-null.sql
      context: mssql
  - include:
      file: db/changelog/h2/2024-09-11-define-number-orderline-not-null.sql
      context: h2
  - include:
      file: db/changelog/all/2024-09-12-save-xml-orders.sql
  - include:
      file: db/changelog/all/2024-09-11_add_addresses_collection_order.sql
  - include:
      file: db/changelog/all/2024-09-16-remove-principal-contact-collection-order.sql
  - include:
      file: db/changelog/all/2024-09-27-add-air-product-codes-to-view.sql
  - include:
      file: db/changelog/all/2024-10-09-remove-road-documenttypes-others.sql
  - include:
      file: db/changelog/all/2024-10-07-Add-collection-option-for-collection-order.sql
  - include:
      file: db/changelog/all/2024-10-15-adjust-collection-option-for-collection-order.sql
  - include:
      file: db/changelog/all/2024-10-15-adjust-pallet-location-for-road-quote-information.sql
  - include:
      file: db/changelog/all/2024-10-18-add_labels_printed_timestamp.sql
  - include:
      file: db/changelog/all/2024-11-08-change-quote-name-fields-to-35.sql
  - include:
      file: db/changelog/mssql/2024-10-31-update-labels-timestamp-existing-orders.yaml
      context: mssql
  - include:
      file: db/changelog/h2/2024-10-31-update-labels-timestamp-existing-orders.yaml
      context: h2
  - include:
      file: db/changelog/all/2024-10-29-add-extensions-for-documenttypes.sql
  - include:
      file: db/changelog/all/2024-10-22-remove_deprecated_q2b.yaml
  - include:
      file: db/changelog/all/2024-10-25-remove_deprecated_q2b_overview.sql
  - include:
      file: db/changelog/all/2024-12-19-add-publish-at-column-order-xml.sql
  - include:
      file: db/changelog/all/2024-12-02-add-packaging-position-road-orders.sql
  - include:
      file: db/changelog/all/2024-12-09-add-packaging-position-q2b.sql
  - include:
      file: db/changelog/all/2025-01-02-add-freight-payer-to-collection-order.sql
  - include:
      file: db/changelog/all/2025-01-08-improve-order-overview-performances_1.sql
  - include:
      file: db/changelog/all/2025-01-08-improve-order-overview-performances_2.sql
  - include:
      file: db/changelog/all/2025-01-20-adjust-document-type-for-CDCTD.sql
  - include:
      file: db/changelog/all/2025-02-03-sanitize-i18n-keys.sql
  - include:
      file: db/changelog/all/2025-02-04-delete-not-null-constraint-for-weight-quote.sql
  - include:
      file: db/changelog/all/2025-02-13-add-full-container-load-sea-order.sql
  - include:
      file: db/changelog/all/2025-02-17-change-constraint-for-weight-quote-decimal.sql
  - include:
      file: db/changelog/all/2025-02-21-define-road-reference-subtype.sql
  - include:
      file: db/changelog/all/2025-02-25-change-containertype.sql
  - include:
      file: db/changelog/all/2025-03-12-drop-inco-terms.sql
  - include:
      file: db/changelog/all/2025-03-18-add-index-to-order-creator.sql
  - include:
      file: db/changelog/all/2025-04-03-adjust-order-overview-calculations.sql
  - include:
      file: db/changelog/all/2025-04-03-drop-air_products.sql
  - include:
      file: db/changelog/all/2025-04-17-adjust-column-length-order-address.sql
  - include:
      file: db/changelog/all/2025-04-24-add-emission-forecast-to-order.sql
  - include:
      file: db/changelog/all/2025-04-24-add-cod-road.sql
  - include:
      file: db/changelog/all/2025-05-12-fix-order-overview-sea-orders-fields-ordering.sql
  - include:
      file: db/changelog/all/2025-05-19-add-basic-structure-dangerous-goods.sql
  - include:
      file: db/changelog/all/2025-05-26_insert_document_types_dangerous_goods.sql
  - include:
      file: db/changelog/all/2025-06-02-dangerous-goods-transfer-list.sql
  - include:
      file: db/changelog/all/2025-06-04-first-adaptions-dangerous-goods.sql
  - include:
      file: db/changelog/all/2025-06-06-second-adaptions-dangerous-goods.sql
  - include:
      file: db/changelog/all/2025-06-16-third-adaptions-dangerous-goods.sql
  - includeAll:
      path: db/mock-data/
      context: mock-data


