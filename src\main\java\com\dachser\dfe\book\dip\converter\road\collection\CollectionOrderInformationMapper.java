package com.dachser.dfe.book.dip.converter.road.collection;

import com.dachser.dfe.book.model.jaxb.order.road.collection.CollectionOrderInformation;
import com.dachser.dfe.book.dip.converter.shared.DateMapper;
import com.dachser.dfe.book.order.road.CollectionOrder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.SubclassExhaustiveStrategy;

@Mapper(uses = { CollectionTransportMapper.class, DateMapper.class,
		CollectionDocumentHeaderMapper.class }, componentModel = "spring", subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION)
@Named("CollectionOrderInformationMapper")
public interface CollectionOrderInformationMapper {

	@Mapping(target = "documentHeader", source = ".")
	@Mapping(target = "transport", source = ".")
	CollectionOrderInformation map(CollectionOrder order);
}
