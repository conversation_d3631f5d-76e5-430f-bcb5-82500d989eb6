package com.dachser.dfe.book.service.postalcode;

import com.dachser.dfe.book.DFEBookApplication;
import com.dachser.dfe.book.model.PostcodeValidationDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@Tag("IntegrationTest")
@SpringBootTest(classes = { DFEBookApplication.class })
@ActiveProfiles("integrationtest")
@Slf4j
class PostalCodeServiceIntegrationTest {

	@Autowired
	private PostalCodeService postalCodeService;

	@ParameterizedTest(name = "postcode [{1}] for country {0} is valid = {2}")
	@CsvSource({ "DE,87435,true", "SK,811 03,true", "SK,1234,false", "IE,H54 YR28,true", "HK,,false", "HK,'',true", "HK,' ',false", "HK,999077,false", "HK,12345,false" })
	void postcodeValidation(String countryCode, String postcode, boolean isValid) {
		// given country code and postcode
		// when cross checking those
		PostcodeValidationDto result = postalCodeService.validatePostCode(countryCode, postcode);
		// then we obtain a result
		Assertions.assertNotNull(result);
		log.debug("validate postcode {} for country {} = {}", postcode, countryCode, result);
		// and evaluation is as expected
		Assertions.assertEquals(isValid, result.getValid());
	}

}