package com.dachser.dfe.book.dip.converter.sea;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.config.term.TermSorter;
import com.dachser.dfe.book.dip.converter.shared.AirSeaGoodsDescriptionMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderContactCommunicationsMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.DateMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.StringNotEmptyConditionMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.generaldata.ContainerTypeMapperImpl;
import com.dachser.dfe.book.generaldata.GeneralDataAdapterMock;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.generaldata.term.TermMapperImpl;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.model.jaxb.order.asl.Location;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.common.fcl.FullContainerLoad;
import com.dachser.dfe.book.order.generator.OrderGenerator;
import com.dachser.dfe.book.order.sea.BaseFullContainerLoadRepository;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaOrderLineHsCode;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
@SpringBootTest(properties = "dfe.book.mock=true", classes = { SeaFreightMapperImpl.class, SeaPickupOrderMapperImpl.class, SeaDeliveryOrderMapperImpl.class, DateMapperImpl.class,
		AirSeaOrderContactCommunicationsMapperImpl.class, SeaOrderLineMapperImpl.class, SeaOrderReferenceMapperImpl.class, AirSeaGoodsDescriptionMapperImpl.class,
		StringNotEmptyConditionMapperImpl.class, SeaOrderCategoryMapperImpl.class, SeaContainerMapperImpl.class, GeneralDataService.class, GeneralDataAdapterMock.class,
		ContainerTypeMapperImpl.class, WhiteListFilterConfiguration.class, TermSorter.class, TermMapperImpl.class })
class SeaFreightMapperTest {

	@Autowired
	SeaFreightMapper seafreightMapper;

	@MockBean
	BaseFullContainerLoadRepository baseFullContainerLoadRepository;

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	@Nested
	class ValidMapping {

		@Nested
		class SeaOrderMapper {
			SeaExportOrder order = testUtil.generateSeaExportOrder();

			@Test
			void shouldMapLocations() {
				final List<Location> locations = seafreightMapper.mapLocations(order);
				assertNotNull(locations);
				assertEquals(2, locations.size());
			}

			@Test
			void shouldMapAirFreightShipment() {
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				assertNotNull(map);
				final List<ForwardingOrder.SeaFreightShipment.PickupOrder> pickupOrder = map.getPickupOrder();
				assertNotNull(pickupOrder);
				assertEquals(1, pickupOrder.size());
			}

			@Test
			void shouldSetOrderCategory() {
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				assertNotNull(map);
				assertEquals("LCL", map.getOrderCategory());
			}

			@Test
			void shouldNotMapStackableAndShockSensitiveForFcl() {
				order.setFullContainerLoad(true);
				order.setStackable(true);
				order.setShockSensitive(true);
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				assertNotNull(map);
				assertTrue(map.getPickupOrder().getFirst().getOrderReference().stream()
						.noneMatch(reference -> reference.getTypeOfReference().equals(Types.HandlingInstructions.NOT_STACKABLE)));
				assertTrue(map.getPickupOrder().getFirst().getOrderReference().stream()
						.noneMatch(reference -> reference.getTypeOfReference().equals(Types.HandlingInstructions.SHOCK_SENSITIVE)));
			}

			@Test
			void shouldSetTransportMovementCategory() {
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				assertNotNull(map);
				assertEquals("Export", map.getTransportMovement());
			}

			@Test
			void shouldMapShipmentAddress() {
				final List<ForwardingOrder.SeaFreightShipment.ShipmentAddress> shipmentAddresses = seafreightMapper.customMapShipmentAddresses(order);
				assertNotNull(shipmentAddresses);
				assertEquals(7, shipmentAddresses.size());
			}

			@Test
			void shouldMapPickupAddressToOrderAddress() {
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				assertNotNull(map.getPickupOrder());
				assertNotNull(map.getPickupOrder().get(0));
				final ForwardingOrder.SeaFreightShipment.PickupOrder.OrderAddress orderAddress = map.getPickupOrder().get(0).getOrderAddress();
				assertNotNull(orderAddress);
				assertEquals("Example GmbH _pickup", orderAddress.getAddress().getName1());
				assertEquals(Types.AddressTypes.PICKUP, orderAddress.getTypeOfAddress());
			}

			@Test
			void shouldNotMapPickupDatesOnRequestArrangement() {
				order.setRequestArrangement(true);
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				final List<ForwardingOrder.SeaFreightShipment.PickupOrder> pickupOrder = map.getPickupOrder();
				assertNotNull(pickupOrder);
				final ForwardingOrder.SeaFreightShipment.PickupOrder pickupOrder1 = pickupOrder.get(0);
				assertNotNull(pickupOrder1);
				assertEquals(0, pickupOrder1.getPickUpDate().size());
			}

			@Test
			void shouldMapShipperAddressToOrderAddress() {
				order.setPickupAddress(null);
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				assertNotNull(map.getPickupOrder());
				assertNotNull(map.getPickupOrder().get(0));
				final ForwardingOrder.SeaFreightShipment.PickupOrder.OrderAddress orderAddress = map.getPickupOrder().get(0).getOrderAddress();
				assertNotNull(orderAddress);
				assertEquals("Example GmbH _shipper", orderAddress.getAddress().getName1());
			}

			@Test
			void shouldMapConsigneeAddressAlsAsConsigneeForCollectOption() {
				order.setCollectFromPort(true);
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				final List<ForwardingOrder.SeaFreightShipment.ShipmentAddress> shipmentAddress = map.getShipmentAddress();
				assertNotNull(shipmentAddress);
				final Optional<ForwardingOrder.SeaFreightShipment.ShipmentAddress> consigneeAddress = shipmentAddress.stream()
						.filter(address -> address.getTypeOfAddress().equals(Types.AddressTypes.CONSIGNEE)).findFirst();
				assertTrue(consigneeAddress.isPresent());
				assertEquals("Example GmbH _consignee", consigneeAddress.get().getAddress().getName1());
				final Optional<ForwardingOrder.SeaFreightShipment.ShipmentAddress> importer = shipmentAddress.stream()
						.filter(address -> address.getTypeOfAddress().equals(Types.AddressTypes.IMPORTER)).findFirst();
				assertTrue(importer.isPresent());
			}

			@Test
			void shouldMapImporterAsImporterIfAvailable() {
				order.setCollectFromPort(true);
				OrderFurtherAddress importerAddress = TestUtil.buildSeaImporterAddress();
				order.addAddress(importerAddress);
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				final List<ForwardingOrder.SeaFreightShipment.ShipmentAddress> shipmentAddress = map.getShipmentAddress();
				assertNotNull(shipmentAddress);
				final Optional<ForwardingOrder.SeaFreightShipment.ShipmentAddress> consigneeAddress = shipmentAddress.stream()
						.filter(address -> address.getTypeOfAddress().equals(Types.AddressTypes.CONSIGNEE)).findFirst();
				assertTrue(consigneeAddress.isPresent());
				assertEquals("Example GmbH _consignee", consigneeAddress.get().getAddress().getName1());
				final Optional<ForwardingOrder.SeaFreightShipment.ShipmentAddress> importer = shipmentAddress.stream()
						.filter(address -> address.getTypeOfAddress().equals(Types.AddressTypes.IMPORTER)).findFirst();
				assertTrue(importer.isPresent());
				assertEquals(importerAddress.getName(), importer.get().getAddress().getName1());
			}

			@Test
			void shouldUseDeliveryAddressAsUltimateConsignee() {
				final List<ForwardingOrder.SeaFreightShipment.ShipmentAddress> shipmentAddresses = seafreightMapper.customMapShipmentAddresses(order);
				assertNotNull(shipmentAddresses);
				assertTrue(shipmentAddresses.stream().anyMatch(shipmentAddress -> shipmentAddress.getTypeOfAddress().equals(Types.AddressTypes.ULTIMATE_CONSIGNEE)));
				final Optional<ForwardingOrder.SeaFreightShipment.ShipmentAddress> first = shipmentAddresses.stream()
						.filter(address -> address.getTypeOfAddress().equals(Types.AddressTypes.ULTIMATE_CONSIGNEE)).findFirst();
				final ForwardingOrder.SeaFreightShipment.ShipmentAddress shipmentAddress = first.get();
				assertEquals("Example GmbH _delivery", shipmentAddress.getAddress().getName1());
			}

			@Test
			void shouldOmitEmptyVolume() {
				order.getOrderLines().get(0).setVolume(BigDecimal.ZERO);
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				final List<ForwardingOrder.SeaFreightShipment.PickupOrder> pickupOrder = map.getPickupOrder();
				assertNotNull(pickupOrder);
				assertEquals(1, pickupOrder.size());
				final ForwardingOrder.SeaFreightShipment.PickupOrder firstPickupOrder = pickupOrder.get(0);
				assertNull(firstPickupOrder.getOrderPosition().get(0).getVolume());
			}

			@Test
			void shouldSetTailLiftCollection() {
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				final List<ForwardingOrder.SeaFreightShipment.PickupOrder> pickupOrder = map.getPickupOrder();
				assertNotNull(pickupOrder);
				assertEquals(1, pickupOrder.get(0).getLoadingInstruction().size());
				assertEquals(Types.LoadingInstruction.TLF, pickupOrder.get(0).getLoadingInstruction().get(0));
			}

			@Test
			void shouldOmitTailLiftIfNotSet() {
				order.setTailLiftCollection(false);
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				final List<ForwardingOrder.SeaFreightShipment.PickupOrder> pickupOrder = map.getPickupOrder();
				assertNotNull(pickupOrder);
				assertEquals(0, pickupOrder.get(0).getLoadingInstruction().size());
			}

			@Test
			void shouldRemoveUnmappedAddressTypes() {
				final OrderFurtherAddress address = new OrderFurtherAddress();
				address.setOrder(order);
				address.setName("Should not be mapped");
				address.setAddressType("XYZ");
				order.addAddress(address);
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				final List<ForwardingOrder.SeaFreightShipment.ShipmentAddress> shipmentAddress = map.getShipmentAddress();
				assertEquals(7, shipmentAddress.size());
			}

			@Test
			void shouldHandleGoodsDescription() {
				final List<SeaOrderLineHsCode> hsCodes = order.getOrderLines().get(0).getHsCodes();
				final String goodsInput = "Goods description with a rather long text to be sure it is not cut off and still fits into the field but is split in xml.";
				hsCodes.get(0).setGoods(goodsInput);
				hsCodes.get(1).setGoods(goodsInput);
				SeaOrderLineHsCode seaOrderLineHsCode = new SeaOrderLineHsCode();
				seaOrderLineHsCode.setGoods(goodsInput);
				hsCodes.add(seaOrderLineHsCode);
				hsCodes.add(seaOrderLineHsCode);
				hsCodes.add(seaOrderLineHsCode);
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				final List<ForwardingOrder.SeaFreightShipment.PickupOrder> pickupOrder = map.getPickupOrder();
				assertNotNull(pickupOrder);
				assertEquals(1, pickupOrder.get(0).getOrderPosition().size());
				List<String> goodsDescription = pickupOrder.get(0).getGoodsDescription();
				assertEquals(2, goodsDescription.size());
				final String validResult = "Goods description with a rather long text to be sure it is not cut off and still fits into the field but is split in xml. | Goods description with a rather long text to be sure it is not cut off and still fits into the field but is split in xml. | goods3 | Goods description with a rather long text to be sure it is not cut off and still fits into the field but is split in xml. | Goods description with a rather long text to be sure it is not cut off and still fits into the field but is split in xm";
				assertEquals(validResult, goodsDescription.get(0));
				assertEquals("l. | Goods description with a rather long text to be sure it is not cut off and still fits into the field but is split in xml.",
						goodsDescription.get(1));
			}

			@Test
			void shouldNotMapContainerOnLCL() {
				final ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				assertNotNull(map);
				assertTrue(map.getContainer().isEmpty());
			}

			@Test
			void shouldMapFCL() {
				asFCL();
				order.getFullContainerLoads().getFirst().getOrderLines().add(OrderGenerator.generateSeaOrderLine());
				final ForwardingOrder.SeaFreightShipment seaFreightShipment = seafreightMapper.map(order);
				assertNotNull(seaFreightShipment);
				assertFalse(seaFreightShipment.getContainer().isEmpty());
				assertEquals(2, seaFreightShipment.getContainer().size());
				ForwardingOrder.SeaFreightShipment.Container container = seaFreightShipment.getContainer().getFirst();
				assertEquals(BigInteger.valueOf(4), container.getTotalPieces());
				assertEquals("20GP", container.getTypeOfContainer());
				assertEquals(BigDecimal.valueOf(100.0), container.getGrossWeight().getValue());
			}

			@Test
			void shouldHandleMissingVolumeCorrectly(){
				order.getOrderLines().getFirst().setVolume(null);
				ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				assertNotNull(map);
			}

			@Test
			void shouldHandleMissingWeightCorrectly(){
				order.getOrderLines().getFirst().setWeight(null);
				ForwardingOrder.SeaFreightShipment map = seafreightMapper.map(order);
				assertNotNull(map);
			}

			private void asFCL() {
				order.setFullContainerLoad(true);
				FullContainerLoad fullContainerLoad = new FullContainerLoad();
				fullContainerLoad.setId(1L);
				fullContainerLoad.setContainerNumber("XXXZ4569845");
				fullContainerLoad.setContainerType("GP-20");
				fullContainerLoad.setOrderLines(order.getOrderLines());
				fullContainerLoad.setSortingPosition(0);

				FullContainerLoad fullContainerLoad2 = new FullContainerLoad();
				fullContainerLoad2.setId(2L);
				fullContainerLoad2.setContainerType("GP-20");
				fullContainerLoad2.setOrderLines(order.getOrderLines());
				fullContainerLoad2.setSortingPosition(1);
				order.setFullContainerLoads(List.of(fullContainerLoad, fullContainerLoad2));
			}

			@Test
			void shouldMapContainerID() {
				asFCL();
				final ForwardingOrder.SeaFreightShipment seaFreightShipment = seafreightMapper.map(order);
				assertNotNull(seaFreightShipment);
				assertEquals(2, seaFreightShipment.getContainer().size());
				ForwardingOrder.SeaFreightShipment.Container firstContainer = seaFreightShipment.getContainer().getFirst();
				ForwardingOrder.SeaFreightShipment.Container secondContainer = seaFreightShipment.getContainer().get(1);
				assertEquals(BigInteger.valueOf(1L), firstContainer.getContainerID());
				// Second container has an auto generated container number as it is not set manually
				assertEquals("1", secondContainer.getContainerNumber());
			}

		}

	}
}