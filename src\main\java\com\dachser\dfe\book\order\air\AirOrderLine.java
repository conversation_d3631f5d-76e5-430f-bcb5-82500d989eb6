package com.dachser.dfe.book.order.air;

import com.dachser.dfe.book.order.MarksAndNumbersOrderLine;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.common.orderline.OrderLine;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "order_line_air")
public class AirOrderLine extends OrderLine implements MarksAndNumbersOrderLine {

	/**
	 * @deprecated use {@link com.dachser.dfe.book.quote.QuoteOrderLine#setPackagingType(String)} instead
	 */
	@Deprecated(forRemoval = true)
	@Size(max = 3)
	private String originalPackagingType;

	/**
	 * @deprecated use {@link com.dachser.dfe.book.quote.QuoteOrderLine#setPackagingTypeDescription(String)} instead
	 */
	@Deprecated(forRemoval = true)
	@Size(max = 50)
	private String originalPackagingTypeDescription;

	@OneToMany(mappedBy = "orderLine", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@ToString.Exclude
	@Valid
	private List<AirOrderLineHsCode> hsCodes;

	@DecimalMin("0.1")
	@Max(99999)
	private BigDecimal weight;

	@Size(max = 1500)
	private String markAndNumbers;

	@JoinColumn(name = "order_id", nullable = false)
	@ManyToOne(fetch = FetchType.LAZY)
	@ToString.Exclude
	@EqualsAndHashCode.Exclude
	private Order order;

	@Override
	public void setWeight(BigDecimal weight) {
		this.weight = weight;
	}
}
