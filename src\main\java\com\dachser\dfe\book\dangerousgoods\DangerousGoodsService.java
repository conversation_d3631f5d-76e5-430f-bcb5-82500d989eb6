package com.dachser.dfe.book.dangerousgoods;

import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.Segment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Locale;

@Service
@RequiredArgsConstructor
@Slf4j
public class DangerousGoodsService {

	private final GeneralDataService generalDataService;

	private final WhiteListFilterConfiguration whiteListFilterConfiguration;

	private final DangerousGoodsThirdPartyAdapter dangerousGoodsThirdPartyAdapter;

	public List<OptionDto> getDangerousGoodsPackagingOptions(final Segment segment) {
		return switch (segment) {
			case ROAD -> getDangerousGoodsPackagingOptionsRoad();
			case AIR, SEA -> {
				log.debug("Dangerous goods packaging options are not implemented for segment {} yet", segment);
				yield List.of();
			}
		};
	}

	public List<DangerousGoodDataItemDto> findDangerousGoodsByUNNumber(final Segment segment, final String searchString) {
		// The Locale only holds the language, not the country code.
		// We use the user language to ensure that the search is performed in the correct language context.
		final String userLanguage = LocaleContextHolder.getLocale().getLanguage();
		return switch (segment) {
			case ROAD -> dangerousGoodsThirdPartyAdapter.searchUnNumbersRoad(searchString, userLanguage);
			case AIR, SEA -> {
				log.debug("Dangerous goods data items are not implemented for segment {} yet", segment);
				yield List.of();
			}
		};
	}

	private List<OptionDto> getDangerousGoodsPackagingOptionsRoad() {
		final Locale locale = LocaleContextHolder.getLocale();
		List<OptionDto> dgPackagingOptions = generalDataService.getPackagingOptionsRoad(locale.getLanguage());
		dgPackagingOptions.forEach(option -> option.setTranslationKey("generalData.packagingOptionRoad." + option.getCode()));
		return doFilteringRoad(dgPackagingOptions);
	}

	private List<OptionDto> doFilteringRoad(List<OptionDto> response) {
		return response.stream().filter(dto -> whiteListFilterConfiguration.getDangerousGoods().getPackagingOptionsFilter().getRoad().contains(dto.getCode())).toList();
	}

}
