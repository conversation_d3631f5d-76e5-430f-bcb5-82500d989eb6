package com.dachser.dfe.book.service.label.shipment;

import com.dachser.dfe.book.DFEBookApplication;
import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.model.TestMockData;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.user.UserService;
import com.dachser.dfe.road.masterdata.model.RMDRelationLabelprintingDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Tag("IntegrationTest")
@SpringBootTest(classes = { DFEBookApplication.class })
@ActiveProfiles("integrationtest")
@TestPropertySource(properties = { "dfe.book.cache2k.enabled=false" })
@Slf4j
class ShipmentLabelAdapterExtIntegrationTest {

	@Autowired
	private TestUtil utils;

	@MockBean
	private UserService userService;

	@Captor
	private ArgumentCaptor<RMDRelationLabelprintingDTO> dispatchRelationCaptor;

	@SpyBean
	private ShipmentLabelAdapterExt shipmentLabelAdapterExt;

	@ParameterizedTest(name = "branch {0} has consolidator relation {1}")
	@CsvSource({ "6,0006", "59,1618" })
	void consolidatorRelations(Integer branch, String consolidatorRelation) {
		// given a branch
		// when looking for its consolidator relation
		String relation = shipmentLabelAdapterExt.relationOfConsolidator(TestMockData.BUSINESS_DOMAIN, branch);
		// then we obtain content
		assertFalse(StringUtils.isBlank(relation));
		assertEquals(consolidatorRelation, relation);
	}

	@ParameterizedTest(name = "branch {0} has dispatch relation {1}")
	@CsvSource({ "6,0008", "59,0008" })
	void dispatchRelations(Integer branch, String dispatchRelation) {
		// given a forwarding order
		ForwardingOrder order = utils.generateForwardingOrder();
		// and a destination branch
		order.setBranchId(branch);
		// when building a label file for this order
		Mockito.when(userService.getCurrentUserId()).thenReturn(UUID.randomUUID().toString());
		byte[] bytes = shipmentLabelAdapterExt.getPrintLabelsForForwardingOrder(order, TestMockData.BUSINESS_DOMAIN);
		// then we obtain content
		assertNotNull(bytes);
		assertNotEquals(0, bytes.length);
		// and dispatch relation is as expected
		Mockito.verify(shipmentLabelAdapterExt, Mockito.times(1)).doPrint(Mockito.any(), Mockito.anyInt(), Mockito.any(), dispatchRelationCaptor.capture());
		RMDRelationLabelprintingDTO dispatchRelationCapture = dispatchRelationCaptor.getValue();
		assertNotNull(dispatchRelationCapture);
		log.warn("dispatch branch {} relation is {}", branch, dispatchRelationCapture);
		assertEquals(dispatchRelation, dispatchRelationCapture.getDispatchRelation());
	}

}