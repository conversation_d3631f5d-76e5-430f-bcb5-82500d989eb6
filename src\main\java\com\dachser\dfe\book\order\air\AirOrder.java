package com.dachser.dfe.book.order.air;

import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.common.AirSeaOrder;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.sea.OrderLineCombiner;
import com.dachser.dfe.book.order.validation.air.CompleteOrderValidationAir;
import com.dachser.dfe.book.order.validation.air.ValidAirCollectionTimeSlot;
import com.dachser.dfe.book.order.validation.air.ValidAirOrder;
import com.dachser.dfe.book.order.validation.air.ValidAirOrderLine;
import com.dachser.dfe.book.order.validation.air.ValidAirProduct;
import com.dachser.dfe.book.order.validation.common.ValidOrder;
import com.dachser.dfe.book.quote.AirQuoteInformation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "order_air")
@ValidAirOrder(groups = CompleteOrderValidationAir.class)
@ValidOrder(groups = CompleteOrderValidationAir.class)
@ValidAirCollectionTimeSlot(groups = CompleteOrderValidationAir.class)
@ValidAirProduct(groups = CompleteOrderValidationAir.class)
@ValidAirOrderLine(groups = CompleteOrderValidationAir.class)
public abstract class AirOrder extends Order implements AirSeaOrder<AirOrderReference>, OrderLineCombiner<AirOrderLine, AirOrderLineHsCode> {

	@Size(max = 5)
	@Column(name = "from_iata")
	@NotNull(groups = CompleteOrderValidationAir.class, message = "{label.text.invalid_input}")
	private String fromIATA;

	@Size(max = 5)
	@Column(name = "to_iata")
	@NotNull(groups = CompleteOrderValidationAir.class, message = "{label.text.invalid_input}")
	private String toIATA;

	private boolean deliverToAirport;

	private boolean collectFromAirport;

	@Size(max = 3)
	@NotNull(groups = CompleteOrderValidationAir.class, message = "{label.text.invalid_input}")
	private String incoTerm;

	@JoinColumn(name = "customer_contact_id")
	@OneToOne(cascade = CascadeType.ALL)
	@Valid
	private OrderContact orderContact;

	@JoinColumn(name = "pickup_address_id")
	@OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
	@Valid
	private OrderAddress pickupAddress;

	@JoinColumn(name = "delivery_address_id")
	@OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
	@Valid
	private OrderAddress deliveryAddress;

	@OneToMany(mappedBy = "order", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@ToString.Exclude
	@Size(min = 1, groups = CompleteOrderValidationAir.class, message = "{label.text.invalid_input}")
	@Valid
	@OrderBy("number")
	private List<AirOrderLine> orderLines;

	@OneToMany(mappedBy = "order", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@ToString.Exclude
	@Valid
	private List<AirOrderReference> orderReferences;

	@Size(max = 35)
	@NotNull(groups = CompleteOrderValidationAir.class, message = "{label.text.invalid_input}")
	private String shipperReference;

	private boolean requestArrangement;

	private boolean tailLiftCollection;

	// Business default is true
	private boolean stackable = true;

	private boolean shockSensitive;

	private Integer productCode;

	@Size(max = 100)
	private String productName;

	public void addOrderLine(AirOrderLine orderLine) {
		if (orderLines == null) {
			orderLines = new ArrayList<>();
		}
		orderLines.add(orderLine);
		orderLine.setOrder(this);
	}

	public void addOrderReference(AirOrderReference orderReference) {
		if (orderReferences == null) {
			orderReferences = new ArrayList<>();
		}
		orderReferences.add(orderReference);
		orderReference.setOrder(this);
	}

	public Double getTotalOrderWeight() {
		if (orderLines != null) {
			return orderLines.stream().map(AirOrderLine::getWeight).filter(Objects::nonNull).mapToDouble(BigDecimal::doubleValue).sum();
		}
		return 0.0;
	}

	public double getTotalOrderVolume() {
		if (orderLines != null) {
			return orderLines.stream().map(AirOrderLine::getVolume).filter(Objects::nonNull).mapToDouble(BigDecimal::doubleValue).sum();
		}
		return 0.0;
	}

	public int getTotalAmountPackages() {
		if (orderLines != null) {
			return orderLines.stream().map(AirOrderLine::getQuantity).filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
		}
		return 0;
	}

	@Transient
	public String getCombinedOrderLineGoods() {
		return combineGoods(getOrderLines(), AirOrderLine::getHsCodes);
	}


	// Provide a getter for the quote information of an air order - this is absolutely necessary for mapper to determine types correctly
	@Override
	public AirQuoteInformation getQuoteInformation() {
		return (AirQuoteInformation) super.getQuoteInformation();
	}

	@Override
	public List<AirOrderReference> getOrderReferences() {
		return orderReferences;
	}
}
