package com.dachser.dfe.book.order.emissionforecast;

import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.user.UserContextService;
import com.dachser.dfe.emissionforecast.model.EFForecastRequestDto;
import com.dachser.dfe.emissionforecast.model.EFForecastResponseDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Answers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestClientException;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EmissionForecastServiceTest {

	@InjectMocks
	private EmissionForecastService emissionForecastService;

	@Mock(answer = Answers.RETURNS_DEEP_STUBS)
	private UserContextService userContextService;

	@Mock
	private EmissionForecastAdapter emissionForecastAdapter;

	@Mock
	private EmissionForecastMapper emissionForecastMapper;

	@Mock
	private Order order;

	@Mock
	private OrderType orderType;

	private final Segment segment = Segment.ROAD;

	private EFForecastRequestDto requestDto;

	@BeforeEach
	void setUp() {
		when(order.getOrderType()).thenReturn(orderType);
		when(orderType.getSegment()).thenReturn(segment);

		requestDto = new EFForecastRequestDto();
		when(emissionForecastMapper.mapToEFForecastRequestDto(any(), anyString(), any()))
				.thenReturn(requestDto);
	}

	@Test
	void getEmissionForecastForOrder_shouldReturnForecastValue_whenAdapterReturnsResponse() {
		EFForecastResponseDto responseDto = new EFForecastResponseDto();
		responseDto.setCo2Equivalents(42.0);
		when(emissionForecastAdapter.getEmissionForecast(requestDto)).thenReturn(Optional.of(responseDto));

		double result = emissionForecastService.getEmissionForecastForOrder(order).get();

		assertEquals(42.0, result, 0.001);
		verify(emissionForecastAdapter, times(1)).getEmissionForecast(requestDto);
	}

	@Test
	void getEmissionForecastForOrder_shouldReturnZero_whenAdapterThrowsException() {
		when(emissionForecastAdapter.getEmissionForecast(requestDto))
				.thenThrow(new RestClientException("API error"));

		Optional<Double> result = emissionForecastService.getEmissionForecastForOrder(order);

		assertEquals(Optional.empty(), result);
		verify(emissionForecastAdapter, times(1)).getEmissionForecast(requestDto);
	}
}