package com.dachser.dfe.book.order.validation.road.delivery;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.TestMockData;
import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.TargoOnSiteProducts;
import com.dachser.dfe.book.order.validation.Messages;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.HibernateValidator;
import org.hibernate.validator.HibernateValidatorConfiguration;
import org.hibernate.validator.cfg.ConstraintMapping;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Set;
import java.util.stream.Stream;

@Slf4j
class DeliveryOptionValidatorTest {

	private static final TestUtil UTILS = TestUtil.createInstanceNonSpring();

	private static HibernateValidatorConfiguration validationConfiguration;

	@BeforeAll
	public static void configure() {
		UTILS.orderGenerator.collectionOrderSupplier = DeliveryOptionValidatorTest.CollectionOrderMock::new;
		validationConfiguration = Validation.byProvider(HibernateValidator.class).configure();
		validationConfiguration.addMapping(constraintMapping());
	}

	private static ConstraintMapping constraintMapping() {
		// @formatter:off
		ConstraintMapping constraintMapping = validationConfiguration.createConstraintMapping();
		constraintMapping
		.type(RoadOrder.class)
		.constraintDefinition(DeliveryOptionValid.class)
		.validatedBy(DeliveryOptionValidatorMock.class)
		.includeExistingValidators(false);
		return constraintMapping;
		// @formatter:on
	}

	@ParameterizedTest(name = "#{index} - order with product {1} and delivery option {2} evaluates to {3}")
	@MethodSource("scenarios")
	void evaluates(RoadOrder order, String product, DeliveryOptions deliveryOption, boolean valid, String i18nViolationKey) {
		// given a road order
		Assertions.assertNotNull(order);
		// having a specific product
		Assertions.assertFalse(StringUtils.isBlank(product));
		order.setProduct(product);
		// having a specific delivery option
		order.setDeliveryOption(deliveryOption);
		// when validating it
		Set<ConstraintViolation<RoadOrder>> violations;
		try (ValidatorFactory validatorFactory = validationConfiguration.buildValidatorFactory()) {
			Validator validator = validatorFactory.getValidator();
			violations = validator.validate(order);
		}
		// then validation evaluates as expected
		log.debug("violations are {}", violations);
		Assertions.assertEquals(valid, violations.isEmpty());
		// and expected failure message is present
		if (i18nViolationKey != null) {
			Assertions.assertTrue(violations.stream().map(ConstraintViolation::getMessageTemplate).anyMatch(i18nViolationKey::equals));
		}
	}

	private static Stream<Arguments> scenarios() {
		// @formatter:off
		return Stream.of(
			Arguments.of(order(), TestMockData.TARGOFLEX, DeliveryOptions.NO, true, null),
			Arguments.of(order(), TargoOnSiteProducts.TARGO_ON_SITE.getCoreSystemValue(), null, false, Messages.MISSING_DELIVERY_OPTION),
			Arguments.of(order(), TargoOnSiteProducts.TARGO_ON_SITE.getCoreSystemValue(), DeliveryOptions.NO, false, Messages.MISSING_DELIVERY_OPTION),
			Arguments.of(order(), TargoOnSiteProducts.TARGO_ON_SITE.getCoreSystemValue(), DeliveryOptions.AC, false, Messages.NOT_ALLOWED_DELIVERY_OPTION),
			Arguments.of(order(), TargoOnSiteProducts.TARGO_ON_SITE.getCoreSystemValue(), DeliveryOptions.AP, true, null)
		);
		// @formatter:on
	}

	private static CollectionOrder order() {
		CollectionOrder order = UTILS.generateCollectionOrder();
		order.setDifferentConsigneeAddress(null);
		order.setStatus(OrderStatus.DRAFT);
		return order;
	}

	@DeliveryOptionValid
	private static class CollectionOrderMock extends CollectionOrder {
	}

}