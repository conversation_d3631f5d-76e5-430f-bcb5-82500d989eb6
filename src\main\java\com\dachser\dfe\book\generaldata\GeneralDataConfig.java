package com.dachser.dfe.book.generaldata;

import com.dachser.dfe.generaldata.ApiClient;
import com.dachser.dfe.generaldata.api.CountryApi;
import com.dachser.dfe.generaldata.api.DeliveryApi;
import com.dachser.dfe.generaldata.api.LanguageApi;
import com.dachser.dfe.generaldata.api.PackagingApi;
import com.dachser.dfe.generaldata.api.TermsApi;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
@ConfigurationProperties(prefix = "com.dachser.dfe.general-data.configuration")
@Getter
@Setter
class GeneralDataConfig {

	@Value("${dfe.general-data.configuration.baseURL}")
	private String baseURL;

	private String username;

	private String password;

	private final RestTemplate bookRestTemplate;

	@Autowired
	GeneralDataConfig(@Qualifier("bookRestTemplate") RestTemplate restTemplate) {
		this.bookRestTemplate = restTemplate;
	}

	@Bean
	CountryApi generalDataCountryApi() {
		return new CountryApi(generalDataApiClient());
	}

	@Bean
	TermsApi termsApi() {
		return new TermsApi(generalDataApiClient());
	}

	@Bean
	PackagingApi generalDataPackagingApi() {
		return new PackagingApi(generalDataApiClient());
	}

	@Bean
	DeliveryApi generalDataDeliveryApi() {
		return new DeliveryApi(generalDataApiClient());
	}

	@Bean
	LanguageApi generalDataLanguageApi() {
		return new LanguageApi(generalDataApiClient());
	}

	private ApiClient generalDataApiClient() {
		final ApiClient apiClient = new ApiClient(bookRestTemplate);
		apiClient.setBasePath(baseURL);
		apiClient.setPassword(password);
		apiClient.setUsername(username);
		return apiClient;
	}

}