package com.dachser.dfe.book.order.sea;

import com.dachser.dfe.book.order.common.orderline.OrderLineHsCode;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@RequiredArgsConstructor
@Table(name = "order_line_sea_hs_code")
public class SeaOrderLineHsCode extends OrderLineHsCode {

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "order_line_id", nullable = false)
	@ToString.Exclude
	@EqualsAndHashCode.Exclude
	private SeaOrderLine orderLine;

	@Size(max = 1500)
	private String goods;

}
