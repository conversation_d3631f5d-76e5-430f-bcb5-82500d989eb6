spring:
  datasource:
    driver-class-name: org.h2.Driver
    username: sa
    password:
    #url: "jdbc:h2:./testdb/h2testdb;MODE=MSSQLServer;DB_CLOSE_ON_EXIT=FALSE;INIT=CREATE SCHEMA IF NOT EXISTS dbo\\;SET SCHEMA dbo"
    url: "jdbc:h2:mem:db1;MODE=MSSQLServer;DB_CLOSE_ON_EXIT=FALSE;INIT=CREATE SCHEMA IF NOT EXISTS dbo\\;SET SCHEMA dbo"
  liquibase:
    contexts: mock-data,h2
    drop-first: true
  jpa:
    properties:
      hibernate:
        # as long as we use h2 in MSSQL mode we need to use the SQLServerDialect
        dialect: org.hibernate.dialect.SQLServerDialect

ENV_BOOK_DB_LOGLEVEL: WARN
ENV_BOOK_APPLICATION_LOGLEVEL: WARN
ENV_BOOK_TRIAL_MODE_ROAD: true
ENV_BOOK_TRIAL_MODE_AIR_SEA: true
ENV_BOOK_UMS_FACTORY: SpringTestTopicConnectionFactory
ENV_BOOK_API_DFE_AIRSEA_MASTERDATA_BASEURL: "https://dach041x.dach041.dachser.com:21397/airsea.masterdata.service"
ENV_BOOK_API_DFE_SHAREDSERVICES_MASTERDATA_BUSINESSPARTNER_BASEURL: "https://dach041y.dach041.dachser.com:21167/masterdata.businesspartner.backend"
DFE_PLATFORM_BACKEND_SERVICE_HOST: "https://dfe-platform-backend-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com/"
ENV_BOOK_API_DFE_PLATFORM_BACKEND_BASEURL: "https://dfe-platform-backend-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"
ENV_BOOK_API_GENERAL_DATA_SERVER_BASEURL: "https://dfe-general-data-backend-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"
ENV_BOOK_API_PDF_EXPORT_SERVER_BASEURL: "https://dfe-pdf-export-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"
ENV_BOOK_API_DFE_EMISSIONFORECAST_BASEURL: "https://dfe-emissionforecast-backend-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"
ENV_BOOK_API_CONFIGSERVER_BASEURL: "https://api.platform-dev.dach041.dachser.com/config/"
ENV_BOOK_API_MASTERDATA_GATEWAY_BASEURL: "https://masterdata-dev.api.dachser.com"
ENV_BOOK_API_DFE_ROAD_MASTERDATA_BASEURL: "https://dach041y.dach041.dachser.com:21365/road.masterdata.order.service"

oAuth:
  server-base-url: "http://localhost/not_set"

bi:
  common:
    print:
      pri:
        generateTrialLabel: true

ums:
  enabled: false
  provider:
    url: nsp://APW041UMST01.dachser-ds.dachser.com:9000,nsp://APW041UMST02.dachser-ds.dachser.com:9000
  dip:
    ediTestFlagRoad: true
    ediTestFlagAirSea: true  
    print-xml: true
  connection:
    factory: DipCF

dfe:
  book:
    mock:
      product:
        road: false
  road:
    collectionOrder:
      enabled: true
    masterdata:
      use-preconfigured-cache-2k: false
  services:
    document:
      basePath: ./integration-test-files/
  platform:
    configuration:
      use-cache: false

  kta:
    url: http://ROADWEB01D.dachser-ds.dachser.com:90/import
    dataSource: DFE
    creationUser: DFE_DEV      