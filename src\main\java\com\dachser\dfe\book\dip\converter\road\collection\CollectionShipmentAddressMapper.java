package com.dachser.dfe.book.dip.converter.road.collection;

import com.dachser.dfe.book.model.jaxb.order.road.collection.AddressInformation;
import com.dachser.dfe.book.model.jaxb.order.road.collection.ContactInformation;
import com.dachser.dfe.book.model.jaxb.order.road.collection.PartnerInformation;
import com.dachser.dfe.book.dip.converter.road.GeneralTransportDataMapper;
import com.dachser.dfe.book.dip.converter.road.RoadShipmentAddressMapper;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = GeneralTransportDataMapper.class)
public interface CollectionShipmentAddressMapper extends RoadShipmentAddressMapper<PartnerInformation, AddressInformation, ContactInformation> {
}
