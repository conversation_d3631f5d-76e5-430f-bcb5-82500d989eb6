server:
  servlet:
    context-path: /
management:
  endpoint:
    health:
      show-components: always
    info:
      enabled: true
    caches:
      enabled: true
  endpoints:
    web:
      exposure:
        include: health,info,caches,caches-cache
      base-path: /mgmt

oAuth:
  server-base-url: ${ENV_BOOK_OAUTH_SERVER_BASE_URL}


spring:
  profiles:
    active: "${variables.spring.profiles.active:}"
    group:
      dev:
        - "com.dachser.road.masterdata.restapi"
        - "road.ohs.pub.rest"
        - "road.ohs.pub.targetsystem.dach041q.liberty"
        - "bi.common.security.pri.rest"
        - "bi.common.security.pri.targetsystem.test"
        - "bi.common.print.pri.rest"
        - "bi.common.print.pri.targetsystem.test"
        - "bi.common.shipment.pri.rest"
        - "bi.common.shipment.pri.targetsystem.test"
        - "bi.common.validation.pri.rest"
        - "bi.common.validation.pri.targetsystem.test"
        - "elogistics.order.service.pub.rest"
        - "elogistics.order.service.pub.targetsystem.test.liberty"
        - "dfe-legacy-advice.rest"
        - "dfe-legacy-advice.targetsystem.test.liberty"
        - "common.pub.archive.rest"
        - "common.pub.archive.targetsystem.development.liberty"
      local:
        - "dev"
        - "no-csrf"
      test:
        - "com.dachser.road.masterdata.restapi"
        - "road.ohs.pub.rest"
        - "road.ohs.pub.targetsystem.production.liberty"
        - "bi.common.security.pri.rest"
        - "bi.common.security.pri.targetsystem.prod"
        - "bi.common.shipment.pri.rest"
        - "bi.common.shipment.pri.targetsystem.test"
        - "bi.common.print.pri.rest"
        - "bi.common.print.pri.targetsystem.prod"
        - "bi.common.validation.pri.rest"
        - "bi.common.validation.pri.targetsystem.prod"
        - "elogistics.order.service.pub.rest"
        - "elogistics.order.service.pub.targetsystem.test.liberty"
        - "dfe-legacy-advice.rest"
        - "dfe-legacy-advice.targetsystem.test.liberty"
        - "common.pub.archive.rest"
        - "common.pub.archive.targetsystem.development.liberty"
      integrationtest:
        - "com.dachser.road.masterdata.restapi"
        - "road.ohs.pub.rest"
        - "road.ohs.pub.targetsystem.dach041q.liberty"
        - "bi.common.security.pri.rest"
        - "bi.common.security.pri.targetsystem.test"
        - "bi.common.print.pri.rest"
        - "bi.common.print.pri.targetsystem.test"
        - "bi.common.shipment.pri.rest"
        - "bi.common.shipment.pri.targetsystem.test"
        - "bi.common.validation.pri.rest"
        - "bi.common.validation.pri.targetsystem.test"
        - "elogistics.order.service.pub.rest"
        - "elogistics.order.service.pub.targetsystem.test.liberty"
        - "dfe-legacy-advice.rest"
        - "dfe-legacy-advice.targetsystem.test.liberty"
        - "common.pub.archive.rest"
        - "common.pub.archive.targetsystem.development.liberty"
      prod:
        - "com.dachser.road.masterdata.restapi"
        - "road.ohs.pub.rest"
        - "road.ohs.pub.targetsystem.production.liberty"
        - "bi.common.security.pri.rest"
        - "bi.common.security.pri.targetsystem.prod"
        - "bi.common.shipment.pri.rest"
        - "bi.common.shipment.pri.targetsystem.prod"
        - "bi.common.print.pri.rest"
        - "bi.common.print.pri.targetsystem.prod"
        - "bi.common.validation.pri.rest"
        - "bi.common.validation.pri.targetsystem.prod"
        - "elogistics.order.service.pub.rest"
        - "elogistics.order.service.pub.targetsystem.prod.liberty"
        - "dfe-legacy-advice.rest"
        - "dfe-legacy-advice.prod.liberty"
        - "common.pub.archive.rest"
        - "common.pub.archive.targetsystem.production.liberty"
  cloud:
    vault:
      host: ${ENV_VAULT_BASE_URL:hcvault-dev-01.dach041.dachser.com}
      port: ${ENV_VAULT_PORT:8200}
      scheme: "https"
      connection-timeout: 5000
      read-timeout: 15000
      kv:
        enabled: true
      enabled: true
      fail-fast: true
  config:
    import:
      - "optional:vault://DFE/kv2_dfe/${ENV_VAULT_PATH:unittest}/dfe-book-backend"
      - "optional:vault://DFE/kv2_dfe/${ENV_VAULT_PATH:unittest}/dfe-legacy-advice-service"
      - "optional:vault://DFE/kv2_dfe/${ENV_VAULT_PATH:unittest}/dfe-general-data"
      - "optional:vault://DFE/kv2_dfe/${ENV_VAULT_PATH:unittest}/dfe-pdf-export"
      - "optional:vault://DFE/kv2_dfe/${ENV_VAULT_PATH:unittest}/dfe-emissionforecast-backend"
      - "optional:vault://DFE/kv2_road/${ENV_VAULT_PATH:unittest}/dfe-road-masterdata"
      - "optional:vault://DFE/kv2_road/${ENV_VAULT_PATH:unittest}/dfe-road-consignment-label"
      - "optional:vault://DFE/kv2_road/${ENV_VAULT_PATH:unittest}/road-ohs-pub-service"
      - "optional:vault://DFE/kv2_airsea/${ENV_VAULT_PATH:unittest}/dfe-airsea-masterdata"
      - "optional:vault://DFE/kv2_shared_service/${ENV_VAULT_PATH:unittest}/dfe-shared-services-masterdata-geo"
      - "optional:vault://DFE/kv2_shared_service/${ENV_VAULT_PATH:unittest}/dfe-shared-services-masterdata-businesspartner"
      - "optional:vault://DFE/kv2_shared_service/${ENV_VAULT_PATH:unittest}/dfe-shared-services-masterdata-gateway"
      - "optional:vault://DFE/kv2_shared_service/${ENV_VAULT_PATH:unittest}/dfe-common-pub-archive-service"
      - "optional:vault://DFE/kv2_shared_service/${ENV_VAULT_PATH:unittest}/kta"
      - "optional:vault://DFE/kv2_business_integration/${ENV_VAULT_PATH:unittest}/ums-provider"
      - "optional:vault://DFE/kv2_business_integration/${ENV_VAULT_PATH:unittest}/bi-common-security-service-pri"
      - "optional:vault://DFE/kv2_business_integration/${ENV_VAULT_PATH:unittest}/bi-common-print-service-pri"
      - "optional:vault://DFE/kv2_business_integration/${ENV_VAULT_PATH:unittest}/bi-common-shipment-service-pri"
      - "optional:vault://DFE/kv2_business_integration/${ENV_VAULT_PATH:unittest}/bi-common-validation-service-pri"
      - "optional:vault://DFE/kv2_business_integration/${ENV_VAULT_PATH:unittest}/elogistics-order-pub-service"
  datasource:
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    username: ${dfe-book-backend.datasource.username}
    password: ${dfe-book-backend.datasource.password}
    url: ${ENV_BOOK_DATASOURCE_URL}
    hikari:
      maximum-pool-size: 50
      register-mbeans: true
      minimum-idle: 4
  liquibase:
    enabled: true
    change-log: classpath:db/changelog/db.changelog-master.yaml
    # Default context, replace with mock-data / h2 if you need Mocks or in-memory database
    contexts: mssql
    parameters:
      default-schema: dbo
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 25MB
  jpa:
    hibernate:
      ddl-auto: none
  h2:
    console:
      enabled: false

  security:
    oauth2:
      resource-server:
        dachserOAuth:
          jwk-set-uri: ${oAuth.server-base-url}/certs
logging:
  performancelogging:
    enabled: true
    threshold: 250
  level:
    org.springframework.vault: WARN
    com.dachser.dfe.platform.security.DfeAuthenticationToken: ERROR

# https://springdoc.org/properties.html
springdoc:
  app:
    version: "@project.version@"
    title: DFE book API
    description: REST Interface DFE book
  swagger-ui:
    oauth:
      client-id: dfe-frontend
      use-pkce-with-authorization-code-grant: true
  oAuthFlow:
    authorizationUrl: ${oAuth.server-base-url}/auth
    tokenUrl: ${oAuth.server-base-url}/token


### SERVICES CONFIG ###
dfe:
  book:
    rest:
      timeoutMS: 120000
    cache2k:
      enabled: true
      expiry:
        # default expiry time for cache entries in hours
        hours: 4
        # custom expiry times for specific cache entries in minutes, e.g. "'cacheName': minutes"
        custom: "{ 'print-labels': 1, 'overview-document-types': 720 }"
    interrupter:
      enabled: ${ENV_BOOK_INTERRUPTER:true}
  book-api:
    version: "@dfe.book-api.version@"
  road:
    masterdata:
      baseURL: ${ENV_BOOK_API_DFE_ROAD_MASTERDATA_BASEURL}
      use-cache: true
    consignment-label:
      baseURL: ${ENV_BOOK_API_DFE_ROAD_CONSIGNMENT_LABEL_BASEURL}

  services:
    document:
      basePath: /application/upload/documents/
    order:
      basePath: /application/upload/documents/orders/
  sharedservices:
    masterdata:
      gateway:
        baseURL: ${ENV_BOOK_API_MASTERDATA_GATEWAY_BASEURL}
        use-cache: true
      geo:
        baseURL: ${ENV_BOOK_API_DFE_SHAREDSERVICES_MASTERDATA_GEO_BASEURL}
        use-cache: true
      businesspartner:
        baseURL: ${ENV_BOOK_API_DFE_SHAREDSERVICES_MASTERDATA_BUSINESSPARTNER_BASEURL}        # to run integration-tests, credentials below have to be set
  airsea:
    masterdata:
      baseURL: ${ENV_BOOK_API_DFE_AIRSEA_MASTERDATA_BASEURL}
      use-cache: true
  platform:
    configuration:
      baseURL: ${ENV_BOOK_API_DFE_PLATFORM_BACKEND_BASEURL}                                    # to run integration-tests, credentials below have to be set
      use-cache: true
    security:
      jwk-set-uri: ${spring.security.oauth2.resource-server.dachserOAuth.jwk-set-uri}
      # dfe-security-starter is in class path, so following properties are mandatory - check the dependency's readme for more details on further configuration options
      any-required-roles: [ "company-book", "groups-book" ]           # users must have at least one of these roles assigned, to get access granted and ...
      required-authority: "ROLE_cmp-active-account"                       # ... need to have this role. The prefix (e.g. "ROLE_") is needed here and depends on the kind of authority.
      public-endpoints: [ "/v1/air-products", "/delivery-products", "/countries", "/freight-terms", "/inco-terms" ] # can be removed when track and trace is no longer using that endpoints
  kta:
    url: ${ENV_BOOK_API_KTA_URL}
    dataSource: DACHSER_PLATFORM
    creationUser: PLATFORM
  configserver:
    baseURL: ${ENV_BOOK_API_CONFIGSERVER_BASEURL}
    airProductsConfig: '/v1/config/air-product-routing.json'
  general-data:
    configuration:
      baseURL: ${ENV_BOOK_API_GENERAL_DATA_SERVER_BASEURL}
  emissionforecast:
    configuration:
      baseURL: ${ENV_BOOK_API_DFE_EMISSIONFORECAST_BASEURL}
      retryCount: 9
  pdf-export:
    configuration:
      baseURL: ${ENV_BOOK_API_PDF_EXPORT_SERVER_BASEURL}

# dip producer for order send
ums:
  provider:
    url: ${ENV_BOOK_UMS_PROVIDER_URL}
  enabled: true
  connection:
    factory: ${ENV_BOOK_UMS_FACTORY}
  dip:
    ediTestFlagRoad: ${ENV_BOOK_TRIAL_MODE_ROAD}
    ediTestFlagAirSea: ${ENV_BOOK_TRIAL_MODE_AIR_SEA}
    destination: /com/dachser/bi/dfe/book/order
    retryDelay: 60000
    retryLimit: 10
  initial:
    context:
      factory: com.pcbsys.nirvana.nSpace.NirvanaContextFactory


bi:
  common:
    print:
      pri:
        generateTrialLabel: ${ENV_BOOK_TRIAL_MODE_ROAD}

port:
  scheduled:
    load-airports: "* 22 10 * * SUN"      # every sunday at 10:22 am
    load-seaports: "* 23 11 * * SUN"      # every sunday at 11:23 am

document:
  scheduled:
    forwarding: "0 * * * * *" # every minute

orderExpired:
  scheduled:
    setStatus: "0 * * * * *" # every minute
    deleteOrder: "0 0 * * * *" # every hour

emissionForecast:
  scheduled:
    calculateEmission: "0 */3 * * * *" # every 3 minutes

cleanUpDocuments:
  scheduled:
    deleteSentOrders: "0 0 * * * *" # every hour
    deleteUnlinkedOrders: "0 0 * * * *" # every hour
    deleteOrders: "0 0 * * * *" # every hour

#forwarding of orders happens via XML that are sent via UM
#these XML are stored for 2 Weeks after forwarding
orderXml:
  scheduled:
    publish: "0 * * * * *" # every minute
    reorg: "0 0 0 * * *" # every day
    publishForwarding: "0 15 * * * *" # every hour

# DFE Book specific configuration for all whitelist filters that are used in the application
whitelistConfig:
  deliveryProducts:
    road:
      foodLogistics: 'Q, N, K, E, X, S, I, L'
      europeanLogistics: 'N, K, A, B, H, V, R, W, Y, Z, X, S, E, U'
  freightTermsFilter:
    road: '011, 031, 081, 082, 083, 084'
  countries:
    road: "DE, ES, IT, NL, CH, LI, LU, BE, AT, FR, GB, CZ, HU, DK, FO, GL, IS, PT,SK, PL, NO, SE, EE, LV, SM, VA, AD, LT, FI, BA, HR, GR, SI, RS, ME, CY, RO, IE, BG, MA, TN, XK, DZ, AL,MD, MK, TR"
  packagingOptionsFilter:
    road: 'CC, BX, B, KT, C2, C1, C4, DD, E2, EU, F, GB, HE, H1, IB, IP, KN, KI, KH, PA, EW, PT, PH, RL, ST, VG, VE'
    air: 'BX, CT, PE, BA, 4A, WA, PK, PX, RO'
  dangerousGoods:
    packagingOptionsFilter:
      road: 'BV, DG, F, FB, FL, GE, IB, KI, KK, KN, KT, KV, LP, S, PL, VG'
  file-types:
    upload: "PDF, XLS, XLSX"
  terms:
    freight: '031, 011, 081, 082, 083, 084'
    inco: 'CFR, CIF, CIP, CPT, DAP, 082, DD4, DD5, DPU, EXW, FAS, FC1, FOA, FOB'
    overview: 'EXW, FC1, FOA, FAS, FOB, CFR, CIF, CPT, CIP, DAP, DPU, 082, DD4, DD5'
  container-types:
    keys: 'GP-20, GP-40, HC-40'

---
spring:
  config:
    activate:
      on-profile: "hosted"
  cloud:
    vault:
      authentication: KUBERNETES
      kubernetes:
        role: application
        service-account-token-file: /var/run/secrets/kubernetes.io/serviceaccount/token
        kubernetes-path: kubernetes-${ENV_CLUSTER_NAME}    