package com.dachser.dfe.book.advice;

import com.dachser.dfe.book.exception.AdviceSubmitFailedException;
import com.dachser.dfe.book.order.OrderRepository;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.time.Instant;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdviceService {

	private final AdviceAdapter adviceAdapter;

	private final OrderRepository orderRepository;

	public static final String ADVICE_STATUS_LABEL_PENDING = StringUtils.SPACE;

	public static final String ADVICE_STATUS_ORDER_COMPLETE = "J";

	public static final String ADVICE_STATUS_ORDER_DELETED = "N";

	@Retryable(retryFor = AdviceSubmitFailedException.class, maxAttempts = 2, backoff = @Backoff(delay = 1000))
	public boolean sendAdviceDataForOrderWithStatusCode(ForwardingOrder order, String legacyStatus) {
		boolean success = adviceAdapter.submitAdvice(order, legacyStatus);
		if (success) {
			order.setAdviceSent(Instant.now());
			orderRepository.save(order);
		}
		return success;
	}

	@Recover
	public boolean recover(AdviceSubmitFailedException e, ForwardingOrder order, String legacyStatus) {
		log.error("Submission of advised order {} with shipment number {} failed after retry: {}", order.getOrderId(), order.getShipmentNumber(), e);
		return false;
	}
}
