-- liquibase formatted sql

-- changeset mvogt:2025-04-17-adjust-column-length-order-address.sql

-- Address
ALTER TABLE order_address
    ALTER COLUMN name nvarchar(40);
ALTER TABLE order_address
    ALTER COLUMN name2 nvarchar(40);
ALTER TABLE order_address
    ALTER COLUMN name3 nvarchar(40);
ALTER TABLE order_address
    ALTER COLUMN street nvarchar(40);
ALTER TABLE order_address
    ALTER COLUMN street2 nvarchar(40);
ALTER TABLE order_address
    ALTER COLUMN postcode nvarchar(12);
ALTER TABLE order_address
    ALTER COLUMN city nvarchar(40);

-- Contact
ALTER TABLE order_contact
    ALTER COLUMN name nvarchar(40);
ALTER TABLE order_contact
    ALTER COLUMN telephone nvarchar(30);
ALTER TABLE order_contact
    ALTER COLUMN mobile nvarchar(30);

-- Further addresses
ALTER TABLE order_further_address
    ALTER COLUMN name nvarchar(40);
ALTER TABLE order_further_address
    ALTER COLUMN name2 nvarchar(40);
ALTER TABLE order_further_address
    ALTER COLUMN name3 nvarchar(40);
ALTER TABLE order_further_address
    ALTER COLUMN street nvarchar(40);
ALTER TABLE order_further_address
    ALTER COLUMN street2 nvarchar(40);
ALTER TABLE order_further_address
    ALTER COLUMN postcode nvarchar(12);
ALTER TABLE order_further_address
    ALTER COLUMN city nvarchar(40);