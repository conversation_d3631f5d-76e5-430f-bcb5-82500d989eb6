package com.dachser.dfe.book.generaldata;

import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.config.term.TermSorter;
import com.dachser.dfe.book.country.Country;
import com.dachser.dfe.book.generaldata.term.TermMapper;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.ContainerTypeDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.FreightTermDto;
import com.dachser.dfe.book.model.IncoTermDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.TermDto;
import com.dachser.dfe.book.order.sea.BaseFullContainerLoadRepository;
import com.dachser.dfe.generaldata.model.GDContainerTypeDto;
import com.dachser.dfe.generaldata.model.GDTermDto;
import com.dachser.dfe.generaldata.model.GDTermTypeDto;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.function.Predicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class GeneralDataService {

	private final GeneralDataAdapter generalDataAdapter;

	private final ContainerTypeMapper containerTypeMapper;

	private final BaseFullContainerLoadRepository containerRepository;

	private final WhiteListFilterConfiguration whiteListFilterConfiguration;

	private final TermMapper termMapper;

	private final TermSorter termSorter;

	public String getMasterdataLanguageForLocale(@NonNull Locale locale) {
		final Map<String, String> masterdataLanguages = generalDataAdapter.getLanguages();
		final String language = locale.getLanguage();

		if (masterdataLanguages.containsKey(language)) {
			return masterdataLanguages.get(language);
		} else {
			return masterdataLanguages.get("en");
		}
	}

	public List<ContainerTypeDto> getAllWhitelistedContainerTypes() {
		List<GDContainerTypeDto> containerTypes = generalDataAdapter.getContainerTypes();
		List<ContainerTypeDto> mappedContainerList = containerTypeMapper.toContainerTypeDtoList(containerTypes);
		return mappedContainerList.stream().filter(new ContainerTypeFilter(whiteListFilterConfiguration.getContainerTypes().getKeys())).toList();
	}

	@Nullable
	public ContainerTypeDto getContainerTypeForKey(String key) {
		List<GDContainerTypeDto> containerTypes = generalDataAdapter.getContainerTypes();
		GDContainerTypeDto containerType = containerTypes.stream().filter(type -> StringUtils.equals(type.getKey(), key)).findFirst().orElse(null);

		return containerTypeMapper.toContainerTypeDto(containerType);
	}

	public List<ContainerTypeDto> getMostFrequentlyUsedContainerTypesForCustomer(final String customerNumber) {

		List<String> mostFrequentlyUsed = containerRepository.getMostFrequentlyUsedContainerTypes(customerNumber);
		if (!mostFrequentlyUsed.isEmpty()) {

			List<ContainerTypeDto> allContainerTypes = getAllWhitelistedContainerTypes();
			return allContainerTypes.stream().filter(new ContainerTypeFilter(mostFrequentlyUsed)).toList();
		}

		return List.of();
	}

	public List<Country> getCountries(String language) {
		return generalDataAdapter.getCountries(language);
	}

	public List<OptionDto> getPackagingOptionsAir(String language) {
		return generalDataAdapter.getPackagingOptionsAir(language);
	}

	public List<OptionDto> getPackagingOptionsRoad(String language) {
		return generalDataAdapter.getPackagingOptionsRoad(language);
	}

	private boolean isTermInFilter(List<String> filter, String dachserCode) {
		return !filter.isEmpty() && filter.contains(dachserCode);
	}

	@Nonnull
	public List<IncoTermDto> getIncoTerms() {
		return termSorter.sortIncoTerms(termMapper.mapIncoTerms(generalDataAdapter.getAllActiveTerms(GDTermTypeDto.INCO).stream().filter(this::isIncoTermVisible).toList()));
	}

	@Nonnull
	public List<FreightTermDto> getFreightTerms() {
		return termSorter.sortFreightTerms(
				termMapper.mapFreightTerms(generalDataAdapter.getAllActiveTerms(GDTermTypeDto.FREIGHT).stream().filter(this::isFreightTermVisible).toList()));
	}

	public List<TermDto> getAllTerms() {
		List<GDTermDto> list = generalDataAdapter.getAllActiveTerms(null).stream().filter(term -> isFreightTermVisible(term) || isIncoTermVisible(term)).toList();
		List<TermDto> termDtos = termMapper.mapTerms(list);
		return termSorter.sortOverviewTerms(termDtos);
	}

	/**
	 * Just make adapter method visible and available.
	 * As there is a dedicated road product service, this method is not applying whitelist filtering or sorting.
	 */
	@Nonnull
	public List<DeliveryProductDto> getDeliveryProductsForDivision(@Nonnull Division division, @Nonnull String language) {
		return generalDataAdapter.getDeliveryProductsForDivision(division, language);
	}

	/**
	 * Just make adapter method visible and available.
	 * As there is a dedicated road product service, this method is not applying whitelist filtering or sorting.
	 */
	@Nonnull
	public List<AirProductDto> getAirProducts(Boolean includeDeactivated) {
		return generalDataAdapter.getAirProducts(includeDeactivated);
	}

	record ContainerTypeFilter(@NonNull List<String> whiteListFilter) implements Predicate<ContainerTypeDto> {

		@Override
		public boolean test(ContainerTypeDto containerType) {
			return whiteListFilter.contains(containerType.getKey());
		}
	}

	private boolean isFreightTermVisible(GDTermDto term) {
		return isTermInFilter(whiteListFilterConfiguration.getTerms().getFreight(), term.getFreightTerm());
	}

	private boolean isIncoTermVisible(GDTermDto term) {
		return isTermInFilter(whiteListFilterConfiguration.getTerms().getInco(), term.getDachserCode());
	}
}
