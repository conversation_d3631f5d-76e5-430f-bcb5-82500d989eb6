package com.dachser.dfe.book.transferlist.service;

import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.dangerousgoods.DangerousGoodsTransferListService;
import com.dachser.dfe.book.exception.DachserCodeNotMappedException;
import com.dachser.dfe.book.jpa.OrderSsccRepository;
import com.dachser.dfe.book.model.ADRDangerousGoodDto;
import com.dachser.dfe.book.model.DocumentDownloadDto;
import com.dachser.dfe.book.model.EQDangerousGoodDto;
import com.dachser.dfe.book.model.LQDangerousGoodDto;
import com.dachser.dfe.book.model.OptionIntegerDto;
import com.dachser.dfe.book.model.OrderLineDetailDto;
import com.dachser.dfe.book.model.PackagingInfoDto;
import com.dachser.dfe.book.model.PackingPositionDto;
import com.dachser.dfe.book.model.RoadOrderLineDto;
import com.dachser.dfe.book.model.TransferListAddressDto;
import com.dachser.dfe.book.model.TransferListPdfItemDto;
import com.dachser.dfe.book.model.TransferListPdfQueryObjectDto;
import com.dachser.dfe.book.model.TransferListPdfRequestDto;
import com.dachser.dfe.book.model.TransferListQueryObjectDto;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderRepositoryFacade;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.overview.CachedOrderOverviewService;
import com.dachser.dfe.book.packaging.PackagingOptionsService;
import com.dachser.dfe.book.transferlist.entity.VTransferList;
import com.dachser.dfe.book.transferlist.exception.PdfGeneratorException;
import com.dachser.dfe.book.transferlist.exception.PdfTransferListNoDataException;
import com.dachser.dfe.book.transferlist.exception.TrackablePackingAidServiceNotAvailableException;
import com.dachser.dfe.book.transferlist.mapper.TransferListPdfMapper;
import com.dachser.dfe.book.transferlist.pdf.PdfService;
import com.dachser.dfe.book.transferlist.repository.VTransferListRepository;
import com.dachser.dfe.book.transferlist.supervisingbranch.BranchService;
import com.dachser.dfe.book.transferlist.trackablepackingaid.TrackablePackingAidService;
import com.dachser.dfe.book.user.UserContextService;
import com.dachser.dfe.road.masterdata.model.RMDTrackablePackingAidResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Hibernate;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class TransferListPdfServiceImpl extends TransferListBaseServiceImpl implements TransferListPdfService {

	private final TransferListPdfMapper transferListPdfMapper;

	private final PdfService pdfService;

	private final BranchService branchService;

	private final TrackablePackingAidService trackablePackingAidService;

	private final CountryService countryService;

	private final OrderRepositoryFacade orderRepositoryFacade;

	private final DangerousGoodsTransferListService dangerousGoodsTransferListService;

	public TransferListPdfServiceImpl(VTransferListRepository transferListRepository,
			UserContextService userContextService, CachedOrderOverviewService cachedOrderOverviewService,
			RoadOrderLineService roadOrderLineService, PackingPositionService packingPositionService,TransferListPdfMapper transferListPdfMapper, OrderSsccRepository orderSsccRepository, PdfService pdfService,
			BranchService branchService, PackagingOptionsService packagingOptionsService, TrackablePackingAidService trackablePackingAidService, CountryService countryService,
			OrderRepositoryFacade orderRepositoryFacade, DangerousGoodsTransferListService dangerousGoodsTransferListService) {
		super(transferListRepository, userContextService, cachedOrderOverviewService, roadOrderLineService, packingPositionService, packagingOptionsService, orderSsccRepository);
		this.transferListPdfMapper = transferListPdfMapper;
		this.pdfService = pdfService;
		this.branchService = branchService;
		this.trackablePackingAidService = trackablePackingAidService;
		this.countryService = countryService;
		this.orderRepositoryFacade = orderRepositoryFacade;
		this.dangerousGoodsTransferListService = dangerousGoodsTransferListService;
	}

	@Override
	public DocumentDownloadDto getTransferListPdf(TransferListPdfQueryObjectDto pdfQueryObject) {
		final String targetLanguage = LocaleContextHolder.getLocale().getLanguage();
		final TransferListQueryObjectDto queryObject = pdfQueryObject != null ? pdfQueryObject.getQueryObject() : null;
		final List<VTransferList> transferListItems = fetchTransferList(queryObject).stream().sorted(compareByConsigneeAddressName(o -> {
			if (o.getConsigneeAddress() != null) {
				return o.getConsigneeAddress().getName();
			} else {
				return null;
			}
		})).toList();

		if (transferListItems.isEmpty()) {
			String message = String.format("No transfer list items found for date '%s' and address hash '%s'", queryObject != null ? queryObject.getPickupDate() : null,
					queryObject != null ? queryObject.getAddressHash() : null);
			log.warn(message);
			throw new PdfTransferListNoDataException(message);
		}

		collectMissingDangerousGoodsInformation(transferListItems, targetLanguage);

		final LocalDate transferListDate = queryObject != null ? queryObject.getPickupDate() : null;
		TransferListPdfRequestDto pdfRequest = new TransferListPdfRequestDto();
		mapMain(transferListItems, pdfRequest);
		mapShipments(transferListItems, pdfRequest, getDateFormat(pdfQueryObject));
		mapSummary(transferListItems, pdfRequest);
		pdfRequest.setTotalElements(transferListItems.size());
		pdfRequest.setTransferListDate(transferListPdfMapper.formattedDateFromLocalDate(transferListDate, getDateFormat(pdfQueryObject), LocaleContextHolder.getLocale()));
		pdfRequest.setPrintDateTime(getPrintDateTime(pdfQueryObject));
		pdfRequest.setNumberLocale(getNumberLocale(pdfQueryObject));

		final String documentName = transferListPdfMapper.mapDocumentName(transferListItems.stream().findFirst().orElse(null), transferListDate);
		return createTransferListPdfResponse(pdfRequest, documentName, targetLanguage);
	}

	private void collectMissingDangerousGoodsInformation(final List<VTransferList> items, final String targetLanguage) {
		for (VTransferList item : items) {
			ForwardingOrder order = loadForwardingOrder(item);
			if (!order.isDangerousGoodsOrder()) {
				continue;
			}
			order = dangerousGoodsTransferListService.fetchLocalizedDescriptionForDangerousGoods(order, targetLanguage);
			log.debug("Recalculating dangerous goods points for order with ID: {}", item.getOrderId());
			boolean allPointsCalculated = dangerousGoodsTransferListService.calculateDangerousGoodsPoints(order);
			if (!allPointsCalculated) {
				throw new PdfGeneratorException(String.format("Order with ID %d has ADR dangerous goods without calculated points. Please check the order.", item.getOrderId()));
			}
			orderRepositoryFacade.save(order);
		}
	}

	private ForwardingOrder loadForwardingOrder(VTransferList item) {
		Order order = orderRepositoryFacade.loadOrderById(item.getOrderId());
		order = (Order) Hibernate.unproxy(order);
		if (!(order instanceof ForwardingOrder fw)) {
			throw new PdfGeneratorException(String.format("Order with ID %d not found for recalculation of dangerous goods points", item.getOrderId()));
		}
		return fw;
	}

	private String getDateFormat(TransferListPdfQueryObjectDto queryObject) {
		return queryObject != null ? queryObject.getDateFormat() : null;
	}

	private String getPrintDateTime(TransferListPdfQueryObjectDto queryObject) {
		return queryObject != null ? queryObject.getPrintDateTime() : null;
	}

	private String getNumberLocale(TransferListPdfQueryObjectDto queryObject) {
		return queryObject != null ? queryObject.getNumberLocale() : null;
	}

	private DocumentDownloadDto createTransferListPdfResponse(TransferListPdfRequestDto request, String documentName, String targetLanguage) {
		try {
			return pdfService.createPdf(request, documentName, targetLanguage);
		} catch (Exception e) {
			log.error("Error while generating TransferList PDF", e);
			throw new PdfGeneratorException("Error while generating TransferList PDF");
		}
	}

	private void mapShipments(List<VTransferList> transferList, TransferListPdfRequestDto pdfRequest, String dateFormat) {
		pdfRequest.setShipment(createTransferListPdfResultSet(transferList, dateFormat));
	}

	private void mapMain(List<VTransferList> transferList, TransferListPdfRequestDto pdfRequest) {
		Optional<VTransferList> transferListItem = transferList.stream().findFirst();
		transferListItem.ifPresent(item -> {
			pdfRequest.customerNumber(item.getCustomerNumber());
			pdfRequest.setFrom(transferListPdfMapper.mapFromAddress(item));
			mapSupervisingBranch(pdfRequest, item.getOrderId());
		});
	}

	private void mapSupervisingBranch(TransferListPdfRequestDto pdfRequest, Long orderId) {
		final Order order = orderRepositoryFacade.loadOrderById(orderId);
		handleSupervisingBranch(pdfRequest, order.getBranchId());
	}

	private void handleSupervisingBranch(TransferListPdfRequestDto pdfRequest, Integer branchId) {
		branchService.getBranchAddress(branchId)
				.ifPresentOrElse(pdfRequest::setSupervisingBranch, () -> logAndSetEmptyBranch(pdfRequest, "No branch address found for supervisingBranch: " + branchId));
	}

	private void logAndSetEmptyBranch(TransferListPdfRequestDto pdfRequest, String errorMessage) {
		log.warn(errorMessage);
		pdfRequest.setSupervisingBranch(new TransferListAddressDto());
	}

	private void mapSummary(List<VTransferList> transferList, TransferListPdfRequestDto pdfRequest) {
		pdfRequest.setNumberOfShipments(transferList.size());
		pdfRequest.setNumberOfNveSccs(getTotalLabelCount(transferList));
		pdfRequest.setTotalVolume(getTotalVolume(transferList));
		pdfRequest.setTotalWeight(getTotalWeight(transferList));
		mapPackingAids(transferList, pdfRequest);
		mapDangerousGoods(pdfRequest);
	}

	private void mapDangerousGoods(TransferListPdfRequestDto pdfRequest) {
		List<Object> allDangerousGoods = getAllDangerousGoodsDtos(pdfRequest);

		setNumberOfShipmentsWithDangerousGoods(pdfRequest);
		setTotalCalculatedPoints(pdfRequest, allDangerousGoods);
		setTotalBulkPerTransportCategory(pdfRequest, allDangerousGoods);
		setExemptedQuantities(pdfRequest, allDangerousGoods);
		setLimitedQuantities(pdfRequest, allDangerousGoods);
		setValuePerTransportCategory(pdfRequest, allDangerousGoods);
	}

	private void setNumberOfShipmentsWithDangerousGoods(TransferListPdfRequestDto pdfRequest) {
		long count = pdfRequest.getShipment().stream().filter(this::shipmentHasDangerousGoods).count();
		pdfRequest.setNumberOfShipmentsWithDangerousGoods(count == 0 ? null : (int) count);
	}

	private boolean shipmentHasDangerousGoods(TransferListPdfItemDto shipment) {
		//@formatter:off
	    return shipment.getOrderLineItems().stream().anyMatch(orderLine ->
	        (orderLine.getDangerousGoodsADR() != null && !orderLine.getDangerousGoodsADR().isEmpty()) ||
	        (orderLine.getDangerousGoodsLQ() != null && !orderLine.getDangerousGoodsLQ().isEmpty()) ||
	        (orderLine.getDangerousGoodsEQ() != null && !orderLine.getDangerousGoodsEQ().isEmpty())
	    );
		//@formatter:on
	}

	private void setTotalCalculatedPoints(TransferListPdfRequestDto pdfRequest, List<Object> allDangerousGoods) {
		int totalPoints = sumIntegerValues(allDangerousGoods, ADRDangerousGoodDto.class, ADRDangerousGoodDto::getCalculatedPoints);
		pdfRequest.setTotalCalculatedPoints(totalPoints == 0 ? null : totalPoints);
	}

	private void setTotalBulkPerTransportCategory(TransferListPdfRequestDto pdfRequest, List<Object> allDangerousGoods) {
		Map<Integer, Integer> bulkMap = groupSumByTransportCategory(allDangerousGoods, ADRDangerousGoodDto.class, this::getGrossMassOrZero, this::getTransportCategory);
		List<OptionIntegerDto> bulkOptions = createOptionIntegerDtoListFromMap(bulkMap).stream().filter(option -> option.getValue() != null && option.getValue() != 0).toList();
		pdfRequest.setTotalBulkPerTransportCategory(bulkOptions);
	}

	private void setValuePerTransportCategory(TransferListPdfRequestDto pdfRequest, List<Object> allDangerousGoods) {
		Map<Integer, Integer> valueMap = groupSumByTransportCategory(allDangerousGoods, ADRDangerousGoodDto.class, this::getCalculatedPointsOrZero, this::getTransportCategory);
		List<OptionIntegerDto> valueOptions = createOptionIntegerDtoListFromMap(valueMap).stream().filter(option -> option.getValue() != null && option.getValue() != 0)
				.collect(Collectors.toCollection(ArrayList::new));
		// Only Add if EQ/LQ is present
		if (pdfRequest.getExemptedQuantities() != null && pdfRequest.getExemptedQuantities() > 0
				|| pdfRequest.getLimitedQuantities() != null && pdfRequest.getLimitedQuantities() > 0) {
			valueOptions.add(createDefaultCategory4OptionInteger());
		}
		pdfRequest.setValuePerTransportCategory(valueOptions);
	}

	private void setExemptedQuantities(TransferListPdfRequestDto pdfRequest, List<Object> allDangerousGoods) {
		int totalEQQuantity = sumIntegerValues(allDangerousGoods, EQDangerousGoodDto.class, EQDangerousGoodDto::getNoOfPackages);
		pdfRequest.setExemptedQuantities(totalEQQuantity == 0 ? null : totalEQQuantity);
	}

	private void setLimitedQuantities(TransferListPdfRequestDto pdfRequest, List<Object> allDangerousGoods) {
		int totalLQQuantity = sumIntegerValues(allDangerousGoods, LQDangerousGoodDto.class, lq -> lq.getGrossMass() != null ? lq.getGrossMass().intValue() : 0);
		pdfRequest.setLimitedQuantities(totalLQQuantity == 0 ? null : totalLQQuantity);
	}

	private Integer getGrossMassOrZero(ADRDangerousGoodDto adr) {
		return adr.getGrossMass() != null ? adr.getGrossMass().intValue() : 0;
	}

	private Integer getCalculatedPointsOrZero(ADRDangerousGoodDto adr) {
		return adr.getCalculatedPoints() != null ? adr.getCalculatedPoints() : 0;
	}

	private Integer getTransportCategory(ADRDangerousGoodDto adr) {
		return Integer.valueOf(adr.getDangerousGoodDataItem().getTransportCategory());
	}

	private OptionIntegerDto createDefaultCategory4OptionInteger() {
		OptionIntegerDto category4 = new OptionIntegerDto();
		category4.setCode(4);
		category4.setValue(0);
		return category4;
	}

	private <T> int sumIntegerValues(List<Object> items, Class<T> clazz, Function<T, Integer> valueExtractor) {
		return items.stream().filter(clazz::isInstance).map(clazz::cast).map(valueExtractor).filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
	}

	private <T> Map<Integer, Integer> groupSumByTransportCategory(List<Object> goods, Class<T> clazz, Function<T, Integer> valueExtractor,
			Function<T, Integer> transportCategoryExtractor) {
		return goods.stream().filter(clazz::isInstance).map(clazz::cast)
				.collect(Collectors.groupingBy(transportCategoryExtractor, Collectors.summingInt(item -> Optional.ofNullable(valueExtractor.apply(item)).orElse(0))));
	}

	private List<OptionIntegerDto> createOptionIntegerDtoListFromMap(Map<Integer, Integer> map) {
		return map.entrySet().stream().map(entry -> {
			OptionIntegerDto dto = new OptionIntegerDto();
			dto.setCode(entry.getKey());
			dto.setValue(entry.getValue());
			return dto;
		}).toList();
	}

	private List<Object> getAllDangerousGoodsDtos(TransferListPdfRequestDto pdfRequest) {
		//@formatter:off
		return pdfRequest.getShipment().stream()
				.flatMap(shipment -> shipment.getOrderLineItems().stream())
				.flatMap(orderLine -> Stream.of(
						orderLine.getDangerousGoodsADR(),
						orderLine.getDangerousGoodsLQ(),
						orderLine.getDangerousGoodsEQ()
				))
				.flatMap(List::stream)
				.collect(Collectors.toList());
		//@formatter:on
	}

	private int getTotalWeight(List<VTransferList> transferList) {
		int totalWeightPackingPositionOrderLines = transferList.stream().map(item -> getPackingPositions(item.getOrderId()))
				.mapToInt(packingPositions -> packingPositions.stream().flatMap(pos -> pos.getLines().stream()).mapToInt(this::getWeightNonNull).sum()).sum();

		int totalWeightOrderLines = transferList.stream().map(item -> getRoadOrderLines(item.getOrderId()))
				.mapToInt(orderLines -> orderLines.stream().mapToInt(this::getWeightNonNull).sum()).sum();

		return totalWeightPackingPositionOrderLines + totalWeightOrderLines;
	}

	private Double getTotalVolume(List<VTransferList> transferList) {
		Double totalVolumePackingPositionsOrderLines = transferList.stream().map(item -> getPackingPositions(item.getOrderId()))
				.mapToDouble(packingPositions -> packingPositions.stream().flatMap(pos -> pos.getLines().stream()).mapToDouble(this::getVolumeNonNull).sum()).sum();

		Double totalVolumeOrderLines = transferList.stream().map(item -> getRoadOrderLines(item.getOrderId()))
				.mapToDouble(item -> item.stream().mapToDouble(this::getVolumeNonNull).sum()).sum();

		return totalVolumePackingPositionsOrderLines + totalVolumeOrderLines;
	}

	private Double getVolumeNonNull(RoadOrderLineDto roadOrderLineDto) {
		return Objects.requireNonNullElse(roadOrderLineDto.getVolume(), 0.0);
	}

	private Double getVolumeNonNull(OrderLineDetailDto orderLineDetailDto) {
		return Objects.requireNonNullElse(orderLineDetailDto.getVolume(), 0.0);
	}

	private Integer getWeightNonNull(RoadOrderLineDto roadOrderLineDto) {
		return Objects.requireNonNullElse(roadOrderLineDto.getWeight(), 0);
	}

	private Integer getWeightNonNull(OrderLineDetailDto orderLineDetailDto) {
		return Objects.requireNonNullElse(orderLineDetailDto.getWeight(), 0);
	}

	private List<TransferListPdfItemDto> createTransferListPdfResultSet(List<VTransferList> items, String dateFormat) {
		return items.stream().map(item -> this.mapTransferListPdf(item, dateFormat)).toList();
	}

	private TransferListPdfItemDto mapTransferListPdf(VTransferList transferListItem, String dateFormat) {
		mapProductName(transferListItem);
		TransferListPdfItemDto transferListItemDto = transferListPdfMapper.mapTransferListPdfItem(transferListItem, dateFormat, LocaleContextHolder.getLocale());
		mapOrderLinePdf(transferListItemDto);
		mapPackingPositionPdf(transferListItemDto);
		return transferListItemDto;
	}

	private void mapOrderLinePdf(TransferListPdfItemDto transferListItem) {
		List<OrderLineDetailDto> orderLineItems = getRoadOrderLines(transferListItem.getOrderId());
		updatePackagingDescriptions(orderLineItems);
		transferListItem.setOrderLineItems(orderLineItems);
		long totalQuantity = orderLineItems.stream().mapToInt(OrderLineDetailDto::getQuantity).sum();
		transferListItem.setQuantity(totalQuantity);
	}

	private void mapPackingPositionPdf(TransferListPdfItemDto transferListItem) {
		List<PackingPositionDto> packingPositions = getPackingPositions(transferListItem.getOrderId());
		mapPackingPosition(packingPositions);
		transferListItem.setPackingPositions(packingPositions);
	}

	private List<PackagingInfoDto> groupByPackagingCode(List<PackagingInfoDto> packagingInfos) {
		Map<String, Integer> groupedByPackagingCode = packagingInfos.stream()
				.collect(Collectors.groupingBy(PackagingInfoDto::getAid, Collectors.summingInt(PackagingInfoDto::getCount)));

		return groupedByPackagingCode.entrySet().stream().map(entry -> new PackagingInfoDto().aid(entry.getKey()).count(entry.getValue())).toList();
	}

	private List<PackagingInfoDto> replacePackagingCodeWithDescription(List<PackagingInfoDto> packagingInfos) {
		return packagingInfos.stream().map(packagingInfo -> packagingInfo.aid(lookupPackagingDescription(packagingInfo.getAid()))).toList();
	}

	private void updatePackagingDescriptions(List<OrderLineDetailDto> orderLineDetails) {
		orderLineDetails.forEach(orderLineDetail -> orderLineDetail.getPackaging().setDescription(lookupPackagingDescription(orderLineDetail.getPackaging().getCode())));
	}

	private void mapPackingAids(List<VTransferList> transferList, TransferListPdfRequestDto pdfRequest) {
		List<PackagingInfoDto> trackablePackingAids = new ArrayList<>();
		List<PackagingInfoDto> nonTrackablePackingAids = new ArrayList<>();

		Optional<String> shipperCountryCodeOpt = getDachserCountryCode(pdfRequest.getFrom().getCountryCode());
		shipperCountryCodeOpt.ifPresentOrElse(shipperCountryCode -> {
			// per shipment identify trackable and non-trackable packing aids then add for total summary
			pdfRequest.getShipment().forEach(shipment -> {
				VTransferList transferListItem = transferList.stream().filter(item -> item.getOrderId().equals(shipment.getOrderId())).findFirst()
						.orElseThrow(() -> new IllegalStateException("TransferList item not found for shipment: " + shipment.getOrderId()));
				Optional<String> consigneeCountryCodeOpt = getDachserCountryCode(shipment.getConsigneeAddress().getCountryCode());
				consigneeCountryCodeOpt.ifPresentOrElse(consigneeCountryCode -> {
					List<OrderLineDetailDto> orderLineItems = shipment.getOrderLineItems();
					List<String> packingAids = new ArrayList<>(orderLineItems.stream().map(item -> item.getPackaging().getCode()).toList());

					List<PackingPositionDto> packingPositions = shipment.getPackingPositions();
					List<String> packingAidsPackingPosition = packingPositions.stream().map(packingPosition -> packingPosition.getPackagingType().getCode()).toList();
					packingAids.addAll(packingAidsPackingPosition);

					List<String> packingAidsPackingPositionOrderLines = packingPositions.stream().flatMap(pos -> pos.getLines().stream()).map(line -> line.getPackaging().getCode())
							.toList();
					packingAids.addAll(packingAidsPackingPositionOrderLines);

					try {
						RMDTrackablePackingAidResponseDTO responseDTO = trackablePackingAidService.categorizeTransferList(shipperCountryCode, consigneeCountryCode,
								transferListItem.getDivision(), transferListItem.getPickUpDateFrom(), packingAids.stream().distinct().toList());

						trackablePackingAids.addAll(mapPackagingInfo(responseDTO.getTrackablePackingAids(), orderLineItems, packingPositions));
						nonTrackablePackingAids.addAll(mapPackagingInfo(responseDTO.getNonTrackablePackingAids(), orderLineItems, packingPositions));

					} catch (TrackablePackingAidServiceNotAvailableException e) {
						log.error("TrackablePackingAidService is not available, summary values for trackable/non-trackable packing aids are missing", e);
					}
				}, () -> log.warn("Consignee country code mapping failed, cannot proceed with packing aids mapping for shipment."));
			});
		}, () -> log.warn("Shipper country code mapping failed, cannot proceed with packing aids mapping."));

		pdfRequest.setTrackablePackingAids(replacePackagingCodeWithDescription(groupByPackagingCode(trackablePackingAids)));
		pdfRequest.setNonTrackablePackingAids(replacePackagingCodeWithDescription(groupByPackagingCode(nonTrackablePackingAids)));
	}

	private Optional<String> getDachserCountryCode(String isoCountryCode) {
		try {
			return Optional.ofNullable(countryService.mapToDachserCountryCode(isoCountryCode));
		} catch (DachserCodeNotMappedException e) {
			log.error("Error during mapping of country code {}: {}", isoCountryCode, e.getMessage());
			return Optional.empty();
		}
	}

	private List<PackagingInfoDto> mapPackagingInfo(List<String> packingAidCodes, List<OrderLineDetailDto> orderLineItems, List<PackingPositionDto> packingPositions) {
		return Stream.concat(orderLineItems.stream().filter(item -> packingAidCodes.contains(item.getPackaging().getCode()))
				.map(item -> new PackagingInfoDto().aid(item.getPackaging().getCode()).count(item.getQuantity())), Stream.concat(
				packingPositions.stream().filter(position -> packingAidCodes.contains(position.getPackagingType().getCode()))
						.map(position -> new PackagingInfoDto().aid(position.getPackagingType().getCode()).count(position.getQuantity())),
				packingPositions.stream().flatMap(position -> position.getLines().stream()).filter(line -> packingAidCodes.contains(line.getPackaging().getCode()))
						.map(line -> new PackagingInfoDto().aid(line.getPackaging().getCode()).count(line.getQuantity())))).toList();
	}
}
