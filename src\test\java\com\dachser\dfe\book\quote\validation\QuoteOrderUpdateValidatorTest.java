package com.dachser.dfe.book.quote.validation;

import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.SpringBasedLocalMockTest;
import com.dachser.dfe.book.hscode.HsCode;
import com.dachser.dfe.book.hscode.HsCodeService;
import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.model.AirExportOrderDto;
import com.dachser.dfe.book.model.AirImportOrderDto;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.CollectionOptionDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderAddressDto;
import com.dachser.dfe.book.model.OrderAddressDto.LockedByQuoteEnum;
import com.dachser.dfe.book.model.OrderReferenceTypeDto;
import com.dachser.dfe.book.model.OrderValidationResultDto;
import com.dachser.dfe.book.model.PackingPositionDto;
import com.dachser.dfe.book.model.PortDto;
import com.dachser.dfe.book.model.RoadCollectionOrderDto;
import com.dachser.dfe.book.model.RoadForwardingOrderDto;
import com.dachser.dfe.book.model.RoadOrderLineDto;
import com.dachser.dfe.book.model.RoadOrderReferenceDto;
import com.dachser.dfe.book.model.SeaExportOrderDto;
import com.dachser.dfe.book.model.ValidationResultEntryDto;
import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderRepositoryFacade;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.exception.OrderSaveProcessException;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.quote.AirQuoteInformation;
import com.dachser.dfe.book.quote.QuoteMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrowsExactly;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@SpringBasedLocalMockTest
class QuoteOrderUpdateValidatorTest implements ResourceLoadingTest {

	private static final String A_TRANSLATION = "A Translation";

	public static final String ORDER_LINE_ITEM_NON_MATCHING_UPDATE_LINE = "orderLineItem_non matching update line";

	public static final String PACKING_POSITION_ITEM_NON_MATCHING_UPDATE_LINE = "packingPositionItem_non matching update line";

	public static final String PACKING_POSITION_NEW_INSERT = "packingPositionsItem_newInsert";

	public static final String PACKING_POSITION_COUNT = "packingPositionItem_count";

	public static final String FREIGHT_TERM = "freightTerm";

	public static final String INCO_TERM = "incoTerm";

	public static final String ORDER_LINE_ITEM_COUNT = "orderLineItem_count";

	public static final String ORDER_LINE_ITEM_WEIGHT = "orderLineItem_weight";

	public static final String COLLECTION_DATE = "collectionDate";

	public static final String ORDER_REFERENCE = "orderReference";

	public static final String GOODS_VALUE = "goodsValue";

	public static final String CASH_ON_DELIVERY = "cashOnDelivery";

	public static final String CASH_ON_DELIVERY_AMOUNT = "cashOnDeliveryAmount";

	@MockBean
	private Translator translator;

	@Autowired
	private OrderMapper orderMapper;

	@Autowired
	private QuoteMapper quoteMapper;

	@Autowired
	private QuoteOrderUpdateValidator quoteOrderUpdateValidator;

	@MockBean
	private HsCodeService hsCodeService;

	@MockBean
	private OrderRepositoryFacade orderRepositoryFacade;

	@BeforeEach
	void setup() {
		final HsCode hsCode = new HsCode();
		hsCode.setId("010129");
		hsCode.setDescription("A description");
		when(hsCodeService.searchHsCodes(anyString())).thenReturn(List.of(hsCode));
	}

	@Test
	void shouldRefuseValidationForEmptyOrder() {
		final BasicOrderDto order = new SeaExportOrderDto();
		final Order mapped = orderMapper.map(order);
		quoteMapper.buildQuoteInformationForOrder(mapped);
		quoteOrderUpdateValidator.validateQuoteUpdates(mapped.getQuoteInformation(), mapped);
	}

	@Nested
	class AirExport {

		private AirExportOrderDto loadAirExportOrder() throws IOException {
			return loadResourceAndConvert("orders/update/existing-air-export-order.json", new TypeReference<>() {
			});
		}

		@Test
		void shouldSucceedForNoChanges() throws IOException {
			final AirExportOrderDto orderExisting = loadAirExportOrder();
			orderExisting.setDeliveryAddress(null);

			final AirExportOrderDto orderUpdate = orderExisting;

			Order mappedExisting = orderMapper.map(orderExisting);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(orderUpdate));
		}

		@Test
		void shouldValidateDeliveryAddressDeleteWithValidationError() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			OrderAddressDto updateConsigneeAddress = new OrderAddressDto();
			updateConsigneeAddress.setCountryCode("AT");
			updateConsigneeAddress.setPostcode("1010");

			OrderAddressDto existingConsigneeAddress = new OrderAddressDto();
			existingConsigneeAddress.setCountryCode("DE");
			existingConsigneeAddress.setPostcode("82131");

			OrderAddressDto existingDeliveryAddress = new OrderAddressDto();
			existingDeliveryAddress.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
			existingDeliveryAddress.setCountryCode("FR");
			existingDeliveryAddress.setPostcode("70123");

			final AirExportOrderDto orderExistingDto = loadAirExportOrder();
			orderExistingDto.setConsigneeAddress(existingConsigneeAddress);
			orderExistingDto.setDeliveryAddress(existingDeliveryAddress);

			Order entityOrderExisting = orderMapper.map(orderExistingDto);
			entityOrderExisting.setOrderId(1L);
			quoteMapper.buildQuoteInformationForOrder(entityOrderExisting);
			when(orderRepositoryFacade.loadOrderById(1L)).thenReturn(entityOrderExisting);

			final AirExportOrderDto orderUpdate = orderExistingDto;
			orderUpdate.setConsigneeAddress(updateConsigneeAddress);
			orderUpdate.setDeliveryAddress(null);

			Order mappedOrderUpdate = orderMapper.map(orderUpdate);
			mappedOrderUpdate.getConsigneeAddress().setLockedByQuote(com.dachser.dfe.book.order.common.LockedByQuoteEnum.PARTIAL);

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(entityOrderExisting.getQuoteInformation(), mappedOrderUpdate));

			List<ValidationResultEntryDto> validationResults = exception.getValidationResult().getResults();
			assertEquals(2, validationResults.size());
		}

		@Test
		void shouldValidateDeliveryAddressDelete() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			OrderAddressDto updateConsigneeAddress = new OrderAddressDto();
			updateConsigneeAddress.setCountryCode("FR");
			updateConsigneeAddress.setPostcode("70123");

			OrderAddressDto existingConsigneeAddress = new OrderAddressDto();
			existingConsigneeAddress.setCountryCode("DE");
			existingConsigneeAddress.setPostcode("82131");

			OrderAddressDto existingDeliveryAddress = new OrderAddressDto();
			existingDeliveryAddress.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
			existingDeliveryAddress.setCountryCode("FR");
			existingDeliveryAddress.setPostcode("70123");

			final AirExportOrderDto orderExistingDto = loadAirExportOrder();
			orderExistingDto.setConsigneeAddress(existingConsigneeAddress);
			orderExistingDto.setDeliveryAddress(existingDeliveryAddress);

			Order entityOrderExisting = orderMapper.map(orderExistingDto);
			entityOrderExisting.setOrderId(1L);
			quoteMapper.buildQuoteInformationForOrder(entityOrderExisting);
			when(orderRepositoryFacade.loadOrderById(1L)).thenReturn(entityOrderExisting);

			final AirExportOrderDto orderUpdate = orderExistingDto;
			orderUpdate.setConsigneeAddress(updateConsigneeAddress);
			orderUpdate.setDeliveryAddress(null);

			quoteOrderUpdateValidator.validateQuoteUpdates(entityOrderExisting.getQuoteInformation(), orderMapper.map(orderUpdate));
		}

		@Test
		void shouldValidateDeliveryAddressSwitch() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			OrderAddressDto updateConsigneeAddress = new OrderAddressDto();
			updateConsigneeAddress.setCountryCode("AT");
			updateConsigneeAddress.setPostcode("1010");

			OrderAddressDto updateDeliveryAddress = new OrderAddressDto();
			updateDeliveryAddress.setCountryCode("FR");
			updateDeliveryAddress.setPostcode("70123");

			OrderAddressDto existingConsigneeAddress = new OrderAddressDto();
			existingConsigneeAddress.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
			existingConsigneeAddress.setCountryCode("FR");
			existingConsigneeAddress.setPostcode("70123");

			final AirExportOrderDto orderExistingDto = loadAirExportOrder();
			orderExistingDto.setConsigneeAddress(existingConsigneeAddress);

			Order entityOrderExisting = orderMapper.map(orderExistingDto);
			entityOrderExisting.setOrderId(1L);
			quoteMapper.buildQuoteInformationForOrder(entityOrderExisting);
			when(orderRepositoryFacade.loadOrderById(1L)).thenReturn(entityOrderExisting);

			final AirExportOrderDto orderUpdate = orderExistingDto;
			orderUpdate.setConsigneeAddress(updateConsigneeAddress);
			orderUpdate.setDeliveryAddress(updateDeliveryAddress);

			quoteOrderUpdateValidator.validateQuoteUpdates(entityOrderExisting.getQuoteInformation(), orderMapper.map(orderUpdate));
		}

		@Test
		void shouldValidateDeliveryAddressSwitchWithValidationError() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			OrderAddressDto updateConsigneeAddress = new OrderAddressDto();
			updateConsigneeAddress.setCountryCode("AT");
			updateConsigneeAddress.setPostcode("1010");

			OrderAddressDto updateDeliveryAddress = new OrderAddressDto();
			updateDeliveryAddress.setCountryCode("DE");
			updateDeliveryAddress.setPostcode("82131");

			OrderAddressDto existingConsigneeAddress = new OrderAddressDto();
			existingConsigneeAddress.setCountryCode("FR");
			existingConsigneeAddress.setPostcode("70123");

			final AirExportOrderDto orderExistingDto = loadAirExportOrder();
			orderExistingDto.setConsigneeAddress(existingConsigneeAddress);

			Order entityOrderExisting = orderMapper.map(orderExistingDto);
			entityOrderExisting.setOrderId(1L);
			quoteMapper.buildQuoteInformationForOrder(entityOrderExisting);
			when(orderRepositoryFacade.loadOrderById(1L)).thenReturn(entityOrderExisting);

			final AirExportOrderDto orderUpdate = orderExistingDto;
			orderUpdate.setConsigneeAddress(updateConsigneeAddress);
			orderUpdate.setDeliveryAddress(updateDeliveryAddress);

			Order mappedOrderUpdate = orderMapper.map(orderUpdate);
			mappedOrderUpdate.getConsigneeAddress().setLockedByQuote(com.dachser.dfe.book.order.common.LockedByQuoteEnum.PARTIAL);

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(entityOrderExisting.getQuoteInformation(), mappedOrderUpdate));

			List<ValidationResultEntryDto> validationResults = exception.getValidationResult().getResults();
			assertEquals(2, validationResults.size());
		}

		@Test
		void shouldValidateFullAddressChanges() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			final AirExportOrderDto orderExisting = loadAirExportOrder();
			orderExisting.setDeliveryAddress(null);
			Order mappedExisting = orderMapper.map(orderExisting);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			final AirExportOrderDto orderUpdate = orderExisting;
			orderUpdate.getShipperAddress().setName("Humpty");
			orderUpdate.getShipperAddress().setName2("Dumpty");
			orderUpdate.getShipperAddress().setName3("Diddly");
			orderUpdate.getShipperAddress().setStreet("Downing Street");
			orderUpdate.getShipperAddress().setStreet2("Odeonsplatz");
			orderUpdate.getShipperAddress().setCity("Hausen");
			orderUpdate.getShipperAddress().setPostcode("82131");
			orderUpdate.getShipperAddress().setSupplement("ps");
			orderUpdate.getShipperAddress().setCountryCode("DE");
			orderUpdate.getShipperAddress().setGln("0123456789012");
			orderUpdate.getShipperAddress().setTaxID("0123456789");

			Order mappedOrderUpdate = orderMapper.map(orderUpdate);
			mappedOrderUpdate.getShipperAddress().setLockedByQuote(com.dachser.dfe.book.order.common.LockedByQuoteEnum.FULL);

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), mappedOrderUpdate));

			assertEquals(5, exception.getValidationResult().getResults().size());

		}

		@Test
		void shouldValidatePartialAddressChanges() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			final AirExportOrderDto orderExisting = loadAirExportOrder();

			orderExisting.setShipperAddress(new OrderAddressDto().countryCode("DE").postcode("82131"));
			orderExisting.setDeliveryAddress(null);
			Order mappedExisting = orderMapper.map(orderExisting);
			mappedExisting.getShipperAddress().setLockedByQuote(com.dachser.dfe.book.order.common.LockedByQuoteEnum.PARTIAL);

			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			final AirExportOrderDto orderUpdate = orderExisting;
			orderUpdate.setDeliveryAddress(null);
			orderUpdate.getShipperAddress().setName("Humpty");
			orderUpdate.getShipperAddress().setName2("Dumpty");
			orderUpdate.getShipperAddress().setName3("Diddly");
			orderUpdate.getShipperAddress().setStreet("Downing Street");
			orderUpdate.getShipperAddress().setStreet2("Odeonsplatz");
			orderUpdate.getShipperAddress().setCity("Hausen");
			orderUpdate.getShipperAddress().setSupplement("ps");
			orderUpdate.getShipperAddress().setGln("0123456789012");
			orderUpdate.getShipperAddress().setTaxID("0123456789");

			quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(orderUpdate));

			orderUpdate.getShipperAddress().setCountryCode("CH");

			Order mappedOrderUpdate = orderMapper.map(orderUpdate);

			mappedOrderUpdate.getShipperAddress().setLockedByQuote(com.dachser.dfe.book.order.common.LockedByQuoteEnum.PARTIAL);

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), mappedOrderUpdate));

			List<ValidationResultEntryDto> validationResults = exception.getValidationResult().getResults();
			assertEquals(1, validationResults.size());
			assertEquals("shipperAddress_countryCode", validationResults.get(0).getField());
		}

		@Test
		void shouldValidatePrincipalAddressChanges() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			final AirExportOrderDto orderDto = loadAirExportOrder();
			AirExportOrder updateOrder = orderMapper.mapAir(orderDto);
			updateOrder.getShipperAddress().setLockedByQuote(com.dachser.dfe.book.order.common.LockedByQuoteEnum.FULL);
			updateOrder.getConsigneeAddress().setLockedByQuote(com.dachser.dfe.book.order.common.LockedByQuoteEnum.FULL);
			quoteMapper.buildQuoteInformationForOrder(updateOrder);
			AirQuoteInformation quoteInformation = updateOrder.getQuoteInformation();
			quoteInformation.setPrincipalAddress(quoteInformation.getConsigneeAddress());

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(quoteInformation, updateOrder));
			assertEquals(5, exception.getValidationResult().getResults().size());
		}

		@Test
		void shouldValidateIATAChanges() throws IOException {
			final AirExportOrderDto order = loadAirExportOrder();

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order));

			order.getFromIATA().code("MUC");
			order.setToIATA(new PortDto().countryCode("DE").code("FRA").name("Frankfurt"));

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(2, validationResultEntries.size());
			assertEquals("fromIATA", validationResultEntries.get(0).getField());
			assertEquals("toIATA", validationResultEntries.get(1).getField());
		}

		@Test
		void shouldValidateTermChanges() throws IOException {
			final AirExportOrderDto order = loadAirExportOrder();

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.getIncoTerm().dachserCode("bla");

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(1, validationResultEntries.size());
			assertEquals(INCO_TERM, validationResultEntries.get(0).getField());
		}

		@Test
		void shouldValidateOrderLineChanges() throws IOException {
			final AirExportOrderDto order = loadAirExportOrder();

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.getOrderLineItems().get(0).setLength(200);
			order.getOrderLineItems().get(0).setWidth(200);
			order.getOrderLineItems().get(0).setHeight(200);
			order.getOrderLineItems().get(0).setWeight(20.00);
			order.getOrderLineItems().get(0).setVolume(200D);
			order.getOrderLineItems().get(0).setQuantity(20);
			order.getOrderLineItems().get(0).getPackaging().setCode("Plastiktüte");

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(1, validationResultEntries.size());
			assertEquals(ORDER_LINE_ITEM_NON_MATCHING_UPDATE_LINE, validationResultEntries.get(0).getField());
		}

		@Test
		void shouldValidateOrderLineAmountChanges() throws IOException {
			final AirExportOrderDto order = loadAirExportOrder();

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.getOrderLineItems().remove(0);

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(1, validationResultEntries.size());
			assertEquals(ORDER_LINE_ITEM_COUNT, validationResultEntries.get(0).getField());
		}

		@Test
		void shouldValidateCollectionDateChanges() throws IOException {
			final AirExportOrderDto order = loadAirExportOrder();

			final Order mappedExisting = orderMapper.map(order);
			mappedExisting.setCollectionDate(LocalDate.now().plusDays(2));
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.getCollectionTime().setCollectionDate(LocalDate.now().minusDays(3));

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(1, validationResultEntries.size());
			assertEquals(COLLECTION_DATE, validationResultEntries.get(0).getField());
		}

		@Test
		void shouldValidateMissingReference() throws IOException {
			final AirExportOrderDto order = loadAirExportOrder();

			final Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.setReferences(new ArrayList<>());

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(1, validationResultEntries.size());
			assertEquals(ORDER_REFERENCE, validationResultEntries.get(0).getField());
		}

		@Test
		void shouldValidateChangedReference() throws IOException {
			final AirExportOrderDto order = loadAirExportOrder();

			final Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.getReferences().get(1).setReferenceValue("newReference");

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(1, validationResultEntries.size());
			assertEquals(ORDER_REFERENCE, validationResultEntries.get(0).getField());

		}

		@Test
		void shouldRefuseValidationForNotEqualWeight() throws IOException {
			final AirExportOrderDto order = loadAirExportOrder();

			final Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.getOrderLineItems().get(0).setWeight(20.01);

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(1, validationResultEntries.size());
			assertEquals(ORDER_LINE_ITEM_WEIGHT, validationResultEntries.get(0).getField());
		}

		@Test
		void shouldNotRefuseValidationForAddedOrderLineWithMatchingTotalWeight() throws IOException {
			final AirExportOrderDto order = loadAirExportOrder();

			final Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.getOrderLineItems().get(0).setWeight(10.00);
			order.getOrderLineItems().get(1).setWeight(14.00);

			assertDoesNotThrow(() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));
		}

	}

	@Nested
	class AirImport {

		private AirImportOrderDto loadAirImportOrder() throws IOException {
			return loadResourceAndConvert("orders/update/existing-air-import-order.json", new TypeReference<>() {
			});
		}

		@Test
		void shouldSucceedForNoChanges() throws IOException {
			final AirImportOrderDto orderExisting = loadAirImportOrder();
			orderExisting.setPickupAddress(null);

			Order mappedExisting = orderMapper.map(orderExisting);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			final AirImportOrderDto orderUpdate = orderExisting;

			quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(orderUpdate));
		}

		@Test
		void shouldValidateFullAddressChanges() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			final AirImportOrderDto orderExisting = loadAirImportOrder();
			orderExisting.setPickupAddress(null);
			Order mappedExisting = orderMapper.map(orderExisting);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			final AirImportOrderDto orderUpdate = orderExisting;
			orderUpdate.getShipperAddress().setName("Humpty");
			orderUpdate.getShipperAddress().setName2("Dumpty");
			orderUpdate.getShipperAddress().setName3("Diddly");
			orderUpdate.getShipperAddress().setStreet("Downing Street");
			orderUpdate.getShipperAddress().setStreet2("Odeonsplatz");
			orderUpdate.getShipperAddress().setCity("Hausen");
			orderUpdate.getShipperAddress().setPostcode("82131");
			orderUpdate.getShipperAddress().setSupplement("ps");
			orderUpdate.getShipperAddress().setCountryCode("DE");
			orderUpdate.getShipperAddress().setGln("0123456789012");
			orderUpdate.getShipperAddress().setTaxID("0123456789");

			Order mappedOrderUpdate = orderMapper.map(orderUpdate);
			mappedOrderUpdate.getShipperAddress().setLockedByQuote(com.dachser.dfe.book.order.common.LockedByQuoteEnum.FULL);

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), mappedOrderUpdate));

			assertEquals(5, exception.getValidationResult().getResults().size());

		}

		@Test
		void shouldValidatePartialAddressChanges() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			final AirImportOrderDto orderExisting = loadAirImportOrder();

			orderExisting.setShipperAddress(new OrderAddressDto().countryCode("DE").postcode("82131"));
			orderExisting.setPickupAddress(null);
			Order mappedExisting = orderMapper.map(orderExisting);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			final AirImportOrderDto orderUpdate = orderExisting;
			orderUpdate.setPickupAddress(null);
			orderUpdate.getShipperAddress().setName("Humpty");
			orderUpdate.getShipperAddress().setName2("Dumpty");
			orderUpdate.getShipperAddress().setName3("Diddly");
			orderUpdate.getShipperAddress().setStreet("Downing Street");
			orderUpdate.getShipperAddress().setStreet2("Odeonsplatz");
			orderUpdate.getShipperAddress().setCity("Hausen");
			orderUpdate.getShipperAddress().setSupplement("ps");
			orderUpdate.getShipperAddress().setGln("0123456789012");
			orderUpdate.getShipperAddress().setTaxID("0123456789");

			quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(orderUpdate));

			orderUpdate.getShipperAddress().setCountryCode("CH");

			Order mappedOrderUpdate = orderMapper.map(orderUpdate);
			mappedOrderUpdate.getShipperAddress().setLockedByQuote(com.dachser.dfe.book.order.common.LockedByQuoteEnum.PARTIAL);

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), mappedOrderUpdate));

			List<ValidationResultEntryDto> validationResults = exception.getValidationResult().getResults();
			assertEquals(1, validationResults.size());
			assertEquals("shipperAddress_countryCode", validationResults.get(0).getField());
		}

		@Test
		void shouldValidatePrincipalAddressChanges() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			final AirImportOrderDto orderDto = loadAirImportOrder();
			AirImportOrder updateOrder = orderMapper.mapAir(orderDto);
			quoteMapper.buildQuoteInformationForOrder(updateOrder);
			AirQuoteInformation quoteInformation = updateOrder.getQuoteInformation();
			quoteInformation.setPrincipalAddress(quoteInformation.getShipperAddress());

			updateOrder.getConsigneeAddress().setLockedByQuote(com.dachser.dfe.book.order.common.LockedByQuoteEnum.FULL);

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(quoteInformation, updateOrder));
			assertEquals(5, exception.getValidationResult().getResults().size());
		}

		@Test
		void shouldValidateIATAChanges() throws IOException {
			final AirImportOrderDto order = loadAirImportOrder();

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order));

			order.getToIATA().code("MUC");
			order.setFromIATA(new PortDto().countryCode("DE").code("FRA").name("Frankfurt"));

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(2, validationResultEntries.size());
			assertEquals("fromIATA", validationResultEntries.get(0).getField());
			assertEquals("toIATA", validationResultEntries.get(1).getField());
		}

		@Test
		void shouldValidateTermChanges() throws IOException {
			final AirImportOrderDto order = loadAirImportOrder();

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.getIncoTerm().dachserCode("bla");

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(1, validationResultEntries.size());
			assertEquals(INCO_TERM, validationResultEntries.get(0).getField());
		}

		@Test
		void shouldValidateOrderLineChanges() throws IOException {
			final AirImportOrderDto order = loadAirImportOrder();

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.getOrderLineItems().get(0).setLength(200);
			order.getOrderLineItems().get(0).setWidth(200);
			order.getOrderLineItems().get(0).setHeight(200);
			order.getOrderLineItems().get(0).setWeight(20.00);
			order.getOrderLineItems().get(0).setVolume(200D);
			order.getOrderLineItems().get(0).setQuantity(20);
			order.getOrderLineItems().get(0).getPackaging().setCode("Plastiktüte");

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(1, validationResultEntries.size());
			assertEquals(ORDER_LINE_ITEM_NON_MATCHING_UPDATE_LINE, validationResultEntries.get(0).getField());
		}

		@Test
		void shouldValidateOrderLineAmountChanges() throws IOException {
			final AirImportOrderDto order = loadAirImportOrder();

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.getOrderLineItems().remove(0);

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(1, validationResultEntries.size());
			assertEquals(ORDER_LINE_ITEM_COUNT, validationResultEntries.get(0).getField());
		}

		@Test
		void shouldValidateCollectionDateChanges() throws IOException {
			final AirImportOrderDto order = loadAirImportOrder();

			final Order mappedExisting = orderMapper.map(order);
			mappedExisting.setCollectionDate(LocalDate.now().plusDays(2));
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.getCollectionTime().setCollectionDate(LocalDate.now().minusDays(3));

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(1, validationResultEntries.size());
			assertEquals(COLLECTION_DATE, validationResultEntries.get(0).getField());
		}

		@Test
		void shouldValidateMissingReference() throws IOException {
			final AirImportOrderDto order = loadAirImportOrder();

			final Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.setReferences(new ArrayList<>());

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(1, validationResultEntries.size());
			assertEquals(ORDER_REFERENCE, validationResultEntries.get(0).getField());
		}

		@Test
		void shouldValidateChangedReference() throws IOException {
			final AirImportOrderDto order = loadAirImportOrder();

			final Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.getReferences().get(1).setReferenceValue("newReference");

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			List<ValidationResultEntryDto> validationResultEntries = exception.getValidationResult().getResults();
			assertEquals(1, validationResultEntries.size());
			assertEquals(ORDER_REFERENCE, validationResultEntries.get(0).getField());

		}

		@Test
		void shouldValidatePickupAddressDeleteWithValidationError() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			OrderAddressDto updateShipperAddress = new OrderAddressDto();
			updateShipperAddress.setCountryCode("AT");
			updateShipperAddress.setPostcode("1010");

			OrderAddressDto existingShipperAddress = new OrderAddressDto();
			existingShipperAddress.setCountryCode("DE");
			existingShipperAddress.setPostcode("82131");

			OrderAddressDto existingPickupAddress = new OrderAddressDto();
			existingPickupAddress.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
			existingPickupAddress.setCountryCode("FR");
			existingPickupAddress.setPostcode("70123");

			final AirImportOrderDto orderExistingDto = loadAirImportOrder();
			orderExistingDto.setShipperAddress(existingShipperAddress);
			orderExistingDto.setPickupAddress(existingPickupAddress);

			Order entityOrderExisting = orderMapper.map(orderExistingDto);
			entityOrderExisting.setOrderId(1L);
			quoteMapper.buildQuoteInformationForOrder(entityOrderExisting);
			when(orderRepositoryFacade.loadOrderById(1L)).thenReturn(entityOrderExisting);

			final AirImportOrderDto orderUpdate = orderExistingDto;
			orderUpdate.setShipperAddress(updateShipperAddress);
			orderUpdate.setPickupAddress(null);

			Order mappedOrderUpdate = orderMapper.map(orderUpdate);
			mappedOrderUpdate.getShipperAddress().setLockedByQuote(com.dachser.dfe.book.order.common.LockedByQuoteEnum.PARTIAL);

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(entityOrderExisting.getQuoteInformation(), mappedOrderUpdate));

			List<ValidationResultEntryDto> validationResults = exception.getValidationResult().getResults();
			assertEquals(2, validationResults.size());
		}

		@Test
		void shouldValidateDeliveryAddressDelete() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			OrderAddressDto updateShipperAddress = new OrderAddressDto();
			updateShipperAddress.setCountryCode("FR");
			updateShipperAddress.setPostcode("70123");

			OrderAddressDto existingShipperAddress = new OrderAddressDto();
			existingShipperAddress.setCountryCode("DE");
			existingShipperAddress.setPostcode("82131");

			OrderAddressDto existingPickupAddress = new OrderAddressDto();
			existingPickupAddress.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
			existingPickupAddress.setCountryCode("FR");
			existingPickupAddress.setPostcode("70123");

			final AirImportOrderDto orderExistingDto = loadAirImportOrder();
			orderExistingDto.setConsigneeAddress(existingShipperAddress);
			orderExistingDto.setDeliveryAddress(existingPickupAddress);

			Order entityOrderExisting = orderMapper.map(orderExistingDto);
			entityOrderExisting.setOrderId(1L);
			quoteMapper.buildQuoteInformationForOrder(entityOrderExisting);
			when(orderRepositoryFacade.loadOrderById(1L)).thenReturn(entityOrderExisting);

			final AirImportOrderDto orderUpdate = orderExistingDto;
			orderUpdate.setShipperAddress(updateShipperAddress);
			orderUpdate.setPickupAddress(null);

			quoteOrderUpdateValidator.validateQuoteUpdates(entityOrderExisting.getQuoteInformation(), orderMapper.map(orderUpdate));
		}

		@Test
		void shouldValidatePickupAddressSwitch() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			OrderAddressDto updateShipperAddress = new OrderAddressDto();
			updateShipperAddress.setCountryCode("AT");
			updateShipperAddress.setPostcode("1010");

			OrderAddressDto updatePickupAddress = new OrderAddressDto();
			updatePickupAddress.setCountryCode("FR");
			updatePickupAddress.setPostcode("70123");

			OrderAddressDto existingShipperAddress = new OrderAddressDto();
			existingShipperAddress.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
			existingShipperAddress.setCountryCode("FR");
			existingShipperAddress.setPostcode("70123");

			final AirImportOrderDto orderExistingDto = loadAirImportOrder();
			orderExistingDto.setShipperAddress(existingShipperAddress);

			Order entityOrderExisting = orderMapper.map(orderExistingDto);
			entityOrderExisting.setOrderId(1L);
			quoteMapper.buildQuoteInformationForOrder(entityOrderExisting);
			when(orderRepositoryFacade.loadOrderById(1L)).thenReturn(entityOrderExisting);

			final AirImportOrderDto orderUpdate = orderExistingDto;
			orderUpdate.setShipperAddress(updateShipperAddress);
			orderUpdate.setPickupAddress(updatePickupAddress);

			quoteOrderUpdateValidator.validateQuoteUpdates(entityOrderExisting.getQuoteInformation(), orderMapper.map(orderUpdate));
		}

		@Test
		void shouldValidateDeliveryAddressSwitchWithValidationError() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			OrderAddressDto updateShipperAddress = new OrderAddressDto();
			updateShipperAddress.setCountryCode("AT");
			updateShipperAddress.setPostcode("1010");

			OrderAddressDto updatePickupAddress = new OrderAddressDto();
			updatePickupAddress.setCountryCode("DE");
			updatePickupAddress.setPostcode("82131");

			OrderAddressDto existingShipperAddress = new OrderAddressDto();
			existingShipperAddress.setCountryCode("FR");
			existingShipperAddress.setPostcode("70123");

			final AirImportOrderDto orderExistingDto = loadAirImportOrder();
			orderExistingDto.setShipperAddress(existingShipperAddress);

			Order entityOrderExisting = orderMapper.map(orderExistingDto);
			entityOrderExisting.setOrderId(1L);
			quoteMapper.buildQuoteInformationForOrder(entityOrderExisting);
			when(orderRepositoryFacade.loadOrderById(1L)).thenReturn(entityOrderExisting);

			final AirImportOrderDto orderUpdate = orderExistingDto;
			orderUpdate.setShipperAddress(updateShipperAddress);
			orderUpdate.setPickupAddress(updatePickupAddress);

			Order mappedOrderUpdate = orderMapper.map(orderUpdate);
			mappedOrderUpdate.getShipperAddress().setLockedByQuote(com.dachser.dfe.book.order.common.LockedByQuoteEnum.PARTIAL);

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(entityOrderExisting.getQuoteInformation(), mappedOrderUpdate));

			List<ValidationResultEntryDto> validationResults = exception.getValidationResult().getResults();
			assertEquals(2, validationResults.size());
		}

	}

	@Nested
	class RoadForwarding {

		private RoadForwardingOrderDto loadRoadOrder() throws IOException {
			RoadForwardingOrderDto roadForwardingOrderDto = loadResourceAndConvert("orders/update/existing-road-forwarding-order.json", new TypeReference<>() {
			});
			// setting defaults
			roadForwardingOrderDto.setTailLiftDelivery(Boolean.FALSE);
			roadForwardingOrderDto.frostProtection(Boolean.FALSE);
			return roadForwardingOrderDto;
		}

		@Test
		void shouldReturnValidationErrorsForCustomRoad() throws IOException {

			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);
			final RoadForwardingOrderDto order = loadRoadOrder();

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.setGoodsValue(1D);
			order.setGoodsCurrency("PLN");
			order.getFreightTerm().setDachserTermKey("1");
			order.setReferences(new ArrayList<>());

			order.getOrderLineItems().get(0).setHeight(200);
			order.getOrderLineItems().get(0).setLength(200);
			order.getOrderLineItems().get(0).setWidth(200);
			order.getOrderLineItems().get(0).setWeight(20);
			order.getOrderLineItems().get(0).setVolume(200D);
			order.getOrderLineItems().get(0).setQuantity(2);
			order.getOrderLineItems().get(0).getPackaging().setCode("Plastiktüte");

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			final OrderValidationResultDto validationResult = exception.getValidationResult();

			assertFalse(validationResult.getValid());
			assertNotNull(validationResult.getResults());

			final List<String> validationErrorNames = validationResult.getResults().stream().map(ValidationResultEntryDto::getField).toList();
			final List<String> expectedErrorNames = List.of(FREIGHT_TERM, ORDER_REFERENCE, ORDER_LINE_ITEM_NON_MATCHING_UPDATE_LINE);

			assertTrue(expectedErrorNames.containsAll(validationErrorNames));
			assertEquals(3, validationResult.getResults().size());
		}

		@Test
		void shouldReturnValidationErrorOnPrincipalChange() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);
			final RoadForwardingOrderDto order = loadRoadOrder();

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.getShipperAddress().setName("newName");
			order.getShipperAddress().setStreet("newStreet");

			Order mappedOrderUpdate = orderMapper.map(order);
			mappedOrderUpdate.getShipperAddress().setLockedByQuote(com.dachser.dfe.book.order.common.LockedByQuoteEnum.FULL);

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), mappedOrderUpdate));

			OrderValidationResultDto validationResult = exception.getValidationResult();
			assertFalse(validationResult.getValid());
			assertNotNull(validationResult.getResults());
			assertEquals(2, validationResult.getResults().size());
		}

		@Test
		void shouldReturnValidationErrorOnPalletLocationsChange() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);
			final RoadForwardingOrderDto order = loadRoadOrder();
			order.setPalletLocations(1.0);

			ForwardingOrder mappedExisting = (ForwardingOrder) orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			mappedExisting.getQuoteInformation().setPalletLocations(BigDecimal.valueOf(10.00));

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			OrderValidationResultDto validationResult = exception.getValidationResult();
			assertFalse(validationResult.getValid());
			assertNotNull(validationResult.getResults());
			assertEquals("palletLocations", validationResult.getResults().get(0).getField());
		}

		@Test
		void shouldReturnValidationErrorsOnDeliveryOptionChanges() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);
			final RoadForwardingOrderDto order = loadRoadOrder();

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.setTailLiftDelivery(true);
			order.setFrostProtection(true);
			order.setSelfCollection(false);

			OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			OrderValidationResultDto validationResult = exception.getValidationResult();
			assertFalse(validationResult.getValid());
			assertNotNull(validationResult.getResults());

			List<String> expectedErrorNames = List.of("tailLift", "frostProtected", "selfCollector");
			List<String> validationErrorNames = validationResult.getResults().stream().map(ValidationResultEntryDto::getField).toList();

			assertTrue(expectedErrorNames.containsAll(validationErrorNames));
		}

		@Test
		void shouldReturnValidationErrorForWrongQuoteReference() throws IOException {

			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);
			final RoadForwardingOrderDto order = loadRoadOrder();

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);
			mappedExisting.getQuoteInformation().setQuoteReference("Didelidoo");

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			final OrderValidationResultDto validationResult = exception.getValidationResult();

			assertFalse(validationResult.getValid());
			assertNotNull(validationResult.getResults());

			final List<String> validationErrorNames = validationResult.getResults().stream().map(ValidationResultEntryDto::getField).toList();
			final List<String> expectedErrorNames = List.of(ORDER_REFERENCE);

			assertTrue(expectedErrorNames.containsAll(validationErrorNames));
			assertEquals(1, validationResult.getResults().size());
		}

		@Test
		void shouldAllowAllChangesOnNonEPricingQuote2Book() throws IOException {
			final RoadForwardingOrderDto order = loadRoadOrder();
			Optional<RoadOrderReferenceDto> first = order.getReferences().stream().filter(ref -> ref.getReferenceType().equals(OrderReferenceTypeDto.DAILY_PRICE_REFERENCE))
					.findFirst();
			first.ifPresent(reference -> order.getReferences().remove(reference));
			ForwardingOrder mappedExisting = (ForwardingOrder) orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);
			order.setCustomerNumber("12345678");
			order.setProduct("X");

			assertDoesNotThrow(() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

		}

		@Test
		void shouldAllowPalletLocationsOfIntSameChange() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);
			final RoadForwardingOrderDto order = loadRoadOrder();
			order.setPalletLocations(10.8);

			ForwardingOrder mappedExisting = (ForwardingOrder) orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			mappedExisting.setPalletLocations(BigDecimal.valueOf(10.2));

			assertDoesNotThrow(() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

		}

		@Test
		void shouldAllowDeliveryOptionChangeForNullOnOrder() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);
			final RoadForwardingOrderDto order = loadRoadOrder();

			ForwardingOrder mappedExisting = (ForwardingOrder) orderMapper.map(order);
			mappedExisting.setDeliveryOption(null);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			assertDoesNotThrow(() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));
		}

		@Test
		void shouldAllowDeliveryOptionChangeForNullOnQuoteInformation() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);
			final RoadForwardingOrderDto order = loadRoadOrder();

			ForwardingOrder mappedExisting = (ForwardingOrder) orderMapper.map(order);
			mappedExisting.setDeliveryOption(DeliveryOptions.NO);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			mappedExisting.getQuoteInformation().setDeliveryOption(null);

			assertDoesNotThrow(() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));
		}

		@Test
		void shouldReturnValidationErrorsForPackingPositionQuantityRoad() throws IOException {

			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);
			final RoadForwardingOrderDto order = loadRoadOrder();

			addPackingPositionToOrder(order);

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			order.getPackingPositions().get(0).setQuantity(2);
			order.getPackingPositions().get(0).setPackagingType(new OptionDto().code("EUR").description("EUR"));

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			final OrderValidationResultDto validationResult = exception.getValidationResult();

			assertFalse(validationResult.getValid());
			assertNotNull(validationResult.getResults());

			final List<String> validationErrorNames = validationResult.getResults().stream().map(ValidationResultEntryDto::getField).toList();
			final List<String> expectedErrorNames = List.of(PACKING_POSITION_ITEM_NON_MATCHING_UPDATE_LINE);

			assertTrue(expectedErrorNames.containsAll(validationErrorNames));
			assertEquals(1, validationResult.getResults().size());
		}

		@Test
		void shouldReturnValidationErrorsForPackingPositionWithoutIdRoad() throws IOException {

			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);
			final RoadForwardingOrderDto order = loadRoadOrder();

			addPackingPositionToOrder(order);

			PackingPositionDto packingPosition = new PackingPositionDto().packagingType(new OptionDto().code("BOX").description("Box")).quantity(1)
					.lines(List.of(order.getOrderLineItems().get(0)));
			order.setPackingPositions(List.of(packingPosition, order.getPackingPositions().get(0)));

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			final OrderValidationResultDto validationResult = exception.getValidationResult();

			assertFalse(validationResult.getValid());
			assertNotNull(validationResult.getResults());

			final List<String> validationErrorNames = validationResult.getResults().stream().map(ValidationResultEntryDto::getField).toList();
			final List<String> expectedErrorNames = List.of(PACKING_POSITION_NEW_INSERT);

			assertTrue(expectedErrorNames.containsAll(validationErrorNames));
			assertEquals(1, validationResult.getResults().size());
		}

		@Test
		void shouldReturnValidationErrorsForPackingPositionCountRoad() throws IOException {

			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);
			final RoadForwardingOrderDto order = loadRoadOrder();

			addPackingPositionToOrder(order);

			PackingPositionDto packingPosition = new PackingPositionDto().packagingType(new OptionDto().code("BOX").description("Box")).quantity(1)
					.lines(List.of(order.getOrderLineItems().get(0)));
			order.setPackingPositions(List.of(packingPosition, order.getPackingPositions().get(0)));

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			PackingPositionDto packingPosition1 = new PackingPositionDto().packagingType(new OptionDto().code("IX").description("ix")).quantity(1)
					.lines(List.of(order.getOrderLineItems().get(0)));
			order.setPackingPositions(List.of(packingPosition1));

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));

			final OrderValidationResultDto validationResult = exception.getValidationResult();

			assertFalse(validationResult.getValid());
			assertNotNull(validationResult.getResults());

			final List<String> validationErrorNames = validationResult.getResults().stream().map(ValidationResultEntryDto::getField).toList();
			final List<String> expectedErrorNames = List.of(PACKING_POSITION_COUNT);

			assertTrue(expectedErrorNames.containsAll(validationErrorNames));
			assertEquals(1, validationResult.getResults().size());
		}

		@Test
		void shouldDetectCashOnDeliveryChangesFromNullToValue() throws IOException {
			RoadForwardingOrderDto roadForwardingOrderDto = loadRoadOrder();

			Order mappedExisting = orderMapper.map(roadForwardingOrderDto);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			roadForwardingOrderDto.setCashOnDelivery(true);
			roadForwardingOrderDto.setCashOnDeliveryAmount(120.0);

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(roadForwardingOrderDto)));

			final OrderValidationResultDto validationResult = exception.getValidationResult();

			assertFalse(validationResult.getValid());
			assertNotNull(validationResult.getResults());
			final List<String> validationErrorNames = validationResult.getResults().stream().map(ValidationResultEntryDto::getField).toList();
			final List<String> expectedErrorNames = List.of(CASH_ON_DELIVERY, CASH_ON_DELIVERY_AMOUNT);

			assertTrue(expectedErrorNames.containsAll(validationErrorNames));

		}

		@Test
		void shouldDetectCashOnDeliveryChanges() throws IOException {
			RoadForwardingOrderDto roadForwardingOrderDto = loadRoadOrder();

			roadForwardingOrderDto.setCashOnDelivery(true);
			roadForwardingOrderDto.setCashOnDeliveryAmount(120.0);

			Order mappedExisting = orderMapper.map(roadForwardingOrderDto);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			roadForwardingOrderDto.setCashOnDeliveryAmount(140.0);

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(roadForwardingOrderDto)));

			final OrderValidationResultDto validationResult = exception.getValidationResult();

			assertFalse(validationResult.getValid());
			assertNotNull(validationResult.getResults());
			final List<String> validationErrorNames = validationResult.getResults().stream().map(ValidationResultEntryDto::getField).toList();
			final List<String> expectedErrorNames = List.of(CASH_ON_DELIVERY, CASH_ON_DELIVERY_AMOUNT);

			assertTrue(expectedErrorNames.containsAll(validationErrorNames));

		}

		@Test
		void shouldReturnNoValidationErrorsWhenPackingPositionIsNull() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);
			final RoadForwardingOrderDto order = loadRoadOrder();

			Order mappedExisting = orderMapper.map(order);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			assertDoesNotThrow(() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(order)));
		}
	}

	@Nested
	class RoadCollection {

		private RoadCollectionOrderDto loadRoadCollectionOrder() throws IOException {
			return loadResourceAndConvert("orders/update/existing-road-collection-order.json", new TypeReference<>() {
			});
		}

		@Test
		void shouldReturnValidationErrorForChangeInGoodsValue() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			RoadCollectionOrderDto orderDto = loadRoadCollectionOrder();

			Order mappedExisting = orderMapper.map(orderDto);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);

			orderDto.setGoodsValue(200D);

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(orderDto)));

			final OrderValidationResultDto validationResult = exception.getValidationResult();

			assertFalse(validationResult.getValid());
			assertNotNull(validationResult.getResults());

			final List<String> validationErrorNames = validationResult.getResults().stream().map(ValidationResultEntryDto::getField).toList();
			final List<String> expectedErrorNames = List.of("goodsValue");

			assertTrue(expectedErrorNames.containsAll(validationErrorNames));
			assertEquals(1, validationResult.getResults().size());
		}

		@Test
		void shouldReturnValidationErrorForChangeInCollectionOption() throws IOException {
			when(translator.toLocale(anyString())).thenReturn(A_TRANSLATION);

			RoadCollectionOrderDto orderDto = loadRoadCollectionOrder();
			Order mappedExisting = orderMapper.map(orderDto);
			quoteMapper.buildQuoteInformationForOrder(mappedExisting);
			orderDto.setCollectionOption(CollectionOptionDto.BOOKING);

			final OrderSaveProcessException exception = assertThrowsExactly(OrderSaveProcessException.class,
					() -> quoteOrderUpdateValidator.validateQuoteUpdates(mappedExisting.getQuoteInformation(), orderMapper.map(orderDto)));

			final OrderValidationResultDto validationResult = exception.getValidationResult();

			assertFalse(validationResult.getValid());
			assertNotNull(validationResult.getResults());
			final List<String> validationErrorNames = validationResult.getResults().stream().map(ValidationResultEntryDto::getField).toList();
			final List<String> expectedErrorNames = List.of("collectionOption");
			assertTrue(expectedErrorNames.containsAll(validationErrorNames));
			assertEquals(1, validationResult.getResults().size());
		}

	}

	private static void addPackingPositionToOrder(RoadForwardingOrderDto order) {
		PackingPositionDto packingPosition = new PackingPositionDto();

		packingPosition.setQuantity(1);
		packingPosition.setId(1L);
		packingPosition.setPackagingType(new OptionDto().code("BOX").description("Box"));
		RoadOrderLineDto orderLine = order.getOrderLineItems().get(0);
		orderLine.setId(11L);
		packingPosition.setLines(List.of(orderLine));
		order.setPackingPositions(List.of(packingPosition));
	}

}
