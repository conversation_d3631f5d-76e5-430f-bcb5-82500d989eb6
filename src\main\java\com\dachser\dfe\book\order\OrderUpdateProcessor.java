package com.dachser.dfe.book.order;

import com.dachser.dfe.book.advice.AdviceService;
import com.dachser.dfe.book.dangerousgoods.DangerousGoodsTransferListService;
import com.dachser.dfe.book.exception.generic.BadRequestException;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.dachser.dfe.book.order.road.validation.StatusCompleteOrderValidator;
import com.dachser.dfe.book.quote.QuoteInformation;
import com.dachser.dfe.book.quote.validation.QuoteOrderUpdateValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
class OrderUpdateProcessor extends OrderProcessorWithUpdateBody {

	private final QuoteOrderUpdateValidator quoteOrderUpdateValidator;

	private final StatusCompleteOrderValidator statusCompleteOrderValidator;

	public OrderUpdateProcessor(final OrderRepositoryFacade orderRepositoryAccess, final OrderMapper orderMapper, final OrderLabelGenerator orderLabelGenerator,
			final List<OrderSubmitter<?>> orderSubmitters, final AdviceService adviceService, final OrderProcessCheckSwitchAndSave orderProcessCheckSwitchAndSave,
			final QuoteOrderUpdateValidator quoteOrderUpdateValidator, StatusCompleteOrderValidator statusCompleteOrderValidator, OrderDefaults orderDefaults,
			OrderLabelPrinter orderLabelPrinter, List<OrderPostProcessor<?>> postProcessors, DangerousGoodsTransferListService dangerousGoodsTransferListService) {

		super(orderRepositoryAccess, orderMapper, orderLabelGenerator, orderSubmitters, adviceService, orderProcessCheckSwitchAndSave, orderDefaults, orderLabelPrinter,
				dangerousGoodsTransferListService, postProcessors);
		this.quoteOrderUpdateValidator = quoteOrderUpdateValidator;
		this.statusCompleteOrderValidator = statusCompleteOrderValidator;
	}

	@Override
	void prepareOrderAndCheckPreconditions(final BasicOrderDto basicOrderDto) {

		final Order loadedOrder = orderRepository.loadOrderById(basicOrderDto.getOrderId());
		final QuoteInformation loadedOrderQuoteInformation = loadedOrder.getQuoteInformation();

		setOrderDto(basicOrderDto);
		setCurrentOrder(loadedOrder);
		checkCurrentOrderForExpiredQuoteOrder();
		checkOrderForAllowedPrincipalSwitch(basicOrderDto);
		statusCompleteOrderValidator.checkLockedFieldsOnCompleteRoadOrder(basicOrderDto, getCurrentOrder());

		// We can simply update the order if the order type is the same, otherwise we will update later during switch in check and save
		if (!getCurrentOrder().getOrderType().isOrderTypeSwitch(basicOrderDto.getOrderType())) {
			orderMapper.update(basicOrderDto, getCurrentOrder());
		}

		if (loadedOrderQuoteInformation != null) {
			quoteOrderUpdateValidator.validateQuoteUpdates(loadedOrderQuoteInformation, getCurrentOrder());
		}

		OrderTransitions.ensureValidTargetStatus(targetStatus(), getCurrentOrder());
	}

	private void checkOrderForAllowedPrincipalSwitch(BasicOrderDto basicOrderDto) {
		boolean isPrincipalSwitch = basicOrderDto.getCustomerNumber() != null && !basicOrderDto.getCustomerNumber().equals(getCurrentOrder().getCustomerNumber());
		if (isPrincipalSwitch && getCurrentOrder().isPrincipalLocked()) {
			throw new BadRequestException("Principal switch is not allowed for order " + getCurrentOrder().getOrderId());
		}
	}

	private OrderStatus targetStatus() {
		switch (getOrderSaveAction()) {
		case VALIDATE:
			return getCurrentOrder().validationSucceededStatus();
		case PRINT_LABELS:
			return OrderStatus.COMPLETE;
		case SUBMIT:
			return OrderStatus.SENT;
		default:
			throw new IllegalArgumentException("Unknown action: " + getOrderSaveAction());
		}
	}

}
