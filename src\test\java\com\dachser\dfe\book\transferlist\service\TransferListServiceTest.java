package com.dachser.dfe.book.transferlist.service;

import com.dachser.dfe.book.jpa.OrderSsccRepository;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderLineDetailDto;
import com.dachser.dfe.book.model.PackingPositionDto;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.model.TransferListItemDto;
import com.dachser.dfe.book.model.TransferListQueryObjectDto;
import com.dachser.dfe.book.model.TransferListResponseDto;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.orderline.RoadOrderLineRepository;
import com.dachser.dfe.book.order.generator.OrderGenerator;
import com.dachser.dfe.book.order.mapper.DangerousGoodsMapper;
import com.dachser.dfe.book.order.mapper.DangerousGoodsMapperImpl;
import com.dachser.dfe.book.order.mapper.OrderLineMapperImpl;
import com.dachser.dfe.book.overview.CachedOrderOverviewService;
import com.dachser.dfe.book.packaging.PackagingOptionsService;
import com.dachser.dfe.book.transferlist.entity.VTransferList;
import com.dachser.dfe.book.transferlist.mapper.TransferListFilterMapper;
import com.dachser.dfe.book.transferlist.mapper.TransferListFilterMapperImpl;
import com.dachser.dfe.book.transferlist.mapper.TransferListMapperImpl;
import com.dachser.dfe.book.transferlist.model.TransferListAddressType;
import com.dachser.dfe.book.transferlist.repository.TransferListFilterRepository;
import com.dachser.dfe.book.transferlist.repository.VTransferListRepository;
import com.dachser.dfe.book.user.UserContextService;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.List;

import static com.dachser.dfe.book.TestUtil.generatePackingPositionDto;
import static com.dachser.dfe.book.TestUtil.generateRoadOrderLine;
import static com.dachser.dfe.book.TestUtil.generateRoadOrderLineDto;
import static com.dachser.dfe.book.TestUtil.generateVTransferList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TransferListServiceTest {
	@InjectMocks
	private TransferListServiceImpl sut;

	@Mock
	private UserContextService userContextService;

	@Mock
	private TransferListFilterRepository transferListFilterRepository;

	@Mock
	private VTransferListRepository vTransferListRepository;

	@Mock
	private RoadOrderLineRepository roadOrderLineRepository;

	@Mock
	private RoadOrderLineService roadOrderLineService;

	@Mock
	private PackingPositionService packingPositionService;

	@Mock
	private CachedOrderOverviewService cachedOrderOverviewService;

	@Spy
	private TransferListFilterMapper filterMapper = new TransferListFilterMapperImpl();

	@Spy
	private DangerousGoodsMapper dangerousGoodsMapper = new DangerousGoodsMapperImpl();

	@Spy
	private TransferListMapperImpl transferListMapper;

	@InjectMocks
	private OrderLineMapperImpl orderLineMapper;

	@Mock
	private PackagingOptionsService packagingOptionsService;

	@Mock
	OrderSsccRepository orderSsccRepository;

	@Nested
	class GetTransferListResponsePage {

		@Test
		void mapItems() {
			TransferListQueryObjectDto queryObject = new TransferListQueryObjectDto().pickupDate(LocalDate.now()).addressHash("hash");
			int expectedTotalLabelCount = 9;
			String expectedCodeEU = "EU";
			String expectedDescriptionEU = "Euroflachpalette";
			String expectedCodeKT = "KT";
			String expectedDescriptionKT = "KT"; // not found by lookupservice so it should be the code
			OrderAddress consigneeAddress = OrderGenerator.generateOrderAddress("_consignee", "DE", false, null);
			OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", "DE", false, null);
			VTransferList vTransferList = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddress, principalAddress, null);
			List<OrderLineDetailDto> orderLineDetailDtos = orderLineMapper.mapOrderLineDetailDtos(
					List.of(generateRoadOrderLine(expectedCodeEU, "Palette", 3, null), generateRoadOrderLine(expectedCodeKT, "Carton", 6, null)));
			DeliveryProductDto product = new DeliveryProductDto().code(vTransferList.getProduct()).description("description");
			List<PackingPositionDto> packingPositions = List.of(generatePackingPositionDto(1, generateRoadOrderLineDto()),
					generatePackingPositionDto(2, generateRoadOrderLineDto()));

			when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList));
			when(cachedOrderOverviewService.getProductNameOrError(anyString(), any(Division.class))).thenReturn(product.getDescription());
			when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(orderLineDetailDtos);
			when(packagingOptionsService.getPackagingOptions(Segment.ROAD)).thenReturn(List.of(new OptionDto().code(expectedCodeEU).description(expectedDescriptionEU)));
			when(packingPositionService.getRoadPackingPositions(any())).thenReturn(packingPositions);
			when(packagingOptionsService.getPackagingOptions(Segment.ROAD)).thenReturn(List.of(new OptionDto().code(expectedCodeEU).description(expectedDescriptionEU)));
			when(orderSsccRepository.countOrderSsccsByOrderIds(any())).thenReturn(expectedTotalLabelCount);

			TransferListResponseDto transferListResponseDto = sut.getTransferList(queryObject);

			assertThat(transferListResponseDto.getTotalElements()).isEqualTo(transferListResponseDto.getContent().size());
			assertThat(transferListResponseDto.getContent()).hasSize(1);
			TransferListItemDto result = transferListResponseDto.getContent().get(0);
			verify(transferListMapper, times(1)).mapTransferList(any(VTransferList.class));

			assertThat(result.getQuantity()).isEqualTo(expectedTotalLabelCount);
			assertThat(result.getProduct().getCode()).isEqualTo(product.getCode());
			assertThat(result.getProduct().getDescription()).isEqualTo(product.getDescription());

			assertThat(result.getOrderLineItems()).hasSize(2);
			assertThat(result.getOrderLineItems().get(0).getPackaging().getCode()).isEqualTo(expectedCodeEU);
			assertThat(result.getOrderLineItems().get(0).getPackaging().getDescription()).isEqualTo(expectedDescriptionEU);
			assertThat(result.getOrderLineItems().get(1).getPackaging().getCode()).isEqualTo(expectedCodeKT);
			assertThat(result.getOrderLineItems().get(1).getPackaging().getDescription()).isEqualTo(expectedDescriptionKT);

			assertThat(result.getPackingPositions()).hasSize(2);
			assertThat(result.getPackingPositions().get(0).getPackagingType().getCode()).isEqualTo(expectedCodeEU);
			assertThat(result.getPackingPositions().get(0).getQuantity()).isEqualTo(1);
			assertThat(result.getPackingPositions().get(1).getPackagingType().getCode()).isEqualTo(expectedCodeEU);
			assertThat(result.getPackingPositions().get(1).getQuantity()).isEqualTo(2);
		}

		@Test
		void mapProduct() {
			TransferListQueryObjectDto queryObject = new TransferListQueryObjectDto().pickupDate(LocalDate.now()).addressHash("hash");
			VTransferList vTransferList = generateVTransferList(TransferListAddressType.PRINCIPAL, null, null, null);
			DeliveryProductDto product = new DeliveryProductDto().code(vTransferList.getProduct()).description("description");

			when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList));
			when(cachedOrderOverviewService.getProductNameOrError(anyString(), any(Division.class))).thenReturn(product.getDescription());
			when(orderSsccRepository.countOrderSsccsByOrderIds(any())).thenReturn(0);

			TransferListResponseDto transferListResponseDto = sut.getTransferList(queryObject);

			assertThat(transferListResponseDto.getTotalElements()).isEqualTo(transferListResponseDto.getContent().size());
			assertThat(transferListResponseDto.getContent()).hasSize(1);
			TransferListItemDto result = transferListResponseDto.getContent().get(0);

			assertThat(result.getProduct().getCode()).isEqualTo(product.getCode());
			assertThat(result.getProduct().getDescription()).isEqualTo(product.getDescription());
		}

		@Test
		void mapProductNotFound() {
			TransferListQueryObjectDto queryObject = new TransferListQueryObjectDto().pickupDate(LocalDate.now()).addressHash("hash");
			VTransferList vTransferList = generateVTransferList(TransferListAddressType.PRINCIPAL, null, null, null);
			DeliveryProductDto product = new DeliveryProductDto().code(vTransferList.getProduct()).description("description");

			when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList));
			when(cachedOrderOverviewService.getProductNameOrError(anyString(), any(Division.class))).thenReturn("ERROR");
			when(orderSsccRepository.countOrderSsccsByOrderIds(any())).thenReturn(0);

			TransferListResponseDto transferListResponseDto = sut.getTransferList(queryObject);

			assertThat(transferListResponseDto.getTotalElements()).isEqualTo(transferListResponseDto.getContent().size());
			assertThat(transferListResponseDto.getContent()).hasSize(1);
			TransferListItemDto result = transferListResponseDto.getContent().get(0);

			assertThat(result.getProduct().getCode()).isEqualTo(product.getCode());
			assertThat(result.getProduct().getDescription()).isEqualTo("ERROR");
		}

		@Test
		void mapProductNull() {
			TransferListQueryObjectDto queryObject = new TransferListQueryObjectDto().pickupDate(LocalDate.now()).addressHash("hash");
			VTransferList vTransferList = generateVTransferList(TransferListAddressType.PRINCIPAL, null, null, null);
			vTransferList.setProduct(null);

			when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList));
			when(orderSsccRepository.countOrderSsccsByOrderIds(any())).thenReturn(0);

			TransferListResponseDto transferListResponseDto = sut.getTransferList(queryObject);

			assertThat(transferListResponseDto.getTotalElements()).isEqualTo(transferListResponseDto.getContent().size());
			assertThat(transferListResponseDto.getContent()).hasSize(1);
			TransferListItemDto result = transferListResponseDto.getContent().get(0);

			assertThat(result.getProduct().getCode()).isEqualTo(vTransferList.getProduct());
			assertThat(result.getProduct().getDescription()).isNull();
		}

		@Test
		void sortItems() {
			TransferListQueryObjectDto queryObject = new TransferListQueryObjectDto().pickupDate(LocalDate.now()).addressHash("hash");
			OrderAddress consigneeAddress1 = OrderGenerator.generateOrderAddress("_consignee_1", "DE", false, null);
			OrderAddress consigneeAddress2 = OrderGenerator.generateOrderAddress("_consignee_2", "DE", false, null);
			OrderAddress consigneeAddress3 = OrderGenerator.generateOrderAddress("_consignee_3", "DE", false, null);
			OrderAddress consigneeAddress4 = OrderGenerator.generateOrderAddress("_consignee_4", "DE", false, null);
			consigneeAddress4.setName(null);
			OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", "DE", false, null);
			VTransferList vTransferList1 = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddress1, principalAddress, null);
			VTransferList vTransferList2 = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddress2, principalAddress, null);
			VTransferList vTransferList3 = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddress3, principalAddress, null);
			VTransferList vTransferList4 = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddress4, principalAddress, null);
			List<OrderLineDetailDto> orderLineDetailDtos = orderLineMapper.mapOrderLineDetailDtos(List.of(generateRoadOrderLine(), generateRoadOrderLine()));
			DeliveryProductDto product = new DeliveryProductDto().code("X").description("description");

			when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList4, vTransferList3, vTransferList1, vTransferList2));
			when(cachedOrderOverviewService.getProductNameOrError(anyString(), any(Division.class))).thenReturn(product.getDescription());
			when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(orderLineDetailDtos);
			when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(orderLineDetailDtos);
			when(packagingOptionsService.getPackagingOptions(Segment.ROAD)).thenReturn(List.of(new OptionDto().code("EU").description("Euroflachpalette")));
			when(orderSsccRepository.countOrderSsccsByOrderIds(any())).thenReturn(0);

			TransferListResponseDto transferListResponseDto = sut.getTransferList(queryObject);

			assertThat(transferListResponseDto.getTotalElements()).isEqualTo(transferListResponseDto.getContent().size());
			assertThat(transferListResponseDto.getContent()).hasSize(4);
			assertThat(transferListResponseDto.getContent().get(0).getConsigneeAddress().getName()).isEqualTo(consigneeAddress1.getName());
			assertThat(transferListResponseDto.getContent().get(1).getConsigneeAddress().getName()).isEqualTo(consigneeAddress2.getName());
			assertThat(transferListResponseDto.getContent().get(2).getConsigneeAddress().getName()).isEqualTo(consigneeAddress3.getName());
			assertThat(transferListResponseDto.getContent().get(3).getConsigneeAddress().getName()).isEqualTo(consigneeAddress4.getName());
		}

	}
}

