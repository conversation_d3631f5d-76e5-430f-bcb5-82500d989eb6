package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.mapper.PersistentListMerger;
import com.dachser.dfe.book.model.AirSeaOrderReferenceDto;
import com.dachser.dfe.book.model.BasicQuoteInformationDto;
import com.dachser.dfe.book.model.OrderReferenceTypeDto;
import com.dachser.dfe.book.model.RoadOrderReferenceDto;
import com.dachser.dfe.book.order.OrderReferenceHelper;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.air.AirOrderReference;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.book.order.sea.SeaOrderReference;
import com.dachser.dfe.book.quote.QuoteInformation;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.ValueMapping;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface OrderReferenceMapper {

	@Mapping(source = "id", target = "orderReferenceId")
	@Mapping(source = "orderReferenceDto", target = "referenceSubtype", qualifiedByName = "mapRoadReferenceSubtype")
	@Mapping(source = "referenceValue", target = "reference")
	RoadOrderReference mapRoadReference(RoadOrderReferenceDto orderReferenceDto);

	@ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL)
	ReferenceType mapReferenceType(OrderReferenceTypeDto orderReferenceDto);

	@InheritInverseConfiguration(name = "mapReferenceType")
	@ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL)
	OrderReferenceTypeDto mapReferenceType(ReferenceType roadOrderReferenceType);

	@Mapping(source = "id", target = "orderReferenceId")
	AirOrderReference mapAirReference(AirSeaOrderReferenceDto orderReferenceDto);

	@InheritInverseConfiguration(name = "mapAirReference")
	AirSeaOrderReferenceDto mapAirReference(AirOrderReference orderReferenceDto);

	@Mapping(source = "id", target = "orderReferenceId")
	SeaOrderReference mapSeaReference(AirSeaOrderReferenceDto orderReferenceDto);

	@InheritInverseConfiguration(name = "mapSeaReference")
	AirSeaOrderReferenceDto mapSeaReference(SeaOrderReference orderReferenceDto);

	@ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL)
	AirSeaOrderReferenceType mapAirSeaReferenceType(OrderReferenceTypeDto orderReferenceDto);

	@InheritInverseConfiguration(name = "mapAirSeaReferenceType")
	@ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL)
	OrderReferenceTypeDto mapAirSeaReferenceType(AirSeaOrderReferenceType orderReferenceDto);

	@InheritConfiguration(name = "mapRoadReference")
	RoadOrderReference updateReference(RoadOrderReferenceDto orderReferenceDto, @MappingTarget RoadOrderReference roadOrderReference);

	List<RoadOrderReference> mapRoadOrderReferences(List<RoadOrderReferenceDto> orderReference);

	@InheritInverseConfiguration(name = "mapRoadReference")
	RoadOrderReferenceDto mapOrderReference(RoadOrderReference roadOrderReference);

	@InheritConfiguration(name = "mapAirReference")
	AirOrderReference updateAirOrderReferences(AirSeaOrderReferenceDto aslOrderReferenceDto, @MappingTarget AirOrderReference reference);

	@InheritConfiguration(name = "mapSeaReference")
	SeaOrderReference updateSeaOrderReferences(AirSeaOrderReferenceDto aslOrderReferenceDto, @MappingTarget SeaOrderReference reference);

	default void mapRoadOrderReferences(List<RoadOrderReferenceDto> orderTextDto, @MappingTarget List<RoadOrderReference> orderTexts) {
		final PersistentListMerger<RoadOrderReference, RoadOrderReferenceDto> listMapper = this::updateReference;
		listMapper.updateList(orderTextDto, orderTexts, RoadOrderReference::getOrderReferenceId, RoadOrderReferenceDto::getId, RoadOrderReference::new);
	}

	default void mapAirReferences(List<AirSeaOrderReferenceDto> aslReferencesDto, @MappingTarget List<AirOrderReference> references) {
		final PersistentListMerger<AirOrderReference, AirSeaOrderReferenceDto> listMapper = this::updateAirOrderReferences;
		listMapper.updateList(aslReferencesDto, references, AirOrderReference::getOrderReferenceId, AirSeaOrderReferenceDto::getId, AirOrderReference::new);
	}

	default void mapSeaReferences(List<AirSeaOrderReferenceDto> aslReferencesDto, @MappingTarget List<SeaOrderReference> references) {
		final PersistentListMerger<SeaOrderReference, AirSeaOrderReferenceDto> listMapper = this::updateSeaOrderReferences;
		listMapper.updateList(aslReferencesDto, references, SeaOrderReference::getOrderReferenceId, AirSeaOrderReferenceDto::getId, SeaOrderReference::new);
	}

	@AfterMapping
	default void mapRoadQuoteReference(BasicQuoteInformationDto quoteInformation, @MappingTarget RoadOrder order) {
		final List<RoadOrderReference> orderReferences = new ArrayList<>();

		if (quoteInformation.getQuoteReference() != null) {
			final RoadOrderReference reference = new RoadOrderReference();
			reference.setReference(quoteInformation.getQuoteReference());
			reference.setReferenceType(ReferenceType.DAILY_PRICE_REFERENCE);
			reference.setOrder(order);

			orderReferences.add(reference);
		}
		order.setOrderReferences(orderReferences);
	}

	@AfterMapping
	default void mapAirQuoteReference(BasicQuoteInformationDto quoteInformation, @MappingTarget AirOrder order) {
		final List<AirOrderReference> orderReferences = new ArrayList<>();

		final AirOrderReference reference = new AirOrderReference();
		reference.setReferenceValue(quoteInformation.getQuoteReference());
		reference.setReferenceType(AirSeaOrderReferenceType.QUOTATION_REFERENCE);
		reference.setOrder(order);

		orderReferences.add(reference);
		order.setOrderReferences(orderReferences);
	}

	@AfterMapping
	default void mapSeaQuoteReference(BasicQuoteInformationDto quoteInformation, @MappingTarget SeaOrder order) {
		final List<SeaOrderReference> orderReferences = new ArrayList<>();

		final SeaOrderReference reference = new SeaOrderReference();
		reference.setReferenceValue(quoteInformation.getQuoteReference());
		reference.setReferenceType(AirSeaOrderReferenceType.QUOTATION_REFERENCE);
		reference.setOrder(order);

		orderReferences.add(reference);
		order.setOrderReferences(orderReferences);
	}

	@AfterMapping
	default void mapInverseRoadQuoteReference(@MappingTarget QuoteInformation quoteInformation, RoadOrder order) {
		if (order.getOrderReferences() != null && !order.getOrderReferences().isEmpty()) {
			Optional<RoadOrderReference> quoteReferenceOpt = order.getOrderReferences().stream().filter(ref -> ref.getReferenceType().equals(ReferenceType.DAILY_PRICE_REFERENCE))
					.findFirst();
			quoteReferenceOpt.map(RoadOrderReference::getReference).ifPresent(quoteInformation::setQuoteReference);
		}
	}

	@AfterMapping
	default void mapInverseAirQuoteReference(@MappingTarget QuoteInformation quoteInformation, AirOrder order) {
		if (order.getOrderReferences() != null && !order.getOrderReferences().isEmpty()) {
			Optional<AirOrderReference> quoteReferenceOpt = order.getOrderReferences().stream()
					.filter(ref -> ref.getReferenceType().equals(AirSeaOrderReferenceType.QUOTATION_REFERENCE)).findFirst();
			quoteReferenceOpt.map(AirOrderReference::getReferenceValue).ifPresent(quoteInformation::setQuoteReference);
		}
	}

	@AfterMapping
	default void stripPrefixFromRoadReferenceValue(@MappingTarget RoadOrderReference.RoadOrderReferenceBuilder orderReferenceBuilder) {
		orderReferenceBuilder.reference(stripPrefixFromRoadReferenceValue(orderReferenceBuilder.build()).getReference());
	}

	@AfterMapping
	default RoadOrderReference stripPrefixFromRoadReferenceValue(@MappingTarget RoadOrderReference orderReference) {
		if (orderReference != null) {
			orderReference.setReference(OrderReferenceHelper.stripSubtypePrefix(orderReference.getReferenceSubtype(), orderReference.getReference()));
		}
		return orderReference;
	}

	@AfterMapping
	default void prependRoadReferenceValue(@MappingTarget RoadOrderReferenceDto orderReferenceDto, RoadOrderReference roadOrderReference) {
		if (orderReferenceDto != null && roadOrderReference != null && roadOrderReference.getReferenceSubtype() != null) {
			orderReferenceDto.setReferenceValue(
					OrderReferenceHelper.prefix(roadOrderReference.getReferenceSubtype()).concat(StringUtils.defaultString(orderReferenceDto.getReferenceValue())));
		}
	}

	@Named("mapRoadReferenceSubtype")
	default RoadOrderReferenceSubtype mapRoadReferenceSubtype(RoadOrderReferenceDto orderReferenceDto) {
		AtomicReference<RoadOrderReferenceSubtype> roadReferenceSubtype = new AtomicReference<>();
		if (orderReferenceDto != null) {
			OrderReferenceHelper.roadSubtype(orderReferenceDto.getReferenceValue(), mapReferenceType(orderReferenceDto.getReferenceType())).ifPresent(roadReferenceSubtype::set);
		}
		return roadReferenceSubtype.get();
	}

}
