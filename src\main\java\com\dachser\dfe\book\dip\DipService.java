package com.dachser.dfe.book.dip;

import com.dachser.dfe.book.dip.converter.DipOrderConverter;
import com.dachser.dfe.book.dip.publisher.DipPublisher;
import com.dachser.dfe.book.dip.repository.OrderXml;
import com.dachser.dfe.book.dip.repository.OrderXmlRepository;
import com.dachser.dfe.book.dip.xml.XmlFileStorageProvider;
import com.dachser.dfe.book.dip.xml.XmlPublishStatus;
import com.dachser.dfe.book.document.DocumentRepositoryFacade;
import com.dachser.dfe.book.document.DocumentStatus;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.service.BaseEntityService;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class DipService {

	private final XmlFileStorageProvider xmlFileStorage;

	private final DipPublisher dipPublisher;

	private final OrderXmlRepository orderXmlRepository;

	private final List<DipOrderConverter<? extends Order>> converters;

	private final DocumentRepositoryFacade documentRepository;

	@Transactional
	public boolean createAndPublishXmlForOrder(Order order) {
		Optional<Entry<OrderXml, String>> orderXmlEntry = convertToAndSaveOrderXml(order);

		if (orderXmlEntry.isPresent()) {
			final boolean publishImmediately = !isFutureForwardingOrder(order);

			if (publishImmediately) {
				final OrderXml orderXml = orderXmlEntry.get().getKey();
				final String xmlContent = orderXmlEntry.get().getValue();
				return tryToPublish(xmlContent, orderXml);
			} else {
				log.info("Order #{} with shipmentNumber {} has been saved for scheduled publishing", order.getOrderId(), order.getShipmentNumber());
				return true;
			}

		} else {
			log.warn("Could not create XML for order #{}", order.getOrderId());
			return false;
		}

	}

	private boolean isFutureForwardingOrder(Order order) {
		return OrderType.ROADFORWARDINGORDER.equals(order.getOrderType()) && order.getCollectionDate().isAfter(LocalDate.now());
	}

	@Transactional
	boolean tryToPublish(String xmlFile, OrderXml orderXml) {
		boolean isPublished = false;
		try {
			isPublished = dipPublisher.publishObject(xmlFile, orderXml.getCustomerNumber(), orderXml.getShipmentNumber(), orderXml.getOrderType(),
					orderXml.getShipmentTransferId());

			log.info("Order #{} with transferId {} has been submitted to EDI successful={}", orderXml.getOrderId(), orderXml.getShipmentTransferId(), isPublished);
		} catch (Exception sendException) {
			log.error("Order #{} has not been submitted to EDI. Add to retry orders", orderXml.getOrderId());
		}

		if (isPublished) {
			setDoneEntry(orderXml);
			updateDocumentStatusForSubmittedOrder(orderXml);
			log.info("Documents for order #{} have been set ready for submission", orderXml.getOrderId());
		} else {
			setRetryCount(orderXml);
			log.info("Order #{} has been added to retry orders", orderXml.getOrderId());
		}
		return isPublished;
	}

	private Optional<Entry<OrderXml, String>> convertToAndSaveOrderXml(@NonNull Order order) {
		// Unfortunate cast to Order is necessary because of the generic type of DipOrderConverter and List<DipOrderConverter<Order>> will not receive all possible implementations
		Optional<DipOrderConverter<Order>> orderConverter = converters.stream().filter(c -> c.supports(order)).findFirst().map(c -> (DipOrderConverter<Order>) c);

		if (orderConverter.isPresent()) {
			final DipOrderConverter<Order> dipOrderConverter = orderConverter.get();
			final String xmlContent = dipOrderConverter.convertToXML(order);

			Optional<OrderXml> orderXml = StringUtils.isNoneBlank(xmlContent) ? createAndSave(xmlContent, order) : Optional.empty();

			if (orderXml.isPresent()) {
				return Optional.of(Map.entry(orderXml.get(), xmlContent));
			}
		} else {
			log.warn("No DIP converter found for order class {}", order.getClass());
		}
		return Optional.empty();
	}

	private void updateDocumentStatusForSubmittedOrder(OrderXml orderXml) {
		documentRepository.findByOrderId(orderXml.getOrderId()).stream().filter(document -> document.getStatus().equals(DocumentStatus.NEW) && document.getDocumentType() != null)
				.forEach(document -> {
					document.setStatus(DocumentStatus.READY);
					documentRepository.save(document);
					log.info("Document #{} for order #{} has been updated for submission", document.getDocumentId(), orderXml.getOrderId());
				});
	}

	private void setRetryCount(OrderXml orderXml) {
		orderXml.setRetryCount(orderXml.getRetryCount() + 1);
		orderXmlRepository.save(orderXml);
	}

	private void setDoneEntry(OrderXml orderXml) {
		orderXml.setStatus(XmlPublishStatus.DONE);
		orderXml.setSendAt(Instant.now());
		orderXmlRepository.save(orderXml);
	}

	private Optional<OrderXml> createAndSave(@NonNull String xmlContent, Order order) {
		Optional<OrderXml> savedOrderXml = Optional.empty();

		try {
			final Long shipmentNumber = order.getShipmentNumber();
			// write the actual xml file to the file system
			xmlFileStorage.saveXml(xmlContent, shipmentNumber);

			// try to find existing entry with status READY for customer & shipmentNumber & orderId
			final Optional<OrderXml> optionalOrderXml = orderXmlRepository.findByStatusAndCustomerNumberAndShipmentNumberAndOrderId(XmlPublishStatus.READY,
					order.getCustomerNumber(), shipmentNumber, order.getOrderId());

			// if entry exists, update it, otherwise create new entry
			if (optionalOrderXml.isPresent()) {
				updateOrderXmlForOrder(optionalOrderXml.get(), order);
			}
			final OrderXml xmlEntry = optionalOrderXml.orElse(createOrderXmlFromOrder(order));

			savedOrderXml = Optional.of(orderXmlRepository.save(xmlEntry));

			// log if entry has been updated or created
			if (optionalOrderXml.isPresent()) {
				log.info("Xml scheduler entry for shipmentNumber {} has been updated", shipmentNumber);
			} else {
				log.info("Xml scheduler entry for shipmentNumber {} has been created", shipmentNumber);
			}
		} catch (IOException ioException) {
			log.error("#ERROR-SUBMISSION-SAVE - Creation of XML file with shipmentNumber {} failed.", order.getShipmentNumber());
		}
		return savedOrderXml;
	}

	private static void updateOrderXmlForOrder(OrderXml orderXml, Order order) {
		orderXml.setLastEditor(order.getLastEditor());
		orderXml.setLastModified(Instant.now());
		orderXml.setPublishAt(order.getCollectionDate().atStartOfDay().toInstant(ZoneOffset.UTC));
	}

	@NotNull
	private static OrderXml createOrderXmlFromOrder(Order order) {
		final OrderXml orderXml = new OrderXml();
		BaseEntityService.fillCreationFields(orderXml, order.getLastEditor());
		orderXml.setShipmentTransferId(UUID.randomUUID().toString());
		orderXml.setCustomerNumber(order.getCustomerNumber());
		orderXml.setShipmentNumber(order.getShipmentNumber());
		orderXml.setOrderId(order.getOrderId());
		orderXml.setOrderType(DipOrderType.fromBookOrderType(order.getOrderType()));
		orderXml.setStatus(XmlPublishStatus.READY);
		orderXml.setRetryCount(0);
		orderXml.setSendAt(null);
		orderXml.setPublishAt(order.getCollectionDate().atStartOfDay().toInstant(ZoneOffset.UTC));
		return orderXml;
	}
}
