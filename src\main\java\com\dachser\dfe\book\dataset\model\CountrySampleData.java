package com.dachser.dfe.book.dataset.model;

import java.util.List;

public record CountrySampleData(String isoCountryCode, String description, String examplePostcode) {

	//@formatter:off
	public static List<CountrySampleData> getCountryRecords() {
		return List.of(
				new CountrySampleData("AD", "Andorra", "AD500"),
				new CountrySampleData("AE", "United Arab Emirates", "00000"),
				new CountrySampleData("AF", "Afghanistan", "1001"),
				new CountrySampleData("AG", "Antigua and Barbuda", "noPostcode"),
				new CountrySampleData("AI", "Anguilla", "noPostcode"),
				new CountrySampleData("AL", "Albania", "1001"),
				new CountrySampleData("AM", "Armenia", "375010"),
				new CountrySampleData("AO", "Angola", "noPostcode"),
				new CountrySampleData("AR", "Argentina", "C1001"),
				new CountrySampleData("AT", "Austria", "1010"),
				new CountrySampleData("AU", "Australia", "2000"),
				new CountrySampleData("AZ", "Azerbaijan", "1000"),
				new CountrySampleData("BA", "Bosnia and Herzegovina", "71000"),
				new CountrySampleData("BD", "Bangladesh", "1000"),
				new CountrySampleData("BE", "Belgium", "1000"),
				new CountrySampleData("BG", "Bulgaria", "1000"),
				new CountrySampleData("BH", "Bahrain", "199"),
				new CountrySampleData("BR", "Brazil", "01000-000"),
				new CountrySampleData("BY", "Belarus", "220030"),
				new CountrySampleData("CA", "Canada", "K1A 0B1"),
				new CountrySampleData("CH", "Switzerland", "8001"),
				new CountrySampleData("CN", "China", "100000"),
				new CountrySampleData("CO", "Colombia", "110111"),
				new CountrySampleData("CU", "Cuba", "10100"),
				new CountrySampleData("CY", "Cyprus", "1010"),
				new CountrySampleData("CZ", "Czech Republic", "110 00"),
				new CountrySampleData("DE", "Germany", "10115"),
				new CountrySampleData("DK", "Denmark", "1000"),
				new CountrySampleData("DO", "Dominican Republic", "10101"),
				new CountrySampleData("DZ", "Algeria", "16000"),
				new CountrySampleData("EC", "Ecuador", "170150"),
				new CountrySampleData("EE", "Estonia", "10111"),
				new CountrySampleData("EG", "Egypt", "11511"),
				new CountrySampleData("ES", "Spain", "28001"),
				new CountrySampleData("FI", "Finland", "00100"),
				new CountrySampleData("FR", "France", "75001"),
				new CountrySampleData("GB", "United Kingdom", "SW1A 1AA"),
				new CountrySampleData("GR", "Greece", "10557"),
				new CountrySampleData("HU", "Hungary", "1051"),
				new CountrySampleData("ID", "Indonesia", "10110"),
				new CountrySampleData("IE", "Ireland", "D01 F5P2"),
				new CountrySampleData("IL", "Israel", "61000"),
				new CountrySampleData("IN", "India", "110001"),
				new CountrySampleData("IS", "Iceland", "101"),
				new CountrySampleData("IT", "Italy", "00100"),
				new CountrySampleData("JP", "Japan", "100-0001"),
				new CountrySampleData("KR", "South Korea", "04524"),
				new CountrySampleData("LT", "Lithuania", "01108"),
				new CountrySampleData("LU", "Luxembourg", "1111"),
				new CountrySampleData("LV", "Latvia", "LV-1010"),
				new CountrySampleData("MX", "Mexico", "01000"),
				new CountrySampleData("MY", "Malaysia", "50000"),
				new CountrySampleData("NL", "Netherlands", "1000 AA"),
				new CountrySampleData("NO", "Norway", "0150"),
				new CountrySampleData("NZ", "New Zealand", "6011"),
				new CountrySampleData("PH", "Philippines", "1000"),
				new CountrySampleData("PL", "Poland", "00-001"),
				new CountrySampleData("PT", "Portugal", "1000-001"),
				new CountrySampleData("RU", "Russia", "101000"),
				new CountrySampleData("SE", "Sweden", "111 20"),
				new CountrySampleData("SG", "Singapore", "018989"),
				new CountrySampleData("TH", "Thailand", "10330"),
				new CountrySampleData("TR", "Turkey", "34010"),
				new CountrySampleData("US", "USA", "10001"),
				new CountrySampleData("ZA", "South Africa", "0001")
		);
	}
	//@formatter:on
}
