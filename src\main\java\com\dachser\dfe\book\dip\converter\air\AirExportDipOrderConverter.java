package com.dachser.dfe.book.dip.converter.air;

import com.dachser.dfe.book.dip.converter.DipOrderConverter;
import com.dachser.dfe.book.dip.xml.XmlConverter;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.order.air.AirExportOrder;
import org.springframework.stereotype.Component;

@Component
public class AirExportDipOrderConverter extends DipOrderConverter<AirExportOrder> {

	private final AirOrderMapper mapper;

	public AirExportDipOrderConverter(XmlConverter xmlConverter, AirOrderMapper mapper) {
		super(xmlConverter);
		this.mapper = mapper;
	}

	@Override
	protected ForwardingOrder map(AirExportOrder order) {
		return mapper.map(order);
	}
}
