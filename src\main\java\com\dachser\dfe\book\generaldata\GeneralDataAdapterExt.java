package com.dachser.dfe.book.generaldata;

import com.dachser.dfe.book.cache.CacheNames;
import com.dachser.dfe.book.country.Country;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ErrorIdExternalServiceNotAvailableException;
import com.dachser.dfe.book.generaldata.country.GeneralDataCountryMapper;
import com.dachser.dfe.book.generaldata.packaging.PackagingOptionMapper;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.user.BusinessDomainProvider;
import com.dachser.dfe.generaldata.api.CountryApi;
import com.dachser.dfe.generaldata.api.DeliveryApi;
import com.dachser.dfe.generaldata.api.LanguageApi;
import com.dachser.dfe.generaldata.api.PackagingApi;
import com.dachser.dfe.generaldata.api.TermsApi;
import com.dachser.dfe.generaldata.model.GDAirProductDto;
import com.dachser.dfe.generaldata.model.GDContainerTypeDto;
import com.dachser.dfe.generaldata.model.GDCountryDto;
import com.dachser.dfe.generaldata.model.GDDeliveryProductDto;
import com.dachser.dfe.generaldata.model.GDPackagingOptionDto;
import com.dachser.dfe.generaldata.model.GDSegmentDto;
import com.dachser.dfe.generaldata.model.GDTermDto;
import com.dachser.dfe.generaldata.model.GDTermTypeDto;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(value = "dfe.book.mock", havingValue = "false", matchIfMissing = true)
class GeneralDataAdapterExt implements GeneralDataAdapter {

	private final CountryApi generalCountryApi;

	private final TermsApi termsApi;

	private final GeneralDataCountryMapper countryMapper;

	private final PackagingApi packagingApi;

	private final BusinessDomainProvider businessDomainProvider;

	private final PackagingOptionMapper packagingOptionMapper;

	private final DeliveryApi deliveryApi;

	private final DeliveryProductMapper deliveryProductMapper;

	private final LanguageApi languageApi;

	@Override
	@Cacheable(cacheNames = CacheNames.GENERAL_DATA_MASTERDATA_LANGUAGES)
	public Map<String, String> getLanguages() {
		try {
			log.debug("Calling General Data Language Api");
			return languageApi.getLanguageMapping();
		} catch (final RestClientException restClientException) {
			log.error("GeneralDataService service (languages) not available with message: {}", restClientException.getMessage());
			throw new ErrorIdExternalServiceNotAvailableException(BookErrorId.ERR_AL_21, restClientException.getMessage());
		}
	}

	@Override
	@Cacheable(cacheNames = CacheNames.GENERAL_DATA_TERMS)
	public List<GDTermDto> getAllActiveTerms(@Nullable GDTermTypeDto termType) {
		try {
			log.debug("Getting all active terms for {}", termType);
			return termsApi.getTerms(termType, false);
		} catch (final RestClientException restClientException) {
			log.error("GeneralDataService service (terms) not available with message: {}", restClientException.getMessage());
			throw new ErrorIdExternalServiceNotAvailableException(BookErrorId.ERR_AL_21, restClientException.getMessage());
		}
	}

	@Override
	@Cacheable(cacheNames = CacheNames.GENERAL_DATA_CONTAINER_TYPES)
	public List<GDContainerTypeDto> getContainerTypes() {
		try {
			log.debug("Calling General Data Packaging Api");
			return packagingApi.getContainerTypes();
		} catch (final RestClientException ex) {
			log.error("GeneralDataService service (container-types) not available with message: {}", ex.getMessage());
			throw new ErrorIdExternalServiceNotAvailableException(BookErrorId.ERR_AL_21, ex.getMessage());
		}
	}

	@Override
	@Cacheable(cacheNames = CacheNames.GENERAL_DATA_COUNTRIES)
	public List<Country> getCountries(String language) {
		try {
			log.debug("Calling General Data Countries Api for language: {}", language);
			List<GDCountryDto> countries = generalCountryApi.getCountries(language);
			return countryMapper.map(countries);
		} catch (RestClientException restClientException) {
			log.error("GeneralDataService service (countries) not available with message: {}", restClientException.getMessage());
			throw new ErrorIdExternalServiceNotAvailableException(BookErrorId.ERR_AL_21, restClientException.getMessage());
		}
	}

	@Override
	@Cacheable(cacheNames = CacheNames.GENERAL_DATA_PACKAGING_AIR)
	public List<OptionDto> getPackagingOptionsAir(String language) {
		try {
			List<GDPackagingOptionDto> packagingOptionDtos = packagingApi.getPackagingOptionsV2(businessDomainProvider.getBusinessDomain(), language, GDSegmentDto.AIR);
			return packagingOptionMapper.mapList(packagingOptionDtos);
		} catch (RestClientException restClientException) {
			log.error("GeneralDataService service (packaging-options-air) not available with message: {}", restClientException.getMessage());
			throw new ErrorIdExternalServiceNotAvailableException(BookErrorId.ERR_PA_02, restClientException.getMessage());
		}
	}

	@Override
	@Cacheable(cacheNames = CacheNames.GENERAL_DATA_PACKAGING_ROAD)
	public List<OptionDto> getPackagingOptionsRoad(String language) {
		try {
			List<GDPackagingOptionDto> packagingOptionDtos = packagingApi.getPackagingOptionsV2(businessDomainProvider.getBusinessDomain(), language, GDSegmentDto.ROAD);
			return packagingOptionMapper.mapList(packagingOptionDtos);
		} catch (RestClientException restClientException) {
			log.error("GeneralDataService service (packaging-options-road) not available with message: {}", restClientException.getMessage());
			throw new ErrorIdExternalServiceNotAvailableException(BookErrorId.ERR_PA_02, restClientException.getMessage());
		}
	}

	@Cacheable(cacheNames = CacheNames.GENERAL_DATA_DELIVERY_PRODUCTS_ROAD)
	@Override
	public List<DeliveryProductDto> getDeliveryProductsForDivision(Division division, String language) {
		try {
			List<GDDeliveryProductDto> deliveryProductsV1 = deliveryApi.getDivisionDeliveryProductsV1(division.getDivisionName(), businessDomainProvider.getBusinessDomain(),
					language);
			return deliveryProductMapper.mapList(deliveryProductsV1);
		} catch (RestClientException restClientException) {
			log.error("GeneralDataService service (delivery-options-road) not available with message: {}", restClientException.getMessage());
			throw new ErrorIdExternalServiceNotAvailableException(BookErrorId.ERR_PA_02, restClientException.getMessage());
		}
	}

	@Override
	@Cacheable(cacheNames = CacheNames.GENERAL_DATA_DELIVERY_PRODUCTS_AIR)
	public List<AirProductDto> getAirProducts(Boolean includeDeactivated) {
		List<GDAirProductDto> airProducts = deliveryApi.getAirProducts(includeDeactivated);
		return deliveryProductMapper.mapAirProductList(airProducts);
	}
}


