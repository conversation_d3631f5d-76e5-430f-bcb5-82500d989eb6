package com.dachser.dfe.book.generaldata;

import com.dachser.dfe.book.country.Country;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ErrorIdExternalServiceNotAvailableException;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.generaldata.model.GDContainerTypeDto;
import com.dachser.dfe.generaldata.model.GDTermDto;
import com.dachser.dfe.generaldata.model.GDTermTypeDto;
import org.jetbrains.annotations.Nullable;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@ConditionalOnProperty(value = "dfe.book.mock", havingValue = "true")
public class GeneralDataAdapterMock implements GeneralDataAdapter {

	private static final GeneralDataAdapter INSTANCE = new GeneralDataAdapterMock();

	public static final String ERROR_LANG = "xx";

	@Override
	public Map<String, String> getLanguages() {
		// @formatter:off
		return Map.ofEntries(
				Map.entry("de", " "),
				Map.entry("en", "001"),
				Map.entry("fr", "002"),
				Map.entry("es", "003"),
				Map.entry("pt", "004"),
				Map.entry("it", "005"),
				Map.entry("nl", "006"),
				Map.entry("dk", "007"),
				Map.entry("hu", "008"),
				Map.entry("cs", "009"),
				Map.entry("sl", "010"),
				Map.entry("pl", "011"),
				Map.entry("sv", "012"),
				Map.entry("no", "013"),
				Map.entry("ro", "018"),
				Map.entry("fi", "021")
		);
		// @formatter:on
	}

	@Override
	public List<Country> getCountries(String language) {
		if (ERROR_LANG.equals(language)) {
			throw new ErrorIdExternalServiceNotAvailableException(BookErrorId.ERR_AL_10, "Country service not available");
		}
		// @formatter:off
			return List.of(
					new Country("DE", "Deutschland", "D", true),
					new Country("FR", "Frankreich", "F", true),
					new Country("ES", "Spanien", "E", false),
					new Country("IE", "Ireland", "IRL", true),
					new Country("IT", "Italien", "I", true),
					new Country("CN", "China", "NL", true)
			);
			// @formatter:on
	}

	@Override
	public List<GDTermDto> getAllActiveTerms(@Nullable GDTermTypeDto termType) {
		if (termType == null) {
			return createFullTermList();
		} else if (termType.equals(GDTermTypeDto.FREIGHT)) {
			return createFreightTerms();
		} else {
			return createIncoTerms();
		}
	}

	public static int getCountriesCount() {
		return INSTANCE.getCountries("en").size();
	}

	public static int getMasterdataLanguagesCount() {
		return INSTANCE.getLanguages().size();
	}

	private List<GDTermDto> createFreightTerms() {
		return createFullTermList().stream().filter(term -> term.getFreightTerm() != null).toList();
	}

	private List<GDTermDto> createIncoTerms() {
		return createFullTermList().stream().filter(term -> term.getIncoTerm() != null).toList();
	}

	private List<GDTermDto> createFullTermList() {
		// @formatter:off
		// Data is copied from general data service including correct sorting of entries
		return List.of(
				new GDTermDto().dachserCode("EXW").freightTerm("011").incoTerm("EXW").label("Ex Works").description(""),
				new GDTermDto().dachserCode("DPU").freightTerm("031").incoTerm("DPU").label("Delivered At Place Unloaded").description(""),
				new GDTermDto().dachserCode("DAP").freightTerm("081").incoTerm("DAP").label("Delivered At Place").description("Cargo is delivered (but not unloaded) at destination location. The buyer is in charge of the customs clearance and unloading."),
				new GDTermDto().dachserCode("082").freightTerm("082").incoTerm("DDP").label("Delivered Duty Paid excl. Duty, excl. Tax").description(""),
				new GDTermDto().dachserCode("DD4").freightTerm("083").incoTerm("DDP").label("Delivered Duty Paid incl. Duty, excl. Tax").description("Duties are not part of our quotation. Incoterm refers to the requested service only."),
				new GDTermDto().dachserCode("DD5").freightTerm("084").incoTerm("DDP").label("Delivered Duty Paid incl. Duty, incl. Tax").description("Duties and Taxes are not part of our quotation. Incoterm refers to the requested service only."),
				new GDTermDto().dachserCode("FC1").freightTerm(null).incoTerm("FCA").label("Free Carrier Pick up Address").description(""),
				new GDTermDto().dachserCode("FOA").freightTerm(null).incoTerm("FCA").label("Free Carrier Departure Airport").description(""),
				new GDTermDto().dachserCode("CPT").freightTerm(null).incoTerm("CPT").label("Carriage Paid To").description("The seller pays all fees until the destination location."),
				new GDTermDto().dachserCode("CIP").freightTerm(null).incoTerm("CIP").label("Carriage and Insurance Paid To").description("Similar to CPT. In that case, seller pays also the insurance for the transportation."),
				new GDTermDto().dachserCode("CFR").freightTerm(null).incoTerm("CFR").label("Cost and Freight").description(""),
				new GDTermDto().dachserCode("CIF").freightTerm(null).incoTerm("CIF").label("Cost, Insurance and Freight").description(""),
				new GDTermDto().dachserCode("FOB").freightTerm(null).incoTerm("FOB").label("Free on Board").description(""),
				new GDTermDto().dachserCode("FAS").freightTerm(null).incoTerm("FAS").label("Free Alongside Ship").description(""),
				new GDTermDto().freightTerm("036").incoTerm(null).label("free delivered to customer specified unloading point").description(""),
				new GDTermDto().freightTerm("085").incoTerm(null).label("free delivered, insured - uncleared excluding duty and tax").description(""),
				new GDTermDto().freightTerm("088").incoTerm(null).label("free delivered cleared at border").description(""),
				new GDTermDto().freightTerm("999").incoTerm(null).label("Without Charge").description(""),
				new GDTermDto().dachserCode("FCA").freightTerm(null).incoTerm("FCA").label("Free Carrier").description(""));
		// @formatter:on
	}

	@Override
	public List<GDContainerTypeDto> getContainerTypes() {

		GDContainerTypeDto containerGP20 = new GDContainerTypeDto();
		containerGP20.setKey("GP-20");
		containerGP20.setSize(20);
		containerGP20.setType("GP");
		containerGP20.setDescription("Standard");

		GDContainerTypeDto containerGP40 = new GDContainerTypeDto();
		containerGP40.setKey("GP-40");
		containerGP40.setSize(40);
		containerGP40.setType("GP");
		containerGP40.setDescription("Standard");

		GDContainerTypeDto containerHC40 = new GDContainerTypeDto();
		containerHC40.setKey("HC-40");
		containerHC40.setSize(40);
		containerHC40.setType("HC");
		containerHC40.setDescription("High cube");

		GDContainerTypeDto containerHT40 = new GDContainerTypeDto();
		containerHT40.setKey("HT-40");
		containerHT40.setSize(40);
		containerHT40.setType("HT");
		containerHT40.setDescription("Hardtop");

		GDContainerTypeDto flatRack40 = new GDContainerTypeDto();
		flatRack40.setKey("PC-40");
		flatRack40.setSize(40);
		flatRack40.setType("PC");
		flatRack40.setDescription("Flat Rack");

		return List.of(containerGP20, containerGP40, containerHT40, containerHC40, flatRack40);
	}

	@Override
	public List<OptionDto> getPackagingOptionsAir(String language) {
		if (ERROR_LANG.equals(language)) {
			throw new ErrorIdExternalServiceNotAvailableException(BookErrorId.ERR_AL_10, "Country service not available");
		}
		//@formatter:off
		return List.of(new OptionDto().code("DH2").description("1/2 CHEP"),
				new OptionDto().code("DH4").description("1/4 CHEP"),
				new OptionDto().code("4B").description("Aluminium Box"),
				new OptionDto().code("1B1").description("Aluminium Cylinder"),
				new OptionDto().code("1B").description("Aluminium Drum"),
				new OptionDto().code("BG").description("Bag"),
				new OptionDto().code("FX").description("Bag, flexible container"),
				new OptionDto().code("BL").description("Bale, compressed"),
				new OptionDto().code("BN").description("Bale, non-compressed"),
				new OptionDto().code("BA").description("Barrel"),
				new OptionDto().code("BX").description("Box"),
				new OptionDto().code("DH").description("Box, CHEP, Eurobox"),
				new OptionDto().code("BE").description("Bundle"),
				new OptionDto().code("DK").description("Cardboard Crate"),
				new OptionDto().code("P4").description("Cardboard Pallet"),
				new OptionDto().code("CT").description("Carton"),
				new OptionDto().code("7B").description("Case, wooden"),
				new OptionDto().code("PE").description("Europallet"));
		//@formatter:on

	}

	@Override
	public List<OptionDto> getPackagingOptionsRoad(String language) {
		if (ERROR_LANG.equals(language)) {
			throw new ErrorIdExternalServiceNotAvailableException(BookErrorId.ERR_AL_10, "Packaging options service not available");
		}
		//@formatter:off
		return List.of(
				// Standard packaging options
				new OptionDto().code("KT").description("CHEP-Pallet"),
				new OptionDto().code("ST").description("Standard-Pallet"),
				new OptionDto().code("SC").description("Case"),
				new OptionDto().code("VG").description("Casing"),
				new OptionDto().code("RO").description("Castor"),
				new OptionDto().code("C2").description("Chep-Half-Pallet"),
				new OptionDto().code("EU").description("Euro-Pallet"),
				new OptionDto().code("BT").description("Bag"),
				new OptionDto().code("ST").description("Piece"),

				// Dangerous goods packaging options
				new OptionDto().code("BV").description("Salvage packaging"),
				new OptionDto().code("DG").description("Pressure receptable"),
				new OptionDto().code("F").description("Drum"),
				new OptionDto().code("FB").description("Light gauge metal packaging"),
				new OptionDto().code("FL").description("Cylinder")
				);

		//@formatter:on

	}

	@Override
	public List<DeliveryProductDto> getDeliveryProductsForDivision(Division division, String language) {
		if (division.equals(Division.T)) {
			//@formatter:off
		return List.of(
				new DeliveryProductDto("P", "Pakete").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("N", "classicline").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("A", "targo on-site").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("U", "targo on-site fix").fixedDeliveryDate(true).hint(null),
				new DeliveryProductDto("B", "targo on-site plus").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("H", "targo on-site premium").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("V", "targofix").fixedDeliveryDate(true).hint(null),
				new DeliveryProductDto("R", "targofix 10").fixedDeliveryDate(true).hint(null),
				new DeliveryProductDto("W", "targofix 12").fixedDeliveryDate(true).hint(null),
				new DeliveryProductDto("Y", "targoflex").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("Z", "targospeed").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("S", "targospeed 10").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("E", "targospeed 12").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("X", "targospeed plus").fixedDeliveryDate(false).hint(null));
		//@formatter:on
		} else {
			//@formatter:off
		return List.of(
				new DeliveryProductDto("Q", "vengofix").fixedDeliveryDate(true).hint(null),
				new DeliveryProductDto("K", "vengoflex").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("N", "vengoflex").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("E", "vengospeed").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("S", "vengospeed 11").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("X", "vengospeed plus").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("I", "weekend service").fixedDeliveryDate(false).hint(null),
				new DeliveryProductDto("L", "weekend service 11").fixedDeliveryDate(false).hint(null));

		}
		//@formatter:on
	}

	@Override
	public List<AirProductDto> getAirProducts(Boolean includeDeactivated) {
		List<AirProductDto> productsList = List.of(new AirProductDto("1", "avigoexpress").hint("Fastest possible service - 1 to 3 days").active(true),
				new AirProductDto("2", "avigospeed").hint("Fast service - 2 to 4 days").active(true),
				new AirProductDto("3", "avigostandard").hint("Standard service - 3 to 5 days").active(true),
				new AirProductDto("4", "avigoflex").hint("Flexible service - 4 to 6 days").active(false));

		if (!includeDeactivated) {
			return productsList.stream().filter(AirProductDto::getActive).toList();
		} else {
			return productsList;
		}
	}
}
