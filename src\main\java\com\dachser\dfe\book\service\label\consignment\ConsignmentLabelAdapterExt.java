package com.dachser.dfe.book.service.label.consignment;

import com.dachser.dfe.book.mapper.order.ConsignmentPrintBeanMapper;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.service.label.LabelAdapter;
import com.dachser.dfe.road.consignmentlabel.api.ConsignmentLabelApi;
import com.dachser.dfe.road.consignmentlabel.model.RCLDmcInputDto;
import com.dachser.dfe.road.consignmentlabel.model.RCLDmcLabelDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "false", matchIfMissing = true)
public class ConsignmentLabelAdapterExt implements LabelAdapter.ConsignmentLabelAdapter {

	private final ConsignmentLabelApi consignmentLabelApi;

	private final ConsignmentPrintBeanMapper mapper;

	@Override
	public byte[] getPrintLabelsForForwardingOrder(@NonNull ForwardingOrder order) {
		RCLDmcInputDto consignmentDto = mapper.map(order);
		try {
			RCLDmcLabelDto dmc = consignmentLabelApi.createDMC(consignmentDto);
			return dmc.getDmc();

		} catch (RestClientException restClientException) {
			log.error("Consignment Label API not available... proceeding.", restClientException);
		}
		return new byte[0];
	}
}
