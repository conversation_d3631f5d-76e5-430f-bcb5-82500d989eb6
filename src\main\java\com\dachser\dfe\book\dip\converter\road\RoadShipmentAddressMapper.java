package com.dachser.dfe.book.dip.converter.road;

import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import org.mapstruct.Mapping;

import java.util.List;

public interface RoadShipmentAddressMapper<P, A, C> {

	@Mapping(target = "partnerID", source = "furtherAddress.gln", qualifiedByName = "mapInvalidGlnAsIdOrNull")
	@Mapping(target = "partnerGLN", source = "furtherAddress.gln", qualifiedByName = "mapValidGlnOrNull")
	@Mapping(target = "partnerName", source = "furtherAddress", qualifiedByName = "mapAddressNames")
	@Mapping(target = "addressInformation", source = "furtherAddress")
	@Mapping(target = "contactInformation", source = "furtherAddress.orderContact", ignore = true)
	P mapPartnerInformation(OrderFurtherAddress furtherAddress);

	@Mapping(target = "street", source = "street")
	@Mapping(target = "city", source = "city")
	@Mapping(target = "postalCode", source = "postcode")
	@Mapping(target = "countryCode", source = "countryCode")
	@Mapping(target = "supplementInformation", source = "supplement")
	A mapAddressInformation(OrderAddress address);

	@Mapping(target = "street", source = "street")
	@Mapping(target = "city", source = "city")
	@Mapping(target = "postalCode", source = "postcode")
	@Mapping(target = "countryCode", source = "countryCode")
	@Mapping(target = "supplementInformation", source = "supplement")
	A mapAddressInformation(OrderFurtherAddress furtherAddress);

	@Mapping(target = "contactName", source = "name")
	@Mapping(target = "contactPhoneNumber", source = "telephone")
	@Mapping(target = "contactEmail", source = "email")
	@Mapping(target = "contactMobilePhoneNumber", source = "mobile")
	C mapContactInformation(OrderContact contact);

	default List<C> mapContactInformationToList(OrderContact contact) {
		if (contact != null) {
			return List.of(mapContactInformation(contact));
		} else {
			return List.of();
		}
	}
}
