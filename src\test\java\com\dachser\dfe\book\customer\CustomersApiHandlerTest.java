package com.dachser.dfe.book.customer;

import com.dachser.dfe.book.BaseOpenApiTest;
import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.model.AddressDto;
import com.dachser.dfe.book.model.CollectionTimeSlotDto;
import com.dachser.dfe.book.model.ContainerTypesWithLastUsedDto;
import com.dachser.dfe.book.model.CustomerSettingsDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.DeliveryProductsWithFavoritesDto;
import com.dachser.dfe.book.model.DivisionDto;
import com.dachser.dfe.book.model.FavoriteCountriesDto;
import com.dachser.dfe.book.model.GoodsGroupResponseItemDto;
import com.dachser.dfe.book.model.MeasurementProposalsDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderTypeDto;
import com.dachser.dfe.book.model.PackagingOptionsWithFavoritesDto;
import com.dachser.dfe.book.model.SegmentDto;
import com.dachser.road.ohs.pub.api.openinghours.bean.OpeningHoursRequest;
import com.fasterxml.jackson.core.type.TypeReference;
import io.restassured.common.mapper.TypeRef;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import io.restassured.module.mockmvc.specification.MockMvcRequestSpecification;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.http.HttpStatus;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static com.dachser.dfe.book.user.UserServiceMock.VALID_CUST_NO_ROAD;
import static io.restassured.module.mockmvc.RestAssuredMockMvc.given;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.http.HttpStatus.BAD_REQUEST;
import static org.springframework.http.HttpStatus.FORBIDDEN;
import static org.springframework.http.HttpStatus.NOT_FOUND;
import static org.springframework.http.HttpStatus.NO_CONTENT;
import static org.springframework.http.HttpStatus.OK;
import static org.springframework.http.HttpStatus.UNAUTHORIZED;

class CustomersApiHandlerTest extends BaseOpenApiTest implements ResourceLoadingTest {

	@Nested
	@TestInstance(TestInstance.Lifecycle.PER_CLASS)
	class InvalidValidation {

		Stream<String> urls() {
			return Stream.of("/customers/further-address-types", "/customers/order-groups", "/customers/packaging-options", "/customers/settings", "/customers/loading-points",
					"/customers/address", "/customers/collection-time-slots", "/customers/packaging-options/measurement-proposals", "/customers/goods-groups",
					"/customers/transports", "/customers/contact-data", "/v1/customers/delivery-products-air");
		}

		@ParameterizedTest(name = "{index} - {0}")
		@MethodSource("urls")
		void shouldReturn404OnUnknownCustomerNumber(String url) {
			MockMvcRequestSpecification request = givenRequest();
			request.header("Accept-Language", "de");
			request.queryParam(CUSTOMER_NUMBER_PARAM, INVALID_CUST_NO);
			request.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.ROAD.getValue());
			request.queryParam(ORDER_TYPE_PARAM, OrderTypeDto.ROAD_FORWARDING_ORDER.getValue());

			if (url.contains("collection-time-slots")) {
				request.queryParam("collectionDate", "2022-09-01");
			} else if (url.contains("measurement-proposals")) {
				request.queryParam("packagingCode", "EU");
			}

			final MockMvcResponse response = request.get(buildUrl(url));
			assertEquals(NOT_FOUND.value(), response.statusCode());
		}

		@ParameterizedTest(name = "{index} - {0}")
		@MethodSource("urls")
		void shouldReturn400OnMissingCustomerNumber(String url) {
			MockMvcRequestSpecification request = givenRequest().header("Authorization", "").header("Content-Type", "application/json").body("{}");
			request.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.ROAD.getValue());
			final MockMvcResponse response = request.get(buildUrl(url));
			assertEquals(BAD_REQUEST.value(), response.statusCode());
		}

		@ParameterizedTest(name = "{index} - {0}")
		@MethodSource("urls")
		void shouldReturn400OnMissingCustomerSegment(String url) {
			MockMvcRequestSpecification request = givenRequest().header("Authorization", "").header("Content-Type", "application/json").body("{}");
			request.queryParam(CUSTOMER_NUMBER_PARAM, INVALID_CUST_NO);
			final MockMvcResponse response = request.get(buildUrl(url));
			assertEquals(BAD_REQUEST.value(), response.statusCode());
		}
	}

	@Nested
	class GetFurtherAddressTypes {
		@Nested
		class GivenValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn200() {
					MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
					request.queryParam(CUSTOMER_NUMBER_PARAM, VALID_CUST_NO_ROAD);
					request.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.ROAD.getValue());

					final MockMvcResponse response = request.get(buildUrl("/customers/further-address-types"));

					assertEquals(200, response.statusCode());
					// Currently only validation of a return type is possible
				}
			}

		}

		@Nested
		class GivenNoAccessToken {
			@Test
			void shouldReturn401() {
				MockMvcRequestSpecification request = given().header("Content-Type", "application/json");

				final MockMvcResponse response = request.get(buildUrl("/customers/further-address-types"));
				assertEquals(401, response.statusCode());
				// Currently only validation of a return type is possible
			}

		}
	}

	@Nested
	class GetOrderGroupsOptions {
		@Nested
		class ValidOrderGroupsRequest {
			@Test
			void shouldReturn200() {
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
				addValidCustomerNumberParam(request);
				addRoadOrderTypeParam(request);

				final MockMvcResponse response = request.get(buildUrl("/customers/order-groups"));
				assertEquals(OK.value(), response.statusCode());
				final List<OptionDto> content = response.getBody().as(new TypeRef<>() {
				});
				// No OrderGroup for mocked Customer available
				assertTrue(content.isEmpty());

			}
		}

		@Nested
		class NoValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given().header("Content-Type", "application/json");

					final MockMvcResponse response = request.get(buildUrl("/customers/order-groups"));
					assertEquals(401, response.statusCode());
					// Currently only validation of a random return size with some content
				}

			}
		}
	}

	@Nested
	class GetFilteredOrderGroupsOptions {
		@Nested
		class ValidOrderGroupsRequest {
			@Test
			void shouldReturn200() {
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
				addValidCustomerNumberParam(request);
				addRoadOrderTypeParam(request);
				addShipperCountryCodeParam(request);
				addConsigneeCountryCodeParam(request);

				final MockMvcResponse response = request.get(buildUrl("/customers/order-groups-filtered"));
				assertEquals(OK.value(), response.statusCode());
				final List<OptionDto> content = response.getBody().as(new TypeRef<>() {
				});
				// No OrderGroup for mocked Customer available
				assertTrue(content.isEmpty());

			}
		}

		@Nested
		class InvalidOrderGroupsRequest {
			@Test
			void shouldReturn400OnMissingParams() {
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
				addValidCustomerNumberParam(request);
				addRoadOrderTypeParam(request);

				final MockMvcResponse response = request.get(buildUrl("/customers/order-groups-filtered"));
				assertEquals(BAD_REQUEST.value(), response.statusCode());
			}
		}

		@Nested
		class NoValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given().header("Content-Type", "application/json");

					final MockMvcResponse response = request.get(buildUrl("/customers/order-groups-filtered"));
					assertEquals(401, response.statusCode());
					// Currently only validation of a random return size with some content
				}

			}
		}
	}

	@Nested
	class GetCustomerPackagingOptions {
		@Nested
		class ValidPackagingRequest {
			@Test
			void shouldReturn200() {
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
				request.header("Accept-Language", "de");
				addValidCustomerNumberParam(request);
				addRoadCustomerSegment(request);

				final MockMvcResponse response = request.get(buildUrl("/customers/packaging-options"));
				assertEquals(HttpStatus.OK.value(), response.statusCode());
				final PackagingOptionsWithFavoritesDto content = response.getBody().as(new TypeRef<>() {
				});
				assertFalse(content.getPackagingOptions().isEmpty());
				final Optional<OptionDto> packagingOptionDto = content.getPackagingOptions().stream().findFirst();
				packagingOptionDto.ifPresent(option -> assertTrue(option.getCode().matches("[A-Z]{2,3}")));

			}

			@Test
			void shouldReturn404() {
				MockMvcRequestSpecification request = givenRequest().header("Authorization", "").header("Content-Type", "application/json").body("{}");
				addValidCustomerNumberParam(request);
				addRoadCustomerSegment(request);

				final MockMvcResponse response = request.get(buildUrl("/customer//packaging-options"));
				assertEquals(HttpStatus.NOT_FOUND.value(), response.statusCode());
			}
		}

		@Nested
		class NoValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given();

					final MockMvcResponse response = request.get(buildUrl("/customers/packaging-options"));
					assertEquals(HttpStatus.UNAUTHORIZED.value(), response.statusCode());
					// Currently only validation of a random return size with some content
				}

				@Test
				void shouldReturn403() {
					MockMvcRequestSpecification request = givenRequestWithInvalidToken();

					final MockMvcResponse response = request.get(buildUrl("/customers/packaging-options"));
					assertEquals(FORBIDDEN.value(), response.statusCode());
					// Currently only validation of a random return size with some content
				}

			}
		}
	}

	@Nested
	class GetCustomerContainerTypes {

		@Nested
		class ValidRequest {

			@Test
			void shouldReturnContainerTypesWithLastused() {

				when(baseFullContainerLoadRepository.getMostFrequentlyUsedContainerTypes(any())).thenReturn(List.of("GP-20"));

				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
				request.header("Accept-Language", "de");
				addValidCustomerNumberParam(request);
				addRoadCustomerSegment(request);

				final MockMvcResponse response = request.get(buildUrl("/customers/container-types"));
				assertEquals(HttpStatus.OK.value(), response.statusCode());
				final ContainerTypesWithLastUsedDto content = response.getBody().as(new TypeRef<>() {
				});

				assertEquals(2, content.getContainerTypes().size());
				assertEquals(1, content.getLastUsed().size());
			}
		}
	}

	@Nested
	class GetCustomerSettings {
		@Nested
		class ValidRequest {
			@Test
			void shouldReturn200() {
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
				addValidCustomerNumberParam(request);
				addRoadCustomerSegment(request);
				final MockMvcResponse response = request.get(buildUrl("/customers/settings"));
				assertEquals(HttpStatus.OK.value(), response.statusCode());
				final CustomerSettingsDto content = response.getBody().as(new TypeRef<>() {
				});
				assertTrue(content.getFrostProtection());
				assertTrue(content.getPalletLocation());
				assertTrue(content.getSelfCollection());
				assertTrue(content.getManualNumberOfLabels());
				assertTrue(content.getOrderNumberMandatory());
			}

			@Test
			void shouldReturn200ForAirSegment() {
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
				request.queryParam(CUSTOMER_NUMBER_PARAM, VALID_CUST_NO_AIR);
				addAirCustomerSegment(request);
				final MockMvcResponse response = request.get(buildUrl("/customers/settings"));
				assertEquals(HttpStatus.OK.value(), response.statusCode());
				final CustomerSettingsDto content = response.getBody().as(new TypeRef<>() {
				});
				assertFalse(content.getAirProducts());
			}

			@Test
			void shouldReturn404OnNonRoadCustomer() {
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
				addInvalidCustomerNumberParam(request);
				addRoadCustomerSegment(request);
				final MockMvcResponse response = request.get(buildUrl("/customers/settings"));
				assertEquals(HttpStatus.NOT_FOUND.value(), response.statusCode());
			}

			@Test
			void shouldReturn404() {
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
				final MockMvcResponse response = request.get(buildUrl("/customer//settings"));
				assertEquals(HttpStatus.NOT_FOUND.value(), response.statusCode());
			}

		}

		@Nested
		class NoValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given().header("Content-Type", "application/json");

					final MockMvcResponse response = request.get(buildUrl("/customers/settings"));
					assertEquals(HttpStatus.UNAUTHORIZED.value(), response.statusCode());
					// Currently only validation of a random return size with some content
				}

			}
		}
	}

	@Nested
	class GetCustomerLoadingPoints {
		@Nested
		class GivenValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn200() {
					MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
					addValidCustomerNumberParam(request);
					addRoadCustomerSegment(request);

					final MockMvcResponse response = request.get(buildUrl("/customers/loading-points"));
					assertEquals(HttpStatus.OK.value(), response.statusCode());
					// Currently only validation of a random return size with some content
					final List<AddressDto> content = response.getBody().as(new TypeRef<>() {
					});
					content.stream().findFirst().ifPresent(principal -> {
						assertTrue(StringUtils.isNotEmpty(principal.getCity()));
						assertNotNull(principal.getId());
						assertTrue(StringUtils.isNotEmpty(principal.getName()));
					});
				}
			}
		}

		@Nested
		class GivenNoAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given().header("Content-Type", "application/json");

					final MockMvcResponse response = request.get(buildUrl("/customers/loading-points"));
					assertEquals(HttpStatus.UNAUTHORIZED.value(), response.statusCode());
				}
			}
		}
	}

	@Nested
	class GetCustomersAddress {
		@Nested
		class GivenValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn200() {
					MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
					addValidCustomerNumberParam(request);
					addRoadCustomerSegment(request);
					final MockMvcResponse response = request.get(buildUrl("/customers/address"));
					assertEquals(HttpStatus.OK.value(), response.statusCode());
					// Currently only validation of a random return size with some content
					final AddressDto address = response.getBody().as(AddressDto.class);
					assertTrue(StringUtils.isNotEmpty(address.getCity()));
					assertTrue(StringUtils.isNotEmpty(address.getName()));
					assertTrue(StringUtils.isNotEmpty(address.getCountryCode()));
				}

				@Test
				void shouldGetDedicatedAddress() {
					MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
					addValidCustomerNumberParam(request);
					addRoadCustomerSegment(request);

					final MockMvcResponse response = request.get(buildUrl("/customers/address"));
					assertEquals(HttpStatus.OK.value(), response.statusCode());
					// Currently only validation of a random return size with some content
					final AddressDto address = response.getBody().as(AddressDto.class);
					assertEquals("Kempten", address.getCity());
					assertEquals("Dachser Name 1", address.getName());
					assertEquals("Dachser Name 2", address.getName2());
					assertEquals("87439", address.getPostcode());
				}
			}
		}

		@Nested
		class GivenNoAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given().header("Content-Type", "application/json");
					final MockMvcResponse response = request.get(buildUrl("/customers/address"));
					assertEquals(HttpStatus.UNAUTHORIZED.value(), response.statusCode());
				}
			}
		}
	}

	@Nested
	class GetCustomersCollectionTimeSlots {

		@Nested
		class ValidAccessToken {
			@Test
			void shouldReturn200() throws IOException {
				when(hoursService.getTimeFrames(any(OpeningHoursRequest.class))).thenReturn(loadResourceAndConvert("openinghours/openinghours.json", new TypeReference<>() {
				}));
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
				request.queryParam("collectionDate", "2022-09-01");
				addValidCustomerNumberParam(request);
				addRoadCustomerSegment(request);
				addRoadOrderTypeParam(request);

				final MockMvcResponse response = request.get(buildUrl("/customers/collection-time-slots"));
				assertEquals(OK.value(), response.statusCode());
				final List<CollectionTimeSlotDto> content = response.getBody().as(new TypeRef<>() {
				});
				assertEquals(1, content.size());
				final CollectionTimeSlotDto collectionTimeSlotDto = content.get(0);
				assertEquals(11, collectionTimeSlotDto.getFrom().getHour());
				assertEquals(15, collectionTimeSlotDto.getTo().getHour());
			}

			@Test
			void shouldReturn200HavingDateFromInResponse() throws IOException {
				when(hoursService.getTimeFrames(any(OpeningHoursRequest.class))).thenReturn(
						loadResourceAndConvert("openinghours/openinghours_with_dates.json", new TypeReference<>() {
						}));
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
				request.queryParam("collectionDate", "2022-09-01");
				addRoadCustomerSegment(request);
				addValidCustomerNumberParam(request);
				addRoadOrderTypeParam(request);

				final MockMvcResponse response = request.get(buildUrl("/customers/collection-time-slots"));
				assertEquals(OK.value(), response.statusCode());
				final List<CollectionTimeSlotDto> content = response.getBody().as(new TypeRef<>() {
				});
				assertEquals(1, content.size());
				final CollectionTimeSlotDto collectionTimeSlotDto = content.get(0);
				assertEquals(11, collectionTimeSlotDto.getFrom().getHour());
				assertEquals(15, collectionTimeSlotDto.getTo().getHour());
			}

			@Test
			void shouldReturn204HavingNoDateInResponse() {
				when(hoursService.getTimeFrames(any(OpeningHoursRequest.class))).thenReturn(List.of());
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
				request.queryParam("collectionDate", "1999-09-01");
				addValidCustomerNumberParam(request);
				addRoadCustomerSegment(request);
				addRoadOrderTypeParam(request);

				final MockMvcResponse response = request.get(buildUrl("/customers/collection-time-slots"));
				assertEquals(NO_CONTENT.value(), response.statusCode());
			}

			@Nested
			class InValidRequest {
				@Test
				void wrongCustomerNumberParamsShouldReturn400() {
					MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
					request.queryParam("collectionDate", "2022-09-01");
					request.queryParam(CUSTOMER_NUMBER_PARAM, "asd");

					final MockMvcResponse response = request.get(buildUrl("/customers/collection-time-slots"));
					assertEquals(BAD_REQUEST.value(), response.statusCode());
				}

				@Test
				void unknownCustomerNumberParamsShouldReturn404() {
					MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
					request.queryParam(CUSTOMER_NUMBER_PARAM, "00000002");

					request.queryParam("collectionDate", "2022-09-01");

					final MockMvcResponse response = request.get(buildUrl("/customers/collection-time-slots"));
					assertEquals(BAD_REQUEST.value(), response.statusCode());
				}

				@Test
				void missingTimeParamsShouldReturn400() {
					MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
					addValidCustomerNumberParam(request);
					addRoadOrderTypeParam(request);

					final MockMvcResponse response = request.get(buildUrl("/customers/collection-time-slots"));
					assertEquals(BAD_REQUEST.value(), response.statusCode());
				}
			}
		}

		@Nested
		class NoValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given().header("Content-Type", "application/json");

					final MockMvcResponse response = request.get(buildUrl("/customers/collection-time-slots"));
					assertEquals(UNAUTHORIZED.value(), response.statusCode());
					// Currently only validation of a random return size with some content
				}

			}
		}
	}

	@Nested
	class GetCustomersMeasurementProposals {
		@Nested
		class ValidMeasurementRequest {
			@Test
			void shouldReturn200WithEuCode() {
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").queryParam("packagingCode", "EU");
				addValidCustomerNumberParam(request);
				addRoadCustomerSegment(request);

				final MockMvcResponse response = request.get(buildUrl("/customers/packaging-options/measurement-proposals"));
				assertEquals(OK.value(), response.statusCode());
				final MeasurementProposalsDto content = response.getBody().as(new TypeRef<>() {
				});
				assertEquals(120, content.getStandard().getLength());
			}

			@Test
			void shouldReturn200WithGBCode() {
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").queryParam("packagingCode", "GB");
				addValidCustomerNumberParam(request);
				addRoadCustomerSegment(request);

				final MockMvcResponse response = request.get(buildUrl("/customers/packaging-options/measurement-proposals"));
				assertEquals(OK.value(), response.statusCode());
				final MeasurementProposalsDto content = response.getBody().as(new TypeRef<>() {
				});
				assertEquals(124, content.getStandard().getLength());

			}

			@Test
			void shouldReturn200WithNoStandardOnXX() {
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").queryParam("packagingCode", "XX");
				addValidCustomerNumberParam(request);
				addRoadCustomerSegment(request);

				final MockMvcResponse response = request.get(buildUrl("/customers/packaging-options/measurement-proposals"));
				assertEquals(OK.value(), response.statusCode());
				final MeasurementProposalsDto content = response.getBody().as(new TypeRef<>() {
				});
				assertNull(content.getStandard());

			}

			@Test
			void shouldReturn404() {
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
				addValidCustomerNumberParam(request);
				addRoadCustomerSegment(request);
				request.queryParam("packagingCode", "AT");

				final MockMvcResponse response = request.get(buildUrl("/customer//packaging-options/measurement-proposals"));
				assertEquals(NOT_FOUND.value(), response.statusCode());
			}

			@Test
			void shouldReturn400MissingQueryParam() {
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
				addValidCustomerNumberParam(request);
				addRoadCustomerSegment(request);

				final MockMvcResponse response = request.get(buildUrl("/customers/packaging-options/measurement-proposals"));
				assertEquals(BAD_REQUEST.value(), response.statusCode());
			}
		}

		@Nested
		class NoValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given().header("Content-Type", "application/json");

					final MockMvcResponse response = request.get(buildUrl("/customers/packaging-options/measurement-proposals"));
					assertEquals(UNAUTHORIZED.value(), response.statusCode());
					// Currently only validation of a random return size with some content
				}

			}
		}
	}

	@Nested
	class GetCustomerGoodsGroups {
		@Nested
		class GivenValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn200() {
					MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
					addValidCustomerNumberParam(request);
					addRoadCustomerSegment(request);
					addRoadOrderTypeParam(request);

					final MockMvcResponse response = request.get(buildUrl("/customers/goods-groups"));
					assertEquals(OK.value(), response.statusCode());
					// Currently only validation of a random return size with some content
					final List<GoodsGroupResponseItemDto> content = response.getBody().as(new TypeRef<>() {
					});
					content.stream().findFirst().ifPresent(principal -> {
						assertTrue(StringUtils.isNotEmpty(principal.getCode()));
						assertTrue(StringUtils.isNotEmpty(principal.getDescription()));
					});
				}

				@Test
				void shouldReturn404() {
					MockMvcRequestSpecification request = givenRequest().header("Authorization", "").header("Content-Type", "application/json").body("{}");
					addValidCustomerNumberParam(request);
					addRoadCustomerSegment(request);

					final MockMvcResponse response = request.get(buildUrl("/customer//goods-groups"));
					assertEquals(404, response.statusCode());
				}
			}
		}

		@Nested
		class NoValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given().header("Content-Type", "application/json");

					final MockMvcResponse response = request.get(buildUrl("/customers/goods-groups"));
					assertEquals(UNAUTHORIZED.value(), response.statusCode());
					// Currently only validation of a random return size with some content
				}

			}
		}
	}

	@Nested
	class GetTransportOptions {
		@Nested
		class ValidTransportsRequest {
			@Test
			void shouldReturn200() {
				MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
				addValidCustomerNumberParam(request);
				addRoadCustomerSegment(request);
				final MockMvcResponse response = request.get(buildUrl("/customers/transports"));
				assertEquals(OK.value(), response.statusCode());
				final List<OptionDto> content = response.getBody().as(new TypeRef<>() {
				});
				assertThat(content).isEmpty();
				final Optional<OptionDto> packagingOptionDto = content.stream().findFirst();
				packagingOptionDto.ifPresent(option -> assertEquals("T1", option.getCode()));

			}

			@Test
			void shouldReturn404() {
				MockMvcRequestSpecification request = givenRequest().header("Authorization", "").header("Content-Type", "application/json").body("{}");
				addValidCustomerNumberParam(request);
				addRoadCustomerSegment(request);

				final MockMvcResponse response = request.get(buildUrl("/customer//transports"));
				assertEquals(NOT_FOUND.value(), response.statusCode());
			}
		}

		@Nested
		class NoValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given().header("Content-Type", "application/json");

					final MockMvcResponse response = request.get(buildUrl("/customers/transports"));
					assertEquals(UNAUTHORIZED.value(), response.statusCode());
					// Currently only validation of a random return size with some content
				}

			}
		}
	}

	@Nested
	class GetCustomersContactData {
		@Nested
		class GivenValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn200() {
					MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
					addValidCustomerNumberParam(request);
					addRoadCustomerSegment(request);
					final MockMvcResponse response = request.get(buildUrl("/customers/contact-data"));
					assertEquals(OK.value(), response.statusCode());
					// Currently only validation of a random return size with some content
					assertThat(response.getBody().asString()).isEmpty();
				}

				@Test
				void shouldReturn404() {
					MockMvcRequestSpecification request = givenRequest().header("Authorization", "").header("Content-Type", "application/json").body("{}");
					addValidCustomerNumberParam(request);
					addRoadCustomerSegment(request);

					final MockMvcResponse response = request.get(buildUrl("/customer//contact-data"));
					assertEquals(NOT_FOUND.value(), response.statusCode());
				}
			}
		}

		@Nested
		class GivenNoAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given().header("Content-Type", "application/json").body("{}");

					final MockMvcResponse response = request.get(buildUrl("/customers/contact-data"));
					assertEquals(UNAUTHORIZED.value(), response.statusCode());

				}

			}
		}
	}

	@Nested
	class GetFavoriteCountries {

		@Nested
		class GivenValidAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn200() {
					MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
					addValidCustomerNumberParam(request);
					addRoadCustomerSegment(request);
					final MockMvcResponse response = request.get(buildUrl("/customers/countries"));
					assertEquals(OK.value(), response.statusCode());
					final FavoriteCountriesDto favoriteCountries = response.getBody().as(new TypeRef<>() {
					});
					assertNotNull(favoriteCountries);
				}

				@Test
				void shouldReturn404() {
					MockMvcRequestSpecification request = givenRequest().header("Authorization", "").header("Content-Type", "application/json").body("{}");
					addValidCustomerNumberParam(request);
					addRoadCustomerSegment(request);

					final MockMvcResponse response = request.get(buildUrl("/customer//countries"));
					assertEquals(NOT_FOUND.value(), response.statusCode());
				}
			}
		}

		@Nested
		class GivenNoAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given().header("Content-Type", "application/json").body("{}");

					final MockMvcResponse response = request.get(buildUrl("/customers/countries"));
					assertEquals(UNAUTHORIZED.value(), response.statusCode());

				}

			}
		}
	}

	@Nested
	class GetAirProducts {

		@Nested
		class GivenValidAccessToken {
			@Nested
			class ValidRequest {

				private final String code = "1";

				@Test
				void shouldReturn200() {
					MockMvcRequestSpecification request = givenRequest();
					addValidCustomerNumberParam(request, "00000200");
					addAirCustomerSegment(request);
					final MockMvcResponse response = request.get(buildUrl("/v1/customers/delivery-products-air"));
					assertEquals(OK.value(), response.statusCode());
					final List<DeliveryProductDto> deliveryProducts = response.getBody().as(new TypeRef<>() {
					});
					assertNotNull(deliveryProducts);
					assertFalse(deliveryProducts.isEmpty());
					assertTrue(deliveryProducts.stream().anyMatch(deliveryProduct -> deliveryProduct.getCode().equals(code)));
				}

				@Test
				void shouldReturn200ButEmptyListWhenAirCustomerHasNoAirProducts() {
					MockMvcRequestSpecification request = givenRequest();
					addValidCustomerNumberParam(request, VALID_CUST_NO_AIR);
					addAirCustomerSegment(request);
					final MockMvcResponse response = request.get(buildUrl("/v1/customers/delivery-products-air"));
					assertEquals(OK.value(), response.statusCode());
					final List<DeliveryProductDto> deliveryProducts = response.getBody().as(new TypeRef<>() {
					});
					assertTrue(deliveryProducts.isEmpty());
				}

				@Test
				void shouldReturn404WithUnallowedUser() {
					MockMvcRequestSpecification request = givenRequest().header("Authorization", "").header("Content-Type", "application/json").body("{}");
					addRoadCustomerSegment(request);
					addInvalidCustomerNumberParam(request);
					final MockMvcResponse response = request.get(buildUrl("/v1/customers/delivery-products-air"));
					assertEquals(NOT_FOUND.value(), response.statusCode());
				}
			}
		}

		@Nested
		class GivenNoAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given().header("Content-Type", "application/json");

					final MockMvcResponse response = request.get(buildUrl("/v1/customers/delivery-products-air"));
					assertEquals(UNAUTHORIZED.value(), response.statusCode());

				}

			}
		}
	}

	@Nested
	class GetRoadProducts {

		@Nested
		class GivenValidAccessToken {

			@Nested
			class ValidRequest {

				private DeliveryProductsWithFavoritesDto buildJsonDeliveryProductsWithFavorites(boolean hasFavorites) {
					DeliveryProductDto product1 = new DeliveryProductDto().code("N").description("Product 1");
					DeliveryProductDto product2 = new DeliveryProductDto().code("E").description("Product 2");
					DeliveryProductDto product3 = new DeliveryProductDto().code("T").description("Product 3");
					DeliveryProductDto product4 = new DeliveryProductDto().code("S").description("Product 4");

					DeliveryProductsWithFavoritesDto productsWithFavorites = new DeliveryProductsWithFavoritesDto().deliveryProducts(List.of(product2, product1, product3));

					if (hasFavorites) {
						productsWithFavorites.favorites(List.of(product4));
					} else {
						productsWithFavorites.favorites(List.of());
					}

					return productsWithFavorites;
				}

				@Test
				void shouldReturn403WithUnallowedUser() {
					MockMvcRequestSpecification request = givenRequest();
					addInvalidCustomerNumberParam(request);
					addRoadCustomerSegment(request);
					addRoadDivision(request);
					final MockMvcResponse response = request.get(buildUrl("/customers/delivery-products-road"));
					assertEquals(NOT_FOUND.value(), response.statusCode());
				}
			}
		}

		@Nested
		class GivenNoAccessToken {
			@Nested
			class ValidRequest {
				@Test
				void shouldReturn401() {
					MockMvcRequestSpecification request = given();

					final MockMvcResponse response = request.get(buildUrl("/customers/delivery-products-road"));
					assertEquals(UNAUTHORIZED.value(), response.statusCode());

				}

			}
		}

	}

	// region Helper

	private void addValidCustomerNumberParam(MockMvcRequestSpecification req) {
		addValidCustomerNumberParam(req, VALID_CUST_NO_ROAD);
	}

	private void addValidCustomerNumberParam(MockMvcRequestSpecification req, String customerNumber) {
		req.queryParam(CUSTOMER_NUMBER_PARAM, customerNumber);
	}

	private void addInvalidCustomerNumberParam(MockMvcRequestSpecification req) {
		req.queryParam(CUSTOMER_NUMBER_PARAM, INVALID_CUST_NO);
	}

	private void addRoadOrderTypeParam(MockMvcRequestSpecification req) {
		req.queryParam(ORDER_TYPE_PARAM, OrderTypeDto.ROAD_FORWARDING_ORDER.getValue());
	}

	private void addRoadCustomerSegment(MockMvcRequestSpecification req) {
		req.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.ROAD.getValue());
	}

	private void addAirCustomerSegment(MockMvcRequestSpecification req) {
		req.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.AIR.getValue());
	}

	private void addRoadDivision(MockMvcRequestSpecification req) {
		req.queryParam(DIVISION_PARAM, DivisionDto.EUROPEAN_LOGISTICS);
	}

	private void addShipperCountryCodeParam(MockMvcRequestSpecification req) {
		req.queryParam(SHIPPER_COUNTRY_CODE_PARAM, "DE");
	}

	private void addConsigneeCountryCodeParam(MockMvcRequestSpecification req) {
		req.queryParam(CONSIGNEE_COUNTRY_CODE_PARAM, "FR");
	}

	// endregion

}