package com.dachser.dfe.book.order.emissionforecast;

import com.dachser.dfe.book.SpringBasedLocalMockTest;
import com.dachser.dfe.emissionforecast.api.ForecastApi;
import com.dachser.dfe.emissionforecast.model.EFForecastRequestDto;
import com.dachser.dfe.emissionforecast.model.EFForecastResponseDto;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EmissionForecastAdapterTest {

	@Nested
	class UnitTest {

		@InjectMocks
		EmissionForecastAdapterExt emissionForecastAdapter;

		@Mock
		ForecastApi forecastApi;

		@Test
		void getEmissionForcast() {
			// Arrange
			EFForecastRequestDto requestDto = new EFForecastRequestDto();
			EFForecastResponseDto responseDto = new EFForecastResponseDto();

			// Mock the behavior of the forecastApi
			when(forecastApi.calculateForecast(any(), any())).thenReturn(responseDto);

			// Act
			EFForecastResponseDto result = emissionForecastAdapter.getEmissionForecast(requestDto).get();

			// Assert
			assertEquals(responseDto, result);
			verify(forecastApi, times(1)).calculateForecast(any(), eq(requestDto));
		}
	}

	@Nested
	@SpringBasedLocalMockTest
	class SpringTest {

		@Autowired
		EmissionForecastAdapter emissionForecastAdapter;

		@Test
		void getEmissionForcast() {
			EFForecastRequestDto requestDto = new EFForecastRequestDto();
			EFForecastResponseDto responseDto = emissionForecastAdapter.getEmissionForecast(requestDto).get();
			assertNotNull(responseDto);
		}
	}

}