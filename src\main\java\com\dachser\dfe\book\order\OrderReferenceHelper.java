package com.dachser.dfe.book.order;

import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OrderReferenceHelper {

	private static final String VALUE_PREFIX_SEPARATOR = ":";

	private static final String DIP_SUFFIX_FOR_VALUE_PREFIX = StringUtils.SPACE;

	public static List<RoadOrderReferenceSubtype> roadSubtypes(ReferenceType type) {
		return Arrays.stream(RoadOrderReferenceSubtype.values()).filter(e -> e.getParentType() == type).toList();
	}

	public static Optional<RoadOrderReferenceSubtype> roadSubtype(String value, ReferenceType parentType) {
		return OrderReferenceHelper.prefix(value).flatMap(
				prefix -> Arrays.stream(RoadOrderReferenceSubtype.values()).filter(e -> prefix(e).equalsIgnoreCase(prefix)).filter(e -> e.getParentType() == parentType).findAny());
	}

	public static String stripSubtypePrefix(RoadOrderReferenceSubtype subtype, String value) {
		String strippedValue = value;
		if (subtype != null && StringUtils.isNotBlank(value)) {
			String prefix = OrderReferenceHelper.prefix(subtype);
			strippedValue = StringUtils.substringAfter(value, prefix);
			if (StringUtils.isEmpty(strippedValue)) {
				strippedValue = StringUtils.substringAfter(value, prefix.toLowerCase());
			}
		}
		return StringUtils.defaultIfEmpty(strippedValue, value);
	}

	public static String prefix(RoadOrderReferenceSubtype subtype) {
		return subtype == null ? null : subtype.name().concat(VALUE_PREFIX_SEPARATOR);
	}

	public static Optional<String> prefix(String value) {
		String prefix = null;
		Optional<String> subtypeAsString = Arrays.stream(StringUtils.split(StringUtils.defaultString(value), VALUE_PREFIX_SEPARATOR)).findFirst();
		if (subtypeAsString.isPresent()) {
			prefix = subtypeAsString.get().concat(VALUE_PREFIX_SEPARATOR);
		}
		return Optional.ofNullable(prefix);
	}

	public static String dipPrefix(RoadOrderReferenceSubtype subtype) {
		return subtype == null ? null : prefix(subtype).concat(DIP_SUFFIX_FOR_VALUE_PREFIX);
	}

}