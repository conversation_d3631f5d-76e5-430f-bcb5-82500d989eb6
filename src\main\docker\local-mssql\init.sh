#!/bin/bash

echo =============== WAITING FOR MSSQL ==========================
#waiting for mssql to start
STATUS=0
i=0
while [[ ${STATUS} -eq 0 ]] || [[ ${i} -lt 60 ]]; do
	sleep 1
	i=$((i+1))
	STATUS=$(grep 'Recovery is complete. This is an informational message only. No user action is required.' /var/opt/mssql/log/errorlog | wc -l)
	if [ $STATUS -eq 1 ]; then
	    i=60
	    sleep 2
	fi
done

echo =============== MSSQL STARTED ==========================

if [ -z ${MSSQL_USER} ]; then
	MSSQL_USER=dfe_user
fi

if [ -z ${MSSQL_PASSWORD} ]; then
	MSSQL_PASSWORD=Passw0rd
fi

if [ -z ${MSSQL_DB} ]; then
	MSSQL_DB=dfe-dev
fi

echo =============== CREATING INIT DATA ==========================

cat <<-EOSQL > /tmp/init.sql
CREATE DATABASE [${MSSQL_DB}];
GO

USE [${MSSQL_DB}];
GO

-- Create Admin user
CREATE LOGIN ${MSSQL_USER} WITH PASSWORD = '${MSSQL_PASSWORD}';
GO

CREATE USER ${MSSQL_USER} FOR LOGIN ${MSSQL_USER};
GO

ALTER SERVER ROLE sysadmin ADD MEMBER [${MSSQL_USER}];
GO

ALTER USER ${MSSQL_USER} WITH DEFAULT_SCHEMA = dbo;
GO

EOSQL

# Nutze den absoluten Pfad für sqlcmd (es könnte sein, dass der Pfad anders ist)
echo =============== RUNNING SQL COMMAND ==========================
/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P ${SA_PASSWORD} -i /tmp/init.sql -o /tmp/initout.log

if [ $? -eq 0 ]; then
    echo =============== INIT SCHEMA CREATED ==========================
else
    echo =============== FAILED TO CREATE INIT SCHEMA ==========================
fi

echo =============== USER CREDENTIALS CREATED ==========================
if [ ! -z ${MSSQL_USER} ]; then
    echo "MSSQL_USER: $MSSQL_USER"
fi

if [ ! -z ${MSSQL_PASSWORD} ]; then
    echo "MSSQL_PASSWORD: $MSSQL_PASSWORD"
fi

if [ ! -z ${MSSQL_DB} ]; then
    echo "MSSQL_DB: $MSSQL_DB"
fi

echo =============== MSSQL SERVER SUCCESSFULLY STARTED ==========================

# Trap für SIGINT oder SIGTERM
while [ "$END" == '' ]; do
    sleep 1
    trap "END=1" INT TERM
done
