package com.dachser.dfe.book.advice;

import com.dachser.dfe.book.SpringBasedLocalMockTest;
import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.exception.AdviceSubmitFailedException;
import com.dachser.dfe.book.order.common.dangerousgood.ADRDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGoodDataItem;
import com.dachser.dfe.book.order.common.dangerousgood.EQDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.LQDangerousGood;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.legacy.advice.bean.AdvisedOrder;
import com.dachser.dfe.legacy.advice.bean.DfeLegacyAdviceExecutionCode;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;

import static com.dachser.dfe.book.advice.AdvisedOrderTestUtil.dangerousGoodAsADR;
import static com.dachser.dfe.book.advice.AdvisedOrderTestUtil.dangerousGoodAsEQ;
import static com.dachser.dfe.book.advice.AdvisedOrderTestUtil.dangerousGoodAsLQ;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AdviceAdapterExtTest {

	@Nested
	class UnitTest {

		@InjectMocks
		AdviceAdapterExt service;

		@Spy
		AdvisedOrderMapper mapper = new AdvisedOrderMapperImpl();

		@Mock
		com.dachser.dfe.legacy.advice.service.api.AdviceService adviceService;

		private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

		final ForwardingOrder mockOrder = testUtil.generateForwardingOrder();

		@Test
		void shouldReturnTrueForAcceptedOrder() {
			when(adviceService.pushAdvice(any(AdvisedOrder.class))).thenReturn(DfeLegacyAdviceExecutionCode.ACCEPTED);

			assertTrue(service.submitAdvice(mockOrder, AdviceService.ADVICE_STATUS_LABEL_PENDING));
		}

		@Test
		void shouldReturnFalseForInvalidOrder() {
			when(adviceService.pushAdvice(any(AdvisedOrder.class))).thenReturn(DfeLegacyAdviceExecutionCode.REJECTED);

			assertFalse(service.submitAdvice(mockOrder, AdviceService.ADVICE_STATUS_LABEL_PENDING));
		}

		@Test
		void shouldThrowExceptionForServiceError() {
			when(adviceService.pushAdvice(any(AdvisedOrder.class))).thenThrow(new RuntimeException("Server error"));

			assertThrows(AdviceSubmitFailedException.class, () -> service.submitAdvice(mockOrder, AdviceService.ADVICE_STATUS_LABEL_PENDING));
		}
	}

	@Tag("IntegrationTest")
	@SpringBasedLocalMockTest
	@ActiveProfiles(profiles = {"integrationtest", "dfe-legacy-advice.rest", "dfe-legacy-advice.targetsystem.dev.liberty" })
	@TestPropertySource(properties = { "ums.dip.ediTestFlagRoad=false" })
	@Nested
	class AdviceAdapterExtIntegrationTest {

		private static final int GERMANY_MEMMINGEN_BRANCH_NUMBER = 6;

		@Autowired
		AdviceAdapterExt service;

		@Test
		void shouldReturnTrueForValidOrder() {
			// given a road FW order
			final ForwardingOrder forwardingOrder = AdvisedOrderTestUtil.createValidForwardingOrderForAdvice();
			forwardingOrder.setOrderNumber("JUNIT E2E BO BE");
			// having a consolidator/branch processed by advice job on dev env
			forwardingOrder.setBranchId(GERMANY_MEMMINGEN_BRANCH_NUMBER);
			// and having at least an order line
			assertNotNull(forwardingOrder.getOrderLines());
			assertFalse(forwardingOrder.getOrderLines().isEmpty());
			RoadOrderLine orderLine = forwardingOrder.getOrderLines().getFirst();
			// that has some dangerous goods
			ADRDangerousGood dangerousGoodAsADR = dangerousGoodAsADR(100L, 1, "1987");
			LQDangerousGood dangerousGoodAsLQ = dangerousGoodAsLQ(200L, 2);
			EQDangerousGood dangerousGoodAsEQ = dangerousGoodAsEQ(300L, 3);
			DangerousGoodDataItem dangerousGoodDataItem = dangerousGoodAsADR.getDangerousGoodDataItem();
			dangerousGoodAsLQ.setDangerousGoodDataItem(dangerousGoodDataItem);
			orderLine.getDangerousGoods().addAll(Arrays.asList(dangerousGoodAsADR, dangerousGoodAsEQ, dangerousGoodAsLQ));
			// when pushing advice for it
			// then it's accepted
			assertTrue(service.submitAdvice(forwardingOrder, AdviceService.ADVICE_STATUS_ORDER_COMPLETE));
		}

	}

}
