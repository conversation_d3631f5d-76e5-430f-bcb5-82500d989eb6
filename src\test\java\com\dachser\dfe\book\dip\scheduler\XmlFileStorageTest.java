package com.dachser.dfe.book.dip.scheduler;

import com.dachser.dfe.book.dip.xml.XmlFileStorage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class XmlFileStorageTest {

	@TempDir
	Path tempDir;

	private XmlFileStorage xmlFileStorage;

	@BeforeEach
	void setUp() throws NoSuchFieldException, IllegalAccessException {
		xmlFileStorage = new XmlFileStorage();
		java.lang.reflect.Field basePathField = XmlFileStorage.class.getDeclaredField("basePath");
		basePathField.setAccessible(true);
		basePathField.set(xmlFileStorage, tempDir.toString() + "/");
	}

	@Test
	void saveOrder_shouldSaveXmlSuccessfully() throws IOException {
		String order = "<order>content</order>";
		Long shipmentNumber = 12345L;

		xmlFileStorage.saveXml(order, shipmentNumber);

		Path filePath = Paths.get(tempDir.toString(), shipmentNumber + ".xml");
		assertTrue(Files.exists(filePath));
		assertEquals(order, Files.readString(filePath));
	}

	@Test
	void getOrder_shouldReturnOrderXmlContent() throws IOException {
		String order = "<order>content</order>";
		Long shipmentNumber = 12345L;
		Path filePath = Paths.get(tempDir.toString(), shipmentNumber + ".xml");
		Files.writeString(filePath, order);

		String result = xmlFileStorage.getXml(shipmentNumber);

		assertEquals(order, result);
	}

	@Test
	void getOrder_Xml_shouldReturnEmptyStringWhenFileNotFound() {
		Long shipmentNumber = 12345L;

		String result = xmlFileStorage.getXml(shipmentNumber);

		assertEquals("", result);
	}

	@Test
	void cleanUpXmlFiles_shouldDeleteOldFiles() throws IOException {
		String order = "<order>content</order>";
		String shipmentNumber = "12345";
		Path filePath = Paths.get(tempDir.toString(), shipmentNumber + ".xml");
		Files.writeString(filePath, order);
		Files.setLastModifiedTime(filePath, FileTime.fromMillis(System.currentTimeMillis() - 15 * 24 * 60 * 60 * 1000L));

		xmlFileStorage.cleanUpXmlFiles(14);

		assertTrue(Files.notExists(filePath));
	}

	@Test
	void cleanUpXmlFiles_shouldNotDeleteRecentFiles() throws IOException {
		String order = "<order>content</order>";
		String shipmentNumber = "12345";
		Path filePath = Paths.get(tempDir.toString(), shipmentNumber + ".xml");
		Files.writeString(filePath, order);

		xmlFileStorage.cleanUpXmlFiles(14);

		assertTrue(Files.exists(filePath));
	}
}