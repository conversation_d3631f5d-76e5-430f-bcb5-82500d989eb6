package com.dachser.dfe.book.order.validation.road;

import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import com.dachser.dfe.book.order.validation.Messages;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

@RequiredArgsConstructor
public class OrderReferenceValidator implements ConstraintValidator<OrderReferenceValid, RoadOrderReference> {

	private final Translator translator;

	// @formatter:off
	private static final List<OrderReferenceRule> RULES = Arrays.asList(
		new OrderReferenceRule(ReferenceType.EKAER_NUMBER, null, Pattern.compile("[eE]\\d[0-9|a-zA-Z]{13}"), Messages.INVALID_EKAER),
		new OrderReferenceRule(ReferenceType.BOOKING_REFERENCE, null, Pattern.compile("[a-zA-Z\\d\\s:]*"), Messages.INVALID_INPUT),
		new OrderReferenceRule(ReferenceType.IDENTIFICATION_CODE_TRANSPORT, RoadOrderReferenceSubtype.UIT, Pattern.compile("[a-zA-Z0-9]{16}"), Messages.INVALID_UIT)
	);
	// @formatter:on

	@AllArgsConstructor
	private static class OrderReferenceRule {

		private ReferenceType type;

		private RoadOrderReferenceSubtype subtype;

		private Pattern pattern;

		private String failureI18nKey;

	}

	@Override
	public boolean isValid(RoadOrderReference reference, ConstraintValidatorContext context) {
		context.disableDefaultConstraintViolation();
		Optional<OrderReferenceRule> orderReferenceRule = RULES.stream().filter(rule -> rule.type == reference.getReferenceType())
				.filter(rule -> rule.subtype == reference.getReferenceSubtype()).findFirst();
		if (orderReferenceRule.isPresent() && !orderReferenceRule.get().pattern.matcher(reference.getReference()).matches()) {
			context.buildConstraintViolationWithTemplate(translator.toLocale(orderReferenceRule.get().failureI18nKey)).addPropertyNode("reference").addConstraintViolation();
			return false;
		}
		return true;
	}

}
