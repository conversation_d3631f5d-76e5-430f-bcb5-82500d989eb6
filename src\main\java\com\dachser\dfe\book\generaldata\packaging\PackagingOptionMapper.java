package com.dachser.dfe.book.generaldata.packaging;

import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.generaldata.model.GDPackagingOptionDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface PackagingOptionMapper {

	@Mapping(target = "description", source = "name")
	OptionDto map(GDPackagingOptionDto packagingOptionDto);

	List<OptionDto> mapList(List<GDPackagingOptionDto> packagingOptionDtos);
}
