{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:best-practices", ":semanticCommits"], "assignees": ["@emmanuel.clisson"], "reviewers": ["@florian.kugler-external", "@moritz.vogt-external", "@joerg.wirsig-external"], "timezone": "Europe/Berlin", "schedule": ["* 0-5 * * 1"], "separateMultipleMajor": true, "automerge": false, "dependencyDashboardApproval": true, "packageRules": [{"description": "Any major dependency", "matchManagers": ["maven"], "matchDatasources": ["maven"], "matchUpdateTypes": ["major"]}, {"description": "All Dachser minor dependencies", "groupName": "Dachser dependencies (minor + patches)", "matchManagers": ["maven"], "matchDatasources": ["maven"], "matchUpdateTypes": ["minor", "patch"], "matchPackageNames": ["/^com.dachser/"], "recreateWhen": "never"}, {"description": "All third party minor dependencies", "groupName": "Third party dependencies (minor + patches)", "matchManagers": ["maven"], "matchDatasources": ["maven"], "matchUpdateTypes": ["minor", "patch"], "matchPackageNames": ["!/^com.dachser/"], "recreateWhen": "never"}]}