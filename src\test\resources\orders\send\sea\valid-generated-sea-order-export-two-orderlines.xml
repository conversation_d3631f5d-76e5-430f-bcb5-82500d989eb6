<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ForwardingOrder>
    <DocumentHeader>
        <EDISender>
            <PartnerInformation>
                <PartnerID>00000100</PartnerID>
            </PartnerInformation>
        </EDISender>
        <EDIReceiver>
            <PartnerInformation>
                <PartnerGLN>4023083000008</PartnerGLN>
            </PartnerInformation>
        </EDIReceiver>
        <DocumentID>0</DocumentID>
        <DocumentDateTime>2022-12-10T01:00:00Z</DocumentDateTime>
        <TestFlag>1</TestFlag>
    </DocumentHeader>
    <SeaFreightShipment Function="Original" CustomerShipmentReference="shipper_reference123" KindOfShipment="Final" TypeOfCarriage="PreAndMain" OrderCategory="LCL" TransportMovement="Export">
        <MessageDate>2022-12-10</MessageDate>
        <OrderDate>2022-12-10</OrderDate>
        <ShipmentReference TypeOfReference="IV" FlagLoadUnload="Unload">FurtherRef1-InvoiceNumber</ShipmentReference>
        <ShipmentReference TypeOfReference="ON" FlagLoadUnload="LoadAndUnload">PurchaseOrderNo</ShipmentReference>
        <ShipmentReference TypeOfReference="CR" FlagLoadUnload="Load">ConsigneeRefNo</ShipmentReference>
        <ShipmentReference TypeOfReference="IV">Commercial</ShipmentReference>
        <ShipmentReference TypeOfReference="PR">QU127766</ShipmentReference>
        <ShipmentReference TypeOfReference="CMK">Marks and numbers</ShipmentReference>
        <ShipmentReference TypeOfReference="DQ">Delivery note number</ShipmentReference>
        <ShipmentReference TypeOfReference="PK">Packaging list</ShipmentReference>
        <ShipmentReference TypeOfReference="PSN" FlagLoadUnload="Unload">Provider shipment</ShipmentReference>
        <ShipmentReference TypeOfReference="SSN" FlagLoadUnload="Load">Supplier shipment</ShipmentReference>
        <ShipmentReference TypeOfReference="ZZZ">Other reference</ShipmentReference>
        <ShipmentReference TypeOfReference="SI" FlagLoadUnload="Unload">shipper_reference123</ShipmentReference>
        <ShipmentAddress TypeOfAddress="Orderer">
            <AddressIdentification>
                <AddressID>00000100</AddressID>
            </AddressIdentification>
        </ShipmentAddress>
        <ShipmentAddress TypeOfAddress="Consignor">
            <AddressIdentification>
                <AddressID>9999920</AddressID>
            </AddressIdentification>
            <Contact TypeOfContact="Information">
                <Name>shipper_contact</Name>
                <Communication TypeOfCommunication="Phone">54321</Communication>
                <Communication TypeOfCommunication="eMail">email</Communication>
                <Communication TypeOfCommunication="Mobile">12345</Communication>
                <Communication TypeOfCommunication="Fax">fax23</Communication>
            </Contact>
        </ShipmentAddress>
        <ShipmentAddress TypeOfAddress="Forwarder">
            <AddressIdentification>
                <AddressID>010</AddressID>
            </AddressIdentification>
        </ShipmentAddress>
        <ShipmentAddress TypeOfAddress="Importer">
            <Address>
                <Name1>Example GmbH _consignee</Name1>
                <Name2>name2</Name2>
                <Street1>Con Street 11</Street1>
                <PostalCode>88888</PostalCode>
                <CityName>Newtown</CityName>
                <CountryCode>DE</CountryCode>
            </Address>
            <Contact TypeOfContact="Information">
                <Name>Consignora_consignee</Name>
                <Communication TypeOfCommunication="Phone">0000777777777</Communication>
                <Communication TypeOfCommunication="eMail"><EMAIL></Communication>
                <Communication TypeOfCommunication="Mobile">0000777777778</Communication>
            </Contact>
            <GovernmentReference>tax1234</GovernmentReference>
        </ShipmentAddress>
        <ShipmentAddress TypeOfAddress="Consignee">
            <Address>
                <Name1>Example GmbH _consignee</Name1>
                <Name2>name2</Name2>
                <Street1>Con Street 11</Street1>
                <PostalCode>88888</PostalCode>
                <CityName>Newtown</CityName>
                <CountryCode>DE</CountryCode>
            </Address>
            <Contact TypeOfContact="Information">
                <Name>Consignora_consignee</Name>
                <Communication TypeOfCommunication="Phone">0000777777777</Communication>
                <Communication TypeOfCommunication="eMail"><EMAIL></Communication>
                <Communication TypeOfCommunication="Mobile">0000777777778</Communication>
            </Contact>
            <GovernmentReference>tax1234</GovernmentReference>
        </ShipmentAddress>
        <ShipmentAddress TypeOfAddress="UltimateConsignee">
            <Address>
                <Name1>Example GmbH _delivery</Name1>
                <Name2>name2</Name2>
                <Name3>name3</Name3>
                <Street1>Con Street 11</Street1>
                <PostalCode>88888</PostalCode>
                <CityName>Newtown</CityName>
                <CountryCode>DE</CountryCode>
            </Address>
            <Contact TypeOfContact="Information">
                <Name>delivery_contact</Name>
                <Communication TypeOfCommunication="Phone">54321</Communication>
                <Communication TypeOfCommunication="eMail">email</Communication>
                <Communication TypeOfCommunication="Mobile">12345</Communication>
                <Communication TypeOfCommunication="Fax">fax23</Communication>
            </Contact>
        </ShipmentAddress>
        <ShipmentAddress TypeOfAddress="MainNotify">
            <Address>
                <Name1>Example GmbH _notification</Name1>
                <Name2>name2</Name2>
                <Name3>name3</Name3>
                <Street1>Con Street 11</Street1>
                <PostalCode>88888</PostalCode>
                <CityName>Newtown</CityName>
                <CountryCode>DE</CountryCode>
            </Address>
        </ShipmentAddress>
        <Location TypeOfLocation="PortOfLoading">HKHKG</Location>
        <Location TypeOfLocation="PortOfUnloading">DEHAM</Location>
        <TermOfDelivery>EXW</TermOfDelivery>
        <PickupOrder TypeOfPickup="LCL" CustomsGoods="NonCustomsGoods">
            <PickUpDate TypeOfDate="From">2022-09-01T10:00:00Z</PickUpDate>
            <PickUpDate TypeOfDate="To">2022-09-01T15:00:00Z</PickUpDate>
            <GoodsDescription>goods1 | goods2 | goods3 | goods1 | goods2 | goods3</GoodsDescription>
            <AdditionalOrderInformation TypeOfInformation="MKS">markAndNumbers - text - content</AdditionalOrderInformation>
            <AdditionalOrderInformation TypeOfInformation="MKS">Another sea orderline marks and numbers</AdditionalOrderInformation>
            <LoadingInstruction>TLF</LoadingInstruction>
            <OrderReference TypeOfReference="ABT">Customs doc</OrderReference>
            <OrderReference TypeOfReference="CMK">markAndNumbers - text - content</OrderReference>
            <OrderReference TypeOfReference="CMK">Another sea orderline marks and numbers</OrderReference>
            <OrderReference TypeOfReference="SI">shipper_reference123</OrderReference>
            <OrderReference TypeOfReference="SO">00000004711</OrderReference>
            <OrderReference TypeOfReference="EDE">PLATFORM_MANUAL_ENTRY</OrderReference>
            <OrderAddress TypeOfAddress="Pickup">
                <Address>
                    <Name1>Example GmbH _pickup</Name1>
                    <Name2>name2</Name2>
                    <Street1>Con Street 11</Street1>
                    <PostalCode>88888</PostalCode>
                    <CityName>Newtown</CityName>
                    <CountryCode>DE</CountryCode>
                </Address>
            </OrderAddress>
            <OrderPosition>
                <Pieces>2</Pieces>
                <PackageType>EU</PackageType>
                <GrossWeight WeightUnit="KGM">50</GrossWeight>
                <Volume VolumeUnit="MTQ">2</Volume>
                <Dimension>
                    <DimensionUnit>CMT</DimensionUnit>
                    <Length>260</Length>
                    <Width>120</Width>
                    <Height>240</Height>
                </Dimension>
                <HSCode>4564654</HSCode>
                <HSCode>1234567</HSCode>
            </OrderPosition>
            <OrderPosition>
                <Pieces>2</Pieces>
                <PackageType>EU</PackageType>
                <GrossWeight WeightUnit="KGM">50</GrossWeight>
                <Volume VolumeUnit="MTQ">2</Volume>
                <Dimension>
                    <DimensionUnit>CMT</DimensionUnit>
                    <Length>260</Length>
                    <Width>120</Width>
                    <Height>240</Height>
                </Dimension>
                <HSCode>4564654</HSCode>
                <HSCode>1234567</HSCode>
            </OrderPosition>
        </PickupOrder>
    </SeaFreightShipment>
</ForwardingOrder>
