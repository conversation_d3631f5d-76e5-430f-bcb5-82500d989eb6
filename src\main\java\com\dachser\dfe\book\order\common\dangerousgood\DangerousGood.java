package com.dachser.dfe.book.order.common.dangerousgood;

import com.dachser.dfe.book.order.road.RoadOrderLine;
import jakarta.persistence.DiscriminatorColumn;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@Entity
@Inheritance(strategy = InheritanceType.JOINED)
@Table(name = "order_dangerous_good")
@DiscriminatorColumn(name = "dangerous_good_type")
public abstract class DangerousGood {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	private Integer sortingPosition;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "road_order_line_id", nullable = false)
	@ToString.Exclude
	@EqualsAndHashCode.Exclude
	private RoadOrderLine roadOrderLine;

	public abstract DangerousGoodType getDangerousGoodType();

}
