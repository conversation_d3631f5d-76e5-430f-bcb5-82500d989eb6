package com.dachser.dfe.book.order;

import com.dachser.dfe.book.advice.AdviceService;
import com.dachser.dfe.book.annotation.RunOnProfile;
import com.dachser.dfe.book.document.DocumentDownloadZipHolder;
import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ErrorIdOrderCloneFailedException;
import com.dachser.dfe.book.exception.ErrorIdOrderExpiredException;
import com.dachser.dfe.book.exception.OrderTypeSwitchException;
import com.dachser.dfe.book.exception.generic.BadRequestException;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.OrderActionWithoutValidationDto;
import com.dachser.dfe.book.model.OrderProcessResultDto;
import com.dachser.dfe.book.model.OrderResponseBodyDto;
import com.dachser.dfe.book.model.OrderSaveActionDto;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.OrderValidationResultDto;
import com.dachser.dfe.book.model.PackingPositionDto;
import com.dachser.dfe.book.model.PrintLabelStartPosition;
import com.dachser.dfe.book.model.PrintLabelStartPositionDto;
import com.dachser.dfe.book.model.RoadCollectionOrderDto;
import com.dachser.dfe.book.model.RoadForwardingOrderDto;
import com.dachser.dfe.book.order.clone.OrderClonerRegistry;
import com.dachser.dfe.book.order.exception.OrderDeletionFailedException;
import com.dachser.dfe.book.order.exception.OrderStatusInvalidForDeletionException;
import com.dachser.dfe.book.order.exception.OrderStatusTransitionInValidException;
import com.dachser.dfe.book.order.exception.OrderValidationFailedException;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.quote.QuoteInformation;
import com.dachser.dfe.book.quote.validation.QuoteOrderUpdateValidator;
import com.dachser.dfe.book.user.UserContextService;
import jakarta.transaction.Transactional;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.io.FileNotFoundException;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dachser.dfe.book.advice.AdviceService.ADVICE_STATUS_ORDER_DELETED;

@Component
@RequiredArgsConstructor
@Slf4j
public class OrderService {

	private final OrderMapper orderMapper;

	private final OrderValidator orderValidator;

	private final OrderRepositoryFacade orderRepository;

	private final OrderCreateProcessor orderCreateProcessor;

	private final OrderUpdateProcessor orderUpdateProcessor;

	private final OrderStatusUpdater orderStatusUpdater;

	private final OrderProcessorWithoutUpdateBody orderProcessorWithoutUpdateBody;

	private final BulkOrderProcessorWithoutUpdateBody bulkOrderProcessorWithoutUpdateBody;

	private final DocumentService documentService;

	private final QuoteOrderUpdateValidator quoteOrderUpdateValidator;

	private final AdviceService adviceService;

	private final DraftOrderProcessor draftOrderProcessor;

	private final OrderClonerRegistry orderClonerRegistry;

	private final OrderLabelPrinter orderLabelPrinter;

	private final UserContextService userService;

	private final OrderTypeSwitchService orderTypeSwitchService;

	@Transactional
	public OrderResponseBodyDto createNewDraftOrder(@NonNull final Order order) {
		return draftOrderProcessor.createNewDraftOrder(order, null);
	}

	@Transactional
	public OrderResponseBodyDto createNewDraftOrder(@NonNull final BasicOrderDto orderDto) {
		return draftOrderProcessor.createNewDraftOrder(null, orderDto);
	}

	@Transactional
	public OrderProcessResultDto createNewOrder(final BasicOrderDto basicOrder, final OrderSaveActionDto saveAction, final PrintLabelStartPositionDto startPosition) {
		log.debug("Recording new order of type {} being created for customer {} processing action {} startPosition {}", basicOrder.getOrderType(), basicOrder.getCustomerNumber(),
				saveAction, startPosition);
		try {
			return orderCreateProcessor.process(basicOrder, saveAction, startPosition).print(orderLabelPrinter, PrintLabelStartPosition.from(startPosition));
		} catch (Exception exception) {
			if (!(exception instanceof OrderValidationFailedException)) {
				log.error("Error while creating new order of type {} being created for content {} processing action {} startPosition {}", basicOrder.getOrderType(), basicOrder,
						saveAction, startPosition);
			}
			throw exception;
		}
	}

	@Transactional
	public BasicOrderDto updateDraftOrder(final Long orderId, final BasicOrderDto orderDto) {
		log.debug("Updating draft order with id #{}", orderId);
		try {
			final Order updatedOrder = loadAndUpdateDraftOrder(orderId, orderDto);
			log.info("Updated order with id #{}", updatedOrder.getOrderId());
			return (BasicOrderDto) orderMapper.map(updatedOrder);
		} catch (Exception exception) {
			log.error("Error while updating draft order with id #{} and content {}", orderId, orderDto);
			throw exception;
		}
	}

	private Order loadAndUpdateDraftOrder(Long orderId, BasicOrderDto orderDto) {
		final Order loadedOrder = orderRepository.loadOrderById(orderId);
		final QuoteInformation loadedOrderQuoteInformation = loadedOrder.getQuoteInformation();
		boolean customerNumberTransfer = !loadedOrder.getCustomerNumber().equals(orderDto.getCustomerNumber());

		documentService.deleteDocuments(orderDto, loadedOrder);

		if (loadedOrder.isOrderExpired()) {
			throw new ErrorIdOrderExpiredException(BookErrorId.ERR_SV_04, String.format("Order with id %d is expired.", loadedOrder.getOrderId()));
		}
		OrderTransitions.ensureValidTargetStatus(OrderStatus.DRAFT, loadedOrder);

		orderMapper.update(orderDto, loadedOrder);

		if (loadedOrderQuoteInformation != null) {
			quoteOrderUpdateValidator.validateQuoteUpdates(loadedOrderQuoteInformation, loadedOrder);
		}

		final Order updatedOrder = updateOrder(loadedOrder);
		if (customerNumberTransfer) {
			documentService.overrideCustomerNumberInDocuments(updatedOrder.getOrderId(), orderDto.getCustomerNumber());
		}
		updatedOrder.setBranchId(userService.getBranchId(orderDto.getCustomerNumber(), OrderType.getByName(orderDto.getOrderType())));

		return updatedOrder;
	}

	@Transactional(Transactional.TxType.NEVER)
	public OrderProcessResultDto updateOrder(final BasicOrderDto order, final OrderSaveActionDto action, final PrintLabelStartPositionDto startPosition) {
		try {
			log.debug("Updating order with id #{} processing action {} startPosition {}", order.getOrderId(), action, startPosition);
			return orderUpdateProcessor.process(order, action, startPosition).print(orderLabelPrinter, PrintLabelStartPosition.from(startPosition));
		} catch (Exception exception) {
			if (!(exception instanceof OrderValidationFailedException)) {
				log.error("Error while updating order with id #{}, content {} and save action {} startPosition {}", order.getOrderId(), order, action, startPosition);
				log.error("Stacktrace", exception);
			}
			throw exception;
		}
	}

	@Transactional
	public OrderProcessResultDto processOrderWithoutOrderBody(final Long orderId, final OrderActionWithoutValidationDto actionWithoutValidation,
			final PrintLabelStartPositionDto startPosition) {
		try {
			log.debug("Processing order with id #{} processing action {} startPosition {}", orderId, actionWithoutValidation, startPosition);
			return orderProcessorWithoutUpdateBody.process(orderId, actionWithoutValidation, startPosition).print(orderLabelPrinter, PrintLabelStartPosition.from(startPosition));
		} catch (Exception exception) {
			log.error("Error while processing order with id #{} processing action {} startPosition {}", orderId, actionWithoutValidation, startPosition);
			throw exception;
		}
	}

	@Transactional
	public OrderProcessResultDto updateDraftOrderWithOrderTypeSwitch(OrderType targetOrderType, BasicOrderDto basicOrder) {
		BasicOrderDto switchedOrder = checkAndSwitchOrderType(targetOrderType, basicOrder);
		return new OrderProcessResultDto().order((OrderResponseBodyDto) updateDraftOrder(switchedOrder.getOrderId(), switchedOrder));
	}

	private BasicOrderDto checkAndSwitchOrderType(OrderType targetOrderType, BasicOrderDto basicOrder) {
		Order existingOrder = orderRepository.loadOrderById(basicOrder.getOrderId());

		if (!targetOrderType.equals(existingOrder.getOrderType())) {

			if (existingOrder.getQuoteInformation() != null) {
				throw new OrderTypeSwitchException("No OrderType switch possible for Q2B orders");
			}

			if (existingOrder.isOrderExpired()) {
				throw new ErrorIdOrderExpiredException(BookErrorId.ERR_SV_04, String.format("Order with id %d is expired.", existingOrder.getOrderId()));
			}

			Long newOrderIdWithChangedType = orderTypeSwitchService.switchOrderType(existingOrder, targetOrderType);

			basicOrder.setOrderId(newOrderIdWithChangedType);
		}

		return basicOrder;
	}

	@Transactional
	public String deleteOrder(final Long orderId) {
		try {
			final Order order = orderRepository.loadOrderByIdWithoutSoftDeleteCheck(orderId);
			return setOrderStatusDeleted(order);
		} catch (OrderStatusTransitionInValidException transEx) {
			throw new OrderStatusInvalidForDeletionException(transEx.getMessage());
		} catch (Exception ex) {
			UUID traceId = UUID.randomUUID();
			String errorMsg = "Something unexpected occurred while soft deletion of order %s. ".formatted(orderId);
			log.error("TraceId: {}. {}", traceId, errorMsg);
			log.error("TraceId: {}. {}", traceId, ex.getMessage());
			throw new OrderDeletionFailedException(errorMsg);
		}
	}

	@Transactional
	public void deleteOrderWithCorrespondingDocuments(final long orderId) {
		log.info("Deleting all documents for order with id #{}", orderId);
		documentService.deleteAllDocumentsForOrder(orderId);
		log.info("Deleting order with id #{}", orderId);
		final Order orderById = orderRepository.loadOrderById(orderId);
		setOrderStatusDeleted(orderById);
	}

	@Transactional
	public OrderProcessResultDto cloneOrder(Long orderId) {
		try {
			Order order = orderRepository.loadOrderById(orderId);
			Order newOrder = orderClonerRegistry.getOrderCloner(order).orElseThrow().cloneOrder(order);
			return new OrderProcessResultDto().order(orderMapper.map(newOrder));
		} catch (Exception e) {
			log.error("Error while cloning order with id #{}", orderId, e);
			throw new ErrorIdOrderCloneFailedException(BookErrorId.ERR_DU_02.getErrorTitle());
		}
	}

	public Optional<DocumentDownloadZipHolder> downloadDocumentsZippedPerOrderId(final Long orderId) throws FileNotFoundException {
		final Order order = orderRepository.loadOrderById(orderId);
		return documentService.downloadDocumentsZippedPerOrder(order);
	}

	OrderValidationResultDto validateOrder(final Order order) {
		return orderValidator.validateOrder(order);
	}

	private String setOrderStatusDeleted(@NotNull final Order order) {
		OrderTransitions.ensureValidTargetStatus(OrderStatus.DELETED, order);
		if (order instanceof final ForwardingOrder forwardingOrder && OrderStatus.DRAFT != forwardingOrder.getStatus()) {
			final boolean avisSent = adviceService.sendAdviceDataForOrderWithStatusCode(forwardingOrder, ADVICE_STATUS_ORDER_DELETED);
			if (avisSent) {
				log.debug("Avis update sent for order with id #{} and code {}", forwardingOrder.getOrderId(), ADVICE_STATUS_ORDER_DELETED);
			} else {
				log.debug("Avis update rejected for order with id #{} and code {}", forwardingOrder.getOrderId(), ADVICE_STATUS_ORDER_DELETED);
			}
		}
		orderStatusUpdater.updateOrderStatus(order, OrderStatus.DELETED);
		log.debug("Order with id #{} marked for deletion", order.getOrderId());
		return "Order with id #" + order.getOrderId() + " marked for deletion";
	}

	private Order updateOrder(final Order order) {
		return orderRepository.save(order);
	}

	@RunOnProfile(value = { "apitest", "localmock", "local", "dev-memory", "dev" })
	public <T extends Order> T createNewTestOrder(final T order) {
		return draftOrderProcessor.createNewOrder(order);
	}

	@Transactional
	public String printLabelsForOrders(final List<Long> orderIds, final PrintLabelStartPositionDto startPosition) {
		orderIds.sort(Long::compareTo);
		try {
			PrintProcessResultDto printProcessResultDto = getOrderProcessResultDto(orderIds, startPosition);
			return printProcessResultDto.print(orderLabelPrinter, PrintLabelStartPosition.from(startPosition)).getOrderLabel();
		} catch (Exception exception) {
			String orderIdsString = orderIds.stream().map(String::valueOf).collect(Collectors.joining(","));
			log.error("Error while printingLabels for orders with ids {} and startPosition {}", orderIdsString, startPosition);
			throw exception;
		}
	}

	private PrintProcessResultDto getOrderProcessResultDto(List<Long> orderIds, PrintLabelStartPositionDto startPosition) {
		Stream<PrintProcessResultDto> orderProcessResultDtoStream = orderIds.stream()
				.map(orderId -> bulkOrderProcessorWithoutUpdateBody.process(orderId, OrderActionWithoutValidationDto.PRINT_LABELS, startPosition));
		final OrderLabelContainer orderLabelContainer = new OrderLabelContainer();
		orderProcessResultDtoStream.forEach(printProcessResultDto -> orderLabelContainer.merge(printProcessResultDto.getOrderLabelContainer()));
		PrintProcessResultDto printProcessResultDto = new PrintProcessResultDto();
		printProcessResultDto.setOrderLabelContainer(orderLabelContainer);
		return printProcessResultDto;
	}

	void checkIfUserIsAllowedToUsePackingPositions(BasicOrderDto order) {
		if (order instanceof RoadCollectionOrderDto || order instanceof RoadForwardingOrderDto) {
			boolean isNotAllowed = order.getCustomerNumber() != null && !userService.isCustomerAllowedToViewPackingAidPosition(order.getCustomerNumber());

			List<PackingPositionDto> packingPositions = null;

			if (order instanceof RoadCollectionOrderDto roadCollectionOrder && roadCollectionOrder.getPackingPositions() != null) {
				packingPositions = roadCollectionOrder.getPackingPositions();
			} else if (order instanceof RoadForwardingOrderDto roadForwardingOrder && roadForwardingOrder.getPackingPositions() != null) {
				packingPositions = roadForwardingOrder.getPackingPositions();
			}

			if (isNotAllowed && packingPositions != null && !packingPositions.isEmpty()) {
				throw new BadRequestException("User is not allowed to use packing positions");
			}
		}
	}
}
