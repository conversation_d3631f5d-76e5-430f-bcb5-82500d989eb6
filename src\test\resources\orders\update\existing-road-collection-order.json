{"orderType": "RoadCollectionOrder", "orderId": 1, "shipmentNumber": 1231233, "quoteRequestId": 1, "expirationTime": "2023-09-15T23:59:59+02:00", "shipperAddress": {"id": 56867, "customerNumber": null, "name": "EDI Test", "street": "EDI Teststreet 123", "postcode": "87572", "city": "EDI Test City", "countryCode": "DE", "individualId": null, "name2": "EDI Test name 2", "name3": "EDI Test name 3", "street2": "", "supplement": "EDI Test Supplement", "gln": "4023083000008", "taxID": null, "addressType": null, "neutralizeAddress": null, "originAddressId": 9342, "contact": null, "dropOfLocation": null, "lockedByQuote": "full"}, "consigneeAddress": {"id": 56865, "customerNumber": null, "name": "EDEKA Handelsges. mbH", "street": "Im Sutenkamp 2", "postcode": "59069", "city": "Hamm", "countryCode": "DE", "individualId": null, "name2": "Trockensortimentslager", "name3": "EHG Rhein-Ruhr", "street2": null, "supplement": null, "gln": "4313705990078", "taxID": null, "addressType": null, "neutralizeAddress": null, "originAddressId": null, "contact": {"name": "DFE User", "email": "<EMAIL>", "telephone": null, "mobile": null, "fax": null, "id": null}}, "principalAddress": {"id": 56866, "customerNumber": null, "name": "EDEKA Handelsges. mbH", "street": "Im Sutenkamp 2", "postcode": "59069", "city": "Hamm", "countryCode": "DE", "individualId": null, "name2": "Trockensortimentslager", "name3": "EHG Rhein-Ruhr", "street2": null, "supplement": null, "gln": "4313705990078", "taxID": null, "addressType": null, "neutralizeAddress": null, "originAddressId": null, "contact": null, "dropOfLocation": null, "lockedByQuote": null}, "customsGoods": true, "goodsCurrency": "EUR", "goodsValue": 1200, "quoteInformation": null, "orderLineItems": [{"id": 2598, "number": 0, "quantity": 2, "packaging": {"code": "EU", "description": "Euro Pallet"}, "length": 120, "width": 80, "height": 120, "volume": 2.304, "weight": 55, "loadingMeter": null, "content": "Goods Group", "goodsGroup": null, "packingPositionId": null}], "packingPositions": [], "collectionTime": {"collectionDate": "2022-11-15", "from": "2022-11-15T10:43:37.0972671+01:00", "to": "2022-11-15T12:43:37.0972671+01:00"}, "division": "T", "deliveryOption": null, "orderNumber": null, "references": [{"id": 774, "referenceType": "ORDER_NUMBER", "referenceValue": "Order Reference"}, {"referenceType": "DAILY_PRICE_REFERENCE", "referenceValue": "quotation_test"}], "texts": null, "furtherAddresses": [{"id": null, "name": "<PERSON><PERSON>", "name2": null, "name3": null, "street": "street 1", "postcode": "41221", "city": "Test further", "countryCode": "DE", "supplement": null, "gln": null, "addressType": "DP", "neutralizeAddress": null, "contact": null}], "status": null, "fixDate": null, "tailLiftDelivery": true, "deliveryContact": {"name": "", "email": "", "telephone": "", "mobile": "", "fax": null, "id": 62701}, "freightTerm": {"dachserTermKey": "011", "incoTermKey": "EXW", "headline": "Ex Works"}, "orderGroup": null, "ekaerNotRequired": false, "frostProtection": false, "product": "Y", "labelPrinted": false, "transferListPrinted": false, "totalOrderWeight": 55, "interpreter": "FIX", "differentConsigneeAddress": null, "tailLiftCollection": false, "collectionOption": null, "freightPayer": "Principal"}