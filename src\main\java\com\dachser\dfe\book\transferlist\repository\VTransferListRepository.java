package com.dachser.dfe.book.transferlist.repository;

import com.dachser.dfe.book.transferlist.entity.VTransferList;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface VTransferListRepository extends JpaRepository<VTransferList, Long> {

	@Query("SELECT v FROM VTransferList v WHERE v.customerNumberWithSegment IN :customerNumbersWithSegment AND v.addressHash = :addressHash AND v.pickUpDateFrom = :pickUpDateFrom")
	List<VTransferList> findAll(@Param("customerNumbersWithSegment") List<String> customerNumbersWithSegment,
			@Param("addressHash") String addressHash,
			@Param("pickUpDateFrom") LocalDate pickUpDateFrom);
}