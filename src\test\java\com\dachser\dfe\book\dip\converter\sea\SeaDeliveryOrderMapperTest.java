package com.dachser.dfe.book.dip.converter.sea;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.config.term.TermSorter;
import com.dachser.dfe.book.dip.converter.shared.AirSeaGoodsDescriptionMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderContactCommunicationsMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.DateMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.StringNotEmptyConditionMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.generaldata.ContainerTypeMapperImpl;
import com.dachser.dfe.book.generaldata.GeneralDataAdapterMock;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.generaldata.term.TermMapperImpl;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.order.sea.BaseFullContainerLoadRepository;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
@SpringBootTest(properties = "dfe.book.mock=true", classes = { SeaFreightMapperImpl.class, SeaPickupOrderMapperImpl.class, SeaDeliveryOrderMapperImpl.class, DateMapperImpl.class,
		AirSeaOrderContactCommunicationsMapperImpl.class, SeaOrderLineMapperImpl.class, SeaOrderReferenceMapperImpl.class, AirSeaGoodsDescriptionMapperImpl.class,
		StringNotEmptyConditionMapperImpl.class, SeaOrderCategoryMapperImpl.class, SeaContainerMapperImpl.class, GeneralDataAdapterMock.class, GeneralDataService.class,
		ContainerTypeMapperImpl.class, WhiteListFilterConfiguration.class, TermMapperImpl.class, TermSorter.class })
class SeaDeliveryOrderMapperTest {

	private SeaExportOrder seaExportOrder;

	@Autowired
	SeaDeliveryOrderMapper seaDeliveryOrderMapper;

	@MockBean
	private BaseFullContainerLoadRepository baseFullContainerLoadRepository;

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	@BeforeEach
	void setUp() {
		seaExportOrder = testUtil.generateSeaExportOrder();
	}

	@Test
	void shouldMapToDeliveryOrderIfDeliverToIATASet() {
		seaExportOrder.setDeliverToPort(true);
		final List<ForwardingOrder.SeaFreightShipment.DeliveryOrder> deliveryOrders = seaDeliveryOrderMapper.mapDeliveryOrders(seaExportOrder);
		assertNotNull(deliveryOrders);
		assertEquals(1, deliveryOrders.size());
		assertNotNull(deliveryOrders.get(0).getOrderAddress());
	}

	@Test
	void shouldNotMapToDeliveryOrderIfDeliverToIATASet() {
		final List<ForwardingOrder.SeaFreightShipment.DeliveryOrder> deliveryOrders = seaDeliveryOrderMapper.mapDeliveryOrders(seaExportOrder);
		assertNotNull(deliveryOrders);
		assertEquals(0, deliveryOrders.size());
	}

	@Test
	void shouldMapShipperToDeliveryOrder() {
		seaExportOrder.setDeliverToPort(true);
		final List<ForwardingOrder.SeaFreightShipment.DeliveryOrder> deliveryOrders = seaDeliveryOrderMapper.mapDeliveryOrders(seaExportOrder);
		assertNotNull(deliveryOrders);
		assertEquals(1, deliveryOrders.size());
		final ForwardingOrder.SeaFreightShipment.DeliveryOrder.OrderAddress orderAddress = deliveryOrders.get(0).getOrderAddress();
		assertEquals(Types.AddressTypes.DELIVERER, orderAddress.getTypeOfAddress());
		assertEquals("Example GmbH _shipper", orderAddress.getAddress().getName1());
	}

	@Test
	void shouldMapGoodsDescription() {
		seaExportOrder.setDeliverToPort(true);
		final List<ForwardingOrder.SeaFreightShipment.DeliveryOrder> deliveryOrders = seaDeliveryOrderMapper.mapDeliveryOrders(seaExportOrder);
		assertNotNull(deliveryOrders);
		final List<String> goodsDescription = deliveryOrders.get(0).getGoodsDescription();
		assertEquals(1, goodsDescription.size());
		assertEquals("goods1 | goods2 | goods3", goodsDescription.get(0));

	}

}