package com.dachser.dfe.book.quote;

import com.dachser.dfe.book.order.common.orderline.OrderLine;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "quote_order_line")
public class QuoteOrderLine extends OrderLine {

	/**
	 * We need the weight to store the most precise value possible for all types of order lines defined by the minimum
	 * ROAD: 1
	 * AIR: 0.1
	 * SEA: 0.001
	 */
	@DecimalMin("0.001")
	@Max(99999)
	private BigDecimal weight;

	@JoinColumn(name = "quote_information_id", nullable = false)
	@ManyToOne(fetch = FetchType.LAZY)
	@ToString.Exclude
	@EqualsAndHashCode.Exclude
	private QuoteInformation quoteInformation;

	@Column(name = "loading_meter")
	private Double loadingMeter;

	@Column(name = "goods_group_code", length = 2)
	private String goodsGroupCode;

	@Column(name = "goods_group_quantity")
	private Integer goodsGroupQuantity;

	@Column(name = "is_stackable")
	private Boolean isStackable;

	@Size(max = 3)
	@Column(name = "original_packaging_type", length = 3)
	private String originalPackagingType;

	@Size(max = 50)
	@Column(name = "original_packaging_type_description", length = 50)
	private String originalPackagingTypeDescription;

	@Column(name = "packing_position_id")
	private Long packingPositionId;

	@Override
	public void setWeight(BigDecimal weight) {
		this.weight = weight;
	}
}
