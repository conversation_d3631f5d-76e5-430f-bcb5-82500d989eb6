package com.dachser.dfe.book.dip.converter.road.collection;

import com.dachser.dfe.book.dip.converter.road.GeneralTransportDataMapper;
import com.dachser.dfe.book.dip.converter.road.RoadTransportMapper;
import com.dachser.dfe.book.dip.converter.road.ShipmentAddressBuilder;
import com.dachser.dfe.book.dip.converter.shared.DateMapper;
import com.dachser.dfe.book.dip.converter.shared.SharedEdiReference;
import com.dachser.dfe.book.jpa.entity.CustomsType;
import com.dachser.dfe.book.model.Interpreter;
import com.dachser.dfe.book.model.SourceOfOrder;
import com.dachser.dfe.book.model.jaxb.order.road.collection.AdditionalReference;
import com.dachser.dfe.book.model.jaxb.order.road.collection.CollectionInstruction;
import com.dachser.dfe.book.model.jaxb.order.road.collection.ContactInformation;
import com.dachser.dfe.book.model.jaxb.order.road.collection.GoodsValue;
import com.dachser.dfe.book.model.jaxb.order.road.collection.PartnerInformation;
import com.dachser.dfe.book.model.jaxb.order.road.collection.ShipmentAddress;
import com.dachser.dfe.book.model.jaxb.order.road.collection.ShipmentHeader;
import com.dachser.dfe.book.model.jaxb.order.road.collection.ShipmentText;
import com.dachser.dfe.book.model.jaxb.order.road.collection.Transport;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.common.OrderText;
import com.dachser.dfe.book.order.road.CollectionOrder;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

import static com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingTransportMapper.CUSTOMS_REFERENCETYPE;
import static io.micrometer.common.util.StringUtils.isNotBlank;

@Mapper(componentModel = "spring", uses = { DateMapper.class, CollectionShipmentLineMapper.class, CollectionShipmentAddressMapper.class, GeneralTransportDataMapper.class,
		CollectionPackingPositionMapper.class, CollectionOrderOriginalTermMapper.class })
public interface CollectionTransportMapper extends RoadTransportMapper<ShipmentText, PartnerInformation, GoodsValue, AdditionalReference, ShipmentAddress, CollectionOrder> {

	Logger log = LoggerFactory.getLogger(CollectionTransportMapper.class);

	String ADDRESS_TYPE_SHIPPER = "CZ";
	String ADDRESS_TYPE_CONSIGNOR = "PW";

	String SHIPMENT_NUMBER_TEXTTYPE = "MI";
	String FROST_PROTECTION_TEXTTYPE = "TH";

	String SHIPMENT_NUMBER_ADDITION = " W";
	String COLLECTION_INSTRUCTION = "A";

	@Mapping(target = "shipmentHeader", expression = "java(List.of(mapShipmentHeader(order)))")
	Transport map(CollectionOrder order);

	default List<Transport> mapList(CollectionOrder order) {
		return Collections.singletonList(map(order));
	}

	@Mapping(target = "ADRflag", source="dangerousGoodsOrder", qualifiedByName = "mapBoolean")
	@Mapping(target = "customsIndicator", source = "customsGoods", qualifiedByName = "mapBoolean")
	@Mapping(target = "customerShipmentReference", expression = "java( order.getOrderNumber() != null? order.getOrderNumber() : \"\")")
	@Mapping(target = "additionalReference", source = "orderReferences", qualifiedByName = "mapReferences")
	@Mapping(target = "dachserProduct", source = "product")
	@Mapping(target = "shipmentLine", source = ".", qualifiedByName = "mapShipmentLines")
	@Mapping(target = "deliveryDateFixed", source = "order.fixDate")
	@Mapping(target = "division", source = "division")
	@Mapping(target = "goodsValue", source = ".")
	@Mapping(target = "orderGroup", source = "orderGroup")
	@Mapping(target = "originalTerm", source = ".", qualifiedByName = "mapOriginalTerm")
	@Mapping(target = "shipmentAddress", source = ".", qualifiedByName = "customShipmentAddressMapping")
	@Mapping(target = "collectionDate.date", source = "collectionDate", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
	@Mapping(target = "collectionDate.type", source = "interpreter", qualifiedByName = "mapInterpreter", defaultValue = "FIX")
	@Mapping(target = "collectionDate.text", source = ".", qualifiedByName = "mapCollectionDateText")
	@Mapping(target = "shipmentText", source = "orderTexts", qualifiedByName = "mapOrderTexts")
	@Mapping(target = "collectionInstruction", source = "orderTexts", qualifiedByName = "mapCollectionInstruction")
	@Mapping(target = "tailLiftDelivery", source = "order.tailLiftDelivery", qualifiedByName = "mapBoolean")
	@Mapping(target = "tailLiftCollection", source = "order.tailLiftCollection", qualifiedByName = "mapBoolean")
	@Mapping(target = "neutralizeCollection", source = "shipperAddress.neutralize", qualifiedByName = "mapBoolean")
	@Mapping(target = "neutralizeForwarding", source = "differentConsigneeAddress.neutralize", qualifiedByName = "mapBoolean")
	@Mapping(target = "packagingAids", source = ".", conditionExpression = "java( order.getPackingPositions() != null && !order.getPackingPositions().isEmpty() )")
	ShipmentHeader mapShipmentHeader(CollectionOrder order);

	@Mapping(target = "partnerID", source = "address.gln", qualifiedByName = "mapInvalidGlnAsIdOrNull")
	@Mapping(target = "partnerGLN", source = "address.gln", qualifiedByName = "mapValidGlnOrNull")
	@Mapping(target = "partnerName", source = "address", qualifiedByName = "mapAddressNames")
	@Mapping(target = "addressInformation", source = "address")
	PartnerInformation mapPartnerInformationForConsignor(OrderAddress address);

	@Mapping(target = "partnerID", source = "order.differentConsigneeAddress.gln", qualifiedByName = "mapInvalidGlnAsIdOrNull")
	@Mapping(target = "partnerGLN", source = "differentConsigneeAddress.gln", qualifiedByName = "mapValidGlnOrNull")
	@Mapping(target = "partnerName", source = "differentConsigneeAddress", qualifiedByName = "mapAddressNames")
	@Mapping(target = "addressInformation", source = "differentConsigneeAddress")
	@Mapping(target = "contactInformation", source = "deliveryContact")
	PartnerInformation mapPartnerInformationForDifferentConsignee(CollectionOrder order);

	@Mapping(target = "amount", source = "goodsValue")
	@Mapping(target = "currency", source = "currency")
	GoodsValue mapGoodsValue(CollectionOrder order);

	default AdditionalReference createAdditionalReference(String code, String value) {
		AdditionalReference additionalReference = new AdditionalReference();
		additionalReference.setCode(code);
		additionalReference.setReference(value);
		return additionalReference;
	}

	@Named("mapInterpreter")
	default String mapInterpreter(Interpreter interpreter) {
		return interpreter.getValue();
	}

	@Named("mapCollectionDateText")
	default String mapCollectionDateText(CollectionOrder order) {
		DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
		if (order.getCollectionFrom() != null && order.getCollectionTo() != null) {
			return dateTimeFormatter.format(order.getCollectionFrom()) + " - " + dateTimeFormatter.format(order.getCollectionTo());
		}
		return null;
	}

	@Named("mapOrderTexts")
	default List<ShipmentText> mapOrderTexts(List<OrderText> orderTexts) {
		List<ShipmentText> shipmentTexts = new ArrayList<>();
		orderTexts.forEach(orderText -> {
			if (!COLLECTION_INSTRUCTION.equals(orderText.getTextType())) {
				shipmentTexts.add(mapShipmentText(orderText));
			}
		});
		return shipmentTexts;
	}

	@Named("mapCollectionInstruction")
	default CollectionInstruction mapCollectionInstruction(List<OrderText> texts) {
		CollectionInstruction collectionInstruction = null;
		if (texts != null) {
			Optional<OrderText> collectionInstructionTexts = texts.stream().filter(text -> COLLECTION_INSTRUCTION.equals(text.getTextType())).findFirst();
			if (collectionInstructionTexts.isPresent()) {
				collectionInstruction = new CollectionInstruction();
				collectionInstruction.setInstructionType(COLLECTION_INSTRUCTION);
				collectionInstruction.getTextInstruction().addAll(splitTextToChunks(collectionInstructionTexts.get().getText()));
			}
		}
		return collectionInstruction;
	}

	// Workaround - We don't have any information except the branchID for the forwarder, so we use the branchId as partnerID
	default ShipmentAddress buildShipmentAddressForForwarder(Integer branchId, String addressTypeForwarder) {
		ShipmentAddress shipmentAddress = new ShipmentAddress();
		shipmentAddress.setAddressType(addressTypeForwarder);
		PartnerInformation partnerInformation = new PartnerInformation();
		partnerInformation.setPartnerID("%08d".formatted(branchId));
		shipmentAddress.setPartnerInformation(partnerInformation);
		return shipmentAddress;
	}

	/**
	 * we create reduced address for shipper. As we have the shippers address in masterdata, we only need the customernumber
	 */
	default ShipmentAddress buildShipmentAddressForShipper(CollectionOrder order, String addressTypeShipper) {
		ShipmentAddress shippersAddress = new ShipmentAddress();
		shippersAddress.setAddressType(addressTypeShipper);

		PartnerInformation partnerInformation = new PartnerInformation();
		partnerInformation.setPartnerID(order.getCustomerNumber());
		if (order.getConsigneeAddress() != null && order.getConsigneeAddress().getOrderContact() != null) {
			partnerInformation.getContactInformation().add(buildContactInformationFromOrderContact(order.getConsigneeAddress().getOrderContact()));
		}
		shippersAddress.setPartnerInformation(partnerInformation);
		return shippersAddress;
	}

	@AfterMapping
	default void mapShipmentNumberAsText(@MappingTarget ShipmentHeader mappedOrder, CollectionOrder order) {
		ShipmentText shipmentNumberAsText = new ShipmentText();
		shipmentNumberAsText.setTextType(SHIPMENT_NUMBER_TEXTTYPE);
		shipmentNumberAsText.getText().add(String.format("%011d", order.getShipmentNumber()) + SHIPMENT_NUMBER_ADDITION);

		mappedOrder.getShipmentText().add(shipmentNumberAsText);
	}

	default ShipmentAddress buildShipmentAddressFromOrder(CollectionOrder order, String addressType) {
		Function<CollectionOrder, PartnerInformation> partnerInformationMapper = this::mapPartnerInformationForConsignee;
		if (order.getDifferentConsigneeAddress() != null) {
			partnerInformationMapper = this::mapPartnerInformationForDifferentConsignee;
		}
		return new CollectionShipmentAddressBuilder().buildShipmentAddressFromOrder(order, addressType, partnerInformationMapper);
	}

	/**
	 * Mapping is done here, because of the complex mapping of the ID and GLN See functions: mapValidGlnOrNull, mapInvalidGlnAsIdOrNull If after the mapping shipper partnerID is
	 * not set, the principal ID will be used
	 */
	@AfterMapping
	default void mapPrincipalNumberAsPartnerID(@MappingTarget ShipmentHeader mappedOrder, CollectionOrder order) {
		for (ShipmentAddress address : mappedOrder.getShipmentAddress()) {
			if (ADDRESS_TYPE_SHIPPER.equals(address.getAddressType()) && address.getPartnerInformation() != null && (address.getPartnerInformation().getPartnerID() == null
					|| address.getPartnerInformation().getPartnerID().isEmpty())) {
				address.getPartnerInformation().setPartnerID(order.getCustomerNumber());
				break;
			}
		}
	}

	@AfterMapping
	default void mapFrostProtectionAsText(@MappingTarget ShipmentHeader mappedOrder, CollectionOrder order) {
		if (Objects.nonNull(order.getFrostProtection()) && Boolean.TRUE.equals(order.getFrostProtection())) {
			ShipmentText shipmentText = new ShipmentText();
			shipmentText.setTextType(FROST_PROTECTION_TEXTTYPE);
			shipmentText.getText().add("Frost Protected");
			mappedOrder.getShipmentText().add(shipmentText);
		}
	}

	@AfterMapping
	default void mapCustomsInformationAsReference(@MappingTarget ShipmentHeader mappedOrder, CollectionOrder order) {
		if (order.isCustomsGoods() && Objects.nonNull(order.getCustomsType())) {
			AdditionalReference customsReference = new AdditionalReference();
			customsReference.setCode(CUSTOMS_REFERENCETYPE);
			customsReference.setReference(order.getCustomsType().equals(CustomsType.DACHSER) ? "J" : "N");
			mappedOrder.getAdditionalReference().add(customsReference);
		}
	}

	@AfterMapping
	default void mapOrderOriginInformationAsReference(@MappingTarget ShipmentHeader mappedOrder, CollectionOrder order) {
		final AdditionalReference orderOriginReference = new AdditionalReference();
		orderOriginReference.setCode(SharedEdiReference.ORDER_ORIGIN_REFERENCE.getReferenceCode());
		if (order.getDatasource() != null) {
			orderOriginReference.setReference(order.getDatasource().getEdiReferenceValue());
		} else {
			orderOriginReference.setReference(SourceOfOrder.BOOK.getEdiReferenceValue());
		}
		mappedOrder.getAdditionalReference().add(orderOriginReference);
	}

	@AfterMapping
	default void mapDropOfLocationToShipmentText(@MappingTarget ShipmentHeader mappedOrder, CollectionOrder order) {
		if (order.getConsigneeAddress() != null && !StringUtils.isEmpty(order.getConsigneeAddress().getDropOfLocation())) {
			ShipmentText dropOffLocationText = new ShipmentText();
			dropOffLocationText.setTextType("AG");
			dropOffLocationText.getText().add(order.getConsigneeAddress().getDropOfLocation());
			mappedOrder.getShipmentText().add(dropOffLocationText);
		}
	}

	@AfterMapping
	default void mapDifferentConsignorAddress(@MappingTarget ShipmentHeader mappedOrder, CollectionOrder order) {
		if (order.getShipperAddress() != null && !order.getPrincipalAddress().equals(order.getShipperAddress())) {
			ShipmentAddress shipmentAddress = new ShipmentAddress();
			shipmentAddress.setAddressType(ADDRESS_TYPE_CONSIGNOR);
			shipmentAddress.setPartnerInformation(mapPartnerInformationForConsignor(order.getShipperAddress()));
			if (order.getShipperAddress().getOrderContact() != null) {
				shipmentAddress.getPartnerInformation().getContactInformation().add(buildContactInformationFromOrderContact(order.getShipperAddress().getOrderContact()));
			}
			mappedOrder.getShipmentAddress().add(shipmentAddress);
		}
	}

	@AfterMapping
	default void mapCollectionOption(@MappingTarget ShipmentHeader mappedOrder, CollectionOrder order) {
		if (order.getCollectionOption() != null && order.getShipperAddress().getOrderContact() != null) {
			OrderContact orderContact = order.getShipperAddress().getOrderContact();

			addShipmentText(mappedOrder, order.getCollectionOption().getEdiKey(), orderContact.getName());
			addShipmentText(mappedOrder, order.getCollectionOption().getEdiKey(), formatContactText(orderContact));
			if (orderContact.getEmail() != null) {
				addShipmentText(mappedOrder, order.getCollectionOption().getEdiKey(), "eMail: " + orderContact.getEmail());
			}
		}
	}

	@AfterMapping
	default void mapDeliveryOption(@MappingTarget ShipmentHeader mappedOrder, CollectionOrder order) {
		if (order.getDeliveryOption() != null && order.getDeliveryContact() != null) {
			OrderContact orderContact = order.getDeliveryContact();

			addShipmentText(mappedOrder, order.getDeliveryOption().getKey(), orderContact.getName());
			addShipmentText(mappedOrder, order.getDeliveryOption().getKey(), formatContactText(orderContact));
			if (orderContact.getEmail() != null) {
				addShipmentText(mappedOrder, order.getDeliveryOption().getKey(), "eMail: " + orderContact.getEmail());
			}
		}
	}

	private String formatContactText(OrderContact orderContact) {
		StringBuilder contactText = new StringBuilder();
		if (isNotBlank(orderContact.getMobile())) {
			contactText.append("SMS: ").append(orderContact.getMobile());
		}
		if (isNotBlank(orderContact.getTelephone())) {
			if (!contactText.isEmpty()) {
				contactText.append(", ");
			}
			contactText.append("TEL: ").append(orderContact.getTelephone());
		}
		return contactText.toString();
	}


	private void addShipmentText(ShipmentHeader mappedOrder, String textType, String text) {
		if (isNotBlank(text)) {
			ShipmentText shipmentText = new ShipmentText();
			shipmentText.setTextType(textType);
			shipmentText.getText().add(text);
			mappedOrder.getShipmentText().add(shipmentText);
		}
	}

	private List<String> splitTextToChunks(String fullText) {
		List<String> chunks = new ArrayList<>();
		int maxLength = 60;
		if (fullText.length() > maxLength) {
			for (int i = 0; i < fullText.length(); i += maxLength) {
				chunks.add(fullText.substring(i, Math.min(fullText.length(), i + maxLength)));
			}
		} else {
			chunks.add(fullText);
		}
		return chunks;
	}

	private ContactInformation buildContactInformationFromOrderContact(@NonNull OrderContact orderContact) {
		ContactInformation contactInformation = new ContactInformation();
		contactInformation.setContactName(orderContact.getName());
		contactInformation.setContactMobilePhoneNumber(orderContact.getMobile());
		contactInformation.setContactPhoneNumber(orderContact.getTelephone());
		contactInformation.setContactEmail(orderContact.getEmail());
		contactInformation.setContactFaxNumber(orderContact.getFax());
		return contactInformation;
	}

	@Named("mapBoolean")
	default String mapBoolean(Boolean value) {
		return Boolean.TRUE == value ? "Y" : "N";
	}

	class CollectionShipmentAddressBuilder implements ShipmentAddressBuilder<ShipmentAddress, CollectionOrder, PartnerInformation> {
		@Override
		public ShipmentAddress newShipmentAddress() {
			return new ShipmentAddress();
		}

		@Override
		public void setAddressType(ShipmentAddress shipmentAddress, String addressType) {
			shipmentAddress.setAddressType(addressType);
		}

		@Override
		public void setServiceContactType(ShipmentAddress shipmentAddress, String serviceContactType) {
			shipmentAddress.setServiceContactType(serviceContactType);
		}

		@Override
		public void setPartnerInformation(ShipmentAddress shipmentAddress, PartnerInformation partnerInformation) {
			shipmentAddress.setPartnerInformation(partnerInformation);
		}

	}
}
