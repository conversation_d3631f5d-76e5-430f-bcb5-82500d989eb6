package com.dachser.dfe.book.quote.validation;

import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.quote.RoadQuoteInformation;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class PriceRelevantCollectionOrderValidator extends PriceRelevantRoadOrderValidator<CollectionOrder> {

	private static final String GOODS_VALUE = "goodsValue";

	private static final String COLLECTION_OPTION = "collectionOption";

	public PriceRelevantCollectionOrderValidator(PriceRelevantOrderLineFieldsValidator<RoadOrderLine> orderLineValidator,
			PriceRelevantPackingPositionFieldsValidator<PackingPosition> packingPositionValidator, PriceRelevantAddressFieldsValidator addressValidator) {
		super(orderLineValidator, packingPositionValidator, addressValidator);
	}

	public List<PriceRelevantChange> getOrderSpecificChanges(RoadQuoteInformation quoteInformation, CollectionOrder order) {
		final List<PriceRelevantChange> priceRelevantChanges = getPriceRelevantChanges(quoteInformation, order);

		//@formatter:off
		List<Pair<String, Boolean>> roadFields = new ArrayList<>(List.of(
				Pair.of(GOODS_VALUE, Objects.equals(quoteInformation.getGoodsValue(), order.getGoodsValue()) || quoteInformation.getGoodsValue() == null),
				Pair.of(COLLECTION_OPTION, Objects.equals(quoteInformation.getCollectionOption(), order.getCollectionOption()))
		));
		//@formatter:on

		priceRelevantChanges.addAll(buildPriceRelevantChangesFromFields(roadFields));

		PriceRelevantAddressFieldsValidator addressValidator = getAddressValidator();

		priceRelevantChanges.addAll(addressValidator.getPriceRelevantChanges(quoteInformation.getConsigneeAddress(), order.getConsigneeAddress()).stream()
				.map(change -> new PriceRelevantChange(PRINCIPAL_SELECTION + change.fieldName())).toList());

		return priceRelevantChanges;
	}
}
