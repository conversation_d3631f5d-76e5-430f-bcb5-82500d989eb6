package com.dachser.dfe.book.quote.validation;

import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.air.AirOrderLine;
import com.dachser.dfe.book.quote.AirQuoteInformation;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PriceRelevantAirImportOrderValidator extends PriceRelevantAirOrderValidator<AirImportOrder> {

	public PriceRelevantAirImportOrderValidator(PriceRelevantOrderLineFieldsValidator<AirOrderLine> orderLineValidator, PriceRelevantAddressFieldsValidator addressValidator) {
		super(orderLineValidator, addressValidator);
	}

	public List<PriceRelevantChange> getOrderSpecificChanges(AirQuoteInformation quoteInformation, AirImportOrder order) {
		final List<PriceRelevantChange> priceRelevantChanges = getPriceRelevantChanges(quoteInformation, order);

		final PriceRelevantAddressFieldsValidator addressValidator = getAddressValidator();

		if (quoteInformation.getPrincipalAddress() != null) {
			priceRelevantChanges.addAll(addressValidator.getPriceRelevantChanges(quoteInformation.getPrincipalAddress(), order.getConsigneeAddress()).stream()
					.map(change -> new PriceRelevantChange(CONSIGNEE_ADDRESS_PREFIX + change.fieldName())).toList());
			priceRelevantChanges.addAll(addressValidator.getPriceRelevantChanges(quoteInformation.getConsigneeAddress(), order.getDeliveryAddress()).stream()
					.map(change -> new PriceRelevantChange(DELIVERY_ADDRESS_PREFIX + change.fieldName())).toList());
		} else {
			priceRelevantChanges.addAll(addressValidator.getPriceRelevantChanges(quoteInformation.getConsigneeAddress(), order.getConsigneeAddress()).stream()
					.map(change -> new PriceRelevantChange(CONSIGNEE_ADDRESS_PREFIX + change.fieldName())).toList());
		}

		priceRelevantChanges.addAll(addressValidator.getPriceRelevantChanges(quoteInformation.getShipperAddress(), order.getShipperAddress()).stream()
				.map(change -> new PriceRelevantChange(SHIPPER_ADDRESS_PREFIX + change.fieldName())).toList());

		priceRelevantChanges.addAll(addressValidator.getPriceRelevantChanges(quoteInformation.getShipperAddress(), order.getPickupAddress()).stream()
				.map(change -> new PriceRelevantChange(PICKUP_ADDRESS_PREFIX + change.fieldName())).toList());

		return priceRelevantChanges;
	}
}
