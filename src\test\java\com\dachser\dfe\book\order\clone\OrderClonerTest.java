package com.dachser.dfe.book.order.clone;

import com.dachser.dfe.book.SpringBasedLocalMockTest;
import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.model.SourceOfOrder;
import com.dachser.dfe.book.order.OrderService;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaImportOrder;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.OffsetDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

@SpringBasedLocalMockTest
class OrderClonerTest {

	@Autowired
	private OrderClonerRegistry orderCloner;

	@Autowired
	private TestUtil testUtil;

	@Autowired
	private OrderService orderService;


	@Nested
	class AirExportOrderCase {

		@Test
		void shouldCloneAirExportOrder() {
			AirExportOrder airExportOrder = testUtil.generateAirExportOrder();
			AirExportOrder newTestOrder = orderService.createNewTestOrder(airExportOrder);

			AirExportOrder clonedOrder = (AirExportOrder) orderCloner.getOrderCloner(newTestOrder).get().cloneOrder(newTestOrder);
			assertNotEquals(newTestOrder.getOrderId(), clonedOrder.getOrderId());
			assertNotEquals(newTestOrder.getShipmentNumber(), clonedOrder.getShipmentNumber());
			assertEquals(SourceOfOrder.CLONE, clonedOrder.getDatasource());

			assertNull(clonedOrder.getShipmentTransferId());
			assertNull(clonedOrder.getSendAt());
			assertNull(clonedOrder.getSendUser());
		}

		@Test
		void shouldClearQ2BProperties() {
			AirExportOrder airExportOrder = testUtil.generateAirExportOrder();
			airExportOrder.setOrderExpiryDate(OffsetDateTime.now().plusDays(2));
			AirExportOrder newTestOrder = orderService.createNewTestOrder(airExportOrder);

			AirExportOrder clonedOrder = (AirExportOrder) orderCloner.getOrderCloner(newTestOrder).get().cloneOrder(newTestOrder);
			assertNotEquals(newTestOrder.getOrderId(), clonedOrder.getOrderId());
			assertNotEquals(newTestOrder.getShipmentNumber(), clonedOrder.getShipmentNumber());
			assertEquals(SourceOfOrder.CLONE, clonedOrder.getDatasource());

			assertNull(clonedOrder.getShipmentTransferId());
			assertNull(clonedOrder.getSendAt());
			assertNull(clonedOrder.getSendUser());
			assertNull(clonedOrder.getQuoteInformation());
		}

		@Test
		void shouldClearOrderReferencesProperties() {
			AirExportOrder airExportOrder = testUtil.generateAirExportOrder();
			AirExportOrder newTestOrder = orderService.createNewTestOrder(airExportOrder);

			AirExportOrder clonedOrder = (AirExportOrder) orderCloner.getOrderCloner(newTestOrder).get().cloneOrder(newTestOrder);
			assertNotEquals(newTestOrder.getOrderId(), clonedOrder.getOrderId());
			assertNotEquals(newTestOrder.getShipmentNumber(), clonedOrder.getShipmentNumber());
			assertEquals(SourceOfOrder.CLONE, clonedOrder.getDatasource());

			assertFalse(clonedOrder.getOrderReferences().stream().anyMatch(reference -> !reference.getReferenceType().equals(AirSeaOrderReferenceType.SHIPPERS_REFERENCE)));
		}


	}

	@Nested
	class AirImportOrderCase {

		@Test
		void shouldCloneAirImportOrder() {
			AirImportOrder airImportOrder = testUtil.generateAirImportOrder();
			AirImportOrder newTestOrder = orderService.createNewTestOrder(airImportOrder);

			AirImportOrder clonedOrder = (AirImportOrder) orderCloner.getOrderCloner(newTestOrder).get().cloneOrder(newTestOrder);
			assertNotEquals(newTestOrder.getOrderId(), clonedOrder.getOrderId());
			assertNotEquals(newTestOrder.getShipmentNumber(), clonedOrder.getShipmentNumber());
			assertEquals(SourceOfOrder.CLONE, clonedOrder.getDatasource());

			assertNull(clonedOrder.getShipmentTransferId());
			assertNull(clonedOrder.getSendAt());
			assertNull(clonedOrder.getSendUser());
		}
	}

	@Nested
	class SeaExportOrderCase {


		@Test
		void shouldCloneSeaExportOrder() {
			SeaExportOrder airExportOrder = testUtil.generateSeaExportOrder();
			SeaExportOrder newTestOrder = orderService.createNewTestOrder(airExportOrder);

			SeaExportOrder clonedOrder = (SeaExportOrder) orderCloner.getOrderCloner(newTestOrder).get().cloneOrder(newTestOrder);
			assertNotEquals(newTestOrder.getOrderId(), clonedOrder.getOrderId());
			assertNotEquals(newTestOrder.getShipmentNumber(), clonedOrder.getShipmentNumber());
			assertEquals(SourceOfOrder.CLONE, clonedOrder.getDatasource());

			assertNull(clonedOrder.getShipmentTransferId());
			assertNull(clonedOrder.getSendAt());
			assertNull(clonedOrder.getSendUser());
		}
	}

	@Nested
	class SeaImportOrderCase {

		@Test
		void shouldCloneSeaImportOrder() {
			SeaImportOrder seaImportOrder = testUtil.generateSeaImportOrder();
			SeaImportOrder newTestOrder = orderService.createNewTestOrder(seaImportOrder);

			SeaImportOrder clonedOrder = (SeaImportOrder) orderCloner.getOrderCloner(newTestOrder).get().cloneOrder(newTestOrder);
			assertNotEquals(newTestOrder.getOrderId(), clonedOrder.getOrderId());
			assertNotEquals(newTestOrder.getShipmentNumber(), clonedOrder.getShipmentNumber());
			assertEquals(SourceOfOrder.CLONE, clonedOrder.getDatasource());

			assertNull(clonedOrder.getShipmentTransferId());
			assertNull(clonedOrder.getSendAt());
			assertNull(clonedOrder.getSendUser());
		}
	}

	@Nested
	class ForwardingOrderCase {
		@Test
		void shouldCloneForwardingOrder() {
			ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();
			ForwardingOrder newTestOrder = orderService.createNewTestOrder(forwardingOrder);

			ForwardingOrder clonedOrder = (ForwardingOrder) orderCloner.getOrderCloner(newTestOrder).get().cloneOrder(newTestOrder);
			assertNotEquals(newTestOrder.getOrderId(), clonedOrder.getOrderId());
			assertNotEquals(newTestOrder.getShipmentNumber(), clonedOrder.getShipmentNumber());
			assertEquals(SourceOfOrder.CLONE, clonedOrder.getDatasource());

			assertNull(clonedOrder.getShipmentTransferId());
			assertNull(clonedOrder.getSendAt());
			assertNull(clonedOrder.getSendUser());
		}

		@Test
		void shouldCloneForwardingOrderWithDangerousGoods() {
			ForwardingOrder forwardingOrder = testUtil.generateForwardingOrderWithDangerousGoods();
			ForwardingOrder newTestOrder = orderService.createNewTestOrder(forwardingOrder);

			ForwardingOrder clonedOrder = (ForwardingOrder) orderCloner.getOrderCloner(newTestOrder).get().cloneOrder(newTestOrder);
			assertNotEquals(newTestOrder.getOrderId(), clonedOrder.getOrderId());
			assertNotEquals(newTestOrder.getShipmentNumber(), clonedOrder.getShipmentNumber());
			assertEquals(SourceOfOrder.CLONE, clonedOrder.getDatasource());

			assertNull(clonedOrder.getShipmentTransferId());
			assertNull(clonedOrder.getSendAt());
			assertNull(clonedOrder.getSendUser());
			assertEquals(3, clonedOrder.getOrderLines().get(1).getDangerousGoods().size());
		}

	}

	@Nested
	class CollectingOrderCase {
		@Test
		void shouldCloneCollectingOrder() {
			CollectionOrder collectionOrder = testUtil.generateCollectionOrder();
			CollectionOrder newTestOrder = orderService.createNewTestOrder(collectionOrder);

			CollectionOrder clonedOrder = (CollectionOrder) orderCloner.getOrderCloner(newTestOrder).get().cloneOrder(newTestOrder);
			assertNotEquals(newTestOrder.getOrderId(), clonedOrder.getOrderId());
			assertNotEquals(newTestOrder.getShipmentNumber(), clonedOrder.getShipmentNumber());
			assertEquals(SourceOfOrder.CLONE, clonedOrder.getDatasource());

			assertNull(clonedOrder.getShipmentTransferId());
			assertNull(clonedOrder.getSendAt());
			assertNull(clonedOrder.getSendUser());
		}

		@Test
		void shouldResetDeliveryFixDate(){
			CollectionOrder collectionOrder = testUtil.generateCollectionOrder();
			collectionOrder.setFixDate(LocalDate.now());
			CollectionOrder newTestOrder = orderService.createNewTestOrder(collectionOrder);

			CollectionOrder clonedOrder = (CollectionOrder) orderCloner.getOrderCloner(newTestOrder).get().cloneOrder(newTestOrder);
			assertNotEquals(newTestOrder.getOrderId(), clonedOrder.getOrderId());
			assertNotEquals(newTestOrder.getShipmentNumber(), clonedOrder.getShipmentNumber());
			assertEquals(SourceOfOrder.CLONE, clonedOrder.getDatasource());
		}

		@Test
		void shouldCloneCollectionOrderWithDangerousGoods() {
			CollectionOrder collectionOrder = testUtil.generateCollectionOrderWithDangerousGoods();
			CollectionOrder newTestOrder = orderService.createNewTestOrder(collectionOrder);

			CollectionOrder clonedOrder = (CollectionOrder) orderCloner.getOrderCloner(newTestOrder).get().cloneOrder(newTestOrder);
			assertNotEquals(newTestOrder.getOrderId(), clonedOrder.getOrderId());
			assertNotEquals(newTestOrder.getShipmentNumber(), clonedOrder.getShipmentNumber());
			assertEquals(SourceOfOrder.CLONE, clonedOrder.getDatasource());

			assertNull(clonedOrder.getShipmentTransferId());
			assertNull(clonedOrder.getSendAt());
			assertNull(clonedOrder.getSendUser());
			assertEquals(3, clonedOrder.getOrderLines().get(1).getDangerousGoods().size());
		}
	}

}