package com.dachser.dfe.book.order.road;

import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.order.common.OrderSscc;
import com.dachser.dfe.book.order.validation.CompleteOrderValidation;
import com.dachser.dfe.book.order.validation.common.ValidationGroup;
import com.dachser.dfe.book.order.validation.road.CompleteOrderValidationRoadForwarding;
import com.dachser.dfe.book.order.validation.road.FreightTermValid;
import com.dachser.dfe.book.order.validation.road.GoodsGroupValid;
import com.dachser.dfe.book.order.validation.road.ValidForwardingOrder;
import jakarta.persistence.CascadeType;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Entity
@EqualsAndHashCode(callSuper = true)
@DiscriminatorValue("3")
@Data
@Table(name = "order_road_forward")
@GoodsGroupValid(groups = CompleteOrderValidation.class)
@FreightTermValid(groups = { CompleteOrderValidationRoadForwarding.class })
@ValidForwardingOrder(groups = { CompleteOrderValidationRoadForwarding.class })
@ValidationGroup(validationGroupClass = CompleteOrderValidationRoadForwarding.class)
public class ForwardingOrder extends RoadOrder {

	private Boolean selfCollection;

	@Size(max = 30)
	private String transportName;

	private Integer sequenceNumberSscc;

	@Min(1)
	@Max(999)
	private Integer manualNumberSscc;

	@OneToMany(mappedBy = "order", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@ToString.Exclude
	private List<OrderSscc> ssccs;

	private Instant adviceSent;

	private Instant labelsPrintedAt;

	@DecimalMin("0.01")
	@DecimalMax("99.99")
	private BigDecimal palletLocations;

	private boolean cashOnDelivery;

	@DecimalMin("0.01")
	@DecimalMax("99999.99")
	// is used for the cash on delivery amount in EUR, currently no other currency is supported, so it is implicitly EUR
	private BigDecimal cashOnDeliveryAmount;

	public int getTotalLabelCount() {
		return manualNumberSscc != null ? manualNumberSscc : super.getTotalAmountPackages();
	}

	@Override
	public void validationSucceeded() {
		setStatus(OrderStatus.LABEL_PENDING);
	}

	@Override
	public boolean isSuccessfullyValidated() {
		return OrderStatus.LABEL_PENDING == getStatus() || OrderStatus.COMPLETE == getStatus();
	}

	@Override
	public OrderStatus validationSucceededStatus() {
		return OrderStatus.LABEL_PENDING;
	}

	public boolean isReadyForLabelPrint() {
		return isSuccessfullyValidated();
	}

	@Override
	public boolean isLabelPrintRequired() {
		return OrderStatus.LABEL_PENDING == getStatus() || Boolean.FALSE == getLabelPrinted();
	}

	public void addOrderSscc(OrderSscc orderSscc) {
		if (ssccs == null) {
			ssccs = new ArrayList<>();
		}
		ssccs.add(orderSscc);
		orderSscc.setOrder(this);
	}

	@Override
	public OrderType getOrderType() {
		return OrderType.ROADFORWARDINGORDER;
	}

	public List<String> getSsccsAsStrings() {
		return ssccs != null ? ssccs.stream().map(OrderSscc::getSscc).toList() : new ArrayList<>();
	}

	public int calculateShipmentInformationHash() {
		// Maybe we should switch later to dedicated hashcode calculations in subjects as well
		return Objects.hash(getCustomerNumber(), getShipperAddress(), getConsigneeAddress(), getBranchId(), getProduct(), getDivision(), getCollectionFrom(), getCollectionTo(),
				getOrderReferences(), getSsccsAsStrings(), getShipmentNumber(), getOrderNumber(), getFreightTerm(), getOrderLines(), getAddresses(), getOrderTexts(),
				getTailLiftDelivery(), getDeliveryOption());
	}

}
