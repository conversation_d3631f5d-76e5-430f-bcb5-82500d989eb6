-- liquibase formatted sql

-- changeset mvogt:2025-05-26_insert_document_types_dangerous_goods.sql

insert into document_type
    (order_type, category, type, label, system, process, supported_extensions)
values
-- Dangerous Goods Note
-- Road Forwarding
(3, 'label.text.dangerous_goods_documents_label', 'GGDGN', 'label.text.dangerous_good_note_label', 'kta', 'dgn', '[ "PDF" ]'),
-- Road Collection
(2, 'label.text.dangerous_goods_documents_label', 'CODDG', 'label.text.dangerous_good_note_label', 'kta', 'dqs', '[ "PDF" ]'),
-- ASL
(5, 'label.text.dangerous_goods_documents_label', 'GGDGA', 'label.text.dangerous_goods_note_imdg_code', 'kta', 'dqs', '[ "PDF" ]'),
(4, 'label.text.dangerous_goods_documents_label', 'GGDGA', 'label.text.dangerous_goods_note_imdg_code', 'kta', 'dqs', '[ "PDF" ]'),
(1, 'label.text.dangerous_goods_documents_label', 'GGDGA', 'label.text.dangerous_goods_note_imdg_code', 'kta', 'dqs', '[ "PDF" ]'),
(6, 'label.text.dangerous_goods_documents_label', 'GGDGA', 'label.text.dangerous_goods_note_imdg_code', 'kta', 'dqs', '[ "PDF" ]'),

-- Other dangerous goods documents
-- Road Forwarding
(3, 'label.text.dangerous_goods_documents_label', 'GGETC', 'label.text.other_dangerous_good_label', 'kta', 'dqs', '[ "PDF" ]'),
-- Road Collection
(2, 'label.text.dangerous_goods_documents_label', 'GGETC', 'label.text.other_dangerous_good_label', 'kta', 'dqs', '[ "PDF" ]'),
-- ASL
(5, 'label.text.dangerous_goods_documents_label', 'GGETA', 'label.text.other_dangerous_good_label', 'kta', 'dqs', '[ "PDF" ]'),
(4, 'label.text.dangerous_goods_documents_label', 'GGETA', 'label.text.other_dangerous_good_label', 'kta', 'dqs', '[ "PDF" ]'),
(1, 'label.text.dangerous_goods_documents_label', 'GGETA', 'label.text.other_dangerous_good_label', 'kta', 'dqs', '[ "PDF" ]'),
(6, 'label.text.dangerous_goods_documents_label', 'GGETA', 'label.text.other_dangerous_good_label', 'kta', 'dqs', '[ "PDF" ]')