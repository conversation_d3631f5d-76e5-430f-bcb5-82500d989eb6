package com.dachser.dfe.book.order.common.dangerousgood;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DangerousGoodType {

	// @formatter:off
	LQ_DANGEROUS_GOOD(1, "LQDangerousGood", "LQ"),
	EQ_DANGEROUS_GOOD(2, "EQDangerousGood", "EQ"),
	ADR_DANGEROUS_GOOD(3, "ADRDangerousGood", "");
	// @formatter:on

	private final int id;

	private final String name;

	private final String ediType;

}

