package com.dachser.dfe.book.user.platform;

import com.dachser.dfe.book.cache.Cache2KConfiguration;
import com.dachser.dfe.platform.ApiClient;
import com.dachser.dfe.platform.api.PlatformV4Api;
import com.dachser.dfe.platform.api.PlatformV5Api;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.client.RestTemplate;

@Configuration
@ConfigurationProperties(prefix = "dfe.platform.configuration")
@Data
@Import(Cache2KConfiguration.class)
public class PlatformApiConfiguration {

	private String baseURL;

	private boolean useCache;

	private final RestTemplate bookRestTemplate;

	@Bean
	public PlatformV4Api platformV4Api() {
		return new PlatformV4Api(apiClient());

	}

	@Bean
	public PlatformV5Api platformV5Api() {
		if (useCache) {
			return new CacheablePlatformV5Api(apiClient());
		} else {
			return new PlatformV5Api(apiClient());
		}
	}

	private ApiClient apiClient() {
		ApiClient apiClient = new ApiClient(bookRestTemplate);
		apiClient.setBasePath(baseURL);
		return apiClient;
	}

}
