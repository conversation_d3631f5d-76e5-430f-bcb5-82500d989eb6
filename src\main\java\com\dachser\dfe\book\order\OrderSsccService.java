package com.dachser.dfe.book.order;

import com.dachser.dfe.book.jpa.OrderSsccRepository;
import com.dachser.dfe.book.order.common.orderline.OrderLine;
import com.dachser.dfe.book.order.common.OrderSscc;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.user.UserContextService;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class OrderSsccService {

	private final UserContextService userContextService;

	private final OrderSsccRepository orderSsccRepository;

	public int getRequiredSsccAmountForOrder(@NotNull ForwardingOrder order) {
		List<String> currentSsccs = order.getSsccsAsStrings();
		boolean isAllowedToSetManualSscc = order.getManualNumberSscc() != null && userContextService.isCustomerAllowedToSetManualSscc(order.getCustomerNumber());
		int ssccAmountTotal = isAllowedToSetManualSscc ? order.getManualNumberSscc() : calculateSsccAmountNeededForOrder(order);
		return (ssccAmountTotal > currentSsccs.size()) ? (ssccAmountTotal - currentSsccs.size()) : 0;
	}

	public List<OrderSscc> createOrderSsccsFromSsccStrings(List<String> ssccStrings, ForwardingOrder order) {
		return (ssccStrings != null && order != null) ? ssccStrings.stream().map(sscc -> {
			final OrderSscc orderSscc = new OrderSscc();
			orderSscc.setSscc(sscc);
			orderSscc.setOrder(order);
			return orderSsccRepository.save(orderSscc);
		}).collect(Collectors.toList()) : new ArrayList<>();
	}

	private int calculateSsccAmountNeededForOrder(ForwardingOrder order) {
		boolean customerAllowedToViewPackingAidPosition = userContextService.isCustomerAllowedToViewPackingAidPosition(order.getCustomerNumber());

		int sumOfQuantityInOrderLines = order.getOrderLines().stream().map(OrderLine::getQuantity).mapToInt(Integer::intValue).sum();
		int sumOfQuantityInPackingPositions = customerAllowedToViewPackingAidPosition ?
				order.getPackingPositions().stream().map(PackingPosition::getQuantity).mapToInt(Integer::intValue).sum() :
				0;
		return sumOfQuantityInOrderLines + sumOfQuantityInPackingPositions;
	}

}
