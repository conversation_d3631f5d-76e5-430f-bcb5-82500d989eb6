package com.dachser.dfe.book.order.common.dangerousgood;

import jakarta.annotation.Nullable;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Entity
@Table(name = "order_dangerous_good_data_item")
public class DangerousGoodDataItem {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	private String externalDgmId;

	@Size(max = 4)
	private String unNumber;

	private String description;

	@Nullable
	private String defaultDescription;

	private String packingGroup;

	private String mainDanger;

	private boolean nosRequired;

	@Size(max = 4)
	private String classificationCode;

	@Size(max = 5)
	private String subsidiaryHazardOne;

	@Size(max = 5)
	private String subsidiaryHazardTwo;

	@Size(max = 5)
	private String subsidiaryHazardThree;

	@Size(max = 10)
	private String tunnelCode;

	@Size(max = 2)
	private String transportCategory;

}
