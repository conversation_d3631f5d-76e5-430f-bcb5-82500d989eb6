package com.dachser.dfe.book.generaldata.term;

import com.dachser.dfe.book.model.FreightTermDto;
import com.dachser.dfe.book.model.IncoTermDto;
import com.dachser.dfe.book.model.TermDto;
import com.dachser.dfe.generaldata.model.GDTermDto;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class TermMapperTest {

	private final TermMapper termMapper = new TermMapperImpl();

	@Test
	void testMapTerm() {
		GDTermDto gdTermDto = new GDTermDto();
		gdTermDto.setIncoTerm("EXW");
		gdTermDto.setDachserCode("DD4");
		gdTermDto.setFreightTerm("082");
		gdTermDto.setLabel("Test Term");
		gdTermDto.setDescription("Test Description");

		TermDto termDto = termMapper.mapTerm(gdTermDto);

		assertThat(termDto).isNotNull();
		assertThat(termDto.getIncoTerm()).isEqualTo("EXW");
		assertThat(termDto.getDachserCode()).isEqualTo("DD4");
		assertThat(termDto.getFreightTerm()).isEqualTo("082");
		assertThat(termDto.getLabel()).isEqualTo("Test Term");
		assertThat(termDto.getDescription()).isEqualTo("Test Description");
	}

	@Test
	void testMapTerms() {
		List<GDTermDto> gdTermDtos = createTerms();

		List<TermDto> termList = termMapper.mapTerms(gdTermDtos);

		assertThat(termList).isNotNull().hasSize(2);
		assertThat(termList.get(0).getIncoTerm()).isEqualTo("EXW");
		assertThat(termList.get(0).getDachserCode()).isEqualTo("DD4");
		assertThat(termList.get(0).getFreightTerm()).isEqualTo("082");
		assertThat(termList.get(0).getLabel()).isEqualTo("Test Term 1");
		assertThat(termList.get(0).getDescription()).isEqualTo("Test Description 1");
		assertThat(termList.get(1).getIncoTerm()).isEqualTo("FOB");
		assertThat(termList.get(1).getDachserCode()).isEqualTo("DD5");
		assertThat(termList.get(1).getFreightTerm()).isEqualTo("083");
		assertThat(termList.get(1).getLabel()).isEqualTo("Test Term 2");
		assertThat(termList.get(1).getDescription()).isEqualTo("Test Description 2");
	}

	@Test
	void testMappingToIncoTerm(){
		// Given
		List<GDTermDto> terms = createTerms();

		// When
		List<IncoTermDto> incoTerms = termMapper.mapIncoTerms(terms);

		// Then
		assertThat(incoTerms).isNotNull().hasSize(2);
		IncoTermDto firstInco = incoTerms.get(0);
		assertThat(firstInco.getCode()).isEqualTo("EXW");
		assertThat(firstInco.getLabel()).isEqualTo("Test Term 1");
		assertThat(firstInco.getDescription()).isEqualTo("Test Description 1");
		assertThat(firstInco.getDachserCode()).isEqualTo("DD4");

	}

	@Test
	void testMappingToFreightTerm(){
		// Given
		List<GDTermDto> terms = createTerms();

		// When
		List<FreightTermDto> incoTerms = termMapper.mapFreightTerms(terms);

		// Then
		assertThat(incoTerms).isNotNull().hasSize(2);
		FreightTermDto freightTermDto = incoTerms.get(0);
		assertThat(freightTermDto.getIncoTermKey()).isEqualTo("EXW");
		assertThat(freightTermDto.getHeadline()).isEqualTo("Test Term 1");
		assertThat(freightTermDto.getDachserTermKey()).isEqualTo("082");

	}

	@NotNull
	private static List<GDTermDto> createTerms() {
		GDTermDto gdTermDto1 = new GDTermDto();
		gdTermDto1.setIncoTerm("EXW");
		gdTermDto1.setDachserCode("DD4");
		gdTermDto1.setFreightTerm("082");
		gdTermDto1.setLabel("Test Term 1");
		gdTermDto1.setDescription("Test Description 1");

		GDTermDto gdTermDto2 = new GDTermDto();
		gdTermDto2.setIncoTerm("FOB");
		gdTermDto2.setDachserCode("DD5");
		gdTermDto2.setFreightTerm("083");
		gdTermDto2.setLabel("Test Term 2");
		gdTermDto2.setDescription("Test Description 2");

		return List.of(gdTermDto1, gdTermDto2);
	}
}