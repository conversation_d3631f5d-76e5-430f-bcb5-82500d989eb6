package com.dachser.dfe.book.service;

import com.dachser.dfe.book.SpringBasedLocalMockTest;
import com.dachser.dfe.book.dataset.DatasetController;
import com.dachser.dfe.book.model.Option;
import com.dachser.dfe.book.model.options.InterpreterOptions;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

@SpringBasedLocalMockTest
@ActiveProfiles({ "apitest", "localmock" })
@Slf4j
class CollectionInterpreterOptionsServiceTest {

	@MockBean
	DatasetController datasetController;

	@Autowired
	private CollectionInterpreterOptionsService collectionInterpreterOptionsService;

	private List<Option> options;

	@Test
	void options() {
		// given a locale
		LocaleContextHolder.setLocale(Locale.ENGLISH);
		// when loading collection interpreter options
		options = collectionInterpreterOptionsService.getInterpreterOptions();
		// then we obtain results
		assertOptions();
		log.info("i18n options = {}", options);
		// and all items are present
		assertEquals(InterpreterOptions.values().length, options.size());
		// and i18n keys are as expected
		List<String> keys = keys();
		assertTrue(options.stream().map(Option::getKey).allMatch(keys::contains));
		// and i18n translations are as expected
		assertOption(InterpreterOptions.FIX.getKey(), "Fix");
		assertOption(InterpreterOptions.BY.getKey(), "By");
		assertOption(InterpreterOptions.COLLECTION_NOT_BEFORE.getKey(), "Not before");
	}

	private void assertOptions() {
		assertNotNull(options);
		assertFalse(options.isEmpty());
		assertFalse(options.stream().anyMatch(Objects::isNull));
	}

	private void assertOption(String key, String value) {
		assertTrue(options.stream().filter(option -> option.getKey().equals(key)).map(Option::getLabel).anyMatch(value::equals));
	}

	private List<String> keys() {
		return Stream.of(InterpreterOptions.values()).map(InterpreterOptions::getKey).toList();
	}

}