package com.dachser.dfe.book.order;

import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.SpringBasedLocalMockTest;
import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.advice.AdviceService;
import com.dachser.dfe.book.dip.DipService;
import com.dachser.dfe.book.document.Document;
import com.dachser.dfe.book.document.DocumentRepositoryFacade;
import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.document.DocumentStatus;
import com.dachser.dfe.book.document.FileStorageCleanup;
import com.dachser.dfe.book.document.FileType;
import com.dachser.dfe.book.exception.ErrorIdOrderCloneFailedException;
import com.dachser.dfe.book.exception.ErrorIdOrderExpiredException;
import com.dachser.dfe.book.exception.OrderTypeSwitchException;
import com.dachser.dfe.book.hscode.HsCode;
import com.dachser.dfe.book.hscode.HsCodeService;
import com.dachser.dfe.book.jpa.OrderReferenceRepository;
import com.dachser.dfe.book.jpa.entity.CustomsType;
import com.dachser.dfe.book.model.AirExportOrderDto;
import com.dachser.dfe.book.model.AirImportOrderDto;
import com.dachser.dfe.book.model.DocumentResponseDto;
import com.dachser.dfe.book.model.DocumentTypeDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderActionWithoutValidationDto;
import com.dachser.dfe.book.model.OrderAddressDto;
import com.dachser.dfe.book.model.OrderProcessResultDto;
import com.dachser.dfe.book.model.OrderReferenceTypeDto;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.OrderTextDto;
import com.dachser.dfe.book.model.OrderTypeDto;
import com.dachser.dfe.book.model.OrderValidationResultDto;
import com.dachser.dfe.book.model.PrintLabelStartPositionDto;
import com.dachser.dfe.book.model.RoadCollectionOrderDto;
import com.dachser.dfe.book.model.RoadForwardingOrderDto;
import com.dachser.dfe.book.model.RoadOrderLineDto;
import com.dachser.dfe.book.model.RoadOrderReferenceDto;
import com.dachser.dfe.book.model.SeaExportOrderDto;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.exception.OrderDeletionFailedException;
import com.dachser.dfe.book.order.exception.OrderNotFoundException;
import com.dachser.dfe.book.order.exception.OrderStatusInvalidForDeletionException;
import com.dachser.dfe.book.order.exception.OrderStatusTransitionInValidException;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.book.order.sea.SeaOrderReference;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.validation.constraints.NotNull;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dachser.dfe.book.document.scheduler.DocumentScheduler.DAYS_THRESHOLD_SENT_ORDERS;
import static com.dachser.dfe.book.document.scheduler.DocumentScheduler.DAYS_TRESHOLD_DELETED_ORDERS;
import static com.dachser.dfe.book.user.UserServiceMock.VALID_CUST_NO_ASL;
import static com.dachser.dfe.book.user.UserServiceMock.VALID_CUST_NO_ROAD;
import static java.time.temporal.ChronoUnit.DAYS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@SpringBasedLocalMockTest
@TestPropertySource(properties = { "whitelist-config.delivery-products.road.food-logistics=E", "whitelist-config.delivery-products.road.european-logistics=E",
		"whitelist-config.file-types.upload=PDF" })
class OrderServiceTest implements ResourceLoadingTest {

	private static final String USERNAME = "username";

	private static final String ORDERS_NEW_AIR_ORDER_JSON = "orders/new-air-export-order.json";

	private static final String ORDERS_NEW_AIR_IMPORT_ORDER_JSON = "orders/new-air-import-order.json";

	@Autowired
	OrderMapper orderMapper;

	@Autowired
	OrderService orderService;

	@Autowired
	OrderRepositoryFacade orderRepositoryFacade;

	@Autowired
	DocumentService documentService;

	@Autowired
	FileStorageCleanup fileStorageCleanup;

	@MockBean
	AdviceService adviceService;

	@Autowired
	OrderRepository orderRepository;

	@Autowired
	OrderReferenceRepository orderReferenceRepository;

	@Autowired
	DocumentRepositoryFacade documentRepositoryFacade;

	@MockBean
	HsCodeService hsCodeService;

	@MockBean
	BulkOrderProcessorWithoutUpdateBody bulkOrderProcessorWithoutUpdateBody;

	@MockBean
	OrderLabelPrinter orderLabelPrinter;

	@MockBean
	OrderTypeSwitchService orderTypeSwitchService;

	@MockBean
	DipService dipService;

	@BeforeEach
	public void setup() {
		HsCode hsCode = new HsCode();
		hsCode.setId("123456789");
		hsCode.setDescription("hs code description");
		when(hsCodeService.searchHsCodes(anyString())).thenReturn(List.of(hsCode));
	}

	@AfterEach
	public void cleanUp() {
		fileStorageCleanup.deleteAllFoldersInBasePath();
	}

	private RoadOrder loadForwardingOrder(final String customerNumber, final String resourceName) throws IOException {
		final RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert(resourceName, new TypeReference<>() {
		});

		forwardingOrder.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
		forwardingOrder.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
		forwardingOrder.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));

		final RoadOrder order = (RoadOrder) orderMapper.map(forwardingOrder);
		order.setCustomerNumber(customerNumber);
		order.setOrderNumber("123456789");
		return order;
	}

	private CollectionOrder loadCollectingOrder(final String customerNumber, final String resourceName) throws IOException {
		final RoadCollectionOrderDto roadCollectionOrder = loadResourceAndConvert(resourceName, new TypeReference<>() {
		});

		roadCollectionOrder.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
		roadCollectionOrder.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
		roadCollectionOrder.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));

		final CollectionOrder order = (CollectionOrder) orderMapper.map(roadCollectionOrder);
		order.setCustomerNumber(customerNumber);
		order.setOrderNumber("123456789");
		return order;
	}

	private AirOrder loadAirOrder(final String customerNumber, final String resourceName) throws IOException {
		final AirExportOrderDto orderJson = loadResourceAndConvert(resourceName, new TypeReference<>() {
		});
		orderJson.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
		orderJson.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
		orderJson.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));

		final AirOrder order = orderMapper.mapAir(orderJson);

		order.setCustomerNumber(customerNumber);
		return order;
	}

	private SeaOrder loadSeaOrder(final String customerNumber, final String resourceName) throws IOException {
		final SeaExportOrderDto orderJson = loadResourceAndConvert(resourceName, new TypeReference<>() {
		});
		orderJson.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
		orderJson.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
		orderJson.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));

		final SeaOrder order = orderMapper.mapSea(orderJson);

		order.setCustomerNumber(customerNumber);
		return order;
	}

	@Nested
	@WithMockUser("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
	class SeaOrderTest {

		private SeaOrder order;

		@BeforeEach
		void setUp() throws IOException {
			order = loadSeaOrder(VALID_CUST_NO_ASL, "orders/new-sea-order.json");

			order.setOrderReferences(new ArrayList<>());
		}

		@ParameterizedTest
		@EnumSource(AirSeaOrderReferenceType.class)
		void shouldCreateNewSeaOrderWithAllPossibleReferenceTypes(final AirSeaOrderReferenceType typeOfReference) {
			final SeaOrderReference ref = SeaOrderReference.builder().order(order).referenceType(typeOfReference).referenceValue("123456789").loading(true).unloading(false)
					.build();

			order.getOrderReferences().add(ref);

			final SeaOrder newOrder = orderService.createNewTestOrder(order);

			assertNotNull(newOrder);
			assertEquals(1, newOrder.getOrderReferences().size());
			assertEquals(typeOfReference, newOrder.getOrderReferences().get(0).getReferenceType());
			assertEquals("123456789", newOrder.getOrderReferences().get(0).getReferenceValue());
		}

		@Test
		void shouldCreateNewSeaOrderWithoutHsCodes() {
			order.getOrderLines().get(0).setHsCodes(new ArrayList<>());
			final SeaOrder newOrder = orderService.createNewTestOrder(order);

			assertNotNull(newOrder);
			assertEquals(0, newOrder.getOrderLines().get(0).getHsCodes().size());
		}

		@Test
		void shouldCreateMoreThanOneReferenceForType() {
			final SeaOrderReference ref1 = SeaOrderReference.builder().order(order).referenceType(AirSeaOrderReferenceType.INVOICE_NUMBER).referenceValue("123456789").loading(true)
					.unloading(false).build();
			final SeaOrderReference ref2 = SeaOrderReference.builder().order(order).referenceType(AirSeaOrderReferenceType.INVOICE_NUMBER).referenceValue("123456789")
					.loading(false).unloading(true).build();

			order.getOrderReferences().add(ref1);
			order.getOrderReferences().add(ref2);

			final SeaOrder newOrder = orderService.createNewTestOrder(order);

			assertNotNull(newOrder);
			assertEquals(2, newOrder.getOrderReferences().size());
			assertEquals(AirSeaOrderReferenceType.INVOICE_NUMBER, newOrder.getOrderReferences().get(0).getReferenceType());
			assertEquals("123456789", newOrder.getOrderReferences().get(0).getReferenceValue());
			assertEquals(AirSeaOrderReferenceType.INVOICE_NUMBER, newOrder.getOrderReferences().get(1).getReferenceType());
			assertEquals("123456789", newOrder.getOrderReferences().get(1).getReferenceValue());
		}

		@Test
		void shouldCreateSeaOrderWithRequestArrangement() {
			order.setRequestArrangement(true);

			final SeaOrder newOrder = orderService.createNewTestOrder(order);

			assertNotNull(newOrder);
		}
	}

	@Nested
	@WithMockUser("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
	class Success {

		@Test
		void shouldNotFailWhenCollectionOptionIsNotSetAndNoContactIsGiven() throws IOException {
			final CollectionOrder order = loadCollectingOrder(VALID_CUST_NO_ROAD, "orders/new-collection-order-complete-valid.json");
			order.setCollectionOption(null);
			order.setFixDate(LocalDate.now().plusDays(14));
			order.getShipperAddress().setOrderContact(null);
			final Order newOrder = orderService.createNewTestOrder(order);
			final OrderValidationResultDto validationResult = orderService.validateOrder(newOrder);
			assertNotNull(validationResult);
			assertTrue(validationResult.getResults().isEmpty());
		}

		@Test
		void shouldFindAllSentOrdersOlderThan30Days() throws IOException {
			final RoadOrder olderThen30Days = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
			olderThen30Days.setCreator("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9");
			olderThen30Days.setLastEditor("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9");
			olderThen30Days.setCreatedAt(Instant.now());
			olderThen30Days.setLastModified(Instant.now());
			olderThen30Days.setStatus(OrderStatus.SENT);
			olderThen30Days.setBranchId(1);
			olderThen30Days.setShipmentNumber(new Random().nextLong());
			olderThen30Days.setSendAt(OffsetDateTime.now().minusDays(31).toInstant());
			orderRepository.save(olderThen30Days);

			final RoadOrder normalSentOrder = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
			normalSentOrder.setCreator("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9");
			normalSentOrder.setLastEditor("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9");
			normalSentOrder.setStatus(OrderStatus.SENT);
			normalSentOrder.setCreatedAt(Instant.now());
			normalSentOrder.setLastModified(Instant.now());
			normalSentOrder.setBranchId(1);
			normalSentOrder.setShipmentNumber(new Random().nextLong());
			normalSentOrder.setSendAt(OffsetDateTime.now().minusDays(29).toInstant());
			orderRepository.save(normalSentOrder);

			List<Order> sentOrdersOlderThan30Days = orderRepositoryFacade.findAllSentOrdersOlderThan(DAYS_THRESHOLD_SENT_ORDERS);

			assertEquals(1, sentOrdersOlderThan30Days.size());
			assertEquals(olderThen30Days.getOrderId(), sentOrdersOlderThan30Days.get(0).getOrderId());
		}

		@Test
		void shouldFindAllDeletedOrdersOlderThan10Days() throws IOException {
			final RoadOrder olderThen10Days = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
			olderThen10Days.setCreator("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9");
			olderThen10Days.setLastEditor("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9");
			olderThen10Days.setCreatedAt(Instant.now());
			olderThen10Days.setLastModified(Instant.now());
			olderThen10Days.setStatus(OrderStatus.DELETED);
			olderThen10Days.setBranchId(1);
			olderThen10Days.setShipmentNumber(3L);
			olderThen10Days.setLastModified(Instant.now().minus(11, DAYS));
			orderRepository.save(olderThen10Days);

			final RoadOrder normalDeletedOrder = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
			normalDeletedOrder.setCreator("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9");
			normalDeletedOrder.setLastEditor("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9");
			normalDeletedOrder.setCreatedAt(Instant.now());
			normalDeletedOrder.setLastModified(Instant.now());
			normalDeletedOrder.setStatus(OrderStatus.DELETED);
			normalDeletedOrder.setBranchId(1);
			normalDeletedOrder.setShipmentNumber(4L);
			olderThen10Days.setLastModified(Instant.now().minus(9, DAYS));
			orderRepository.save(normalDeletedOrder);

			List<Order> sentOrdersOlderThan30Days = orderRepositoryFacade.findAllDeletedOrdersOlderThan(DAYS_TRESHOLD_DELETED_ORDERS);

			assertEquals(1, sentOrdersOlderThan30Days.size());
			assertEquals(olderThen10Days.getOrderId(), sentOrdersOlderThan30Days.get(0).getOrderId());
		}

		@Test
		void shouldFindAllUnlinkedDocumentsOlderThan2Days() {
			Document docOlderThen2Days = new Document();
			docOlderThen2Days.setCreator("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9");
			docOlderThen2Days.setLastEditor("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9");
			docOlderThen2Days.setDocumentId(1L);
			docOlderThen2Days.setCustomerNumber("11111111");
			docOlderThen2Days.setStatus(DocumentStatus.NEW);
			docOlderThen2Days.setDocumentName("test.pdf");
			docOlderThen2Days.setSize(234L);
			docOlderThen2Days.setFileType(FileType.PDF);
			docOlderThen2Days.setCreatedAt(Instant.now().minus(3, DAYS));

			documentRepositoryFacade.save(docOlderThen2Days);

			Document normalDoc = new Document();
			normalDoc.setCreator("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9");
			normalDoc.setLastEditor("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9");
			normalDoc.setDocumentId(1L);
			normalDoc.setCustomerNumber("11111111");
			normalDoc.setStatus(DocumentStatus.NEW);
			normalDoc.setDocumentName("test.pdf");
			normalDoc.setSize(234L);
			normalDoc.setFileType(FileType.PDF);
			normalDoc.setCreatedAt(Instant.now().minus(1, DAYS));

			List<Document> unlinkedDocumentsOlderThan2Days = documentRepositoryFacade.findUnlinkedDocuments();

			assertEquals(1, unlinkedDocumentsOlderThan2Days.size());
			assertEquals(normalDoc.getDocumentId(), unlinkedDocumentsOlderThan2Days.get(0).getDocumentId());

		}

		@Test
		void shouldSucceedWithWeightOnlyOnSecondRoadOrderLine() throws IOException {
			final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
			order.getOrderLines().get(0).setWeight(null);
			final Order newOrder = orderService.createNewTestOrder(order);
			final OrderValidationResultDto validationResult = orderService.validateOrder(newOrder);
			assertNotNull(validationResult);
			assertTrue(validationResult.getResults().isEmpty());
		}

		@Test
		void shouldSucceedWithDocumentsAndEDN() throws IOException {
			final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
			final RoadOrder newOrder = (RoadOrder) orderService.createNewTestOrder(order);
			final RoadOrderReference roadOrderReference = new RoadOrderReference();
			roadOrderReference.setReferenceType(ReferenceType.DELIVERY_NOTE_NUMBER);
			roadOrderReference.setReference("123213");
			roadOrderReference.setOrder(newOrder);
			newOrder.addOrderReference(roadOrderReference);
			final byte[] content = "test".getBytes();
			final MultipartFile mockFile = new MockMultipartFile("file", "orig", null, content);
			final DocumentResponseDto document1 = documentService.uploadDocument(VALID_CUST_NO_ROAD, newOrder.getOrderId(), USERNAME, 1, "harmless Test",
					MediaType.APPLICATION_PDF, mockFile);
			final DocumentResponseDto document2 = documentService.uploadDocument(VALID_CUST_NO_ROAD, newOrder.getOrderId(), USERNAME, 2, "harmless Different Doc",
					MediaType.APPLICATION_PDF, mockFile);
			final OrderValidationResultDto validationResult = orderService.validateOrder(newOrder);
			assertNotNull(validationResult);
			assertTrue(validationResult.getResults().isEmpty());
		}

		@Test
		void shouldSucceedWithOtherDocument() throws IOException {
			final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
			final RoadOrder newOrder = (RoadOrder) orderService.createNewTestOrder(order);
			final byte[] content = "test".getBytes();
			final MultipartFile mockFile = new MockMultipartFile("file", "orig", null, content);
			final DocumentResponseDto document = documentService.uploadDocument(VALID_CUST_NO_ROAD, newOrder.getOrderId(), USERNAME, 2, "harmless Different Doc",
					MediaType.APPLICATION_PDF, mockFile);
			final OrderValidationResultDto validationResult = orderService.validateOrder(newOrder);
			assertNotNull(validationResult);
			assertTrue(validationResult.getResults().isEmpty());

		}

		@Test
		void shouldSucceedWithBothCollectionTimesEmpty() throws IOException {
			final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
			final RoadOrder newOrder = (RoadOrder) orderService.createNewTestOrder(order);
			newOrder.setCollectionTo(null);
			newOrder.setCollectionFrom(null);
			final OrderValidationResultDto validationResult = orderService.validateOrder(newOrder);
			assertNotNull(validationResult);
			assertTrue(validationResult.getResults().isEmpty());
		}
	}

	@Nested
	@WithMockUser("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
	class NoSuccess {

		@Test
		void shouldFailWhenCollectionOptionIsSetAndNoContactIsGiven() throws IOException {
			final CollectionOrder order = loadCollectingOrder(VALID_CUST_NO_ROAD, "orders/new-collection-order-complete-valid.json");
			order.getShipperAddress().setOrderContact(null);
			order.setFixDate(LocalDate.now().plusDays(14));
			final Order newOrder = orderService.createNewTestOrder(order);
			final OrderValidationResultDto validationResult = orderService.validateOrder(newOrder);
			assertNotNull(validationResult);
			assertFalse(validationResult.getResults().isEmpty());
			assertEquals("shipperAddress.contact", validationResult.getResults().get(0).getField());
		}

		@Test
		void shouldFailWithoutAnInvoiceDocument() throws IOException {
			final AirOrder airOrder = loadAirOrder(VALID_CUST_NO_ASL, "orders/new-air-export-order.json");
			airOrder.getOrderLines().get(0).setWeight(null);
			final Order newOrder = orderService.createNewTestOrder(airOrder);
			newOrder.setCustomsType(CustomsType.CUSTOMER);

			final byte[] content = "test".getBytes();
			final MultipartFile mockFile = new MockMultipartFile("file", "orig", null, content);

			final List<DocumentTypeDto> documents = documentService.getDocumentTypes(OrderTypeDto.AIR_EXPORT_ORDER);
			final Optional<DocumentTypeDto> edn = documents.stream().filter(document -> document.getDescription().equalsIgnoreCase("Export Customs Document")).findFirst();
			final int doctypeIdNotInvoice = edn.get().getTypeId();// must be there otherwhise the exception is fine
			final DocumentResponseDto document1 = documentService.uploadDocument(VALID_CUST_NO_ASL, newOrder.getOrderId(), USERNAME, doctypeIdNotInvoice, "harmless Test",
					MediaType.APPLICATION_PDF, mockFile);

			newOrder.setDocumentIds(List.of());
			final OrderValidationResultDto validationResult = orderService.validateOrder(newOrder);
			assertNotNull(validationResult);
			assertFalse(validationResult.getResults().isEmpty());
		}
	}

	@Nested
	@WithMockUser(username = "e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
	class UpdateOrderTests {

		Order order;

		ForwardingOrder forwardingOrder;

		RoadForwardingOrderDto forwardingOrderDto;

		private AirExportOrderDto basicAirExportOrder;

		private AirImportOrderDto basicAirImportOrder;

		@BeforeEach
		void setup() throws IOException {
			order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
			forwardingOrder = (ForwardingOrder) orderService.createNewTestOrder(order);
			forwardingOrderDto = orderMapper.mapForwardingOrderToDto(forwardingOrder);

			basicAirExportOrder = loadResourceAndConvert(ORDERS_NEW_AIR_ORDER_JSON, new TypeReference<>() {
			});
			basicAirExportOrder.setCustomerNumber(VALID_CUST_NO_ASL);

			basicAirExportOrder.setQuoteRequestId(1L);
			basicAirExportOrder.setExpirationTime(OffsetDateTime.now().plusDays(1));
			orderService.createNewDraftOrder(basicAirExportOrder);

			basicAirImportOrder = loadResourceAndConvert(ORDERS_NEW_AIR_IMPORT_ORDER_JSON, new TypeReference<>() {
			});
			basicAirImportOrder.setCustomerNumber(VALID_CUST_NO_ASL);

			basicAirImportOrder.setQuoteRequestId(1L);
			basicAirImportOrder.setExpirationTime(OffsetDateTime.now().plusDays(1));
		}

		@Test
		void shouldUpdateOrderAddFurtherAddress() {
			final OrderAddressDto furtherAddress = createFurtherAddress("should-update");
			furtherAddress.setAddressType("LP");
			forwardingOrderDto.addFurtherAddressesItem(furtherAddress);
			forwardingOrderDto.setOrderType("RoadForwardingOrder");
			final RoadForwardingOrderDto updatedOrder = (RoadForwardingOrderDto) orderService.updateDraftOrder(forwardingOrder.getOrderId(), forwardingOrderDto);
			final Optional<OrderAddressDto> lpAddress = updatedOrder.getFurtherAddresses().stream().filter(address -> address.getAddressType().equals("LP")).findFirst();
			assertTrue(lpAddress.isPresent());
			final OrderAddressDto orderFurtherAddress = lpAddress.get();
			assertEquals(furtherAddress.getName(), orderFurtherAddress.getName());
			assertEquals(furtherAddress.getStreet(), orderFurtherAddress.getStreet());
		}

		@Test
		void shouldRemoveAndAddOrderReference() {
			forwardingOrderDto.setReferences(null);
			forwardingOrderDto.setOrderType("RoadForwardingOrder");
			RoadForwardingOrderDto updatedOrder = (RoadForwardingOrderDto) orderService.updateDraftOrder(forwardingOrder.getOrderId(), forwardingOrderDto);
			final String refValue = "Ref-123456";
			forwardingOrderDto.addReferencesItem(new RoadOrderReferenceDto().referenceType(OrderReferenceTypeDto.DELIVERY_NOTE_NUMBER).referenceValue(refValue));
			forwardingOrderDto.setOrderType("RoadForwardingOrder");
			updatedOrder = (RoadForwardingOrderDto) orderService.updateDraftOrder(forwardingOrder.getOrderId(), forwardingOrderDto);
			assertFalse(updatedOrder.getReferences().isEmpty());
			assertEquals(2, updatedOrder.getReferences().size());
			assertEquals(refValue, updatedOrder.getReferences().get(0).getReferenceValue());
		}

		@Test
		void shouldRemoveAndAddOrderTexts() {
			forwardingOrderDto.setTexts(null);
			forwardingOrderDto.setOrderType("RoadForwardingOrder");
			RoadForwardingOrderDto updatedOrder = (RoadForwardingOrderDto) orderService.updateDraftOrder(forwardingOrder.getOrderId(), forwardingOrderDto);
			assertTrue(updatedOrder.getTexts().isEmpty());
			final String refValue = "DI-123456";
			final String textType = "DI";
			forwardingOrderDto.addTextsItem(new OrderTextDto().textType(textType).value(refValue));
			forwardingOrderDto.setOrderType("RoadForwardingOrder");
			updatedOrder = (RoadForwardingOrderDto) orderService.updateDraftOrder(forwardingOrder.getOrderId(), forwardingOrderDto);
			final List<OrderTextDto> orderTexts = updatedOrder.getTexts();
			assertFalse(orderTexts.isEmpty());
			assertEquals(1, orderTexts.size());
			assertEquals(refValue, orderTexts.get(0).getValue());
			assertEquals(textType, orderTexts.get(0).getTextType());
		}

		@Test
		void shouldRemoveAndAddOrderLines() {
			forwardingOrderDto.getOrderLineItems().add(createOrderLine("Item-3", 3));
			forwardingOrderDto.setOrderType("RoadForwardingOrder");

			forwardingOrderDto = (RoadForwardingOrderDto) orderService.updateDraftOrder(forwardingOrder.getOrderId(), forwardingOrderDto);
			assertEquals(4, forwardingOrderDto.getOrderLineItems().size());
			assertEquals("Item-3", forwardingOrderDto.getOrderLineItems().get(3).getContent());
			forwardingOrderDto.getOrderLineItems().remove(3);
			forwardingOrderDto.getOrderLineItems().get(0).setContent("Updated Item-1");
			forwardingOrderDto.getOrderLineItems().add(createOrderLine("Item-4", 3));
			forwardingOrderDto.setOrderType("RoadForwardingOrder");

			forwardingOrderDto = (RoadForwardingOrderDto) orderService.updateDraftOrder(forwardingOrder.getOrderId(), forwardingOrderDto);
			final List<RoadOrderLineDto> orderLines = forwardingOrderDto.getOrderLineItems();
			assertEquals(4, orderLines.size());
			assertEquals("Updated Item-1", orderLines.get(0).getContent());
			assertEquals("Item-4", orderLines.get(3).getContent());
		}

		@Test
		void shouldPreventSubmitInStatusSent() throws IOException {

			final RoadOrder normalSentOrder = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
			normalSentOrder.setCreator("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9");
			normalSentOrder.setLastEditor("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9");
			normalSentOrder.setStatus(OrderStatus.SENT);
			normalSentOrder.setCreatedAt(Instant.now());
			normalSentOrder.setLastModified(Instant.now());
			normalSentOrder.setBranchId(1);
			normalSentOrder.setShipmentNumber(2L);
			normalSentOrder.setSendAt(OffsetDateTime.now().minusDays(29).toInstant());
			normalSentOrder.setOrderReferences(new ArrayList<>());
			RoadOrder savedOrder = orderRepository.save(normalSentOrder);

			assertThrows(OrderStatusTransitionInValidException.class,
					() -> orderService.processOrderWithoutOrderBody(savedOrder.getOrderId(), OrderActionWithoutValidationDto.SUBMIT, PrintLabelStartPositionDto.TOP_LEFT));

		}

		private RoadOrderLineDto createOrderLine(final String name, final Integer number) {
			return new RoadOrderLineDto().number(number).content(name).quantity(1).length(100).width(100).height(100).volume(102.0d)
					.packaging(new OptionDto().code("I").description("Package"));
		}

	}

	@Nested
	@WithMockUser(username = "e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
	class DeleteOrderTests {
		ForwardingOrder forwardingOrder;

		AirExportOrder airExportOrder;

		SeaExportOrder seaExportOrder;

		@BeforeEach
		void setup() throws IOException {
			forwardingOrder = (ForwardingOrder) loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
			airExportOrder = (AirExportOrder) loadAirOrder(VALID_CUST_NO_ASL, "orders/new-air-export-order.json");
			seaExportOrder = (SeaExportOrder) loadSeaOrder(VALID_CUST_NO_ASL, "orders/new-sea-order.json");
		}

		@Test
		void shouldDeleteForwardingOrderAndSendAvis() {
			final ForwardingOrder createdOrder = orderService.createNewTestOrder(forwardingOrder);
			orderService.validateOrder(createdOrder);
			final String customerNumber = createdOrder.getCustomerNumber();
			final Long orderId = createdOrder.getOrderId();
			orderService.deleteOrder(orderId);

			verify(adviceService, times(1)).sendAdviceDataForOrderWithStatusCode(any(ForwardingOrder.class), any(String.class));
			assertThrows(OrderNotFoundException.class, () -> orderRepositoryFacade.loadOrderById(orderId));

			final Optional<Order> deletedOrder = orderRepository.findById(createdOrder.getOrderId());
			assertTrue(deletedOrder.isPresent());
			assertEquals(OrderStatus.DELETED, deletedOrder.get().getStatus());
			assertEquals(createdOrder.getStatus(), deletedOrder.get().getPreviousStatus());
		}

		@Test
		void shouldDeleteForwardingOrderAndNotSendAvisOnDraft() {
			final ForwardingOrder createdOrder = orderService.createNewTestOrder(forwardingOrder);
			final Long orderId = createdOrder.getOrderId();
			orderService.deleteOrder(orderId);

			verify(adviceService, times(0)).sendAdviceDataForOrderWithStatusCode(any(ForwardingOrder.class), any(String.class));
			assertThrows(OrderNotFoundException.class, () -> orderRepositoryFacade.loadOrderById(orderId));

			final Optional<Order> deletedOrder = orderRepository.findById(createdOrder.getOrderId());
			assertTrue(deletedOrder.isPresent());
			assertEquals(OrderStatus.DELETED, deletedOrder.get().getStatus());
			assertEquals(createdOrder.getStatus(), deletedOrder.get().getPreviousStatus());
		}

		@Test
		void shouldDeleteAirOrder() {
			final AirExportOrder createdOrder = orderService.createNewTestOrder(airExportOrder);
			final Long orderId = createdOrder.getOrderId();

			orderService.deleteOrder(orderId);

			verify(adviceService, times(0)).sendAdviceDataForOrderWithStatusCode(any(ForwardingOrder.class), any(String.class));
			assertThrows(OrderNotFoundException.class, () -> orderRepositoryFacade.loadOrderById(orderId));

			final Optional<Order> deletedOrder = orderRepository.findById(createdOrder.getOrderId());
			assertTrue(deletedOrder.isPresent());
			assertEquals(OrderStatus.DELETED, deletedOrder.get().getStatus());
			assertEquals(createdOrder.getStatus(), deletedOrder.get().getPreviousStatus());
		}

		@Test
		void shouldDeleteSeaOrder() {
			final SeaExportOrder createdOrder = orderService.createNewTestOrder(seaExportOrder);
			final Long orderId = createdOrder.getOrderId();

			orderService.deleteOrder(orderId);

			verify(adviceService, times(0)).sendAdviceDataForOrderWithStatusCode(any(ForwardingOrder.class), any(String.class));
			assertThrows(OrderNotFoundException.class, () -> orderRepositoryFacade.loadOrderById(orderId));

			final Optional<Order> deletedOrder = orderRepository.findById(createdOrder.getOrderId());
			assertTrue(deletedOrder.isPresent());
			assertEquals(OrderStatus.DELETED, deletedOrder.get().getStatus());
			assertEquals(createdOrder.getStatus(), deletedOrder.get().getPreviousStatus());
		}

		@Test
		void shouldThrowNotFoundForNonExistingOrder() {
			assertThrows(OrderDeletionFailedException.class, () -> orderService.deleteOrder(-1L));
		}

		@Test
		void shouldThrowNotFoundForEmptyOrderId() {
			assertThrows(OrderDeletionFailedException.class, () -> orderService.deleteOrder(null));
		}

		@Test
		void shouldThrowSentOrderStatusInvalidForDeletionException() {

			final ForwardingOrder createdOrder = orderService.createNewTestOrder(forwardingOrder);
			final Long orderId = createdOrder.getOrderId();

			final Optional<Order> orderToDeleteOptional = orderRepository.findById(createdOrder.getOrderId());
			Order orderToDelete = orderToDeleteOptional.get();
			orderToDelete.setStatus(OrderStatus.SENT);
			orderRepository.save(orderToDelete);

			assertThrows(OrderStatusInvalidForDeletionException.class, () -> orderService.deleteOrder(orderId));
		}

		@Test
		void shouldThrowDeleteOrderStatusInvalidForDeletionException() {

			final ForwardingOrder createdOrder = orderService.createNewTestOrder(forwardingOrder);
			final Long orderId = createdOrder.getOrderId();

			final Optional<Order> orderToDeleteOptional = orderRepository.findById(createdOrder.getOrderId());
			Order orderToDelete = orderToDeleteOptional.get();
			orderToDelete.setStatus(OrderStatus.DELETED);
			orderRepository.save(orderToDelete);

			assertThrows(OrderStatusInvalidForDeletionException.class, () -> orderService.deleteOrder(orderId));
		}

	}

	@Nested
	@WithMockUser(username = "e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
	class CloneOrderTests {

		ForwardingOrder forwardingOrder;

		@BeforeEach
		void setup() throws IOException {
			forwardingOrder = (ForwardingOrder) loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
			orderService.createNewTestOrder(forwardingOrder);
		}

		@Test
		void shouldCloneForwardingOrder() {
			OrderProcessResultDto orderProcessResultDto = orderService.cloneOrder(forwardingOrder.getOrderId());

			assertNotNull(orderProcessResultDto);
			RoadForwardingOrderDto order = (RoadForwardingOrderDto) orderProcessResultDto.getOrder();
			assertNotNull(order.getOrderId());
		}

		@Test
		void shouldThrowErrorIdExceptionWhenCloningFails() {
			assertThrows(ErrorIdOrderCloneFailedException.class, () -> orderService.cloneOrder(99999L));
		}

	}

	@Nested
	@WithMockUser("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
	class SwitchOrderTypeTests {

		Order order;

		ForwardingOrder forwardingOrder;

		@BeforeEach
		void setup() throws IOException {
			order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
		}

		@Test
		void shouldRejectQ2BOrder() {

			order.setQuoteInformation(TestUtil.createRoadQuoteInformation());
			forwardingOrder = (ForwardingOrder) orderService.createNewTestOrder(order);

			RoadForwardingOrderDto forwardingOrderDto = orderMapper.mapForwardingOrderToDto(forwardingOrder);
			assertThrows(OrderTypeSwitchException.class, () -> orderService.updateDraftOrderWithOrderTypeSwitch(OrderType.ROADCOLLECTIONORDER, forwardingOrderDto));
		}

		@Test
		void shouldRejectExpiredOrder() {

			forwardingOrder = (ForwardingOrder) orderService.createNewTestOrder(order);
			forwardingOrder.setStatus(OrderStatus.EXPIRED);
			orderRepositoryFacade.save(forwardingOrder);

			RoadForwardingOrderDto forwardingOrderDto = orderMapper.mapForwardingOrderToDto(forwardingOrder);
			assertThrows(ErrorIdOrderExpiredException.class, () -> orderService.updateDraftOrderWithOrderTypeSwitch(OrderType.ROADCOLLECTIONORDER, forwardingOrderDto));
		}

		@Test
		void shouldDoNothingOnIdenticalOrderType() {
			forwardingOrder = (ForwardingOrder) orderService.createNewTestOrder(order);

			RoadForwardingOrderDto forwardingOrderDto = orderMapper.mapForwardingOrderToDto(forwardingOrder);
			forwardingOrderDto.setOrderType("RoadForwardingOrder");
			orderService.updateDraftOrderWithOrderTypeSwitch(OrderType.ROADFORWARDINGORDER, forwardingOrderDto);

			verifyNoInteractions(orderTypeSwitchService);
		}
	}

	@Nested
	class PrintLabelsForOrders {

		@Captor
		private ArgumentCaptor<Long> orderIdArgumentCaptor;

		@Test
		void shouldPrintLabelsInOrder() {
			final PrintProcessResultDto printProcessResultDto = new PrintProcessResultDto();
			final OrderLabelContainer orderLabelContainer = new OrderLabelContainer();
			orderLabelContainer.setRawShipmentLabels(List.of("label".getBytes()));
			printProcessResultDto.setOrderLabelContainer(orderLabelContainer);
			when(bulkOrderProcessorWithoutUpdateBody.process(any(), any(OrderActionWithoutValidationDto.class), any())).thenReturn(printProcessResultDto);
			when(orderLabelPrinter.printLabelsToPDF(any(), any())).thenReturn(new OrderLabelContainer());
			final List<Long> orderIds = Stream.of(3L, 2L, 1L).collect(Collectors.toList());

			orderService.printLabelsForOrders(orderIds, null);

			verify(bulkOrderProcessorWithoutUpdateBody, times(orderIds.size())).process(orderIdArgumentCaptor.capture(), eq(OrderActionWithoutValidationDto.PRINT_LABELS), any());
			final List<Long> sortedOrderIds = orderIdArgumentCaptor.getAllValues();
			assertEquals(orderIds.size(), sortedOrderIds.size());
			assertEquals(1L, sortedOrderIds.get(0));
			assertEquals(2L, sortedOrderIds.get(1));
			assertEquals(3L, sortedOrderIds.get(2));
		}
	}

	@NotNull
	private static OrderAddressDto createFurtherAddress(final String suffix) {
		final OrderAddressDto orderFurtherAddress = new OrderAddressDto();
		orderFurtherAddress.setName("fAName - " + suffix);
		orderFurtherAddress.setStreet("fAStreet - " + suffix);
		orderFurtherAddress.setCountryCode("DE");
		orderFurtherAddress.setCity("fACity - " + suffix);
		orderFurtherAddress.setPostcode("12345");
		return orderFurtherAddress;
	}

}