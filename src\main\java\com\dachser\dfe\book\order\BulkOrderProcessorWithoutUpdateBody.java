package com.dachser.dfe.book.order;

import com.dachser.dfe.book.advice.AdviceService;
import com.dachser.dfe.book.dangerousgoods.DangerousGoodsTransferListService;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ErrorIdBaseException;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.order.exception.OrderSaveProcessException;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
class BulkOrderProcessorWithoutUpdateBody extends OrderProcessorWithoutUpdateBody {
	public BulkOrderProcessorWithoutUpdateBody(List<OrderSubmitter<?>> orderSubmitters, AdviceService adviceService, OrderRepositoryFacade orderRepository, OrderMapper orderMapper,
			OrderLabelGenerator orderLabelGenerator, OrderLabelPrinter labelPrinter, OrderDefaults orderDefaults, List<OrderPostProcessor<?>> postProcessors,
			DangerousGoodsTransferListService dangerousGoodsTransferListService) {
		super(orderSubmitters, adviceService, orderRepository, orderMapper, orderLabelGenerator, labelPrinter, orderDefaults, postProcessors, dangerousGoodsTransferListService);
	}

	@Override
	void handlePrintLabelError(Exception exception) {
		log.debug("Error while printing labels", exception);
		BookErrorId errorId = BookErrorId.ERR_LB_01;
		if (exception instanceof ErrorIdBaseException errorIdException) {
			errorId = errorIdException.getErrorId();
		}
		throw new OrderSaveProcessException(exception.getMessage(), exception, getProcessResult().getValidationResult(), (BasicOrderDto) getProcessResult().getOrder(), errorId);
	}
}
