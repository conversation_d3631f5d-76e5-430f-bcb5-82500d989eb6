package com.dachser.dfe.book.dip.converter.air;

import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderLineMapper;
import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderLineMapperConfig;
import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.model.jaxb.order.asl.AdditionalOrderInformation;
import com.dachser.dfe.book.model.jaxb.order.asl.OrderPosition;
import com.dachser.dfe.book.order.air.AirOrderLine;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, config = AirSeaOrderLineMapperConfig.class)
public interface AirOrderLineMapper extends AirSeaOrderLineMapper {

	@InheritConfiguration
	@Mapping(target = "grossWeight", source = "weight")
	OrderPosition mapOrderPosition(AirOrderLine orderLine);

	List<OrderPosition> mapOrderPositions(List<AirOrderLine> orderLines);

	@Mapping(target = "typeOfInformation", constant = Types.AdditionalOrderInformation.MARKS_AND_NUMBERS)
	@Mapping(target = "value", source = "markAndNumbers")
	AdditionalOrderInformation mapMarksAndNumbers(AirOrderLine orderLine);

	@AfterMapping
	default void removeEmptyMarksAndNumbers(@MappingTarget List<AdditionalOrderInformation> additionalOrderInformationList) {
		additionalOrderInformationList.removeIf(info -> StringUtils.isEmpty(info.getValue()));
	}

	List<AdditionalOrderInformation> mapMarksAndNumbers(List<AirOrderLine> orderLines);
}
