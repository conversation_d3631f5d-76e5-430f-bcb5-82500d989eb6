package com.dachser.dfe.book.document;

import com.dachser.bi.common.security.service.pri.api.scan.bean.FileWrapper;
import com.dachser.bi.common.security.service.pri.api.scan.service.FileScanService;
import com.dachser.bi.common.validation.service.pri.api.file.bean.FileWrapperBean;
import com.dachser.bi.common.validation.service.pri.api.file.service.FileCheckService;
import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.customer.SegmentMapper;
import com.dachser.dfe.book.document.documenttype.DocumentTypeCategory;
import com.dachser.dfe.book.document.documenttype.DocumentTypeCategoryBuilder;
import com.dachser.dfe.book.document.documenttype.DocumentTypeEntry;
import com.dachser.dfe.book.document.exception.CannotUpdateDocumentException;
import com.dachser.dfe.book.document.exception.CustomerDoesNotOwnOrderException;
import com.dachser.dfe.book.document.exception.DocumentDownloadFailedException;
import com.dachser.dfe.book.document.exception.DocumentNotFoundException;
import com.dachser.dfe.book.document.exception.ErrorIdDocumentException;
import com.dachser.dfe.book.document.exception.FileExtensionInvalidException;
import com.dachser.dfe.book.document.exception.FileIsMaliciousException;
import com.dachser.dfe.book.document.exception.MediaTypeNotAllowedException;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.mapper.DocumentMapper;
import com.dachser.dfe.book.model.BasicDocumentDto;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.DocumentDownloadDto;
import com.dachser.dfe.book.model.DocumentDownloadDto.FileTypeEnum;
import com.dachser.dfe.book.model.DocumentGroupDto;
import com.dachser.dfe.book.model.DocumentInfoDto;
import com.dachser.dfe.book.model.DocumentResponseDto;
import com.dachser.dfe.book.model.DocumentResponseDto.UploadStatusEnum;
import com.dachser.dfe.book.model.DocumentTypeDto;
import com.dachser.dfe.book.model.ExtensionDto;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.OrderTypeDto;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.model.SegmentDto;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderRepositoryFacade;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.order.OrderTypeStatusAndOwner;
import com.dachser.dfe.book.order.exception.OrderNotFoundException;
import com.dachser.dfe.book.order.mapper.OrderTypeMapper;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.service.BaseEntityService;
import com.dachser.dfe.book.user.UserContextService;
import jakarta.transaction.Transactional;
import jakarta.validation.constraints.NotNull;
import jakarta.xml.bind.DatatypeConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.dachser.dfe.book.document.DocumentConst.CUSTOMS_CATEGORY;
import static com.dachser.dfe.book.document.DocumentConst.TYPE_COMMERCIAL_INVOICE;
import static com.dachser.dfe.book.document.DocumentConst.TYPE_DG_DOCUMENTS;
import static com.dachser.dfe.book.document.DocumentConst.TYPE_DG_DOCUMENTS_IMDG;
import static com.dachser.dfe.book.document.DocumentExceptionType.CREATE;
import static com.dachser.dfe.book.document.DocumentExceptionType.UPDATE;
import static com.dachser.dfe.book.order.OrderType.isAirOrderType;
import static com.dachser.dfe.book.order.OrderType.isSeaOrderType;

@Service
@Slf4j
@RequiredArgsConstructor
public class DocumentService {
	private static final String MSG_MEDIA_TYPE_IS_NOT_VALID = "MediaType {} is not valid";

	private final DocumentRepositoryFacade documentRepository;

	private final DocumentTypeRepository documentTypeRepository;

	private final FileCheckService fileCheckService;

	private final FileScanService fileScanService;

	private final Translator translator;

	private final UserContextService userContextService;

	private final SegmentMapper segmentMapper;

	private final OrderTypeMapper orderTypeMapper;

	private final DocumentMapper documentMapper;

	private final OrderRepositoryFacade orderRepositoryFacade;

	private final DocumentSortingService documentSortingService;

	private final WhiteListFilterConfiguration whiteListFilterConfiguration;

	private final DocumentZipService documentZipService;

	private final DocumentDataHandler documentDataHandler;

	@Transactional
	public DocumentResponseDto uploadDocument(String customerNumber, Long orderId, String username, Integer documentTypeId, String documentName, MediaType mediaType,
			MultipartFile file) {
		try {
			FileType fileType = FileType.getFileTypeForContentType(mediaType);

			if (fileType == null || !isFileTypeSupported(fileType)) {
				log.warn(MSG_MEDIA_TYPE_IS_NOT_VALID, mediaType);
				throw new MediaTypeNotAllowedException(String.format(MSG_MEDIA_TYPE_IS_NOT_VALID, mediaType));
			}

			if (documentTypeId != null) {
				DocumentType documentType = documentTypeRepository.findByDocumentTypeId(documentTypeId);
				if (!documentType.getSupportedExtensions().contains(fileType)) {
					log.warn(MSG_MEDIA_TYPE_IS_NOT_VALID, mediaType);
					throw new MediaTypeNotAllowedException("MediaType " + mediaType + " is not valid for documentType " + documentType.getLabel());
				}
			}

			checkIfFileExtensionIsCorrect(file.getBytes(), documentName, fileType);
			checkIfFileIsMalicious(file.getBytes(), username, documentName);

			// when uploading a document to a new order, the order id is null
			if (orderId != null) {
				checkIfCustomerOwnsTheOrder(customerNumber, orderId, CREATE);
			}

			Document savedDocument = createNewDocument(customerNumber, orderId, documentTypeId, documentName, fileType, file.getSize());
			try (InputStream inputStream = file.getInputStream()) {
				documentDataHandler.writeToStorage(customerNumber, savedDocument.getDocumentId(), fileType.getExtension(), inputStream);
			}

			DocumentResponseDto response = new DocumentResponseDto();
			response.setDocumentId(savedDocument.getDocumentId());
			response.setDocumentProcessed(false);
			response.setDocumentName(documentName);
			response.setUploadStatus(UploadStatusEnum.SUCCESS);
			return response;
		} catch (IOException e) {
			log.error("Could not read file content {}: ", file, e);
			throw new ErrorIdDocumentException(BookErrorId.ERR_DO_07, "Could not read file");
		}
	}

	@Transactional
	public void deleteDocument(Long documentId) {
		final Document document = documentRepository.loadById(documentId);
		checkDocumentAndDelete(document.getDocumentId());
	}

	@Transactional
	public void deleteAllDocumentsForOrderWithoutCheck(long orderId) {
		List<Document> documentsPerOrder = documentRepository.findByOrderId(orderId);
		documentsPerOrder.forEach(this::doDelete);
	}

	@Transactional
	public void deleteDocumentWithoutCheck(Long documentId) {
		final Document document = documentRepository.loadById(documentId);
		doDelete(document);
	}

	@Transactional
	public void deleteDocuments(BasicOrderDto orderDto, Order loadedOrder) {
		List<Long> difference = getDocumentsOfOrder(loadedOrder).stream().map(Document::getDocumentId).filter(id -> !orderDto.getDocumentIds().contains(id)).toList();
		difference.forEach(this::checkDocumentAndDelete);
		log.info("Deleted following documents {} for order {}", difference, loadedOrder.getOrderId());
	}

	public void deleteAllDocumentsForOrder(long orderId) {
		List<Document> documentsPerOrder = documentRepository.findByOrderId(orderId);
		documentsPerOrder.stream().map(Document::getDocumentId).forEach(this::checkDocumentAndDelete);
	}

	@Transactional
	public void updateDocument(Long documentId, BasicDocumentDto basicDocument) {
		Document documentToUpdate = documentRepository.loadById(documentId);

		checkIfDocumentCanBeUpdatedOrDeleted(documentToUpdate);

		if (basicDocument.getOrderId() != null) {
			Long orderId = basicDocument.getOrderId();
			checkIfCustomerOwnsTheOrder(documentToUpdate.getCustomerNumber(), orderId, UPDATE);
			log.debug("Updating order id to {} of document {}", orderId, documentId);
			documentToUpdate.setOrderId(orderId);
		}

		if (basicDocument.getDocumentTypeId() != null) {
			final Optional<DocumentType> documentTypeById = documentTypeRepository.findById(basicDocument.getDocumentTypeId());
			if (documentTypeById.isPresent()) {
				DocumentType documentType = documentTypeById.get();
				documentToUpdate.setDocumentType(documentType);

				if (documentToUpdate.getDocumentType().getSupportedExtensions().stream()
						.noneMatch(extension -> StringUtils.equalsIgnoreCase(extension.getLabel(), documentToUpdate.getFileType().getLabel()))) {
					throw new MediaTypeNotAllowedException("MediaType " + basicDocument.getExtension() + " is not valid for documentType " + documentType.getLabel());
				}
			}
		}

		if (basicDocument.getStartProcessing() != null && basicDocument.getStartProcessing() && documentToUpdate.getStatus() == DocumentStatus.NEW) {
			documentToUpdate.setStatus(DocumentStatus.READY);
		}

		documentRepository.save(documentToUpdate);
	}

	// Is used within validation, not excluding it leads to persistence exceptions
	@Transactional(Transactional.TxType.NOT_SUPPORTED)
	public List<Document> getDocumentsOfOrder(Order order) {
		List<Document> documentsOfOrder = new ArrayList<>();
		if (order.getOrderId() != null) {
			documentsOfOrder.addAll(documentRepository.findByOrderId(order.getOrderId()));
		}
		final List<Long> alreadyAssociatedDocsIds = documentsOfOrder.stream().map(Document::getDocumentId).toList();
		if (order.getDocumentIds() != null) {
			final List<Document> additionalDocuments = order.getDocumentIds().stream().filter(id -> !alreadyAssociatedDocsIds.contains(id)).map(documentRepository::findById)
					.filter(Optional::isPresent).map(Optional::get).toList();
			documentsOfOrder.addAll(additionalDocuments);
		}
		log.debug("Order {} has following documents {}", order.getOrderId(), documentsOfOrder);
		return documentsOfOrder;
	}

	@Transactional(Transactional.TxType.NOT_SUPPORTED)
	public List<Document> getDocumentsOfOrder(Long orderId) {
		// Checking order permissions
		return documentRepository.findByOrderId(orderId);
	}

	public List<DocumentTypeDto> getRecentlyUsedDocumentTypes(String customerNumber, SegmentDto segment) {
		Segment mappedSegment = segmentMapper.map(segment);

		List<DocumentTypeDto> recentlyUsed = new ArrayList<>();
		for (DocumentGroupReturn document : documentRepository.getRecentlyUsedDocumentTypes(customerNumber, Instant.now().minus(30, ChronoUnit.DAYS))) {
			DocumentTypeDto documentType = new DocumentTypeDto();
			documentType.setCategory(translator.toLocale(document.getDocumentCategory()));
			documentType.setType(document.getDocumentType());
			documentType.setTypeId(document.getDocumentTypeId());

			DocumentType type = documentTypeRepository.findByDocumentTypeId(document.getDocumentTypeId());
			if (type != null) {
				documentType.setSupportedExtensions(mapSupportedExtensionsFromFileTypes(type.getSupportedExtensions()));
			}

			if (document.getDocumentTypeLabel() != null) {
				String translatedType = translator.toLocale(document.getDocumentTypeLabel());
				if (Segment.AIR.equals(mappedSegment) && TYPE_COMMERCIAL_INVOICE.equals(document.getDocumentType())) {
					documentType.setDescription(translatedType + "*");
				} else {
					documentType.setDescription(translatedType);
				}
			}
			recentlyUsed.add(documentType);
		}

		// DFE-4551 - For now DG should only be available for ROAD orders
		if (Segment.AIR.equals(mappedSegment) || Segment.SEA.equals(mappedSegment)) {
			removeDGDocumentTypes(recentlyUsed);
		}

		return recentlyUsed;
	}

	public List<DocumentTypeDto> getDocumentTypes(OrderTypeDto orderType) {
		final OrderType mappedOrderType = orderTypeMapper.map(orderType);

		final DocumentTypeCategoryBuilder documentTypeCategoryBuilder = DocumentTypeCategoryBuilder.create(translator);

		documentTypeRepository.findByOrderType(mappedOrderType.getId()).forEach(documentTypeCategoryBuilder::addCategory);
		List<DocumentTypeCategory> transformedTypes = documentTypeCategoryBuilder.build();

		// we iterate over categories and translate and sort the types accordingly
		List<DocumentTypeDto> types = new ArrayList<>();
		for (DocumentTypeCategory subSetOfTypes : transformedTypes) {

			if (subSetOfTypes.getCategory().equals(CUSTOMS_CATEGORY)) {
				sortCustomsDocumentTypes(subSetOfTypes.getTypes(), mappedOrderType);
			} else {
				sortOtherDocumentTypes(subSetOfTypes.getTypes());
			}

			for (DocumentTypeEntry type : subSetOfTypes.getTypes()) {
				DocumentTypeDto tmpType = new DocumentTypeDto();

				tmpType.setCategory(subSetOfTypes.getTranslatedCategory());
				tmpType.setType(type.getType());
				tmpType.setTypeId(type.getId());

				tmpType.setSupportedExtensions(mapSupportedExtensionsFromFileTypes(type.getSupportedExtensions()));
				if ((isAirOrderType(mappedOrderType)) && TYPE_COMMERCIAL_INVOICE.equals(type.getType())) {
					tmpType.setDescription(type.getLabel() + "*");
				} else {
					tmpType.setDescription(type.getLabel());
				}

				types.add(tmpType);
			}
		}

		// DFE-4551 - For now DG should only be available for ROAD orders
		if (isAirOrderType(mappedOrderType) || isSeaOrderType(mappedOrderType)) {
			removeDGDocumentTypes(types);
		}

		return types;
	}

	private List<ExtensionDto> mapSupportedExtensionsFromFileTypes(List<FileType> fileTypes) {
		List<ExtensionDto> supportedExtensions = new ArrayList<>();

		if (fileTypes != null) {
			for (FileType fileType : fileTypes) {
				supportedExtensions.add(mapFileType(fileType));
			}
		}

		return supportedExtensions;
	}

	private ExtensionDto mapFileType(FileType fileType) {
		ExtensionDto extension = new ExtensionDto();
		extension.setLabel(fileType.getLabel());
		extension.setDescription(fileType.getContentType().toString());
		return extension;
	}

	public Optional<DocumentType> findDocumentTypeById(@NotNull String documentTypeId) {
		return documentTypeRepository.findById(Integer.parseInt(documentTypeId));
	}

	public DocumentDownloadDto downloadDocument(Long documentId) {
		final Document document = documentRepository.loadById(documentId);
		FileType fileType = Objects.requireNonNullElse(document.getFileType(), FileType.BINARY);
		DocumentDownloadDto documentDownload = createDocumentDownloadWithContent(document);
		documentDownload.setDocumentName(document.getDocumentName());
		documentDownload.setFileType(FileTypeEnum.valueOf(fileType.toString()));
		return documentDownload;
	}

	protected DocumentDownloadDto createDocumentDownloadWithContent(Document document) {
		DocumentDownloadDto documentDownload = new DocumentDownloadDto();
		try (InputStream stream = documentDataHandler.retrieveData(document)) {
			documentDownload.setContent(DatatypeConverter.printBase64Binary(stream.readAllBytes()));
		} catch (ErrorIdDocumentException ex) {
			throw new ErrorIdDocumentException(BookErrorId.ERR_DO_10, ex.getMessage());
		} catch (Exception ex) {
			log.error(ex.getMessage());
			throw new DocumentDownloadFailedException(String.format("Download of document with id %d not possible", document.getDocumentId()));
		}
		return documentDownload;
	}

	/**
	 * For road orders its necessary to forward documents on order complete</br>
	 * This will happen same time as advice is sent
	 *
	 * @param order
	 */
	@Transactional
	public void forwardCustomsDocumentsOnOrderComplete(RoadOrder order) {
		if (OrderType.isRoadOrderType(order.getOrderType()) && (order.getStatus() == OrderStatus.COMPLETE || order.getStatus() == OrderStatus.LABEL_PENDING)) {
			documentRepository.findByOrderId(order.getOrderId()).stream()
					.filter(document -> document.getStatus().equals(DocumentStatus.NEW) && document.getDocumentType().getCategory().equals(CUSTOMS_CATEGORY)).forEach(document -> {
						document.setStatus(DocumentStatus.READY);
						documentRepository.save(document);
					});
		}
	}

	public List<DocumentGroupDto> getDocumentsGrouped(Long orderId) {
		// Checking order permissions
		return documentSortingService.sortDocumentResponse(documentMapper.mapDocumentInfo(getDocumentsOfOrder(orderId)));
	}

	public List<DocumentInfoDto> getDocuments(Long orderId) {
		// Checking order permissions
		List<DocumentInfoDto> documentInfoList = documentMapper.mapDocumentInfo(getDocumentsOfOrder(orderId));
		return documentSortingService.sortDocumentInfoById(documentInfoList);
	}

	public boolean isFileTypeSupported(FileType fileType) {
		return whiteListFilterConfiguration.getFileTypes().getUpload().contains(fileType.name());
	}

	/**
	 * The customs documents needs to be sorted according to the order type. <br>
	 * some order types have more restrictions then the others
	 */
	private void sortCustomsDocumentTypes(List<DocumentTypeEntry> customsTypes, OrderType orderType) {
		List<DocumentTypeEntry> sortedList = new ArrayList<>(customsTypes.stream().sorted(Comparator.comparing(DocumentTypeEntry::getLabel)).toList());

		if (orderType == OrderType.ROADFORWARDINGORDER) {
			Optional<DocumentTypeEntry> invoice = customsTypes.stream().filter(type -> type.getType().equals(TYPE_COMMERCIAL_INVOICE)).findAny();
			Optional<DocumentTypeEntry> prInvoice = customsTypes.stream().filter(type -> type.getType().equals(DocumentConst.TYPE_PROFORMA_INVOICE)).findAny();

			if (invoice.isPresent()) {
				sortedList.remove(invoice.get());
				sortedList.add(0, invoice.get());
			}
			if (prInvoice.isPresent()) {
				sortedList.remove(prInvoice.get());
				sortedList.add(1, prInvoice.get());
			}
		} else if (orderType == OrderType.AIREXPORTORDER || orderType == OrderType.AIRIMPORTORDER) {
			Optional<DocumentTypeEntry> invoice = customsTypes.stream().filter(type -> type.getType().equals(TYPE_COMMERCIAL_INVOICE)).findAny();

			if (invoice.isPresent()) {
				sortedList.remove(invoice.get());
				sortedList.add(0, invoice.get());
			}
		}

		Optional<DocumentTypeEntry> misc = customsTypes.stream().filter(type -> type.getType().equals(DocumentConst.TYPE_CUSTOMS_OTHERS)).findAny();
		if (misc.isPresent()) {// remove the misc type and re-add it at the end
			sortedList.remove(misc.get());
			sortedList.add(misc.get());
		}

		customsTypes.clear();
		customsTypes.addAll(sortedList);
	}

	private void sortOtherDocumentTypes(List<DocumentTypeEntry> otherTypes) {
		List<DocumentTypeEntry> sortedList = new ArrayList<>(otherTypes.stream().sorted(Comparator.comparing(DocumentTypeEntry::getLabel)).toList());

		Optional<DocumentTypeEntry> misc = otherTypes.stream().filter(type -> type.getType().equals(DocumentConst.TYPE_MISC)).findAny();
		if (misc.isPresent()) {// remove the misc type and re-add it at the end
			sortedList.remove(misc.get());
			sortedList.add(misc.get());
		}

		otherTypes.clear();
		otherTypes.addAll(sortedList);
	}

	public Optional<DocumentDownloadZipHolder> downloadDocumentsZippedPerOrder(Order order) throws FileNotFoundException {

		List<Document> documents = documentRepository.findByOrderId(order.getOrderId());
		if (documents.isEmpty()) {
			return Optional.empty();
		}
		DocumentDownloadDto documentDownload = new DocumentDownloadDto();
		DocumentDownloadZipHolder zipHolder = new DocumentDownloadZipHolder();
		zipHolder.setDocumentDownload(documentDownload);
		log.info("Start zipping all orders for order {}.", order.getOrderId());
		documentDownload.setDocumentName(documentZipService.getZipFileName(String.valueOf(order.getShipmentNumber())));
		log.info("Successfully zipped all orders for order {}.", order.getOrderId());
		zipHolder.setZipFileInputStream(documentZipService.zipDocuments(documents));
		documentDownload.setFileType(FileTypeEnum.ZIP);
		return Optional.of(zipHolder);
	}

	@Transactional
	public void overrideCustomerNumberInDocuments(Long orderId, String newCustomerNumber) {
		documentRepository.findByOrderId(orderId).forEach(document -> documentDataHandler.transferToCustomer(document, newCustomerNumber));
		documentRepository.updateCustomerNumberOfDocuments(orderId, newCustomerNumber);
	}

	@Transactional
	public void overrideCustomerNumberInDocuments(List<Long> documentIds, Long orderId, String newCustomerNumber) {
		if (documentIds == null) {
			return;
		}
		documentIds.forEach(docId -> documentRepository.findById(docId).ifPresentOrElse(document -> {
			documentDataHandler.transferToCustomer(document, newCustomerNumber);
			if (!orderId.equals(document.getOrderId()) || !newCustomerNumber.equals(document.getCustomerNumber())) {
				document.setOrderId(orderId);
				document.setCustomerNumber(newCustomerNumber);
				documentRepository.save(document);
			}
		}, () -> {
			throw new DocumentNotFoundException();
		}));

	}

	@Transactional
	public void checkDocumentAndDelete(Long documentId) {
		final Document document = documentRepository.loadById(documentId);
		checkIfDocumentCanBeUpdatedOrDeleted(document);
		doDelete(document);
	}

	private void checkIfDocumentCanBeUpdatedOrDeleted(Document document) {
		Optional<OrderTypeStatusAndOwner> order = orderRepositoryFacade.getOrderTypeStatusAndOwner(document.getOrderId());
		if (order.isPresent() && order.get().getStatus() == OrderStatus.SENT) {
			log.warn("Cannot delete document. Order {} is already sent", document.getOrderId());
			throw new CannotUpdateDocumentException("Order is already sent");
		} else if (DocumentStatus.NEW != document.getStatus()) {
			log.warn("Cannot delete document. Document {} is already processed", document.getOrderId());
			throw new CannotUpdateDocumentException("Document is already processed");
		}
	}

	private void doDelete(Document document) {
		// delete it first in the repo, if the file is not deleted, the transaction will be rolled back
		documentRepository.delete(document);
		documentDataHandler.deleteDocumentFromStorage(document);
	}

	private Document createNewDocument(String customerNumber, Long orderId, Integer documentTypeId, String documentName, FileType fileType, long fileSizeInBytes) {
		// Convert filesize from bytes to KB
		long fileSizeInKB = fileSizeInBytes / 1024;

		Document document = new Document();
		document.setSize(fileSizeInKB);
		BaseEntityService.fillCreationFields(document, userContextService.getCurrentUserId());
		document.setCustomerNumber(customerNumber);
		document.setOrderId(orderId);
		if (documentTypeId != null) {
			final Optional<DocumentType> documentType = documentTypeRepository.findById(documentTypeId);
			documentType.ifPresent(document::setDocumentType);
		}
		document.setDocumentName(documentName);
		document.setFileType(fileType);
		document.setStatus(DocumentStatus.NEW);
		return documentRepository.save(document);
	}

	private void checkIfFileExtensionIsCorrect(byte[] file, String documentName, FileType fileType) {
		FileWrapperBean fileWrapperBean = new FileWrapperBean(file, documentName);
		boolean isValidFile = fileCheckService.isFileExtensionCorrect(fileWrapperBean, fileType.getLabel());

		if (!isValidFile) {
			log.warn("File extension is not correct for file {}", documentName);
			throw new FileExtensionInvalidException();
		}
	}

	private void checkIfFileIsMalicious(byte[] file, String username, String documentName) {
		FileWrapper fileWrapper = new FileWrapper(file);
		boolean fileMalicious = !fileScanService.isThisFileHarmless(fileWrapper, username, documentName);

		if (fileMalicious) {
			log.warn("File {} is malicious", documentName);
			throw new FileIsMaliciousException();
		}
	}

	private void checkIfCustomerOwnsTheOrder(String customerNumber, Long orderId, DocumentExceptionType status) {
		final Optional<OrderTypeStatusAndOwner> orderTypeAndStatus = orderRepositoryFacade.getOrderTypeStatusAndOwner(orderId);
		if (orderTypeAndStatus.isPresent()) {
			final OrderTypeStatusAndOwner orderTypeStatusAndOwner = orderTypeAndStatus.get();
			if (!orderTypeStatusAndOwner.getCustomerNumber().equals(customerNumber)) {
				log.warn("customer {} does not own order {}, instead {} is owner", customerNumber, orderId, orderTypeStatusAndOwner.getCustomerNumber());
				throw new CustomerDoesNotOwnOrderException("Customer " + customerNumber + " does not own order " + orderId, status);
			}
		} else {
			log.warn("Could not find order {}", orderId);
			throw new OrderNotFoundException();
		}
	}

    private void removeDGDocumentTypes(List<DocumentTypeDto> types) {
        types.removeIf(documentTypeDto ->
            TYPE_DG_DOCUMENTS.equals(documentTypeDto.getType()) ||
            TYPE_DG_DOCUMENTS_IMDG.equals(documentTypeDto.getType())
        );
    }
}
