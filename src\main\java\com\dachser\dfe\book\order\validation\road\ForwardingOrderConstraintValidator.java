package com.dachser.dfe.book.order.validation.road;

import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.validation.Messages;
import com.dachser.dfe.book.order.validation.PayloadProvidingValidator;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
class ForwardingOrderConstraintValidator implements ConstraintValidator<ValidForwardingOrder, ForwardingOrder>, PayloadProvidingValidator {

	private final Translator translator;

	@Override
	public boolean isValid(ForwardingOrder order, ConstraintValidatorContext context) {
		context.disableDefaultConstraintViolation();
		boolean isValid = true;

		if (checkMissingCashOnDelivery(order)) {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode("cashOnDelivery").addConstraintViolation();
			isValid = false;
		}
		return isValid;
	}

	private boolean checkMissingCashOnDelivery(ForwardingOrder order) {
		return order.isCashOnDelivery() && order.getCashOnDeliveryAmount() == null;
	}
}
