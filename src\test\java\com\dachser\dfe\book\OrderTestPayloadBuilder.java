package com.dachser.dfe.book;

import com.dachser.dfe.book.order.common.OrderBaseAddress;
import com.dachser.dfe.book.order.common.ReferenceType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class OrderTestPayloadBuilder {

	public static OrderTestPayloadBuilder resourceBuilder(Resource resource) throws IOException {
		return new OrderTestPayloadBuilder(stringify(resource));
	}

	private static String stringify(Resource resource) throws IOException {
		return resource.getContentAsString(StandardCharsets.UTF_8);
	}

	private String payload;

	private OrderTestPayloadBuilder(String payload) {
		this.payload = StringUtils.defaultString(payload);
	}

	public OrderTestPayloadBuilder shipper(OrderBaseAddress shipper) {
		// @formatter:off
		payload = payload
		.replace("${shipper.name}", shipper.getName())
		.replace("${shipper.street}", shipper.getStreet())
		.replace("${shipper.postcode}", shipper.getPostcode())
		.replace("${shipper.city}", shipper.getCity())
		.replace("${shipper.countryCode}", shipper.getCountryCode());
		// @formatter:on
		return this;
	}

	public OrderTestPayloadBuilder reference(ReferenceType referenceType, String referenceValue) {
		return reference(referenceType.name(), referenceValue);
	}

	public OrderTestPayloadBuilder reference(String referenceType, String referenceValue) {
		// @formatter:off
		payload = payload
		.replace("${reference.type}", referenceType)
		.replace("${reference.value}", referenceValue);
		// @formatter:on
		return this;
	}

	public String build() {
		// @formatter:off
		return payload
		.replace("${tomorrow}", LocalDate.now().plusDays(1L).format(DateTimeFormatter.ISO_DATE));
		// @formatter:on
	}

}