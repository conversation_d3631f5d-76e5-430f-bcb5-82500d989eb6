package com.dachser.dfe.book.dip.xml;

import com.dachser.dfe.book.OrderTestPayloadBuilder;
import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.dip.converter.road.GeneralTransportDataMapperImpl;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionDangerousGoodsMapperImpl;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionDocumentHeaderMapperImpl;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionOrderInformationMapper;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionOrderInformationMapperImpl;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionOrderOriginalTermMapper;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionPackingPositionMapperImpl;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionShipmentAddressMapperImpl;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionShipmentLineMapperImpl;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionTransportMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingDangerousGoodsMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingDocumentHeaderMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingOrderInformationMapper;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingOrderInformationMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingPackingPositionMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingShipmentAddressMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingShipmentLineMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingTransportMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.DateMapperImpl;
import com.dachser.dfe.book.model.TestMockData;
import com.dachser.dfe.book.model.jaxb.order.road.collection.CollectionOrderInformation;
import com.dachser.dfe.book.model.jaxb.order.road.collection.ShipmentText;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.DocumentHeader;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ForwardingOrderInformation;
import com.dachser.dfe.book.model.options.CollectionOptions;
import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.OrderReferenceHelper;
import com.dachser.dfe.book.order.address.AddressTestHelper;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGood;
import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.order.reference.ReferenceTestHelper;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Named;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Stream;

import static com.dachser.dfe.book.TestUtil.collectionDifferentConsigneeAddress;
import static com.dachser.dfe.book.TestUtil.collectionShipperAddress;
import static com.dachser.dfe.book.TestUtil.generateForwardingOrderInformation;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@SpringBootTest(classes = { ForwardingOrderInformationMapperImpl.class, ForwardingTransportMapperImpl.class, DateMapperImpl.class, ForwardingDocumentHeaderMapperImpl.class,
		XmlConverter.class, CollectionOrderInformationMapperImpl.class, CollectionTransportMapperImpl.class, CollectionDocumentHeaderMapperImpl.class,
		CollectionShipmentLineMapperImpl.class, ForwardingShipmentLineMapperImpl.class, GeneralTransportDataMapperImpl.class, CollectionShipmentAddressMapperImpl.class,
		ForwardingShipmentAddressMapperImpl.class, ForwardingPackingPositionMapperImpl.class, CollectionPackingPositionMapperImpl.class, ForwardingDangerousGoodsMapperImpl.class,
		CollectionDangerousGoodsMapperImpl.class })
@Slf4j
class XmlConverterTest {

	@Autowired
	private ForwardingOrderInformationMapper forwardingOrderInformationMapper;

	@Autowired
	private CollectionOrderInformationMapper collectionOrderInformationMapper;

	@Autowired
	private XmlConverter xmlConverter;

	@MockBean
	protected CollectionOrderOriginalTermMapper collectionOrderOriginalTermMapper;

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	@BeforeEach
	void beforeAll() {
		when(collectionOrderOriginalTermMapper.mapOriginalTerm(any())).thenReturn("031");
	}

	@Nested
	class ForwardingOrderConversion {

		@Nested
		class GenerateXML {

			@Value("classpath:/orders/send/road/edi-demo-forwarding-order.xml")
			private Resource xmlEdiDemo;

			@Value("classpath:/orders/send/road/valid-forwarding-order.xml")
			private Resource xmlValid;

			@Value("classpath:/orders/send/road/valid-forwarding-order-with-deliveryoption.xml")
			private Resource xmlWithDeliveryOptions;

			@Value("classpath:/orders/send/road/valid-forwarding-order-with-LP.xml")
			private Resource xmlWithLoadingPointAddress;

			@Value("classpath:/orders/send/road/valid-forwarding-order-with-reference.xml")
			private Resource xmlWithReference;

			@Value("classpath:/orders/send/road/valid-forwarding-order-dangerous-goods.xml")
			private Resource dangerousGoodsXml;

			final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();

			@BeforeEach
			void setIDs() {
				for (PackingPosition packingPosition : forwardingOrder.getPackingPositions()) {
					packingPosition.setId(new Random().nextLong(3));
					packingPosition.getOrderLines().forEach(orderLines -> orderLines.setPackingPositionId(packingPosition.getId()));
				}
			}

			@Test
			void shouldGenerateXmlFromDemoForwardingOrder() throws IOException {
				assertDipSerialization(OrderTestPayloadBuilder.resourceBuilder(xmlEdiDemo), generateForwardingOrderInformation());
			}

			@Test
			void shouldGenerateXmlFromForwardingOrder() throws IOException {
				forwardingOrder.setShipmentTransferId("e9cb5979-a10d-4bbf-a68d-bc513e70de27");
				forwardingOrder.getShipperAddress().setGln("9012345100001");
				forwardingOrder.getConsigneeAddress().setGln("0004230830008");
				forwardingOrder.setShipmentNumber(4918712902L);
				forwardingOrder.setFixDate(LocalDate.of(2016, 12, 17));
				forwardingOrder.setSendAt(LocalDateTime.of(2016, 12, 17, 9, 29, 53, 0).toInstant(ZoneOffset.UTC));
				forwardingOrder.setDeliveryOption(null);//reset delivery option
				assertDipSerialization(OrderTestPayloadBuilder.resourceBuilder(xmlValid), forwardingOrderInformationMapper.map(forwardingOrder));
			}

			@Test
			void shouldGenerateXmlFromForwardingOrderWithDeliveryOption() throws IOException {
				forwardingOrder.setShipmentTransferId("e9cb5979-a10d-4bbf-a68d-bc513e70de27");
				forwardingOrder.getShipperAddress().setGln("9012345100001");
				forwardingOrder.getConsigneeAddress().setGln("0004230830008");
				forwardingOrder.setShipmentNumber(4918712902L);
				forwardingOrder.setFixDate(LocalDate.of(2016, 12, 17));
				forwardingOrder.setSendAt(LocalDateTime.of(2016, 12, 17, 9, 29, 53, 0).toInstant(ZoneOffset.UTC));
				assertDipSerialization(OrderTestPayloadBuilder.resourceBuilder(xmlWithDeliveryOptions), forwardingOrderInformationMapper.map(forwardingOrder));
			}

			@Test
			void shouldGenerateXmlFromForwardingOrderWithLP() throws IOException {
				forwardingOrder.setShipmentTransferId("e9cb5979-a10d-4bbf-a68d-bc513e70de27");
				forwardingOrder.getShipperAddress().setGln("9012345100001");
				forwardingOrder.getConsigneeAddress().setGln("0004230830008");
				forwardingOrder.setShipmentNumber(4918712902L);
				forwardingOrder.setFixDate(LocalDate.of(2016, 12, 17));
				forwardingOrder.setSendAt(LocalDateTime.of(2016, 12, 17, 9, 29, 53, 0).toInstant(ZoneOffset.UTC));

				OrderFurtherAddress lpAddress = new OrderFurtherAddress();
				lpAddress.setAddressType("LP");
				lpAddress.setName("name1");
				lpAddress.setName2("name2");
				lpAddress.setName3("name3");
				lpAddress.setStreet("Con Street 11");
				lpAddress.setCity("Newtown");
				lpAddress.setPostcode("88888");
				lpAddress.setCountryCode("DE");
				forwardingOrder.getAddresses().add(lpAddress);

				assertDipSerialization(OrderTestPayloadBuilder.resourceBuilder(xmlWithLoadingPointAddress), forwardingOrderInformationMapper.map(forwardingOrder));
			}

			@Test
			void shouldGenerateXmlWithoutDeliveryOptionSet() {
				forwardingOrder.setDeliveryOption(null); // in case of no option selected -> this remains null and should not prevent generation

				final ForwardingOrderInformation forwardingOrderInformation = forwardingOrderInformationMapper.map(forwardingOrder);
				final String xml = xmlConverter.convertToXml(forwardingOrderInformation);
				assertNotNull(xml);
				assertFalse(xml.isEmpty());
			}

			@ParameterizedTest(name = "#{index} - order having reference of type {0} serializes to DIP")
			@MethodSource("roadReferences")
			void withReference(RoadOrderReference roadOrderReference, String expectedReferenceValue) throws IOException {
				// given a road order
				forwardingOrder.setShipmentTransferId("e9cb5979-a10d-4bbf-a68d-bc513e70de27");
				forwardingOrder.getShipperAddress().setGln("9012345100001");
				forwardingOrder.getConsigneeAddress().setGln("0004230830008");
				forwardingOrder.setShipmentNumber(4918712902L);
				forwardingOrder.setFixDate(LocalDate.of(2016, 12, 17));
				forwardingOrder.setSendAt(LocalDateTime.of(2016, 12, 17, 9, 29, 53, 0).toInstant(ZoneOffset.UTC));
				forwardingOrder.setDeliveryOption(null);
				// having a specific reference
				forwardingOrder.getOrderReferences().clear();
				forwardingOrder.getOrderReferences().add(roadOrderReference);
				// when serializing order to DIP (XML format)
				// then reference are written as expected
				assertDipSerialization(OrderTestPayloadBuilder.resourceBuilder(xmlWithReference).reference(roadOrderReference.getReferenceType().getCoreSystemsValue(),
						Optional.ofNullable(expectedReferenceValue).orElse(roadOrderReference.getReference())), forwardingOrderInformationMapper.map(forwardingOrder));
			}

			@Test
			void shouldGenerateXmlFromForwardingOrderWithDangerousGoods() throws IOException {
				forwardingOrder.setShipmentNumber(4918712902L);
				forwardingOrder.setFixDate(LocalDate.of(2016, 12, 17));
				forwardingOrder.setSendAt(LocalDateTime.of(2016, 12, 17, 9, 29, 53, 0).toInstant(ZoneOffset.UTC));

				RoadOrderLine roadOrderLine = forwardingOrder.getOrderLines().getFirst();
				List<DangerousGood> dangerousGoods = roadOrderLine.getDangerousGoods();
				dangerousGoods.add(testUtil.lqDangerousGoods(roadOrderLine));
				dangerousGoods.add(testUtil.eqDangerousGoods(roadOrderLine));
				dangerousGoods.add(testUtil.adrDangerousGoods(roadOrderLine));
				assertDipSerialization(OrderTestPayloadBuilder.resourceBuilder(dangerousGoodsXml), forwardingOrderInformationMapper.map(forwardingOrder));
			}

			private static Stream<Arguments> roadReferences() {
				// @formatter:off
				return Stream.of(
					Arguments.of(Named.of(ReferenceType.PURCHASE_ORDER_NUMBER.name(), ReferenceTestHelper.roadOrderReference(ReferenceType.PURCHASE_ORDER_NUMBER, null, "my 1st purchase")), null),
					Arguments.of(Named.of(ReferenceType.BOOKING_REFERENCE.name(), ReferenceTestHelper.roadOrderReference(ReferenceType.BOOKING_REFERENCE, null, "Amazon: 1st booking")), null),
						Arguments.of(
							Named.of(
								RoadOrderReferenceSubtype.UIT.name(),
								ReferenceTestHelper.roadOrderReference(
									ReferenceType.IDENTIFICATION_CODE_TRANSPORT,
									RoadOrderReferenceSubtype.UIT,
									TestMockData.UIT_REFERENCE_VALUE)
							),
							OrderReferenceHelper.prefix(RoadOrderReferenceSubtype.UIT).concat(StringUtils.SPACE).concat(TestMockData.UIT_REFERENCE_VALUE)
						)
				);
				// @formatter:on
			}

		}

	}

	@Nested
	class InvalidCase {

		@Nested
		class GenerateXML {

			@Test
			void shouldReturnNullOnNullInput() {
				try {
					final String xml = xmlConverter.convertToXml(null);
					assertNull(xml);
				} catch (Exception e) {
					fail("Should not throw exception on null input but return null instead");
				}
			}

			@Test
			void shouldReturnNullOnConversionError() {
				final ForwardingOrderInformation forwardingOrderInformation = new ForwardingOrderInformation() {
				};
				forwardingOrderInformation.setDocumentHeader(new DocumentHeader());
				forwardingOrderInformation.getDocumentHeader().setDocumentID("<&>");
				final String xml = xmlConverter.convertToXml(forwardingOrderInformation);
				assertNull(xml);
			}
		}

	}

	@Nested
	class CollectionOrderConversion {

		@Nested
		class GenerateXML {

			@Value("classpath:/orders/send/road/valid-collection-order.xml")
			private Resource xml;

			@Value("classpath:/orders/send/road/valid-collection-order-dangerous-goods.xml")
			private Resource dangerousGoodsXml;

			@Value("classpath:/orders/send/road/valid-collection-order-with-manual-consignor.xml")
			private Resource xmlWithConsignor;

			@Value("classpath:/orders/send/road/valid-collection-order-with-manual-consignee.xml")
			private Resource xmlWithConsignee;

			final CollectionOrder collectionOrder = testUtil.generateCollectionOrder();

			@BeforeEach
			void setIDs() {
				for (PackingPosition packingPosition : collectionOrder.getPackingPositions()) {
					packingPosition.setId(new Random().nextLong(3));
					packingPosition.getOrderLines().forEach(orderLines -> orderLines.setPackingPositionId(packingPosition.getId()));
				}
			}

			@Test
			void shouldGenerateXmlFromCollectionOrder() throws IOException {
				RoadOrderLine firstOrderLine = collectionOrder.getOrderLines().get(0);
				firstOrderLine.setPackagingType("KT");
				firstOrderLine.setContent("Mat.de constructions");
				collectionOrder.setFixDate(LocalDate.of(2024, 2, 1));
				assertDipSerialization(OrderTestPayloadBuilder.resourceBuilder(xml), collectionOrderInformationMapper.map(collectionOrder));
			}

			@Test
			void shouldGenerateXmlFromCollectionOrderWithCollectionOptionBooking() {

				RoadOrderLine firstOrderLine = collectionOrder.getOrderLines().get(0);
				firstOrderLine.setPackagingType("KT");
				firstOrderLine.setContent("Mat.de constructions");
				collectionOrder.setFixDate(LocalDate.of(2024, 2, 1));
				collectionOrder.setCollectionOption(CollectionOptions.BOOKING);
				collectionOrder.setShipperAddress(collectionShipperAddress());
				final CollectionOrderInformation orderInformation = collectionOrderInformationMapper.map(collectionOrder);

				List<ShipmentText> shipmentText = orderInformation.getTransport().get(0).getShipmentHeader().get(0).getShipmentText().stream()
						.filter(text -> text.getTextType().equals(CollectionOptions.BOOKING.getEdiKey())).toList();
				assertEquals(3, shipmentText.size());
				assertEquals("Consignora_shipper", shipmentText.get(0).getText().getFirst());
				assertEquals("SMS: 0000777777778, TEL: 0000777777777", shipmentText.get(1).getText().getFirst());
				assertEquals("eMail: <EMAIL>", shipmentText.get(2).getText().getFirst());
			}

			@Test
			void shouldGenerateXmlFromCollectionOrderWithCollectionOptionNotification() {

				RoadOrderLine firstOrderLine = collectionOrder.getOrderLines().getFirst();
				firstOrderLine.setPackagingType("KT");
				firstOrderLine.setContent("Mat.de constructions");
				collectionOrder.setFixDate(LocalDate.of(2024, 2, 1));
				collectionOrder.setCollectionOption(CollectionOptions.NOTIFICATION);
				collectionOrder.setShipperAddress(collectionShipperAddress());
				final CollectionOrderInformation orderInformation = collectionOrderInformationMapper.map(collectionOrder);

				List<ShipmentText> shipmentText = orderInformation.getTransport().getFirst().getShipmentHeader().getFirst().getShipmentText().stream()
						.filter(text -> text.getTextType().equals(CollectionOptions.NOTIFICATION.getEdiKey())).toList();
				assertEquals(3, shipmentText.size());
				assertEquals("Consignora_shipper", shipmentText.get(0).getText().getFirst());
				assertEquals("SMS: 0000777777778, TEL: 0000777777777", shipmentText.get(1).getText().getFirst());
				assertEquals("eMail: <EMAIL>", shipmentText.get(2).getText().getFirst());
			}

			@Test
			void shouldGenerateXmlFromCollectionOrderWithDeliveryOptionNotification() {

				RoadOrderLine firstOrderLine = collectionOrder.getOrderLines().getFirst();
				firstOrderLine.setPackagingType("KT");
				firstOrderLine.setContent("Mat.de constructions");
				collectionOrder.setFixDate(LocalDate.of(2024, 2, 1));
				collectionOrder.setShipperAddress(collectionShipperAddress());
				collectionOrder.setDeliveryOption(DeliveryOptions.AP);
				collectionOrder.setDeliveryContact(collectionShipperAddress().getOrderContact());
				final CollectionOrderInformation orderInformation = collectionOrderInformationMapper.map(collectionOrder);

				List<ShipmentText> shipmentText = orderInformation.getTransport().getFirst().getShipmentHeader().getFirst().getShipmentText().stream()
						.filter(text -> text.getTextType().equals(DeliveryOptions.AP.getKey())).toList();
				assertEquals(3, shipmentText.size());
				assertEquals("Consignora_shipper", shipmentText.get(0).getText().getFirst());
				assertEquals("SMS: 0000777777778, TEL: 0000777777777", shipmentText.get(1).getText().getFirst());
				assertEquals("eMail: <EMAIL>", shipmentText.get(2).getText().getFirst());
			}

			@Test
			void withDifferentConsignor() throws IOException {
				// given a road collection order
				collectionOrder.getOrderLines().get(0).setPackagingType("KT");
				collectionOrder.getOrderLines().get(0).setContent("Mat.de constructions");
				collectionOrder.setFixDate(LocalDate.of(2024, 2, 1));
				// and principal is not the shipper
				collectionOrder.setShipperAddress(collectionShipperAddress());
				log.info("principal = {}", AddressTestHelper.stringify(collectionOrder.getPrincipalAddress()));
				log.info("shipper = {}", AddressTestHelper.stringify(collectionOrder.getShipperAddress()));
				assertFalse(AddressTestHelper.is(collectionOrder.getPrincipalAddress(), collectionOrder.getShipperAddress()));
				// when serializing order to DIP (XML format)
				// then shipper is written as PW address type with all fields
				// and principal is written as CZ address type with only customer number field
				assertDipSerialization(OrderTestPayloadBuilder.resourceBuilder(xmlWithConsignor), collectionOrderInformationMapper.map(collectionOrder));
			}

			@Test
			void withDifferentConsignee() throws IOException {
				// given a road collection order
				collectionOrder.setShipperAddress(null);
				collectionOrder.getOrderLines().get(0).setPackagingType("KT");
				collectionOrder.getOrderLines().get(0).setContent("Mat.de constructions");
				collectionOrder.setFixDate(LocalDate.of(2024, 2, 1));
				// and principal is not the consignee (specific one provided)
				collectionOrder.setDifferentConsigneeAddress(collectionDifferentConsigneeAddress());
				log.info("principal = {}", AddressTestHelper.stringify(collectionOrder.getPrincipalAddress()));
				log.info("consignee = {}", AddressTestHelper.stringify(collectionOrder.getConsigneeAddress()));
				log.info("different consignee = {}", AddressTestHelper.stringify(collectionOrder.getDifferentConsigneeAddress()));
				assertFalse(AddressTestHelper.is(collectionOrder.getPrincipalAddress(), collectionOrder.getDifferentConsigneeAddress()));
				// when serializing order to DIP (XML format)
				// then specific consignee is written as CN address type with all fields
				// and principal is written as CZ address type with only customer number field
				assertDipSerialization(OrderTestPayloadBuilder.resourceBuilder(xmlWithConsignee), collectionOrderInformationMapper.map(collectionOrder));
			}

			@Test
			void shouldGenerateXmlFromCollectionOrderWithDangerousGoods() throws IOException {
				collectionOrder.setShipmentNumber(4918712902L);
				collectionOrder.setFixDate(LocalDate.of(2024, 2, 1));
				collectionOrder.setSendAt(LocalDateTime.of(2022, 12, 10, 0, 0, 0, 0).toInstant(ZoneOffset.UTC));

				RoadOrderLine roadOrderLine = collectionOrder.getOrderLines().getFirst();
				List<DangerousGood> dangerousGoods = roadOrderLine.getDangerousGoods();
				dangerousGoods.add(testUtil.lqDangerousGoods(roadOrderLine));
				dangerousGoods.add(testUtil.eqDangerousGoods(roadOrderLine));
				dangerousGoods.add(testUtil.adrDangerousGoods(roadOrderLine));
				assertDipSerialization(OrderTestPayloadBuilder.resourceBuilder(dangerousGoodsXml), collectionOrderInformationMapper.map(collectionOrder));
			}

		}
	}

	private void assertDipSerialization(OrderTestPayloadBuilder dipPayloadBuilder, Object data) {
		assertEquals(toCRLF(dipPayloadBuilder.build()), toCRLF(xmlConverter.convertToXml(data)));
	}

	private String toCRLF(String value) {
		return StringUtils.defaultString(value).replaceAll("\\R", "\r\n");
	}

}