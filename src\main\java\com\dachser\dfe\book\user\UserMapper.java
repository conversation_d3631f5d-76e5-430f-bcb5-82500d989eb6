package com.dachser.dfe.book.user;

import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.AddressDto;
import com.dachser.dfe.book.model.CustomerSettingsDto;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.user.model.Address;
import com.dachser.dfe.book.user.model.AslCustomer;
import com.dachser.dfe.book.user.model.Company;
import com.dachser.dfe.book.user.model.OrderOptions;
import com.dachser.dfe.book.user.model.RoadCustomer;
import com.dachser.dfe.book.user.model.User;
import com.dachser.dfe.book.user.model.UserPreferences;
import com.dachser.dfe.platform.model.PlatformAslCustomerV5;
import com.dachser.dfe.platform.model.PlatformCompanyV5;
import com.dachser.dfe.platform.model.PlatformPreferencesV4;
import com.dachser.dfe.platform.model.PlatformPrincipalAddressV5;
import com.dachser.dfe.platform.model.PlatformRoadCustomerV5;
import com.dachser.dfe.platform.model.PlatformUserV5;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.slf4j.LoggerFactory;

@Mapper(componentModel = "spring")
public interface UserMapper {

	User map(PlatformUserV5 user);

	Company map(PlatformCompanyV5 company);

	@Mapping(target = "customerNumber", source = "number")
	@Mapping(target = "branchId", source = "bookBranchNumber")
	@Mapping(target = "division", source = "businessLine")
	@Mapping(target = "address", source = "principalAddress")
	@Mapping(target = "orderOptions", source = ".")
	@Mapping(target = "cashOnDelivery", source = "cashOnDelivery")
	RoadCustomer mapRoadCustomer(PlatformRoadCustomerV5 customer);

	@Mapping(target = "customerNumber", source = "number")
	@Mapping(target = "address", source = "principalAddress")
	@Mapping(target = "orderOptions", source = ".")
	AslCustomer mapAslCustomer(PlatformAslCustomerV5 customer);

	@Mapping(target = ".", source = "addressTypes")
	@Mapping(target = ".", source = "orderTexts")
	@Mapping(target = ".", source = "orderOptions")
	OrderOptions map(PlatformRoadCustomerV5 customer);

	OrderOptions map(PlatformAslCustomerV5 customer);

	Address map(PlatformPrincipalAddressV5 address);

	@Mapping(target = "name", source = "address.name1", qualifiedByName = "mapNameGeneric")
	@Mapping(target = "name2", source = "address.name2", qualifiedByName = "mapNameGeneric")
	@Mapping(target = "name3", source = "address.name3", qualifiedByName = "mapNameGeneric")
	@Mapping(target = "gln", source = "address.gln", qualifiedByName = "mapGln")
	OrderAddress mapOrderAddress(Address address, User user);

	@Mapping(target = "telephone", source = "phoneNumber")
	@Mapping(target = "name", source = "user", qualifiedByName = "mapOrderContactUsername")
	OrderContact mapOrderContact(User user);

	@InheritConfiguration(name = "mapOrderAddress")
	void updateOrderAddress(Address address, User user, @MappingTarget OrderAddress orderAddress);

	@Named("mapNameGeneric")
	default String limitLengthTo40(String name) {
		if (name == null) {
			return null;
		}
		if (name.length() <= 40) {
			return name;
		}
		LoggerFactory.getLogger(UserMapper.class).warn("Name is too long: {} will substring it to 40 chars", name);
		return name.substring(0, 40);
	}

	@Named("mapOrderContactUsername")
	default String mapOrderContactUsername(User user) {
		final String firstName = user.getFirstName();
		final String lastName = user.getLastName();
		if (firstName.concat(" ").concat(lastName).length() <= 40) {
			return firstName.concat(" ").concat(lastName);
		} else {
			return limitLengthTo40(lastName);
		}
	}

	default Division mapDivision(PlatformRoadCustomerV5.BusinessLineEnum businessLineEnum) {
		return switch (businessLineEnum) {
			case EL -> Division.T;
			case FL -> Division.F;
		};
	}

	@Mapping(target = "airProducts", source = "airProducts")
	@Mapping(target = ".", source = "orderOptions")
	CustomerSettingsDto mapCustomerOptions(AslCustomer customer);

	@Mapping(target = "manualNumberOfLabels", source = "manualNumberSscc")
	@Mapping(target = "goodsDescription", source = "goodsDescriptions")
	CustomerSettingsDto mapCustomerOptions(OrderOptions options);

	@Mapping(target = "name", source = "name1")
	@Mapping(target = "gln", source = "gln", qualifiedByName = "mapGln")
	AddressDto mapCustomerAddress(Address address);

	// Currently test data contains only 0 as gln which leads to validation errors later, so we just skip it, it's not required anyway
	@Named("mapGln")
	default String mapGln(String gln) {
		if ("0".equals(gln) || "".equals(gln)) {
			return null;
		}
		return gln;
	}

	UserPreferences map(PlatformPreferencesV4 userPreferences);
}
