package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.model.ADRDangerousGoodDto;
import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.dfe.book.model.DangerousGoodDto;
import com.dachser.dfe.book.model.EQDangerousGoodDto;
import com.dachser.dfe.book.model.LQDangerousGoodDto;
import com.dachser.dfe.book.order.common.dangerousgood.ADRDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGoodDataItem;
import com.dachser.dfe.book.order.common.dangerousgood.EQDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.LQDangerousGood;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class DangerousGoodsMapperTest {

	private final DangerousGoodsMapper mapper = new DangerousGoodsMapperImpl();

	@Nested
	class MapDangerousGoodTests {

		@Test
		void map_WithLQDto_ReturnsLQDangerousGood() {
			LQDangerousGoodDto dto = new LQDangerousGoodDto();
			dto.setSortingPosition(1);
			dto.setNos("N1");
			dto.setGrossMass(BigDecimal.valueOf(1.5));
			dto.setDangerousGoodDataItem(new DangerousGoodDataItemDto());

			DangerousGood result = mapper.map(dto);
			assertInstanceOf(LQDangerousGood.class, result);
			LQDangerousGood lq = (LQDangerousGood) result;
			assertEquals(1, lq.getSortingPosition());
			assertEquals("N1", lq.getNos());
			assertEquals(BigDecimal.valueOf(1.5), lq.getGrossMass());
			assertNotNull(lq.getDangerousGoodDataItem());
		}

		@Test
		void map_WithEQDto_ReturnsEQDangerousGood() {
			EQDangerousGoodDto dto = new EQDangerousGoodDto();
			dto.setSortingPosition(2);
			dto.setNoOfPackages(10);

			DangerousGood result = mapper.map(dto);
			assertInstanceOf(EQDangerousGood.class, result);
			EQDangerousGood eq = (EQDangerousGood) result;
			assertEquals(2, eq.getSortingPosition());
			assertEquals(10, eq.getNoOfPackages());
		}

		@Test
		void map_WithUnNumberDto_ReturnsADRDangerousGood() {
			ADRDangerousGoodDto dto = new ADRDangerousGoodDto();
			dto.setSortingPosition(3);
			dto.setNos("NU");
			dto.setNoOfPackages(5);
			dto.setGrossMass(BigDecimal.valueOf(2.2));
			DangerousGoodDataItemDto uds = new DangerousGoodDataItemDto();
			dto.setDangerousGoodDataItem(uds);

			DangerousGood result = mapper.map(dto);
			assertInstanceOf(ADRDangerousGood.class, result);
			ADRDangerousGood un = (ADRDangerousGood) result;
			assertEquals(3, un.getSortingPosition());
			assertEquals("NU", un.getNos());
			assertEquals(5, un.getNoOfPackages());
			assertEquals(BigDecimal.valueOf(2.2), un.getGrossMass());
			assertNotNull(un.getDangerousGoodDataItem());
		}

		@Test
		void map_UnsupportedSubclass_ThrowsException() {
			// Erstelle eine anonyme Unterklasse von DangerousGoodDto
			DangerousGoodDto fake = new DangerousGoodDto() {
			};
			IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, () -> mapper.map(fake));
			assertTrue(ex.getMessage().contains("Not all subclasses are supported"));
		}
	}

	@Nested
	class MapLQDangerousGoodTests {
		@Test
		void mapLQDangerousGood_NullInput_ReturnsNull() {
			assertNull(mapper.mapLQDangerousGood(null));
		}

		@Test
		void mapLQDangerousGood_FullMapping() {
			LQDangerousGoodDto dto = new LQDangerousGoodDto();
			dto.setSortingPosition(4);
			dto.setNos("NOS2");
			dto.setGrossMass(BigDecimal.valueOf(3.3));
			DangerousGoodDataItemDto uds = new DangerousGoodDataItemDto();
			dto.setDangerousGoodDataItem(uds);

			LQDangerousGood result = mapper.mapLQDangerousGood(dto);
			assertEquals(4, result.getSortingPosition());
			assertEquals("NOS2", result.getNos());
			assertEquals(BigDecimal.valueOf(3.3), result.getGrossMass());
			assertNotNull(result.getDangerousGoodDataItem());
		}
	}

	@Nested
	class MapEQDangerousGoodTests {
		@Test
		void mapEQDangerousGood_NullInput_ReturnsNull() {
			assertNull(mapper.mapEQDangerousGood(null));
		}

		@Test
		void mapEQDangerousGood_FullMapping() {
			EQDangerousGoodDto dto = new EQDangerousGoodDto();
			dto.setSortingPosition(5);
			dto.setNoOfPackages(20);

			EQDangerousGood result = mapper.mapEQDangerousGood(dto);
			assertEquals(5, result.getSortingPosition());
			assertEquals(20, result.getNoOfPackages());
		}
	}

	@Nested
	class MapADRDangerousGoodTests {
		@Test
		void mapADRDangerousGood_NullInput_ReturnsNull() {
			assertNull(mapper.mapADRDangerousGood(null));
		}

		@Test
		void mapADRDangerousGood_FullMapping() {
			ADRDangerousGoodDto dto = new ADRDangerousGoodDto();
			dto.setSortingPosition(6);
			dto.setNos("NOSU");
			dto.setNoOfPackages(8);
			dto.setGrossMass(BigDecimal.valueOf(4.4));
			DangerousGoodDataItemDto uds = new DangerousGoodDataItemDto();
			dto.setDangerousGoodDataItem(uds);

			ADRDangerousGood result = mapper.mapADRDangerousGood(dto);
			assertEquals(6, result.getSortingPosition());
			assertEquals("NOSU", result.getNos());
			assertEquals(8, result.getNoOfPackages());
			assertEquals(BigDecimal.valueOf(4.4), result.getGrossMass());
			assertNotNull(result.getDangerousGoodDataItem());
		}
	}

	@Nested
	class UpdateLQDangerousGoodTests {
		@Test
		void updateLQDangerousGood_NullDto_ReturnsOriginal() {
			LQDangerousGood original = new LQDangerousGood();
			LQDangerousGood result = mapper.updateLQDangerousGood(null, original);
			assertSame(original, result);
		}

		@Test
		void updateLQDangerousGood_FullUpdate() {
			LQDangerousGoodDto dto = new LQDangerousGoodDto();
			dto.setSortingPosition(11);
			dto.setNos("NEW");
			dto.setGrossMass(BigDecimal.valueOf(5.5));
			DangerousGoodDataItemDto uds = new DangerousGoodDataItemDto();
			dto.setDangerousGoodDataItem(uds);

			LQDangerousGood domain = new LQDangerousGood();
			mapper.updateLQDangerousGood(dto, domain);

			assertEquals(11, domain.getSortingPosition());
			assertEquals("NEW", domain.getNos());
			assertEquals(BigDecimal.valueOf(5.5), domain.getGrossMass());
		}
	}

	@Nested
	class UpdateEQDangerousGoodTests {
		@Test
		void updateEQDangerousGood_NullDto_ReturnsOriginal() {
			EQDangerousGood original = new EQDangerousGood();
			EQDangerousGood result = mapper.updateEQDangerousGood(null, original);
			assertSame(original, result);
		}

		@Test
		void updateEQDangerousGood_FullUpdate() {
			EQDangerousGoodDto dto = new EQDangerousGoodDto();
			dto.setSortingPosition(12);
			dto.setNoOfPackages(99);

			EQDangerousGood domain = new EQDangerousGood();
			domain.setNoOfPackages(1);
			mapper.updateEQDangerousGood(dto, domain);

			assertEquals(12, domain.getSortingPosition());
			assertEquals(99, domain.getNoOfPackages());
		}
	}

	@Nested
	class UpdateADRDangerousGoodTests {
		@Test
		void updateADRDangerousGood_NullDto_ReturnsOriginal() {
			ADRDangerousGood original = new ADRDangerousGood();
			ADRDangerousGood result = mapper.updateADRDangerousGood(null, original);
			assertSame(original, result);
		}

		@Test
		void updateADRDangerousGood_FullUpdate() {
			ADRDangerousGoodDto dto = new ADRDangerousGoodDto();
			dto.setSortingPosition(13);
			dto.setNos("NU2");
			dto.setNoOfPackages(15);
			dto.setGrossMass(BigDecimal.valueOf(6.6));
			DangerousGoodDataItemDto uds = new DangerousGoodDataItemDto();
			dto.setDangerousGoodDataItem(uds);

			ADRDangerousGood domain = new ADRDangerousGood();
			mapper.updateADRDangerousGood(dto, domain);

			assertEquals(13, domain.getSortingPosition());
			assertEquals("NU2", domain.getNos());
			assertEquals(15, domain.getNoOfPackages());
			assertEquals(BigDecimal.valueOf(6.6), domain.getGrossMass());
		}
	}

	@Nested
	class DtoMappingTests {
		@Test
		void mapLQDangerousGoodDto_NullInput_ReturnsNull() {
			assertNull(mapper.mapLQDangerousGoodDto(null));
		}

		@Test
		void mapLQDangerousGoodDto_FullMapping() {
			LQDangerousGood domain = new LQDangerousGood();
			domain.setSortingPosition(21);
			domain.setNos("X1");
			domain.setGrossMass(BigDecimal.TEN);
			DangerousGoodDataItem uds = new DangerousGoodDataItem();
			domain.setDangerousGoodDataItem(uds);

			LQDangerousGoodDto dto = mapper.mapLQDangerousGoodDto(domain);
			assertEquals(21, dto.getSortingPosition());
			assertEquals("X1", dto.getNos());
			assertEquals(BigDecimal.valueOf(10), dto.getGrossMass());
		}

		@Test
		void mapEQDangerousGoodDto_FullMapping() {
			EQDangerousGood domain = new EQDangerousGood();
			domain.setSortingPosition(22);
			domain.setNoOfPackages(33);

			EQDangerousGoodDto dto = mapper.mapEQDangerousGoodDto(domain);
			assertEquals(22, dto.getSortingPosition());
			assertEquals(33, dto.getNoOfPackages());
		}

		@Test
		void mapADRDangerousGoodDto_FullMapping() {
			ADRDangerousGood domain = new ADRDangerousGood();
			domain.setSortingPosition(23);
			domain.setNos("Y1");
			domain.setNoOfPackages(44);
			domain.setGrossMass(BigDecimal.TEN);
			DangerousGoodDataItem uds = new DangerousGoodDataItem();
			domain.setDangerousGoodDataItem(uds);

			ADRDangerousGoodDto dto = mapper.mapADRDangerousGoodDto(domain);
			assertEquals(23, dto.getSortingPosition());
			assertEquals("Y1", dto.getNos());
			assertEquals(44, dto.getNoOfPackages());
			assertEquals(BigDecimal.valueOf(10), dto.getGrossMass());
		}
	}

	@Nested
	class DangerousGoodDataItemDtoMappingTests {
		@Test
		void mapDangerousGoodDataItemDto_NullInput_ReturnsNull() {
			assertNull(mapper.mapDangerousGoodDataItem((DangerousGoodDataItemDto) null));
		}

		@Test
		void mapDangerousGoodDataItemDto_FullMapping() {
			DangerousGoodDataItem domain = new DangerousGoodDataItem();
			domain.setUnNumber("UNX");
			domain.setDescription("DescX");
			domain.setDefaultDescription("DefaultDescX");
			domain.setPackingGroup("PGX");
			domain.setMainDanger("4");
			domain.setNosRequired(true);
			domain.setClassificationCode("CCX");
			domain.setSubsidiaryHazardOne("7");
			domain.setSubsidiaryHazardTwo("8.1");
			domain.setSubsidiaryHazardThree("9");
			domain.setExternalDgmId("123");

			DangerousGoodDataItemDto dto = mapper.mapDangerousGoodDataItem(domain);
			assertEquals("UNX", dto.getUnNumber());
			assertEquals("DescX", dto.getDescription());
			assertEquals("DefaultDescX", dto.getDefaultDescription());
			assertEquals("PGX", dto.getPackingGroup());
			assertEquals("4", dto.getMainDanger());
			assertTrue(dto.getNosRequired());
			assertEquals("CCX", dto.getClassificationCode());
			assertEquals("7", dto.getSubsidiaryHazardOne());
			assertEquals("8.1", dto.getSubsidiaryHazardTwo());
			assertEquals("9", dto.getSubsidiaryHazardThree());
			assertEquals("123", dto.getDgmId());
		}

		@Test
		void mapUnNumberDataSet_FullMapping() {
			DangerousGoodDataItemDto dto = new DangerousGoodDataItemDto();
			dto.setUnNumber("UNY");
			dto.setDescription("DescY");
			dto.setPackingGroup("PGY");
			dto.setMainDanger("6");
			dto.setNosRequired(false);
			dto.setClassificationCode("CCY");
			dto.setSubsidiaryHazardOne("10");
			dto.setSubsidiaryHazardThree("12");
			dto.setSubsidiaryHazardTwo("11");

			DangerousGoodDataItem domain = mapper.mapDangerousGoodDataItem(dto);
			assertEquals("UNY", domain.getUnNumber());
			assertEquals("DescY", domain.getDescription());
			assertEquals("PGY", domain.getPackingGroup());
			assertEquals("6", domain.getMainDanger());
			Assertions.assertFalse(domain.isNosRequired());
			assertEquals("CCY", domain.getClassificationCode());
		}
	}
}