package com.dachser.dfe.book.user;

import com.dachser.dfe.book.customer.SegmentMapper;
import com.dachser.dfe.book.customer.SegmentMapperImpl;
import com.dachser.dfe.book.exception.BranchUnresolvableException;
import com.dachser.dfe.book.exception.DivisionUnresolvableException;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.CustomerDto;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.product.road.ShipperMasterdataService;
import com.dachser.dfe.book.user.model.Address;
import com.dachser.dfe.book.user.model.AslCustomer;
import com.dachser.dfe.book.user.model.OrderOptions;
import com.dachser.dfe.book.user.model.RoadCustomer;
import com.dachser.dfe.book.user.platform.PlatformApi;
import com.dachser.dfe.platform.model.PlatformPrincipalAddressV5;
import com.dachser.dfe.platform.model.PlatformRoadCustomerV5;
import com.dachser.dfe.platform.model.PlatformUserV5;
import com.dachser.dfe.platform.security.PermissionExpressions;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static com.dachser.dfe.book.TestUtil.createPlatformUser;
import static com.dachser.dfe.book.user.UserServiceMock.VALID_CUST_NO_ASL;
import static com.dachser.dfe.book.user.UserServiceMock.VALID_CUST_NO_ROAD;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserServiceTest {

	public static final String INVALID_CUSTOMER_NUMBER = "0001012";

	@Mock
	PlatformApi platformApi;

	@Mock
	PermissionExpressions permissionExpressions;

	@Mock
	ShipperMasterdataService shipperMasterdataService;

	@Spy
	UserMapper userMapper = new UserMapperImpl();

	@Spy
	SegmentMapper segmentMapper = new SegmentMapperImpl();

	@InjectMocks
	UserService sut;

	PlatformUserV5 user;

	@BeforeEach
	void setUp() {
		user = createPlatformUser();
		lenient().when(this.platformApi.getPlatformUser()).thenReturn(user);
		lenient().when(permissionExpressions.canBook(anyString(), eq(VALID_CUST_NO_ROAD), eq(Segment.ROAD.name()))).thenReturn(Boolean.TRUE);
		lenient().when(permissionExpressions.canBook(anyString(), eq(INVALID_CUSTOMER_NUMBER), eq(Segment.ROAD.name()))).thenReturn(Boolean.TRUE);
		lenient().when(permissionExpressions.canBook(anyString(), eq(VALID_CUST_NO_ASL), eq("ASL"))).thenReturn(Boolean.TRUE);
		lenient().when(shipperMasterdataService.isOrderNumberMandatoryForCustomerNumber(any())).thenReturn(Boolean.TRUE);
	}

	@Test
	void shouldReturnCurrentUserId() {
		String currentUserId = sut.getCurrentUserId();
		assertNotNull(currentUserId);
	}

	@Test
	void shouldGetAllCustomersForUserIfBookAllowed() {
		when(permissionExpressions.canBook(anyString(), anyString(), anyString())).thenReturn(true);
		List<String> customerIds = sut.getAllBookPermittedCustomerIds();
		assertNotNull(customerIds);
		assertFalse(customerIds.isEmpty());
		assertEquals(2, customerIds.size());
	}

	@Test
	void shouldGetAllCustomerNumbersWithSegmentForUserIfBookAllowed() {
		when(permissionExpressions.canBook(anyString(), anyString(), anyString())).thenReturn(true);
		List<String> customerNumbersWithSegment = sut.getAllBookPermittedCustomerIdsWithSegment();
		assertNotNull(customerNumbersWithSegment);
		assertEquals(2, customerNumbersWithSegment.size());
		customerNumbersWithSegment.forEach(customerNumber -> {
			if (customerNumber.contains(VALID_CUST_NO_ROAD)) {
				assertTrue(customerNumber.endsWith("ROAD"));
			}
			if (customerNumber.contains(VALID_CUST_NO_ASL)) {
				assertTrue(customerNumber.endsWith("ASL"));
			}
		});
	}

	@Test
	void shouldGetAllCustomersForUserIfBookNotAllowed() {
		when(permissionExpressions.canBook(anyString(), anyString(), anyString())).thenReturn(false);
		List<String> customerIds = sut.getAllBookPermittedCustomerIds();
		assertNotNull(customerIds);
		assertTrue(customerIds.isEmpty());
	}

	@Test
	void shouldGetAllCustomerNumbersWithSegmentForUserIfBookNotAllowed() {
		when(permissionExpressions.canBook(anyString(), anyString(), anyString())).thenReturn(false);
		List<String> customerNumbersWithSegment = sut.getAllBookPermittedCustomerIdsWithSegment();
		assertNotNull(customerNumbersWithSegment);
		assertTrue(customerNumbersWithSegment.isEmpty());
	}

	@ParameterizedTest
	@NullSource
	@ValueSource(strings = { VALID_CUST_NO_ROAD, VALID_CUST_NO_ASL, INVALID_CUSTOMER_NUMBER })
	void shouldGetCustomerInformationRoadForCustomer(String customerNumber) {
		Optional<RoadCustomer> roadCustomer = sut.getCustomerInformationRoad(customerNumber);

		if (customerNumber == null || !customerNumber.equals(VALID_CUST_NO_ROAD)) {
			assertTrue(roadCustomer.isEmpty());
		} else {
			assertTrue(roadCustomer.isPresent());
			assertEquals(VALID_CUST_NO_ROAD, roadCustomer.get().getCustomerNumber());
		}
	}

	@ParameterizedTest
	@NullSource
	@ValueSource(strings = { VALID_CUST_NO_ROAD, VALID_CUST_NO_ASL, INVALID_CUSTOMER_NUMBER })
	void shouldGetCustomerInformationAslForCustomer(String customerNumber) {
		Optional<AslCustomer> aslCustomer = sut.getCustomerInformationAsl(customerNumber);

		if (customerNumber == null || !customerNumber.equals(VALID_CUST_NO_ASL)) {
			assertTrue(aslCustomer.isEmpty());
		} else {
			assertTrue(aslCustomer.isPresent());
			assertEquals(VALID_CUST_NO_ASL, aslCustomer.get().getCustomerNumber());
		}
	}

	@ParameterizedTest
	@MethodSource("provideCustomerNoAndOrderTypes")
	void shouldGetBranchIdForOrderTypeDivisionFL(String customerNumber, OrderType orderType) {
		if (customerNumber.equals(INVALID_CUSTOMER_NUMBER)) {
			if (orderType.equals(OrderType.ROADFORWARDINGORDER) || orderType.equals(OrderType.ROADCOLLECTIONORDER)) {
				BranchUnresolvableException exception = assertThrows(BranchUnresolvableException.class, () -> sut.getBranchId(customerNumber, orderType));
				assertEquals("Branch could not be resolved for customerNumber: " + customerNumber, exception.getMessage());
			} else {
				assertThrows(BranchUnresolvableException.class, () -> sut.getBranchId(customerNumber, orderType));
			}
		} else {
			int branchId = sut.getBranchId(customerNumber, orderType);
			assertEquals(10, branchId);
		}
	}

	@ParameterizedTest
	@MethodSource("provideCustomerNoAndOrderTypes")
	void shouldGetBranchIdForOrderTypeDivisonEL(String customerNumber, OrderType orderType) {
		user = createPlatformUser();
		user.getCompany().getCustomersROAD().get(0).setBusinessLine(PlatformRoadCustomerV5.BusinessLineEnum.EL);
		lenient().when(this.platformApi.getPlatformUser()).thenReturn(user);

		if (customerNumber.equals(INVALID_CUSTOMER_NUMBER)) {
			if (orderType.equals(OrderType.ROADFORWARDINGORDER) || orderType.equals(OrderType.ROADCOLLECTIONORDER)) {
				assertThrows(BranchUnresolvableException.class, () -> sut.getBranchId(customerNumber, orderType));
			} else {
				assertThrows(BranchUnresolvableException.class, () -> sut.getBranchId(customerNumber, orderType));
			}
		} else {
			int branchId = sut.getBranchId(customerNumber, orderType);
			assertEquals(10, branchId);
		}
	}

	@Test
	void shouldThrowErrorOnMissingBranchForRoadCustomerEL() {
		user = createPlatformUser();
		user.getCompany().getCustomersROAD().get(0).setBusinessLine(PlatformRoadCustomerV5.BusinessLineEnum.EL);
		user.getCompany().getCustomersROAD().get(0).setBookCollectingElBranchNumber(null);
		user.getCompany().getCustomersROAD().get(0).setBookForwardingElBranchNumber(null);
		lenient().when(this.platformApi.getPlatformUser()).thenReturn(user);
		assertThrows(BranchUnresolvableException.class, () -> sut.getBranchId(VALID_CUST_NO_ROAD, OrderType.ROADCOLLECTIONORDER));
	}

	@Test
	void shouldThrowErrorOnMissingBranchForRoadCustomerFL() {
		user = createPlatformUser();
		user.getCompany().getCustomersROAD().get(0).setBusinessLine(PlatformRoadCustomerV5.BusinessLineEnum.FL);
		user.getCompany().getCustomersROAD().get(0).setBookCollectingFlBranchNumber(null);
		user.getCompany().getCustomersROAD().get(0).setBookForwardingFlBranchNumber(null);
		lenient().when(this.platformApi.getPlatformUser()).thenReturn(user);
		assertThrows(BranchUnresolvableException.class, () -> sut.getBranchId(VALID_CUST_NO_ROAD, OrderType.ROADCOLLECTIONORDER));
	}

	@ParameterizedTest
	@MethodSource("provideCustomerNoAndSegment")
	void shouldValidateCustomerNumber(String customerNumber, Segment segment) {
		String mappedSegment = segmentMapper.mapToAsl(segment);
		when(permissionExpressions.canBook(anyString(), eq(customerNumber), eq(mappedSegment))).thenReturn(!customerNumber.equals(INVALID_CUSTOMER_NUMBER));
		boolean validationResult = sut.isCustomerNumberValid(customerNumber, segment);
		if (customerNumber.equals(INVALID_CUSTOMER_NUMBER)) {
			assertFalse(validationResult);
		} else {
			assertTrue(validationResult);
		}
	}

	@ParameterizedTest
	@ValueSource(strings = { VALID_CUST_NO_ROAD, VALID_CUST_NO_ASL, INVALID_CUSTOMER_NUMBER })
	void shouldCheckIfCustomerIsAllowedToSetManualSsscc(String customerNumber) {
		boolean customerAllowedToSetManualSscc = sut.isCustomerAllowedToSetManualSscc(customerNumber);
		if (!customerNumber.equals(VALID_CUST_NO_ROAD)) {
			assertFalse(customerAllowedToSetManualSscc);
		} else {
			assertTrue(customerAllowedToSetManualSscc);
		}
	}

	@ParameterizedTest
	@MethodSource("provideCustomerNoAndSegment")
	void shouldCheckIfCustomerIsAllowedToHaveAirProducts(String customerNumber, Segment segment) {
		if (customerNumber.equals(INVALID_CUSTOMER_NUMBER)) {
			return;
		}
		boolean viewAirProducts = sut.isCustomerAllowedToViewAirProducts(customerNumber, segment);
		if (customerNumber.equals(VALID_CUST_NO_ASL) && segment == Segment.AIR) {
			assertTrue(viewAirProducts);
		} else {
			assertFalse(viewAirProducts);
		}
	}

	@ParameterizedTest
	@MethodSource("provideCustomerNoAndSegment")
	void shouldGetOrderOptionsForCustomerNumberAndSegment(String customerNumber, Segment segment) {
		Optional<OrderOptions> orderOptions = sut.getOrderOptions(customerNumber, segment);
		if (customerNumber.equals(INVALID_CUSTOMER_NUMBER)) {
			assertTrue(orderOptions.isEmpty());
		} else {
			assertTrue(orderOptions.isPresent());
			if (segment == Segment.ROAD)
				assertTrue(orderOptions.get().getFrostProtection());
		}
	}

	@ParameterizedTest
	@MethodSource("provideCustomerNoAndSegment")
	void shouldGetAddressForCustomerNumberAndSegment(String customerNumber, Segment segment) {
		Optional<Address> customerAddress = sut.getCustomerAddress(customerNumber, segment);
		if (customerNumber.equals(INVALID_CUSTOMER_NUMBER)) {
			assertTrue(customerAddress.isEmpty());
		} else {
			assertTrue(customerAddress.isPresent());
			assertEquals("name", customerAddress.get().getName());
			assertEquals("name1", customerAddress.get().getName1());
			assertEquals("12345", customerAddress.get().getPostcode());
		}
	}

	@ParameterizedTest
	@MethodSource("provideCustomerNoAndSegment")
	void shouldReturnFirstDivisionForCustomerNumberAndSegment(String customerNumber, Segment segment) {
		if (customerNumber.equals(INVALID_CUSTOMER_NUMBER)) {
			assertThrows(DivisionUnresolvableException.class, () -> sut.getFirstFoundDivision(customerNumber, segment));
		} else {
			Division division = sut.getFirstFoundDivision(customerNumber, segment);
			assertNotNull(division);
			if (segment == Segment.ROAD) {
				assertEquals("F", division.getKey());
			}
			if (segment == Segment.AIR) {
				assertEquals("T", division.getKey());
			}
		}
	}

	@Test
	void shouldGetCustomersTitleWithAllNames() {
		List<PlatformRoadCustomerV5> customersROAD = user.getCompany().getCustomersROAD();
		PlatformPrincipalAddressV5 principalAddress = customersROAD.getFirst().getPrincipalAddress();
		principalAddress.setName1("Name1");
		principalAddress.setName2("Name2");
		principalAddress.setName3("Name3");

		List<CustomerDto> customers = sut.getCustomers(Segment.ROAD);

		assertEquals("Name1, Name2, Name3", getCustomerFromList(customers, VALID_CUST_NO_ROAD).getLabel());
	}

	private static CustomerDto getCustomerFromList(List<CustomerDto> customers, @Nonnull String customerNumber) {
		Optional<CustomerDto> first = customers.stream().filter(customerDto -> customerNumber.equals(customerDto.getCustomerNumber())).findFirst();
		return first.get();
	}

	@Test
	void shouldReturnEmptyListForRoadCustomersWithMissingBranch() {
		user.getCompany().getCustomersROAD().getFirst().setBookCollectingElBranchNumber(null);
		user.getCompany().getCustomersROAD().getFirst().setBookForwardingElBranchNumber(null);
		user.getCompany().getCustomersROAD().getFirst().setBusinessLine(PlatformRoadCustomerV5.BusinessLineEnum.EL);
		lenient().when(this.platformApi.getPlatformUser()).thenReturn(user);

		List<CustomerDto> customers = sut.getCustomers(Segment.ROAD);

		assertTrue(customers.isEmpty());
	}

	@Test
	void shouldGetCustomersTitleWithName1() {
		List<PlatformRoadCustomerV5> customersROAD = user.getCompany().getCustomersROAD();
		PlatformPrincipalAddressV5 principalAddress = customersROAD.getFirst().getPrincipalAddress();
		principalAddress.setName1("Name1");
		principalAddress.setName2(null);
		principalAddress.setName3(null);

		List<CustomerDto> customers = sut.getCustomers(Segment.ROAD);

		assertEquals("Name1", getCustomerFromList(customers, VALID_CUST_NO_ROAD).getLabel());
	}

	@Test
	void shouldGetCustomersTitleWithName1AndName2() {
		List<PlatformRoadCustomerV5> customersROAD = user.getCompany().getCustomersROAD();
		PlatformPrincipalAddressV5 principalAddress = customersROAD.getFirst().getPrincipalAddress();
		principalAddress.setName1("Name1");
		principalAddress.setName2("Name2");
		principalAddress.setName3(null);

		List<CustomerDto> customers = sut.getCustomers(Segment.ROAD);

		assertEquals("Name1, Name2", getCustomerFromList(customers, VALID_CUST_NO_ROAD).getLabel());
	}

	@Test
	void shouldGetCustomersTitleWithName1AndName3() {
		List<PlatformRoadCustomerV5> customersROAD = user.getCompany().getCustomersROAD();
		PlatformPrincipalAddressV5 principalAddress = customersROAD.getFirst().getPrincipalAddress();
		principalAddress.setName1("Name1");
		principalAddress.setName2(null);
		principalAddress.setName3("Name3");

		List<CustomerDto> customers = sut.getCustomers(Segment.ROAD);

		assertEquals("Name1, Name3", getCustomerFromList(customers, VALID_CUST_NO_ROAD).getLabel());
	}

	@Test
	void shouldGetCustomersTitleWithName1AndEmptyString() {
		List<PlatformRoadCustomerV5> customersROAD = user.getCompany().getCustomersROAD();
		PlatformPrincipalAddressV5 principalAddress = customersROAD.getFirst().getPrincipalAddress();
		principalAddress.setName1("Name1");
		principalAddress.setName2(" ");
		principalAddress.setName3(null);

		List<CustomerDto> customers = sut.getCustomers(Segment.ROAD);

		assertEquals("Name1", getCustomerFromList(customers, VALID_CUST_NO_ROAD).getLabel());
	}

	@Test
	void shouldGetCustomersTitleWithNameLonger30() {
		List<PlatformRoadCustomerV5> customersROAD = user.getCompany().getCustomersROAD();
		PlatformPrincipalAddressV5 principalAddress = customersROAD.getFirst().getPrincipalAddress();
		principalAddress.setName1("This is a very long name");
		principalAddress.setName2("that should be cut off");
		principalAddress.setName3(null);

		List<CustomerDto> customers = sut.getCustomers(Segment.ROAD);

		assertEquals("This is a very long name, t...", getCustomerFromList(customers, VALID_CUST_NO_ROAD).getLabel());
	}


	@Test
	void shouldNotHaveCashOnDeliveryInOtherCountries() {
		List<PlatformRoadCustomerV5> customersROAD = user.getCompany().getCustomersROAD();
		customersROAD.getFirst().setCashOnDelivery(true);

		List<CustomerDto> customers = sut.getCustomers(Segment.ROAD);

		assertTrue(getCustomerFromList(customers, VALID_CUST_NO_ROAD).getCashOnDelivery());
	}

	private static Stream<Arguments> provideCustomerNoAndOrderTypes() {
		return Stream.of(Arguments.of(VALID_CUST_NO_ROAD, OrderType.ROADFORWARDINGORDER), Arguments.of(VALID_CUST_NO_ROAD, OrderType.ROADCOLLECTIONORDER),
				Arguments.of(VALID_CUST_NO_ASL, OrderType.AIREXPORTORDER), Arguments.of(VALID_CUST_NO_ASL, OrderType.AIRIMPORTORDER),
				Arguments.of(VALID_CUST_NO_ASL, OrderType.SEAEXPORTORDER), Arguments.of(VALID_CUST_NO_ASL, OrderType.SEAIMPORTORDER),
				Arguments.of(INVALID_CUSTOMER_NUMBER, OrderType.ROADFORWARDINGORDER), Arguments.of(INVALID_CUSTOMER_NUMBER, OrderType.ROADCOLLECTIONORDER),
				Arguments.of(INVALID_CUSTOMER_NUMBER, OrderType.AIREXPORTORDER), Arguments.of(INVALID_CUSTOMER_NUMBER, OrderType.AIRIMPORTORDER),
				Arguments.of(INVALID_CUSTOMER_NUMBER, OrderType.SEAEXPORTORDER), Arguments.of(INVALID_CUSTOMER_NUMBER, OrderType.SEAIMPORTORDER));
	}

	private static Stream<Arguments> provideCustomerNoAndSegment() {
		return Stream.of(Arguments.of(VALID_CUST_NO_ROAD, Segment.ROAD), Arguments.of(VALID_CUST_NO_ASL, Segment.AIR), Arguments.of(INVALID_CUSTOMER_NUMBER, Segment.ROAD),
				Arguments.of(INVALID_CUSTOMER_NUMBER, Segment.AIR));
	}
}
