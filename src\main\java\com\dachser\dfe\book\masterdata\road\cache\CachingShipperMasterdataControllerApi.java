package com.dachser.dfe.book.masterdata.road.cache;

import com.dachser.dfe.book.cache.CacheNames;
import com.dachser.dfe.book.exception.ErrorIdExternalServiceNotAvailableException;
import com.dachser.dfe.road.masterdata.api.ShipperMasterdataControllerApi;
import com.dachser.dfe.road.masterdata.model.RMDShipperMasterdataDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpStatusCodeException;

import java.util.ArrayList;
import java.util.List;

import static com.dachser.dfe.book.exception.BookErrorId.ERR_RF_01;

@Slf4j
public class CachingShipperMasterdataControllerApi extends ShipperMasterdataControllerApi {

	private final ShipperMasterdataControllerApi delegate;

	public CachingShipperMasterdataController<PERSON>pi(ShipperMasterdataControllerApi delegate) {
		this.delegate = delegate;
	}

	@Override
	@Cacheable(cacheNames = CacheNames.ROAD_SHIPPER_DATA)
	public List<RMDShipperMasterdataDTO> getShipperMasterdata(Long shipperNumber, Integer businessDomain) {
		log.debug("Requesting shipper masterdata for input {}, {}", shipperNumber, businessDomain);
		final List<RMDShipperMasterdataDTO> shipperMasterdata = new ArrayList<>();
		try {
			shipperMasterdata.addAll(delegate.getShipperMasterdata(shipperNumber, businessDomain));
		} catch (HttpStatusCodeException statusCodeException) {
			if (statusCodeException.getStatusCode() == HttpStatus.NOT_FOUND) {
				log.debug("No ShipperMasterdata for customerNumber: {}", shipperNumber);
			} else {
				throw new ErrorIdExternalServiceNotAvailableException(ERR_RF_01,
						String.format("Shipper masterdata service not available with code: %s and message: %s ", statusCodeException.getStatusCode(),
								statusCodeException.getMessage()));
			}
		}
		return shipperMasterdata;
	}

}
