package com.dachser.dfe.book.order.validation.common;

import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.order.common.orderline.OrderLine;
import com.dachser.dfe.book.order.validation.Messages;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.function.Function;

@RequiredArgsConstructor
public abstract class OrderLineConstraintValidator<R extends OrderLine> {

	private final Translator translator;

	protected OrderLineConstraintValidator() {
		this.translator = null;
	}

	public boolean isOrderLineValid(List<R> orderLines, ConstraintValidatorContext context) {
		context.disableDefaultConstraintViolation();
		if (noWeightOnAnyOrderLine(orderLines, OrderLine::getWeight)) {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode("orderLines[0]")
					.addContainerElementNode("weight", OrderLine.class, 0).addConstraintViolation();
			return false;
		}
		return true;
	}

	private boolean noWeightOnAnyOrderLine(List<R> lines, Function<R, ?> weightGetter) {
		return lines != null && !lines.isEmpty() && lines.stream().noneMatch(line -> weightGetter.apply(line) != null);
	}
}