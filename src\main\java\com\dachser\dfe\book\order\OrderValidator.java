package com.dachser.dfe.book.order;

import com.dachser.dfe.book.mapper.OrderStatusMapper;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.OrderStatusDto;
import com.dachser.dfe.book.model.OrderValidationResultDto;
import com.dachser.dfe.book.model.ValidationResultEntryDto;
import com.dachser.dfe.book.order.exception.MissingAnnotationException;
import com.dachser.dfe.book.order.exception.OrderValidationFailedException;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.dachser.dfe.book.order.validation.OrderAddressValidator;
import com.dachser.dfe.book.order.validation.common.ValidationGroup;
import jakarta.transaction.Transactional;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@RequiredArgsConstructor
@Slf4j
@Component
public class OrderValidator {

	private final OrderRepositoryFacade orderRepositoryAccess;

	private final Validator validator;

	private final OrderStatusUpdater orderStatusUpdater;

	private final OrderStatusMapper orderStatusMapper;

	private final OrderValidationResultMapper orderValidationResultMapper;

	private final OrderAddressValidator addressValidator;

	private final OrderMapper orderMapper;

	OrderValidationResultDto validateOrder(Order order) {
		orderRepositoryAccess.preLoadLazyOrderCollections(order);
		final OrderValidationResultDto orderValidationResultDto = new OrderValidationResultDto();
		orderValidationResultDto.setResults(new ArrayList<>());
		final Set<ConstraintViolation<Order>> validate = validator.validate(order, determineValidationClass(order));
		List<ValidationResultEntryDto> addressValidationResults = validateAddresses(order);
		if (hasNoValidationErrors(validate, addressValidationResults) && OrderStatus.DRAFT.equals(order.getStatus())) {
			orderStatusUpdater.updateOrderStatus(order, order.validationSucceededStatus());
			log.info("Order with id #{} is now valid ", order.getOrderId());

		}
		OrderStatusDto orderStatusDto = orderStatusMapper.mapStatus(order.getStatus());
		List<ValidationResultEntryDto> validationResultEntryDto = orderValidationResultMapper.map(validate);
		orderValidationResultDto.setNewOrderStatus(orderStatusDto);
		orderValidationResultDto.setValid(hasNoValidationErrors(validate, addressValidationResults));
		orderValidationResultDto.getResults().addAll(validationResultEntryDto);
		orderValidationResultDto.getResults().addAll(addressValidationResults);
		if (orderValidationResultDto.getValid() == Boolean.TRUE) {
			log.info("Order #{} has been validated to valid.", order.getOrderId());
		} else {
			log.info("Order #{} has been validated to invalid with results: {}", order.getOrderId(), orderValidationResultDto.getResults());
		}
		return orderValidationResultDto;
	}

	private static boolean hasNoValidationErrors(Set<ConstraintViolation<Order>> validate, List<ValidationResultEntryDto> addressValidationResults) {
		return validate.isEmpty() && addressValidationResults.isEmpty();
	}

	private List<ValidationResultEntryDto> validateAddresses(Order order) {
		OrderType orderType = order.getOrderType();
		// Consignee
		List<ValidationResultEntryDto> validationResultEntryDtos = addressValidator.validateAddressBasedOnOrderType(order.getConsigneeAddress(), orderType);
		// Shipper
		validationResultEntryDtos.addAll(addressValidator.validateAddressBasedOnOrderType(order.getShipperAddress(), orderType));

		return validationResultEntryDtos;
	}

	private Class<?> determineValidationClass(Order order) {
		final ValidationGroup annotation = order.getClass().getAnnotation(ValidationGroup.class);
		if (annotation != null) {
			return annotation.validationGroupClass();
		} else {
			throw new MissingAnnotationException("Annotation ValidationGroup is missing on class " + order.getClass().getName());
		}
	}

	@Transactional(value = Transactional.TxType.MANDATORY)
	public OrderValidationResultDto checkValidateOrder(Order order) throws OrderValidationFailedException {
		final OrderValidationResultDto validationResult = validateOrder(order);
		BasicOrderDto orderDto = (BasicOrderDto) orderMapper.map(order);
		if (Boolean.FALSE.equals(validationResult.getValid())) {
			throw new OrderValidationFailedException(validationResult, orderDto);
		} else {
			return validationResult;
		}
	}
}
