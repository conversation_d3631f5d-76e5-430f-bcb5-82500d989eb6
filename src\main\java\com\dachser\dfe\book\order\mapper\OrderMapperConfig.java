package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.mapper.OrderStatusMapper;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.port.PortMapper;
import com.dachser.dfe.book.quote.QuoteMapper;
import org.mapstruct.MapperConfig;
import org.mapstruct.Mapping;
import org.mapstruct.MappingInheritanceStrategy;

@MapperConfig(mappingInheritanceStrategy = MappingInheritanceStrategy.AUTO_INHERIT_FROM_CONFIG, uses = { OrderTextMapper.class, OrderReferenceMapper.class, OrderLineMapper.class,
		OrderContactMapper.class, FreightTermCodeMapper.class, OrderStatusMapper.class, PortMapper.class, OrderAddressMapper.class, QuoteMapper.class, DeliveryOptionMapper.class,
		PackingPositionMapper.class, FullContainerLoadMapper.class, IncoTermCodeMapper.class, DangerousGoodsMapper.class, })
public interface OrderMapperConfig {

	@Mapping(target = "orderTexts", source = "texts")
	@Mapping(source = "furtherAddresses", target = "addresses")
	@Mapping(source = "consigneeAddress", target = "consigneeAddress")
	@Mapping(source = "shipperAddress", target = "shipperAddress")
	@Mapping(source = "goodsCurrency", target = "currency")
	@Mapping(target = "collectionDate", source = "collectionTime.collectionDate", nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "collectionFrom", source = "collectionTime.from")
	@Mapping(target = "collectionTo", source = "collectionTime.to")
	@Mapping(target = "orderExpiryDate", source = "expirationTime")
	@Mapping(target = "principalAddress", source = "principalAddress", ignore = true)
	@Mapping(target = "lastModified", source = "lastModified", ignore = true)
	@Mapping(target = "quoteInformation", source = "quoteInformation", ignore = true)
	@Mapping(source = "createdAt", target = "createdAt", ignore = true)
	@Mapping(source = "createdBy", target = "creator", ignore = true)
	@Mapping(source = "sendAt", target = "sendAt", ignore = true)
	@Mapping(source = "sendBy", target = "sendUser", ignore = true)
	Order map(BasicOrderDto order);

	@Mapping(target = "orderStatus.status", source = "status")
	@Mapping(target = "orderStatus.description", source = "status", qualifiedByName = "mapStatusDescription")
	@Mapping(target = "clonedOrder", source = "datasource", qualifiedByName = "mapOrderCloned")
	@Mapping(target = "emissionForecast", source = "emissionForecast")
	BasicOrderDto map(Order order);

}
