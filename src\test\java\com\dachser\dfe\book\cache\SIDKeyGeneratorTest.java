package com.dachser.dfe.book.cache;

import com.dachser.dfe.platform.security.model.DfePrincipal;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@DirtiesContext
@ExtendWith(SpringExtension.class)
class SIDKeyGeneratorTest {

	@Mock
	private DfePrincipal principal;

	@Mock
	private Authentication authentication;

	SIDKeyGenerator sidKeyGenerator = new SIDKeyGenerator();

	@Test
	void shouldGenerateKeyForValidAuthenticationToken() throws NoSuchMethodException {
		UUID uuid = UUID.randomUUID();
		when(principal.getSessionId()).thenReturn(Optional.of(uuid));
		when(authentication.getPrincipal()).thenReturn(principal);
		SecurityContextHolder.getContext().setAuthentication(authentication);
		Object generated = sidKeyGenerator.generate(new Object(), SIDKeyGenerator.class.getMethod("generate", Object.class, Method.class, Object[].class), new Object[] {});
		assertTrue(generated instanceof Optional);
		assertEquals(uuid, ((Optional) generated).get());
	}

	@Test
	void shouldThrowErrorForInvalidAuthenticationToken() {
		Map<String, Object> jwtClaim = new HashMap<>();
		jwtClaim.put("SID", "LEYEMTEFWUWBOLGIVTSRSQYIKYLMNYZZFGHLPLTTEKOEHQIMIMGPZOD");
		HashMap<String, Object> header = new HashMap<>();
		header.put("key", "value");
		Jwt jwt = new Jwt("token", null, null, header, jwtClaim);
		JwtAuthenticationToken jwtAuthenticationToken = new JwtAuthenticationToken(jwt);
		SecurityContextHolder.getContext().setAuthentication(jwtAuthenticationToken);
		IllegalStateException exception = assertThrows(IllegalStateException.class,
				() -> sidKeyGenerator.generate(new Object(), SIDKeyGenerator.class.getMethod("generate", Object.class, Method.class, Object[].class), new Object[] {}));
		assertEquals("Unexpected token implementation found in the security context", exception.getMessage());
	}

}
