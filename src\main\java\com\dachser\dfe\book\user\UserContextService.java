package com.dachser.dfe.book.user;

import com.dachser.dfe.book.exception.generic.NotAuthenticatedException;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.CustomerDto;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.user.model.Address;
import com.dachser.dfe.book.user.model.AslCustomer;
import com.dachser.dfe.book.user.model.OrderOptions;
import com.dachser.dfe.book.user.model.RoadCustomer;
import com.dachser.dfe.book.user.model.User;
import com.dachser.dfe.book.user.model.UserPreferences;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.Optional;

public interface UserContextService {

	List<String> getAllBookPermittedCustomerIds();

	List<String> getAllBookPermittedCustomerIdsWithSegment();

	String getCurrentUserId() throws NotAuthenticatedException;

	String getCurrentUserIdIfAvailable();

	List<CustomerDto> getCustomers(Segment segment);

	Optional<RoadCustomer> getCustomerInformationRoad(String customerNumber);

	Optional<AslCustomer> getCustomerInformationAsl(String customerNumber);

	@NonNull
	int getBranchId(String customerNumber, OrderType orderType);


	/**
	 * @deprecated use {@link BusinessDomainProvider} instead
	 * Placeholder for future extension of business domains
	 */
	@Deprecated(forRemoval = true)
	Integer getBusinessDomain();

	/**
	 * Temporary method to get the first division of a customer.
	 * Will be removed later when the user can choose a division.
	 * Currentyl there should be just one division per customer.
	 */
	@NonNull
	Division getFirstFoundDivision(String customerNumber, Segment segment);

	boolean isCustomerAllowedToSetManualSscc(String customerNumber);

	boolean isCustomerAllowedToViewAirProducts(String customerNumber, Segment map);

	boolean isCustomerAllowedToViewPackingAidPosition(String customerNumber);

	boolean isCustomerAllowedToViewDangerousGoods(String customerNumber);

	boolean isCustomerAllowedToPrintConsignmentLabels(String customerNumber);

	Optional<OrderOptions> getOrderOptions(String customerNumber, Segment segment);

	Optional<Address> getCustomerAddress(String customerNumber, Segment segment);

	User getCurrentUser() throws NotAuthenticatedException;

	UserPreferences getUserPreferences();
}
