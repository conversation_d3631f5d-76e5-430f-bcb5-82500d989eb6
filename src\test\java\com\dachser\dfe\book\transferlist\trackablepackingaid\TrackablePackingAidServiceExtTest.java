package com.dachser.dfe.book.transferlist.trackablepackingaid;

import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.masterdata.road.RoadMasterDataApiWrapper;
import com.dachser.dfe.book.transferlist.exception.TrackablePackingAidServiceNotAvailableException;
import com.dachser.dfe.book.user.BusinessDomainProvider;
import com.dachser.dfe.road.masterdata.model.RMDTrackablePackingAidRequestDTO;
import com.dachser.dfe.road.masterdata.model.RMDTrackablePackingAidResponseDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestClientException;

import java.time.LocalDate;
import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TrackablePackingAidServiceExtTest {

	@Mock
	private RoadMasterDataApiWrapper roadMasterdataApiWrapper;

	@Mock
	private BusinessDomainProvider businessDomainProvider;

	@Captor
	private ArgumentCaptor<RMDTrackablePackingAidRequestDTO> requestCaptor;

	@InjectMocks
	private TrackablePackingAidServiceExt sut;

	private RMDTrackablePackingAidResponseDTO createResponse() {
		RMDTrackablePackingAidResponseDTO response = new RMDTrackablePackingAidResponseDTO();
		response.setTrackablePackingAids(List.of("EU"));
		response.setNonTrackablePackingAids(List.of("BX", "KT", "CC"));
		return response;
	}

	@Test
	void callService() {
		LocalDate collectionDate = LocalDate.of(2024, 6, 19);
		String shipperCountryCode = "DE";
		String consigneeCountryCode = "DE";
		Division division = Division.F;
		List<String> packingAidTypes = List.of("EU,BX,KT,CC");
		var expectedResponse = createResponse();

		when(roadMasterdataApiWrapper.categorizeTransferList(any())).thenReturn(expectedResponse);
		when(businessDomainProvider.getBusinessDomain()).thenReturn(1);

		RMDTrackablePackingAidResponseDTO actualResponse = sut.categorizeTransferList(shipperCountryCode, consigneeCountryCode, division, collectionDate, packingAidTypes);
		verify(roadMasterdataApiWrapper).categorizeTransferList(requestCaptor.capture());

		final RMDTrackablePackingAidRequestDTO capturedPRequest = requestCaptor.getValue();
		assertThat(capturedPRequest.getBusinessDomain()).isEqualTo(1);
		assertThat(capturedPRequest.getNationConsignor()).isEqualTo(shipperCountryCode);
		assertThat(capturedPRequest.getNationConsignee()).isEqualTo(consigneeCountryCode);
		assertThat(capturedPRequest.getDivision()).isEqualTo(division.name());
		assertThat(capturedPRequest.getDate()).isEqualTo(20240619);

		assertThat(actualResponse).isEqualTo(expectedResponse);
	}

	@Test
	void shouldThrowServiceNotAvailableExceptionIfApiCallFails() {
		LocalDate collectionDate = LocalDate.of(2024, 6, 19);
		when(roadMasterdataApiWrapper.categorizeTransferList(any())).thenThrow(RestClientException.class);

		List<String> packingAidTypes = List.of("EU");
		assertThrows(TrackablePackingAidServiceNotAvailableException.class, () -> sut.categorizeTransferList("DE", "DE", Division.F, collectionDate, packingAidTypes));
	}
}