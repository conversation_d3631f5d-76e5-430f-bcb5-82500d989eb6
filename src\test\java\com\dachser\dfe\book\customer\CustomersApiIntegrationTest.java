package com.dachser.dfe.book.customer;

import com.dachser.dfe.book.BaseWebClientTest;
import com.dachser.dfe.book.country.CountryCode;
import com.dachser.dfe.book.model.SegmentDto;
import com.dachser.dfe.book.model.TestMockData;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.reactive.server.EntityExchangeResult;

import static com.dachser.dfe.book.MockConstants.ACTIVE_COMPANY_ACCESS_TOKEN;
import static com.dachser.dfe.book.model.TestMockData.CLASSICLINE;
import static com.dachser.dfe.book.model.TestMockData.TARGOFLEX;
import static com.dachser.dfe.book.model.TestMockData.TARGO_ONSITE;
import static com.dachser.dfe.book.model.TestMockData.TARGO_ONSITE_FIX;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.not;


@TestPropertySource(properties = { "dfe.book.mock.product.road=false",
		"ENV_BOOK_API_DFE_ROAD_MASTERDATA_BASEURL=https://dach041y.dach041.dachser.com:21365/road.masterdata.order.service" })
@Slf4j
class CustomersApiIntegrationTest extends BaseWebClientTest {

	@Test
	void roadDeliveryProducts() {
		// @formatter:off
		EntityExchangeResult<byte[]> response = client
		.get()
		.uri(uriBuilder ->
			uriBuilder
			.path("/v1/customers/delivery-products-road")
			// given a ROAD customer
			.queryParam("customerNumber", TestMockData.Customer.DACHSER.getCustomerNumber())
			.queryParam("customerSegment", SegmentDto.ROAD.getValue())
			// shipping between Germany and France
			.queryParam("shipperCountryCode", CountryCode.GERMANY.getIsoCode())
			.queryParam("shipperPostalCode", "87435")
			.queryParam("consigneeCountryCode", CountryCode.FRANCE.getIsoCode())
			.queryParam("consigneePostalCode", "85130")
			.build())
		.header(HttpHeaders.AUTHORIZATION, "Bearer ".concat(ACTIVE_COMPANY_ACCESS_TOKEN))
		// when looking for possible delivery ROAD products
		.exchange()
		// then we obtain a JSON response
		.expectStatus().isOk()
		.expectHeader().contentType(MediaType.APPLICATION_JSON)
		// and a list of products
		.expectBody()
		.jsonPath("$.deliveryProducts").exists()
		.jsonPath("$.deliveryProducts").isArray()
		.jsonPath("$.deliveryProducts").isNotEmpty()
		// and TARGOFLEX is one of them
		.jsonPath("$.deliveryProducts[*].code").value(hasItem(TARGOFLEX))
		.returnResult();
		// @formatter:on
		log.info("road delivery products - HTTP {} response = {}", response.getStatus(), response.getResponseBodyContent());
	}

	@Test
	void irishRoadDeliveryProducts() {
		// @formatter:off
		EntityExchangeResult<byte[]> response = client
		.get()
		.uri(uriBuilder ->
			uriBuilder
			.path("/v1/customers/delivery-products-road")
			// given a ROAD customer
			.queryParam("customerNumber", TestMockData.Customer.DACHSER.getCustomerNumber())
			.queryParam("customerSegment", SegmentDto.ROAD.getValue())
			// shipping within Ireland
			.queryParam("shipperCountryCode", CountryCode.IRELAND.getIsoCode())
			.queryParam("shipperPostalCode", "03153") // Dachser identifier that stands for eircode "D08 K3V9"
			.queryParam("consigneeCountryCode", CountryCode.IRELAND.getIsoCode())
			.queryParam("consigneePostalCode", "04467") // Dachser identifier that stands for eircode "F42 N129"
			.build())
		.header(HttpHeaders.AUTHORIZATION, "Bearer ".concat(ACTIVE_COMPANY_ACCESS_TOKEN))
		// when looking for possible delivery ROAD products
		.exchange()
		// then we obtain a JSON response
		.expectStatus().isOk()
		.expectHeader().contentType(MediaType.APPLICATION_JSON)
		// and a list of products
		.expectBody()
		.jsonPath("$.deliveryProducts").exists()
		.jsonPath("$.deliveryProducts").isArray()
		.jsonPath("$.deliveryProducts").isNotEmpty()
		// and CLASSICLINE is not present
		.jsonPath("$.deliveryProducts[*].code").value(not(hasItem(CLASSICLINE)))
		// and expected products are present
		.jsonPath("$.deliveryProducts[*].code").value(hasItem(TARGO_ONSITE))
		.jsonPath("$.deliveryProducts[*].code").value(hasItem(TARGO_ONSITE_FIX))
		.returnResult();
		// @formatter:on
		log.info("irish road delivery products - HTTP {} response = {}", response.getStatus(), response.getResponseBodyContent());
	}

}