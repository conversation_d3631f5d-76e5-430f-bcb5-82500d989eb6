package com.dachser.dfe.book.country;

import com.dachser.dfe.book.model.IrelandPostalCodeDto;
import com.dachser.dfe.masterdata.geo.model.GMDDachserPostalCode;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PostalCodeMapper {

	IrelandPostalCodeDto map(GMDDachserPostalCode mdgDachserPostalCode);

	List<IrelandPostalCodeDto> map(List<GMDDachserPostalCode> mdgDachserPostalCodes);
}
