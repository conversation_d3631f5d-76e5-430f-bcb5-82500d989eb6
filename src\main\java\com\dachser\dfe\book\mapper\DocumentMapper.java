package com.dachser.dfe.book.mapper;

import com.dachser.dfe.book.document.Document;
import com.dachser.dfe.book.document.DocumentCategory;
import com.dachser.dfe.book.document.DocumentStatus;
import com.dachser.dfe.book.model.DocumentCategoryTypeDto;
import com.dachser.dfe.book.model.DocumentInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.springframework.http.MediaType;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;

@Mapper(componentModel = "spring")
public interface DocumentMapper {

	@Slf4j
	final class LogHolder
	{}

	@Mapping(target = "documentCategoryEnum", source = "documentType.category", qualifiedByName = { "mapDocumentCategoryType" })
	@Mapping(target = "documentCategory", source = "documentType.category")
	@Mapping(target = "documentClass", source = "documentType.label")
	@Mapping(target = "documentTypeId", source = "documentType.documentTypeId")
	@Mapping(target = "documentType", source = "documentType.type")
	@Mapping(target = "extension", source = "fileType.extension")
	@Mapping(target = "mimeType", source = "fileType.contentType")
	@Mapping(target = "documentProcessed", source = "status", qualifiedByName = { "mapStatusToProcessed" })
	DocumentInfoDto mapDocumentInfo(Document document);

	List<DocumentInfoDto> mapDocumentInfo(List<Document> documents);

	@Named("mapDocumentCategoryType")
	default DocumentCategoryTypeDto mapDocumentCategoryType(String documentCategory) {
		DocumentCategory categoryToMap = DocumentCategory.getByLabel(documentCategory);
		if (DocumentCategory.UNKNOWN.equals(categoryToMap)) {
			LogHolder.log.warn("No Document Category mapping possible for {}.", documentCategory);
		}
		return DocumentCategoryTypeDto.fromValue(categoryToMap.name());
	}

	@Named("mapStatusToProcessed")
	default boolean mapStatusToProcessed(DocumentStatus status) {
		return DocumentStatus.NEW != status;
	}

	default OffsetDateTime map(Instant instant) {
		return instant == null ? null : OffsetDateTime.ofInstant(instant, ZoneOffset.UTC);
	}

	default String map(MediaType mediaType) {
		return mediaType == null ? null : mediaType.toString();
	}

}
