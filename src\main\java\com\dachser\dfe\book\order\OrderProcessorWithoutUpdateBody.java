package com.dachser.dfe.book.order;

import com.dachser.dfe.book.advice.AdviceService;
import com.dachser.dfe.book.dangerousgoods.DangerousGoodsTransferListService;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ErrorIdBaseException;
import com.dachser.dfe.book.exception.ErrorIdOrderSendFailedException;
import com.dachser.dfe.book.exception.generic.BadRequestException;
import com.dachser.dfe.book.order.exception.OrderSubmissionFailedException;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
class OrderProcessorWithoutUpdateBody extends OrderProcessor<Long> {
	public OrderProcessorWithoutUpdateBody(List<OrderSubmitter<?>> orderSubmitters, AdviceService adviceService, OrderRepositoryFacade orderRepository, OrderMapper orderMapper,
			OrderLabelGenerator orderLabelGenerator, OrderLabelPrinter labelPrinter, OrderDefaults orderDefaults, List<OrderPostProcessor<?>> postProcessors,
			DangerousGoodsTransferListService dangerousGoodsTransferListService) {
		super(orderSubmitters, adviceService, orderRepository, orderMapper, orderLabelGenerator, labelPrinter, dangerousGoodsTransferListService, orderDefaults, postProcessors);
	}

	@Override
	void fillStateWithOrder(Long orderId) {
		setCurrentOrder(orderRepository.loadOrderById(orderId));
		checkCurrentOrderForExpiredQuoteOrder();
		writeCurrentOrderToState();
	}

	@Override
	void handleSubmissionError(OrderSubmissionFailedException orderSaveFailedException) {
		throw new ErrorIdOrderSendFailedException(orderSaveFailedException.getMessage(), orderSaveFailedException.getErrorId());
	}

	@Override
	void handlePrintLabelError(Exception exception) {
		log.debug("Error while printing labels", exception);
		if (exception instanceof ErrorIdBaseException labelsErrorIdException) {
			throw labelsErrorIdException;
		} else if (exception instanceof BadRequestException badRequestException) {
			throw badRequestException;
		} else {
			throw new ErrorIdBaseException(BookErrorId.ERR_LB_01, exception.getMessage());
		}
	}
}
