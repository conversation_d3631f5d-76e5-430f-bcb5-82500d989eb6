package com.dachser.dfe.book.order.sea;

import com.dachser.dfe.book.order.MarksAndNumbersOrderLine;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.common.fcl.FullContainerLoad;
import com.dachser.dfe.book.order.common.orderline.OrderLine;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "order_line_sea")
public class SeaOrderLine extends OrderLine implements MarksAndNumbersOrderLine {

	@OneToMany(mappedBy = "orderLine", cascade = { CascadeType.ALL }, orphanRemoval = true, fetch = FetchType.LAZY)
	@ToString.Exclude
	@Valid
	private List<SeaOrderLineHsCode> hsCodes;

	@DecimalMin("0.001")
	@Max(99999)
	private BigDecimal weight;

	@Size(max = 1500)
	private String markAndNumbers;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "order_id", nullable = false)
	@ToString.Exclude
	@EqualsAndHashCode.Exclude
	private Order order;

	@JoinColumn(name = "full_container_load_id")
	private Long fullContainerLoadId;

	@Override
	public void setWeight(BigDecimal weight) {
		this.weight = weight;
	}

	@Transient
	public FullContainerLoad getFullContainerLoad() {
		if (getOrder() instanceof SeaOrder seaOrder && seaOrder.getFullContainerLoads() != null && seaOrder.isFullContainerLoad()) {
			return seaOrder.getFullContainerLoads().stream().filter(fcl -> fcl.getId().equals(fullContainerLoadId)).findFirst().orElse(null);
		}
		return null;
	}
}
