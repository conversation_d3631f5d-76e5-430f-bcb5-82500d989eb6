package com.dachser.dfe.book.resthandler;

import com.dachser.dfe.book.api.OrderOverviewApiDelegate;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.mapper.DivisionMapper;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.DivisionDto;
import com.dachser.dfe.book.model.TermDto;
import com.dachser.dfe.book.product.air.AirProductService;
import com.dachser.dfe.book.product.road.RoadProductsService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class OrderOverviewApiHandler implements OrderOverviewApiDelegate {

	private final GeneralDataService generalDataService;

	private final AirProductService airProductService;

	private final RoadProductsService roadProductsService;

	private final DivisionMapper divisionMapper;

	@Override
	public ResponseEntity<List<TermDto>> getTerms() {
		return ResponseEntity.ok(generalDataService.getAllTerms());
	}

	@Override
	public ResponseEntity<List<DeliveryProductDto>> getRoadDeliveryProducts(DivisionDto division) {
		final Division mappedDivision = divisionMapper.map(division);
		final List<DeliveryProductDto> products = roadProductsService.getDeliveryProductsForDivision(mappedDivision);
		return ResponseEntity.ok(products);
	}

	@Override
	public ResponseEntity<List<AirProductDto>> getAirProducts(Boolean includeDeactivated) {
		return ResponseEntity.ok(airProductService.getAirProducts(includeDeactivated));
	}
}

