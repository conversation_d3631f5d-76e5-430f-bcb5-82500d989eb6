package com.dachser.dfe.book.dip.converter.road.forwarding;

import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ForwardingOrderInformation;
import com.dachser.dfe.book.dip.converter.shared.DateMapper;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.SubclassExhaustiveStrategy;

@Mapper(uses = { ForwardingTransportMapper.class, DateMapper.class,
		ForwardingDocumentHeaderMapper.class }, componentModel = "spring", subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION)
@Named("ForwardingOrderInformationMapper")
public interface ForwardingOrderInformationMapper {

	@Mapping(target = "documentHeader", source = ".")
	@Mapping(target = "transport", source = ".")
	ForwardingOrderInformation map(ForwardingOrder order);
}
