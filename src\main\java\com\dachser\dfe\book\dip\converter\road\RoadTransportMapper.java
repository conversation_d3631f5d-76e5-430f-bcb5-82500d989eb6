package com.dachser.dfe.book.dip.converter.road;

import com.dachser.dfe.book.order.OrderReferenceHelper;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.common.OrderText;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public interface RoadTransportMapper<S, P, G, R, A, O extends RoadOrder> {

	String ADDRESS_TYPE_SHIPPER = "CZ";
	String ADDRESS_TYPE_CONSIGNEE = "CN";
	String ADDRESS_TYPE_FORWARDER = "FW";

	@Mapping(target = "textType", source = "textType")
	@Mapping(target = "text", expression = "java(List.of(orderText.getText()))")
	S mapShipmentText(OrderText orderText);

	List<S> mapShipmentText(List<OrderText> orderTexts);

	@Mapping(target = "partnerID", source = "consigneeAddress.gln", qualifiedByName = "mapInvalidGlnAsIdOrNull")
	@Mapping(target = "partnerGLN", source = "consigneeAddress.gln", qualifiedByName = "mapValidGlnOrNull")
	@Mapping(target = "partnerName", source = "consigneeAddress", qualifiedByName = "mapAddressNames")
	@Mapping(target = "addressInformation", source = "consigneeAddress")
	@Mapping(target = "contactInformation", source = "deliveryContact")
	P mapPartnerInformationForConsignee(O order);

	@Mapping(target = "amount", source = "goodsValue")
	@Mapping(target = "currency", source = "currency")
	G mapGoodsValue(O order);

	@Named("mapReferences")
	default List<R> mapReferences(List<RoadOrderReference> roadOrderReferences) {
		List<R> additionalReferences = new ArrayList<>();
		roadOrderReferences.forEach(roadOrderReference -> additionalReferences.add(mapAdditionalReference(roadOrderReference)));
		additionalReferences.removeIf(Objects::isNull);
		return additionalReferences;
	}

	default R mapAdditionalReference(RoadOrderReference roadOrderReference) {
		// Order number should not be part of additional references
		if (ReferenceType.ORDER_NUMBER == roadOrderReference.getReferenceType()) {
			return null;
		}
		String code = roadOrderReference.getReferenceType().getCoreSystemsValue();
		String value = roadOrderReference.getReference();
		if (RoadOrderReferenceSubtype.UIT == roadOrderReference.getReferenceSubtype()) {
			value = OrderReferenceHelper.dipPrefix(roadOrderReference.getReferenceSubtype()).concat(roadOrderReference.getReference());
		}
		return createAdditionalReference(code, value);
	}

	R createAdditionalReference(String code, String value);

	@Named("customShipmentAddressMapping")
	default List<A> customMapShipmentAddresses(O order) {
		final List<A> shipmentAddresses = new ArrayList<>();
		// Add Shipper Address - Required
		shipmentAddresses.add(buildShipmentAddressForShipper(order, ADDRESS_TYPE_SHIPPER));
		// Add Consignee Address - Required
		A shipmentAddress = buildShipmentAddressFromOrder(order, ADDRESS_TYPE_CONSIGNEE);
		if (shipmentAddress != null) {
			shipmentAddresses.add(shipmentAddress);
		}
		// Add Forwarder Address - Required
		shipmentAddresses.add(buildShipmentAddressForForwarder(order.getBranchId(), ADDRESS_TYPE_FORWARDER));

		// Add all further addresses (if not empty)
		if (Objects.nonNull(order.getAddresses())) {
			List<OrderFurtherAddress> furtherAddresses = order.getAddresses().stream()
					.filter(address -> StringUtils.isNotBlank(address.getName()) || ADDRESS_TYPE_SHIPPER.equals(address.getAddressType()) || ADDRESS_TYPE_FORWARDER.equals(
							address.getAddressType())).toList();

			shipmentAddresses.addAll(mapShipmentAddresses(furtherAddresses));
		}

		return shipmentAddresses;
	}

	@Mapping(target = "addressType", source = "furtherAddress.addressType")
	@Mapping(target = "partnerInformation", source = "furtherAddress")
	A mapShipmentAddress(OrderFurtherAddress furtherAddress);

	List<A> mapShipmentAddresses(List<OrderFurtherAddress> orderAddresses);

	A buildShipmentAddressFromOrder(O order, String addressType);

	A buildShipmentAddressForForwarder(Integer branchId, String addressTypeForwarder);

	A buildShipmentAddressForShipper(O order, String addressTypeShipper);

}
