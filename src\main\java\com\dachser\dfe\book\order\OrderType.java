package com.dachser.dfe.book.order;

import com.dachser.dfe.book.model.Segment;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum OrderType {
	// @formatter:off
	ROADFORWARDINGORDER(3, "RoadForwardingOrder", Segment.ROAD),
	ROADCOLLECTIONORDER(2, "RoadCollectionOrder", Segment.ROAD),
	AIREXPORTORDER(5, "AirExportOrder", Segment.AIR),
	AIRIMPORTORDER(4, "AirImportOrder", Segment.AIR),
	SEAEXPORTORDER(1, "SeaExportOrder", Segment.SEA),
	SEAIMPORTORDER(6, "SeaImportOrder", Segment.SEA);
	// @formatter:on

	private final String name;

	private final int id;

	private final Segment segment;

	OrderType(int id, String name, Segment segment) {
		this.name = name;
		this.id = id;
		this.segment = segment;
	}

	public static OrderType getById(Integer orderTypeId) {
		return Arrays.stream(OrderType.values()).filter(Objects::nonNull).filter(orderTypeEnum -> orderTypeEnum.getId() == orderTypeId).findFirst().orElse(null);
	}

	public static OrderType getByName(String orderTypeName) {
		return Arrays.stream(OrderType.values()).filter(Objects::nonNull).filter(orderTypeEnum -> Objects.equals(orderTypeEnum.getName(), orderTypeName)).findFirst().orElse(null);
	}

	public static boolean isAirOrderType(OrderType orderType) {
		return orderType == AIREXPORTORDER || orderType == AIRIMPORTORDER;
	}

	public static boolean isSeaOrderType(OrderType orderType) {
		return orderType == SEAEXPORTORDER || orderType == SEAIMPORTORDER;
	}

	public static boolean isRoadOrderType(OrderType orderType) {
		return orderType == ROADCOLLECTIONORDER || orderType == ROADFORWARDINGORDER;
	}

	public boolean isOrderTypeSwitch(String jsonOrderType){
		return !this.equals(getByName(jsonOrderType));
	}
}
