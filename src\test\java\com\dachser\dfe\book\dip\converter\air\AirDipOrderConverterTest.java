package com.dachser.dfe.book.dip.converter.air;

import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.dip.converter.DipOrderConverter;
import com.dachser.dfe.book.dip.converter.shared.AirSeaDocumentHeaderMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.AirSeaGoodsDescriptionMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderContactCommunicationsMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.DateMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.StringNotEmptyConditionMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.dip.xml.XmlConverter;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.air.AirOrderLineHsCode;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.sea.BaseFullContainerLoadRepository;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
@SpringBootTest(properties = "dfe.book.mock=true", classes = { AirExportDipOrderConverter.class, AirImportDipOrderConverter.class, XmlConverter.class, AirOrderMapperImpl.class,
		DateMapperImpl.class, AirSeaDocumentHeaderMapperImpl.class, AirFreightMapperImpl.class, AirPickupOrderMapperImpl.class, AirSeaOrderContactCommunicationsMapperImpl.class,
		AirOrderLineMapperImpl.class, AirOrderReferenceMapperImpl.class, AirDeliveryOrderMapperImpl.class, StringNotEmptyConditionMapperImpl.class,
		AirSeaGoodsDescriptionMapperImpl.class })
class AirDipOrderConverterTest implements ResourceLoadingTest {

	@Autowired
	private AirExportDipOrderConverter airExportOrderConverter;

	@Autowired
	private AirImportDipOrderConverter airImportOrderConverter;

	@Autowired
	private XmlConverter xmlConverter;

	@MockBean
	private BaseFullContainerLoadRepository baseFullContainerLoadRepository;

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	@Nested
	class ConvertToXML {

		AirExportOrder airExportOrder = testUtil.generateAirExportOrder();

		AirImportOrder airImportOrder = testUtil.generateAirImportOrder();

		@Test
		void shouldConvertAirExportOrderToXML() throws IOException {
			setValidCollectionDates(airExportOrder);
			final String convertToXML = airExportOrderConverter.convertToXML(airExportOrder);
			String expectedXML = loadXMLResourceToString("orders/send/air/valid-generated-air-export-order.xml");
			assertEquals(expectedXML, convertToXML);
		}

		@Test
		void shouldConvertAirImportOrderToXML() throws IOException {
			setValidCollectionDates(airImportOrder);
			final String convertToXML = airImportOrderConverter.convertToXML(airImportOrder);
			String expectedXML = loadXMLResourceToString("orders/send/air/valid-generated-air-import-order.xml");
			assertEquals(expectedXML, convertToXML);
		}

		@Test
		void shouldConvertAirExportDeliveryOrderToXML() throws IOException {
			String expectedXML = loadXMLResourceToString("orders/send/air/valid-generated-air-export-delivery-order.xml");
			final AirExportOrder exportOrder = testUtil.generateAirExportOrder();
			appendHSCodeWithDescription(exportOrder);
			// Should use from iata for mapping but this field changes generate type to delivery order
			exportOrder.setDeliverToAirport(true);
			exportOrder.setShockSensitive(true);
			exportOrder.setStackable(false);
			setValidCollectionDates(exportOrder);

			final String convertToXML = airExportOrderConverter.convertToXML(exportOrder);
			assertEquals(expectedXML, convertToXML);
		}

		@Test
		void shouldConvertAirImportDeliveryOrderToXML() throws IOException {
			String expectedXML = loadXMLResourceToString("orders/send/air/valid-generated-air-import-delivery-order.xml");
			final AirImportOrder importOrder = testUtil.generateAirImportOrder();
			appendHSCodeWithDescription(importOrder);
			// Should use from iata for mapping but this field changes generate type to delivery order
			importOrder.setDeliverToAirport(true);
			importOrder.setShockSensitive(true);
			importOrder.setStackable(false);
			setValidCollectionDates(importOrder);

			final String convertToXML = airImportOrderConverter.convertToXML(importOrder);
			assertEquals(expectedXML, convertToXML);
		}

		@Test
		void shouldConvertAirExportDeliveryOrderWithoutDatesToXML() throws IOException {
			String expectedXML = loadXMLResourceToString("orders/send/air/valid-generated-air-export-delivery-order-request-arrangement.xml");
			final AirExportOrder exportOrder = testUtil.generateAirExportOrder();
			appendHSCodeWithDescription(exportOrder);
			exportOrder.setRequestArrangement(true);
			exportOrder.setDeliverToAirport(true);
			exportOrder.setCollectionDate(null);
			exportOrder.setCollectionFrom(null);
			exportOrder.setCollectionTo(null);
			// Should use from iata for mapping but this field changes generate type to delivery order

			final String convertToXML = airExportOrderConverter.convertToXML(exportOrder);
			assertEquals(expectedXML, convertToXML);
		}

		@Test
		void shouldConvertAirImportDeliveryOrderWithoutDatesToXML() throws IOException {
			String expectedXML = loadXMLResourceToString("orders/send/air/valid-generated-air-import-delivery-order-request-arrangement.xml");
			AirImportOrder importOrder = testUtil.generateAirImportOrder();
			appendHSCodeWithDescription(importOrder);
			importOrder.setRequestArrangement(true);
			importOrder.setDeliverToAirport(true);
			importOrder.setCollectionDate(null);
			importOrder.setCollectionFrom(null);
			importOrder.setCollectionTo(null);
			// Should use from iata for mapping but this field changes generate type to delivery order

			final String convertToXML = airImportOrderConverter.convertToXML(importOrder);
			assertEquals(expectedXML, convertToXML);
		}

		@Test
		void shouldConvertAirExportOrderWithAirProductsToXML() throws IOException {
			setValidCollectionDates(airExportOrder);
			airExportOrder.setProductCode(111);
			final String convertToXML = airExportOrderConverter.convertToXML(airExportOrder);
			String expectedXML = loadXMLResourceToString("orders/send/air/valid-generated-air-export-order-product.xml");
			assertEquals(expectedXML, convertToXML);
		}

		@Nested
		class AddressTests {
			@Test
			void useConsigneeAsDeliveryAddress() throws IOException, JAXBException {
				final ForwardingOrder forwardingOrder = loadValidBase();
				airExportOrder.setDeliveryAddress(null);
				airExportOrder.setStackable(false);
				setValidCollectionDates(airExportOrder);
				final ForwardingOrder.AirFreightShipment airFreightShipment = forwardingOrder.getAirFreightShipment().get(0);
				final Optional<ForwardingOrder.AirFreightShipment.ShipmentAddress> newShipmentList = airFreightShipment.getShipmentAddress().stream()
						.filter(address -> address.getTypeOfAddress().equals(Types.AddressTypes.ULTIMATE_CONSIGNEE)).findFirst();
				newShipmentList.ifPresent(shipmentAddress -> airFreightShipment.getShipmentAddress().remove(shipmentAddress));
				airFreightShipment.getPickupOrder().get(0).getOrderReference().stream()
						.filter(orderReference -> orderReference.getTypeOfReference().equals(AirSeaOrderReferenceType.SHIPPERS_REFERENCE.getEdiReferenceType()))
						.forEach(orderReference -> orderReference.setHandlingInstruction("NST"));

				assertByXMLComparison(forwardingOrder, airExportOrder);
			}

			private void assertByXMLComparison(ForwardingOrder forwardingOrder, AirExportOrder airExportOrder) {
				final String airExportBasedXML = airExportOrderConverter.convertToXML(airExportOrder);
				final String targetObjectSerialized = xmlConverter.convertToXml(forwardingOrder);
				assertEquals(targetObjectSerialized, airExportBasedXML);
			}

			private ForwardingOrder loadValidBase() throws JAXBException, IOException {
				JAXBContext jaxbContext = JAXBContext.newInstance(ForwardingOrder.class);
				Unmarshaller jaxbMarshaller = jaxbContext.createUnmarshaller();
				return (ForwardingOrder) jaxbMarshaller.unmarshal(new StringReader(loadXMLResourceToString("orders/send/air/valid-generated-air-export-order.xml")));
			}
		}
	}

	private static <T extends AirOrder> void appendHSCodeWithDescription(T airExportOrder) {
		final List<AirOrderLineHsCode> hsCodes = airExportOrder.getOrderLines().get(0).getHsCodes();

		for (int i = 0; i < 10; i++) {
			AirOrderLineHsCode airOrderLineHsCode = new AirOrderLineHsCode();
			airOrderLineHsCode.setHsCode("123456");
			airOrderLineHsCode.setGoods("Lorem ipsum text with some more chars (" + i + ") to come");
			hsCodes.add(airOrderLineHsCode);
		}
	}

	private static <T extends AirOrder> void setValidCollectionDates(T airOrder) {
		airOrder.setCollectionDate(LocalDate.of(2022, 9, 1).atStartOfDay().toLocalDate());
		airOrder.setCollectionFrom(OffsetDateTime.of(2022, 9, 1, 10, 0, 0, 0, ZoneOffset.UTC));
		airOrder.setCollectionTo(OffsetDateTime.of(2022, 9, 1, 15, 0, 0, 0, ZoneOffset.UTC));
	}

	@Nested
	class Supports {

		@Test
		void shouldSupportAirExportOrder() {
			final boolean supports = airExportOrderConverter.supports(new AirExportOrder());
			assertTrue(supports);
		}

		@Test
		void shouldSupportAirImportOrder() {
			final boolean supports = airImportOrderConverter.supports(new AirImportOrder());
			assertTrue(supports);
		}

		@Test
		void shouldNotSupportForwardingOrder() {
			final DipOrderConverter airOrderConverter1 = airExportOrderConverter;
			final boolean supports = airOrderConverter1.supports(new com.dachser.dfe.book.order.road.ForwardingOrder());
			assertFalse(supports);
		}
	}

	@Nested
	class OrderPosition {

		@Test
		void shouldSkipZeroValuesForOrderPositionAndDimension() {
			final AirExportOrder airExportOrder = testUtil.generateAirExportOrder();

			airExportOrder.getOrderLines().get(0).setLength(0);
			airExportOrder.getOrderLines().get(0).setWidth(0);
			airExportOrder.getOrderLines().get(0).setHeight(0);
			airExportOrder.getOrderLines().get(0).setWeight(BigDecimal.valueOf(0L));
			airExportOrder.getOrderLines().get(0).setVolume(BigDecimal.valueOf(0));
			airExportOrder.getOrderLines().get(0).setQuantity(0);

			final String convertToXML = airExportOrderConverter.convertToXML(airExportOrder);

			assertFalse(convertToXML.contains("<Length>"));
			assertFalse(convertToXML.contains("<Width>"));
			assertFalse(convertToXML.contains("<Height>"));
			assertFalse(convertToXML.contains("<GrossWeight"));
			assertFalse(convertToXML.contains("<Volume"));
			assertFalse(convertToXML.contains("<Pieces>"));
		}
	}

	@Nested
	class ShipperInformation {

		@Test
		void shouldMapAirShipperAddressIfCustNoMissing() {
			final AirImportOrder airImportOrder = testUtil.generateAirImportOrder();

			airImportOrder.getShipperAddress().setCustomerNumber(null);

			airImportOrder.getShipperAddress().setName("Shipper Name");
			airImportOrder.getShipperAddress().setStreet("Shipper Street");
			airImportOrder.getShipperAddress().setPostcode("Shipper Zip");
			airImportOrder.getShipperAddress().setCity("Shipper City");
			airImportOrder.getShipperAddress().setCountryCode("DE");

			final String convertedXml = airImportOrderConverter.convertToXML(airImportOrder);

			assertTrue(convertedXml.contains("<ShipmentAddress TypeOfAddress=\"Consignor\">"));
			assertTrue(convertedXml.contains("<Name1>Shipper Name</Name1>"));
			assertTrue(convertedXml.contains("<Street1>Shipper Street</Street1>"));
			assertTrue(convertedXml.contains("<PostalCode>Shipper Zip</PostalCode>"));
			assertTrue(convertedXml.contains("<CityName>Shipper City</CityName>"));
			assertTrue(convertedXml.contains("<CountryCode>DE</CountryCode>"));
		}

		@Test
		void shouldMapAirShipperCustNo() {
			final AirImportOrder airImportOrder = testUtil.generateAirImportOrder();

			airImportOrder.getShipperAddress().setName("Shipper Name");

			final String convertedXml = airImportOrderConverter.convertToXML(airImportOrder);

			assertTrue(convertedXml.contains("<ShipmentAddress TypeOfAddress=\"Consignor\">"));
			assertTrue(convertedXml.contains("<AddressID>"));
			assertFalse(convertedXml.contains("<Name1>Shipper Name</Name1>"));
		}

	}

}