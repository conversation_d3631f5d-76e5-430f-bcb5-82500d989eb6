package com.dachser.dfe.book.product.road;

import com.dachser.dfe.book.SpringBasedLocalMockTest;
import com.dachser.dfe.book.jpa.entity.CustomsType;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.road.RoadOrder;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;

import java.util.List;

import static com.dachser.dfe.book.model.TestMockData.BUSINESS_DOMAIN;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBasedLocalMockTest
@TestPropertySource(properties = { "whitelist-config.delivery-products.road.food-logistics= E", "whitelist-config.delivery-products.road.european-logistics= E" })
public class RoadProductsServiceTest {

	private final Division transportDivision = Division.T;

	@Autowired
	private RoadProductsService service;

	@MockBean
	private FavoriteRoadProductsProvider favoriteRoadProductsProvider;

	@Nested
	class GetProductNames {

		@Nested
		class ValidRequests {

			@Test
			void receiveNonEmptyListTransportDivision() {
				final List<DeliveryProductDto> serviceResponse = service.getDeliveryProductsForDivision(transportDivision);
				assertNotNull(serviceResponse);
				assertFalse(serviceResponse.isEmpty());
				assertEquals(1, serviceResponse.size());
			}

			@Test
			void receiveNonEmptyListFoodDivision() {
				final List<DeliveryProductDto> serviceResponse = service.getDeliveryProductsForDivision(Division.F);
				assertNotNull(serviceResponse);
				assertFalse(serviceResponse.isEmpty());
				assertEquals(1, serviceResponse.size());
			}

			@Test
			void throwExceptionWhenWrongDivisionIsGiven() {
				assertThrows(IllegalArgumentException.class, () -> service.getDeliveryProductsForDivision(null));
			}

		}
	}

	@Nested
	class GetProductByKey {

		@Test
		void returnEmptyOptionalWhenKeyIsNull() {
			final var result = service.getProductByKey(null, transportDivision);
			assertTrue(result.isEmpty());
		}

		@Test
		void returnEmptyOptionalWhenProductNotFound() {
			final var result = service.getProductByKey("unknown", transportDivision);
			assertTrue(result.isEmpty());
		}

		@Test
		void returnProductWhenFound() {
			final var result = service.getProductByKey("E", transportDivision);
			assertTrue(result.isPresent());
			assertEquals("E", result.get().getCode());
		}
	}

	@Nested
	class ValidateProduct {

		@Test
		void returnTrueWhenProductIsValid() {
			final var result = service.validateProduct(createRoadOrder(), 1);
			assertTrue(result);
		}

		@Test
		void returnFalseWhenOrderIsNull() {
			final var result = service.validateProduct(null, 1);
			assertFalse(result);
		}

		@Test
		void returnFalseWhenProductIsNull() {
			RoadOrder roadOrder = createRoadOrder();
			roadOrder.setProduct(null);
			final var result = service.validateProduct(roadOrder, 1);
			assertFalse(result);
		}

		@Test
		void returnFalseWhenProductIsInvalid() {
			RoadOrder roadOrder = createRoadOrder();
			roadOrder.setProduct("INVALID");
			final var result = service.validateProduct(roadOrder, 1);
			assertFalse(result);
		}

		@Test
		void returnFalseWhenProductServiceIsNotAvailable() {
			final var result = service.validateProduct(createRoadOrder(), 0);
			assertFalse(result);
		}

		private RoadOrder createRoadOrder() {
			OrderAddress orderAddress = new OrderAddress();
			orderAddress.setPostcode("12345");
			orderAddress.setCountryCode("DE");

			RoadOrder roadOrder = new RoadOrder();
			roadOrder.setProduct("E");
			roadOrder.setDivision(transportDivision);
			roadOrder.setCustomsType(CustomsType.CUSTOMER);
			roadOrder.setConsigneeAddress(orderAddress);
			roadOrder.setShipperAddress(orderAddress);
			return roadOrder;
		}
	}

	@Nested
	class GetApplicableDeliveryProductsFavoritesForCustomer {

		@Test
		void returnFavorites() {
			when(favoriteRoadProductsProvider.determineFavoriteProductCodes(any())).thenReturn(List.of("E"));

			final var result = service.getOnlyApplicableDeliveryProductsFavoritesForCustomer("123", transportDivision, BUSINESS_DOMAIN, "DE", "12345", "DE", "12345");
			assertNotNull(result);
			assertFalse(result.getFavorites().isEmpty());
			assertTrue(result.getDeliveryProducts().isEmpty());
		}

		@Test
		void returnEmptyFavorites() {
			final var result = service.getOnlyApplicableDeliveryProductsFavoritesForCustomer("123", transportDivision, BUSINESS_DOMAIN, "DE", "12345", "DE", "12345");
			assertNotNull(result);
			assertTrue(result.getFavorites().isEmpty());
			assertFalse(result.getDeliveryProducts().isEmpty());
			assertTrue(result.getDeliveryProducts().stream().anyMatch(product -> product.getCode().equals("E")));
		}
	}
}
