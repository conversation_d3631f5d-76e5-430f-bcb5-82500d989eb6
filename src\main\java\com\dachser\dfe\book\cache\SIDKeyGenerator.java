package com.dachser.dfe.book.cache;

import com.dachser.dfe.platform.security.model.DfePrincipal;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.lang.NonNull;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.lang.reflect.Method;

public class SIDKeyGenerator implements KeyGenerator {

	@NonNull
	@Override
	public Object generate(@NonNull final Object target, @NonNull final Method method, @NonNull final Object... params) {
		final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		if (authentication != null) {
			var principal = authentication.getPrincipal();
			if (principal instanceof DfePrincipal dfePrincipal) {
				return dfePrincipal.getSessionId();
			}
		}
		throw new IllegalStateException("Unexpected token implementation found in the security context");
	}
}
