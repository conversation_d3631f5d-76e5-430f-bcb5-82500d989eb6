package com.dachser.dfe.book.dip.converter.road;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingDangerousGoodsMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingDocumentHeaderMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingOrderInformationMapper;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingOrderInformationMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingPackingPositionMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingShipmentAddressMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingShipmentLineMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingTransportMapper;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingTransportMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.DateMapperImpl;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.AdditionalReference;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.AddressInformation;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.COD;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ContactInformation;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.DocumentHeader;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ForwardingOrderInformation;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.GoodsValue;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.PackagingAids;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.PartnerInformation;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentAddress;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentHeader;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentLine;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.Transport;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderText;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = { ForwardingOrderInformationMapperImpl.class, ForwardingTransportMapperImpl.class, DateMapperImpl.class, ForwardingDocumentHeaderMapperImpl.class,
		ForwardingShipmentLineMapperImpl.class, ForwardingShipmentAddressMapperImpl.class, GeneralTransportDataMapperImpl.class, ForwardingPackingPositionMapperImpl.class,
		ForwardingDangerousGoodsMapperImpl.class })
public class ForwardingOrderInformationMapperTest {

	@Autowired
	private ForwardingOrderInformationMapper forwardingOrderInformationMapper;

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	@Nested
	class MapForwardingOrder {

		final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();

		@BeforeEach
		void setIDs() {
			for (PackingPosition packingPosition : forwardingOrder.getPackingPositions()) {
				packingPosition.setId(new Random().nextLong(3));
				packingPosition.getOrderLines().forEach(orderLines -> orderLines.setPackingPositionId(packingPosition.getId()));
			}
		}

		@Nested
		class ToJaxb {

			@Test
			void shouldMapRequiredFields() {
				forwardingOrder.setShipmentNumber(12700L);
				final ForwardingOrderInformation forwardingOrderInformation = forwardingOrderInformationMapper.map(forwardingOrder);

				final DocumentHeader documentHeader = forwardingOrderInformation.getDocumentHeader();
				assertNotNull(documentHeader);
				assertDocumentHeader(documentHeader);

				final Transport transport = forwardingOrderInformation.getTransport().get(0);
				assertEquals("", transport.getNumber());

				final List<ShipmentHeader> shipmentHeaderList = transport.getShipmentHeader();
				assertFalse(shipmentHeaderList.isEmpty());
				final ShipmentHeader shipmentHeader = shipmentHeaderList.get(0);
				assertNotNull(shipmentHeader);
				assertShipmentHeader(shipmentHeader);

				final List<ShipmentAddress> shipmentAddressList = shipmentHeader.getShipmentAddress();
				assertFalse(shipmentAddressList.isEmpty());
				// minimum 3 required addresses
				assertTrue(shipmentAddressList.size() >= 3);
				// SHIPPER - required
				assertNotNull(shipmentAddressList.get(0));
				assertValidShipperAddress(shipmentAddressList.get(0));
				// CONSIGNEE - required
				assertNotNull(shipmentAddressList.get(1));
				assertValidConsigneeAddress(shipmentAddressList.get(1));
				// FORWARDER - required
				assertNotNull(shipmentAddressList.get(2));
				assertValidForwarderAddress(shipmentAddressList.get(2));

				final ShipmentLine shipmentLine = shipmentHeader.getShipmentLine().get(0);
				// minimum 1 required shipment line
				assertNotNull(shipmentLine);
				assertShipmentLine(shipmentLine);

				// Order number not in additional reference
				assertEquals("55667788", shipmentHeader.getCustomerShipmentReference());
				assertFalse(shipmentHeader.getAdditionalReference().stream().anyMatch(ar -> ReferenceType.ORDER_NUMBER.getCoreSystemsValue().equals(ar.getCode())));
			}

			@Test
			void shouldBeNullSafe() {
				final ForwardingOrderInformation pojo = forwardingOrderInformationMapper.map(null);
				assertNull(pojo);
			}

			@Test
			void mapNotSetDates() {
				forwardingOrder.setShipmentNumber(12700L);
				forwardingOrder.setSendAt(null);
				forwardingOrder.setFixDate(null);
				final ForwardingOrderInformation forwardingOrderInformation = forwardingOrderInformationMapper.map(forwardingOrder);

				final DocumentHeader documentHeader = forwardingOrderInformation.getDocumentHeader();
				assertNotNull(documentHeader);
			}

			@Test
			void mapNotSetAddresses() {
				forwardingOrder.setShipmentNumber(12700L);
				forwardingOrder.setConsigneeAddress(null);
				forwardingOrder.setShipperAddress(null);
				forwardingOrder.setAddresses(null);
				final ForwardingOrderInformation forwardingOrderInformation = forwardingOrderInformationMapper.map(forwardingOrder);

				final DocumentHeader documentHeader = forwardingOrderInformation.getDocumentHeader();
				assertNotNull(documentHeader);
			}

			@Test
			void mapNotSetAddresseName2() {
				forwardingOrder.setShipmentNumber(12700L);
				forwardingOrder.setConsigneeAddress(null);
				final OrderAddress shipperAddress = new OrderAddress();
				shipperAddress.setName("name1");
				forwardingOrder.setShipperAddress(shipperAddress);
				forwardingOrder.setAddresses(null);
				final ForwardingOrderInformation forwardingOrderInformation = forwardingOrderInformationMapper.map(forwardingOrder);

				final DocumentHeader documentHeader = forwardingOrderInformation.getDocumentHeader();
				assertNotNull(documentHeader);
			}

			@Test
			void mapTexts() {
				forwardingOrder.setShipmentNumber(12700L);

				forwardingOrder.setOrderTexts(new ArrayList<>());

				OrderText information = new OrderText();
				information.setOrderTextId(1L);
				information.setTextType("SI");
				information.setText("Sonstige Informationen");

				OrderText instructions = new OrderText();
				instructions.setOrderTextId(1L);
				instructions.setTextType("ZU");
				instructions.setText("Zustellhinweise");

				OrderText invoice = new OrderText();
				invoice.setOrderTextId(1L);
				invoice.setTextType("RE");
				invoice.setText("Rechnung");

				forwardingOrder.getOrderTexts().add(information);
				forwardingOrder.getOrderTexts().add(instructions);
				forwardingOrder.getOrderTexts().add(invoice);

				final ForwardingOrderInformation forwardingOrderInformation = forwardingOrderInformationMapper.map(forwardingOrder);

				final DocumentHeader documentHeader = forwardingOrderInformation.getDocumentHeader();
				assertNotNull(documentHeader);
				assertNotNull(forwardingOrderInformation.getTransport().get(0).getShipmentHeader().get(0).getShipmentText());

				// we expect 5 texts. 3 Added and one for the shipment number and dropOffLocation
				assertEquals(5, forwardingOrderInformation.getTransport().get(0).getShipmentHeader().get(0).getShipmentText().size());
			}
		}

		@Test
		void testMapPackingPositions() {
			ForwardingOrder order = testUtil.generateForwardingOrder();
			order.setShipmentNumber(12700L);

			PackingPosition packingPosition = order.getPackingPositions().get(0);
			packingPosition.setId(8L);
			packingPosition.setQuantity(5);
			packingPosition.setPackagingType("Pallet");
			packingPosition.getOrderLines().get(0).setPackingPositionId(8L);
			order.setPackingPositions(List.of(packingPosition));

			final ForwardingOrderInformation forwardingOrderInformation = forwardingOrderInformationMapper.map(order);
			List<PackagingAids> packagingAids = forwardingOrderInformation.getTransport().stream().flatMap(transport -> transport.getShipmentHeader().stream())
					.flatMap(shipmentHeader -> shipmentHeader.getPackagingAids().stream()).toList();

			List<ShipmentLine> shipmentLines = forwardingOrderInformation.getTransport().stream().flatMap(transport -> transport.getShipmentHeader().stream())
					.flatMap(shipmentHeader -> shipmentHeader.getShipmentLine().stream()).toList();

			assertFalse(packagingAids.isEmpty());

			assertEquals(1, packagingAids.size());
			assertEquals(BigInteger.valueOf(1), packagingAids.get(0).getPackagingAidsPosition());
			assertEquals(BigInteger.valueOf(5), packagingAids.get(0).getPackagesQuantity());
			assertEquals("Pallet", packagingAids.get(0).getPackingType());

			assertEquals(2, shipmentLines.size());
			assertTrue(shipmentLines.stream().anyMatch(shipmentLine -> shipmentLine.getPackagingAidsPosition() != null));
			assertEquals(1, shipmentLines.stream().filter(shipmentLine -> shipmentLine.getPackagingAidsPosition() != null).count());
			assertEquals(BigInteger.valueOf(1),
					shipmentLines.stream().filter(shipmentLine -> shipmentLine.getPackagingAidsPosition() != null).findFirst().get().getPackagingAidsPosition());
		}

		@Test
		void shouldMapCashOnDelivery(){
			forwardingOrder.setCashOnDelivery(true);
			forwardingOrder.setCashOnDeliveryAmount(BigDecimal.TEN);

			ForwardingOrderInformation map = forwardingOrderInformationMapper.map(forwardingOrder);
			assertNotNull(map);
			COD cod = map.getTransport().getFirst().getShipmentHeader().getFirst().getCOD();
			assertNotNull(cod);
			assertEquals(BigDecimal.TEN.floatValue(), cod.getAmount());
			assertEquals("EUR", cod.getCurrency());
			assertEquals("02", cod.getCode());
		}

		@Test
		void shouldNotHaveCashOnDelivery(){
			ForwardingOrderInformation map = forwardingOrderInformationMapper.map(forwardingOrder);
			assertNotNull(map);
			COD cod = map.getTransport().getFirst().getShipmentHeader().getFirst().getCOD();
			assertNull(cod);
		}

	}

	private void assertPartnerInformation(ShipmentAddress shipmentAddress, String addressType) {
		assertNotNull(shipmentAddress.getAddressType());
		assertEquals(addressType, shipmentAddress.getAddressType());

		final PartnerInformation partnerInformation = shipmentAddress.getPartnerInformation();
		assertNotNull(partnerInformation);
	}

	private void assertValidForwarderAddress(ShipmentAddress shipmentAddress) {
		assertPartnerInformation(shipmentAddress, ForwardingTransportMapper.ADDRESS_TYPE_FORWARDER);
	}

	private void assertValidShipperAddress(ShipmentAddress shipmentAddress) {
		assertPartnerInformation(shipmentAddress, ForwardingTransportMapper.ADDRESS_TYPE_SHIPPER);
		assertEquals("99999993", shipmentAddress.getPartnerInformation().getPartnerID());
	}

	private void assertValidConsigneeAddress(ShipmentAddress shipmentAddress) {
		assertPartnerInformation(shipmentAddress, ForwardingTransportMapper.ADDRESS_TYPE_CONSIGNEE);
		assertShipmentAddress(shipmentAddress, "_consignee");
	}

	private void assertDocumentHeader(DocumentHeader documentHeader) {
		assertEquals("0", documentHeader.getDocumentID());
		assertEquals("4023083000008", documentHeader.getEDIReceiver().getPartnerInformation().getPartnerGLN().toString());
		assertEquals("99999993", documentHeader.getEDISender().getPartnerInformation().getPartnerID());
		assertEquals("1", documentHeader.getTestFlag());
	}

	private void assertShipmentHeader(ShipmentHeader shipmentHeader) {
		assertEquals("55667788", shipmentHeader.getCustomerShipmentReference());

		final List<AdditionalReference> additionalReference = shipmentHeader.getAdditionalReference();
		assertFalse(additionalReference.isEmpty());
		assertEquals(ReferenceType.PURCHASE_ORDER_NUMBER.getCoreSystemsValue(), additionalReference.get(0).getCode());
		assertEquals("47110815", additionalReference.get(0).getReference());

		assertEquals("N", shipmentHeader.getADRflag());
		assertEquals("Y", shipmentHeader.getConsigneeCollectionIndicator());
		assertEquals("N", shipmentHeader.getCustomsIndicator());
		assertEquals("Z", shipmentHeader.getDachserProduct());
		assertNotNull(shipmentHeader.getDeliveryDateFixed().getDate());
		assertEquals("T", shipmentHeader.getDivision());

		final GoodsValue goodsValue = shipmentHeader.getGoodsValue();
		assertEquals(99f, goodsValue.getAmount());
		assertEquals("EUR", goodsValue.getCurrency());

		assertEquals("A", shipmentHeader.getOrderGroup());
		assertEquals("031", shipmentHeader.getOriginalTerm());

		assertEquals("00340258761102639537", shipmentHeader.getPackageIdentification().get(0).getSSCCBarCode());

		assertNotNull(shipmentHeader.getShipmentText().get(0));
		assertEquals("ZU", shipmentHeader.getShipmentText().get(0).getTextType());
		assertEquals("Ein Zustelltext", shipmentHeader.getShipmentText().get(0).getText().get(0));

		assertEquals("Y", shipmentHeader.getTailLiftRequired());
	}

	private void assertShipmentAddress(ShipmentAddress shipmentAddress, String suffix) {
		final AddressInformation addressInformation = shipmentAddress.getPartnerInformation().getAddressInformation();
		assertNotNull(addressInformation);

		assertEquals("Newtown", addressInformation.getCity());
		assertEquals("DE", addressInformation.getCountryCode());
		assertEquals("88888", addressInformation.getPostalCode());
		assertEquals("Con Street 11", addressInformation.getStreet());
		assertEquals("supplement" + suffix, addressInformation.getSupplementInformation());

		final List<ContactInformation> contactInformationList = shipmentAddress.getPartnerInformation().getContactInformation();
		assertFalse(contactInformationList.isEmpty());

		final ContactInformation contactInformation = contactInformationList.get(0);
		assertNotNull(contactInformation);

		assertEquals("<EMAIL>", contactInformation.getContactEmail());
		assertEquals("0000777777778", contactInformation.getContactMobilePhoneNumber());
		assertEquals("Delivery Contact", contactInformation.getContactName());
		assertEquals("0000777777777", contactInformation.getContactPhoneNumber());

		assertEquals("9012345000004", shipmentAddress.getPartnerInformation().getPartnerGLN().toString());

		assertEquals("AP", shipmentAddress.getServiceContactType());
	}

	private void assertShipmentLine(ShipmentLine shipmentLine) {
		assertEquals("CAR SPARE", shipmentLine.getGoodsDescription());
		assertEquals("01", shipmentLine.getGoodsGroup());
		assertEquals(BigInteger.valueOf(3), shipmentLine.getGoodsGroupQuantity());

		assertEquals(50.0, shipmentLine.getMeasurements().getWeight().get(0).getMeasurement().getValue());
		assertEquals(2.40, shipmentLine.getMeasurements().getHeight().getMeasurement().getValue(), 0.001);
		assertEquals(3.60, shipmentLine.getMeasurements().getLoadingMeter().getMeasurement().getValue(), 0.01);
		assertEquals(1.20, shipmentLine.getMeasurements().getWidth().getMeasurement().getValue(), 0.001);
		assertEquals(2.60, shipmentLine.getMeasurements().getLength().getMeasurement().getValue(), 0.001);
		assertEquals(2.00, shipmentLine.getMeasurements().getVolume().getMeasurement().getValue(), 0.001);

		assertEquals(BigInteger.valueOf(2), shipmentLine.getPackagesQuantity());
		assertEquals("EU", shipmentLine.getPackingType());
	}

}
