package com.dachser.dfe.book.product.road.cache;

import com.dachser.dfe.book.exception.ErrorIdExternalServiceNotAvailableException;
import com.dachser.dfe.book.masterdata.road.cache.CachingShipperMasterdataControllerApi;
import com.dachser.dfe.road.masterdata.api.ShipperMasterdataControllerApi;
import com.dachser.dfe.road.masterdata.model.RMDShipperMasterdataDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpStatusCodeException;

import java.util.List;

import static com.dachser.dfe.book.exception.BookErrorId.ERR_RF_01;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CachingShipperMasterdataControllerApiTest {

	@Mock
	private ShipperMasterdataControllerApi shipperMasterdataControllerApi;

	@InjectMocks
	private CachingShipperMasterdataControllerApi cachingShipperMasterdataControllerApi;

	@Test
	void testGetShipperMasterdata_Success() {
		List<RMDShipperMasterdataDTO> expectedData = List.of(new RMDShipperMasterdataDTO());
		when(shipperMasterdataControllerApi.getShipperMasterdata(1L, 1)).thenReturn(expectedData);

		List<RMDShipperMasterdataDTO> result = cachingShipperMasterdataControllerApi.getShipperMasterdata(1L, 1);

		assertNotNull(result);
		assertEquals(expectedData, result);
	}

	@Test
	void testGetShipperMasterdata_NotFound() {
		HttpStatusCodeException notFoundException = mock(HttpStatusCodeException.class);
		when(notFoundException.getStatusCode()).thenReturn(HttpStatus.NOT_FOUND);
		when(shipperMasterdataControllerApi.getShipperMasterdata(1L, 1)).thenThrow(notFoundException);

		List<RMDShipperMasterdataDTO> result = cachingShipperMasterdataControllerApi.getShipperMasterdata(1L, 1);

		assertNotNull(result);
		assertTrue(result.isEmpty());
	}

	@Test
	void testGetShipperMasterdata_ServiceUnavailable() {
		HttpStatusCodeException serviceUnavailableException = mock(HttpStatusCodeException.class);
		when(serviceUnavailableException.getStatusCode()).thenReturn(HttpStatus.SERVICE_UNAVAILABLE);
		when(shipperMasterdataControllerApi.getShipperMasterdata(1L, 1)).thenThrow(serviceUnavailableException);

		ErrorIdExternalServiceNotAvailableException exception = assertThrows(ErrorIdExternalServiceNotAvailableException.class,
				() -> cachingShipperMasterdataControllerApi.getShipperMasterdata(1L, 1));

		assertEquals(ERR_RF_01, exception.getErrorId());
	}
}
