package com.dachser.dfe.book.order.validation.road;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.TestMockData;
import com.dachser.dfe.book.order.address.AddressTestHelper;
import com.dachser.dfe.book.order.address.OrderBaseAddressTestMapper;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import com.dachser.dfe.book.order.validation.Messages;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.HibernateValidator;
import org.hibernate.validator.HibernateValidatorConfiguration;
import org.hibernate.validator.cfg.ConstraintMapping;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mapstruct.factory.Mappers;

import java.util.Collections;
import java.util.Set;
import java.util.stream.Stream;

@Slf4j
public class UitValidatorTest {

	private static final TestUtil UTILS = TestUtil.createInstanceNonSpring();

	private static final OrderBaseAddressTestMapper ADDRESS_MAPPER = Mappers.getMapper(OrderBaseAddressTestMapper.class);

	private static HibernateValidatorConfiguration validationConfiguration;

	@BeforeAll
	public static void configure() {
		UTILS.orderGenerator.collectionOrderSupplier = CollectionOrderMock::new;
		validationConfiguration = Validation.byProvider(HibernateValidator.class).configure();
		validationConfiguration.addMapping(constraintMapping());
	}

	private static ConstraintMapping constraintMapping() {
		// @formatter:off
		ConstraintMapping constraintMapping = validationConfiguration.createConstraintMapping();
		constraintMapping
		.type(RoadOrder.class)
		.constraintDefinition(UitValid.class)
		.validatedBy(UitValidatorMock.class)
		.includeExistingValidators(false);
		return constraintMapping;
		// @formatter:on
	}

	@ParameterizedTest(name = "#{index} - {0} shall be {2}")
	@MethodSource("scenarios")
	void evaluates(String scenario, RoadOrder roadOrder, boolean valid, String i18nViolationKey) {
		// given a road order
		Assertions.assertNotNull(roadOrder);
		// when validating it
		Set<ConstraintViolation<RoadOrder>> violations;
		try (ValidatorFactory validatorFactory = validationConfiguration.buildValidatorFactory()) {
			Validator validator = validatorFactory.getValidator();
			violations = validator.validate(roadOrder);
		}
		// then validation evaluates as expected
		log.debug("violations are {}", violations);
		Assertions.assertEquals(valid, violations.isEmpty());
		// and expected failure message is present
		if (i18nViolationKey != null) {
			Assertions.assertTrue(violations.stream().map(ConstraintViolation::getMessageTemplate).anyMatch(i18nViolationKey::equals));
		}
	}

	private static Stream<Arguments> scenarios() {
		// @formatter:off
		return Stream.of(
			Arguments.of("order related to Romania having UIT code", orderRelatedToRomania(), true, null),
			Arguments.of("order unrelated to Romania having UIT code", orderUnrelatedToRomania(), false, Messages.NOT_ALLOWED_UIT)
		);
		// @formatter:on
	}

	private static CollectionOrder orderRelatedToRomania() {
		CollectionOrder order = orderUnrelatedToRomania();
		order.setPrincipalAddress(ADDRESS_MAPPER.mapAsOrderAddress(AddressTestHelper.addressInBucarest()));
		return order;
	}

	private static CollectionOrder orderUnrelatedToRomania() {
		CollectionOrder order = roadOrder();
		order.setPrincipalAddress(ADDRESS_MAPPER.mapAsOrderAddress(AddressTestHelper.addressInKempten()));
		order.setShipperAddress(ADDRESS_MAPPER.mapAsOrderAddress(AddressTestHelper.addressInMunich()));
		order.setConsigneeAddress(ADDRESS_MAPPER.mapAsOrderAddress(AddressTestHelper.addressInChanverrie()));
		order.setAddresses(Collections.emptyList());
		return order;
	}

	private static CollectionOrder roadOrder() {
		CollectionOrder order = UTILS.generateCollectionOrder();
		order.setDifferentConsigneeAddress(null);
		order.setStatus(OrderStatus.DRAFT);
		order.addOrderReference(roadOrderReference(ReferenceType.IDENTIFICATION_CODE_TRANSPORT, RoadOrderReferenceSubtype.UIT, TestMockData.UIT_REFERENCE_VALUE));
		return order;
	}

	private static RoadOrderReference roadOrderReference(ReferenceType type, RoadOrderReferenceSubtype subtype, String value) {
		// @formatter:off
		return RoadOrderReference.builder()
		.referenceType(type)
		.referenceSubtype(subtype)
		.reference(value)
		.build();
		// @formatter:on
	}

	@UitValid
	private static class CollectionOrderMock extends CollectionOrder {}

}