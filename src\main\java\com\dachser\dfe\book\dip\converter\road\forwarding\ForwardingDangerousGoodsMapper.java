package com.dachser.dfe.book.dip.converter.road.forwarding;

import com.dachser.dfe.book.dip.converter.road.DangerousGoodsMapper;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ADRWeight;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.DangerousGoods;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ForwardingDangerousGoodsMapper extends DangerousGoodsMapper<DangerousGoods, ADRWeight> {
}
