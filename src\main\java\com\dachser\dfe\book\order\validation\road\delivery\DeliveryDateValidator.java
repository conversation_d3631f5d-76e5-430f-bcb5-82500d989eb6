package com.dachser.dfe.book.order.validation.road.delivery;

import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.ValidationResultEntryDto;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.validation.Messages;
import com.dachser.dfe.book.order.validation.PayloadProvidingValidator;
import com.dachser.dfe.book.product.road.RoadProductsService;
import com.dachser.dfe.book.service.FixedDateValidationService;
import com.dachser.dfe.book.user.BusinessDomainProvider;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

@RequiredArgsConstructor
public class DeliveryDateValidator implements ConstraintValidator<DeliveryDateValid, RoadOrder>, PayloadProvidingValidator {

	private final RoadProductsService roadProductsService;

	private final FixedDateValidationService validationService;

	private final BusinessDomainProvider businessDomainProvider;

	private final Translator translator;

	@Override
	public boolean isValid(RoadOrder roadOrder, ConstraintValidatorContext context) {
		context.disableDefaultConstraintViolation();
		if (roadOrder.getProduct() != null) {
			final Optional<DeliveryProductDto> product = roadProductsService.getProductByKey(roadOrder.getProduct(), roadOrder.getDivision());
			if (product.isPresent() && getFixedDeliveryDate(product.get()) && roadOrder.getFixDate() == null) {
				context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode("fixDate").addConstraintViolation();
				return false;
			}
			final boolean valid = validationService.validateFixedDate(roadOrder, businessDomainProvider.getBusinessDomain());
			if (!valid) {
				createBuilderWithErrorType(context, translator.toLocale(Messages.INVALID_INPUT), ValidationResultEntryDto.ErrorTypeEnum.CONSTRAINT_VIOLATION).addPropertyNode(
						"fixDate").addConstraintViolation();
				return false;
			}
		}
		return true;
	}

	private static boolean getFixedDeliveryDate(DeliveryProductDto product) {
		final Boolean fixedDeliveryDate = product.getFixedDeliveryDate();
		if (fixedDeliveryDate == null) {
			return false;
		}
		return fixedDeliveryDate;
	}

}
