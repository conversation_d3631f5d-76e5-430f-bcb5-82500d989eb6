package com.dachser.dfe.book.config;

import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Profile("local")
@Configuration
public class JmxConfiguration {

	@Bean
	public HikariPoolMXBean pool(HikariDataSource dataSource) {
		return dataSource.getHikariPoolMXBean();
	}

}