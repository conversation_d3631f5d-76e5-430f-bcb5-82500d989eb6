FROM mcr.microsoft.com/mssql/server:2019-latest

ENV SA_PASSWORD=Passw0rd
ENV ACCEPT_EULA=Y
ENV MSSQL_DB=dfe-dev
ENV MSSQL_PASSWORD=Passw0rd
ENV MSSQL_USER=dfe_user

USER root

RUN apt-get update && \
    apt-get install -y curl apt-transport-https gnupg && \
    curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - && \
    curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list > /etc/apt/sources.list.d/mssql-release.list && \
    apt-get update && \
    ACCEPT_EULA=Y apt-get install -y msodbcsql17 mssql-tools unixodbc-dev && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

ENV PATH=$PATH:/opt/mssql-tools/bin

COPY docker-entrypoint.sh /usr/local/bin/
COPY init.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh /usr/local/bin/init.sh

EXPOSE 1433

USER mssql

ENTRYPOINT ["docker-entrypoint.sh"]