package com.dachser.dfe.book.order.emissionforecast;

import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderRepository;
import com.dachser.dfe.book.order.OrderRepositoryFacade;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.sea.SeaOrder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "false", matchIfMissing = true)
public class EmissionForecastScheduler {

	private final EmissionForecastService emissionForecastService;

	private final OrderRepositoryFacade orderRepositoryFacade;

	private final OrderRepository orderRepository;

	@Scheduled(cron = "${emissionForecast.scheduled.calculateEmission}") // run every 3 minutes
	@SchedulerLock(name = "processOrdersWithMissingEmissionForecast", lockAtLeastFor = "PT1M", lockAtMostFor = "PT2M")
	public void processOrdersWithMissingEmissionForecast() {
		List<RoadOrder> ordersToRetryRoad = orderRepositoryFacade.findAllWithEmissionForecastNullAndRetryCountBelow10Road();
		List<AirOrder> ordersToRetryAir = orderRepositoryFacade.findAllWithEmissionForecastNullAndRetryCountBelow10Air();
		List<SeaOrder> ordersToRetrySea = orderRepositoryFacade.findAllWithEmissionForecastNullAndRetryCountBelow10Sea();

		List<Order> allOrdersToRetry = new ArrayList<>();
		allOrdersToRetry.addAll(ordersToRetryRoad);
		allOrdersToRetry.addAll(ordersToRetryAir);
		allOrdersToRetry.addAll(ordersToRetrySea);

		if (allOrdersToRetry.isEmpty()) {
			log.info("No orders found with missing emission forecast and retry count below 10");
			return;
		}

		log.info("Processing emission forecast for {} orders", allOrdersToRetry.size());
		allOrdersToRetry.forEach(this::processSingleOrder);
	}

	private void processSingleOrder(Order order) {
		try {
			if (order.getEmissionForecast() == null) {
				Optional<Double> forecast = emissionForecastService.getEmissionForecastForOrder(order);
				if (forecast.isPresent()) {
					order.setEmissionForecast(forecast.get());
					log.info("Successfully calculated emission forecast for order {}", order.getOrderId());
				} else {
					order.setEmissionForecast(null);
					order.setEmissionForecastRetryCount(order.getEmissionForecastRetryCount() + 1);
					log.warn("Failed to calculate emission forecast for order {}. Retry count is now {}", order.getOrderId(), order.getEmissionForecastRetryCount());
				}
			}
		} catch (Exception e) {
			log.error("Unexpected error while processing emission forecast for order {}", order.getOrderId(), e);
			order.setEmissionForecastRetryCount(order.getEmissionForecastRetryCount() + 1);
		}
		orderRepository.save(order);
	}

}