package com.dachser.dfe.book.dip.converter.road.forwarding;

import com.dachser.dfe.book.model.jaxb.order.road.forwarding.AddressInformation;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ContactInformation;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.PartnerInformation;
import com.dachser.dfe.book.dip.converter.road.GeneralTransportDataMapper;
import com.dachser.dfe.book.dip.converter.road.RoadShipmentAddressMapper;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = GeneralTransportDataMapper.class, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface ForwardingShipmentAddressMapper extends RoadShipmentAddressMapper<PartnerInformation, AddressInformation, ContactInformation> {
}
