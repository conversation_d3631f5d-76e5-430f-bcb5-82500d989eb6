package com.dachser.dfe.book.config.term;

import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.model.FreightTermDto;
import com.dachser.dfe.book.model.IncoTermDto;
import com.dachser.dfe.book.model.TermDto;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class TermSorter {

	private final WhiteListFilterConfiguration whiteListFilterConfiguration;

	@Nonnull
	public List<FreightTermDto> sortFreightTerms(@Nonnull List<FreightTermDto> freightTerms) {
		return sortTerms(freightTerms, whiteListFilterConfiguration.getTerms().getFreight(), FreightTermDto::getDachserTermKey);
	}

	@Nonnull
	public List<TermDto> sortOverviewTerms(@Nonnull List<TermDto> termDtos) {
		return sortTerms(termDtos, whiteListFilterConfiguration.getTerms().getOverview(), TermDto::getDachserCode);
	}

	@Nonnull
	public List<IncoTermDto> sortIncoTerms(@Nonnull List<IncoTermDto> incoTermDtos) {
		return sortTerms(incoTermDtos, whiteListFilterConfiguration.getTerms().getInco(), IncoTermDto::getDachserCode);
	}

	private <T> List<T> sortTerms(List<T> termDtos, List<String> sortingKeys, Function<T, String> keyExtractor) {
		Map<String, T> mapOfTerms = termDtos.stream().filter(entry -> keyExtractor.apply(entry) != null).collect(Collectors.toMap(keyExtractor, Function.identity()));
		List<T> sortedTermList = new ArrayList<>();
		sortingKeys.forEach(term -> {
			if (mapOfTerms.containsKey(term)) {
				sortedTermList.add(mapOfTerms.get(term));
			}
		});
		return sortedTermList;
	}
}
