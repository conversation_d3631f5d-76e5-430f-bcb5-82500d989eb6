package com.dachser.dfe.book.user;

import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.CustomerSettingsDto;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.user.model.Address;
import com.dachser.dfe.book.user.model.AslCustomer;
import com.dachser.dfe.book.user.model.RoadCustomer;
import com.dachser.dfe.book.user.model.User;
import com.dachser.dfe.platform.model.PlatformRoadCustomerV5;
import com.dachser.dfe.platform.model.PlatformUserV5;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static com.dachser.dfe.book.user.UserServiceMock.buildOrderOptions;
import static com.dachser.dfe.book.user.platform.PlatformApiTest.buildPlatformUser;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class UserMapperTest {

	@InjectMocks
	private UserMapperImpl mapper;

	@Test
	void shouldMapUser() {
		PlatformUserV5 user = buildPlatformUser();
		User mapped = mapper.map(user);
		assertEquals("John", mapped.getFirstName());
		assertEquals("Doe", mapped.getLastName());
		assertEquals("John Doe", mapped.getName());
		assertEquals("<EMAIL>", mapped.getEmail());
		assertEquals("Example Company", mapped.getCompany().getName());

		assertNotNull(mapped.getCompany().getCustomersASL().getFirst());

		AslCustomer aslCustomer = mapped.getCompany().getCustomersASL().getFirst();

		assertEquals("Asl Customer", aslCustomer.getName());

		assertNotNull(mapped.getCompany().getCustomersROAD().getFirst());
		RoadCustomer roadCustomer = mapped.getCompany().getCustomersROAD().getFirst();

		assertEquals(6, roadCustomer.getBranchId());
		assertEquals("9876543", roadCustomer.getCustomerNumber());
		assertTrue(roadCustomer.getOrderOptions().getManualNumberSscc());
	}

	@Test
	void shouldMapOrderContactCorrectly() {
		PlatformUserV5 user = buildPlatformUser();
		user.setFirstName("Max");
		user.setLastName("Mustermann");
		User mappedUser = mapper.map(user);
		OrderContact mappedOrderContact = mapper.mapOrderContact(mappedUser);
		assertEquals("Max Mustermann", mappedOrderContact.getName());

		PlatformUserV5 userWithLongName = buildPlatformUser();
		userWithLongName.setFirstName("Alexander-Maximilian");
		userWithLongName.setLastName("von Mustermann der dritte");
		User mappedLongName = mapper.map(userWithLongName);
		OrderContact mappedOrderContactLongName = mapper.mapOrderContact(mappedLongName);
		assertEquals("von Mustermann der dritte", mappedOrderContactLongName.getName());
	}

	@Nested
	class MapOrderAddress {

		private User user;

		@BeforeEach
		void setUp() {
			user = mapper.map(buildPlatformUser());
		}

		@Test
		void shouldLimitName2IfNameIsTooLong() {
			Address address = Address.builder().name2("Name is too long containing (40) chars!!").name("Name 1 is okay").build();
			OrderAddress orderAddressDto = mapper.mapOrderAddress(address, user);
			assertEquals("Name is too long containing (40) chars!!", orderAddressDto.getName2());
		}

		@Test
		void shouldUseName3IfNameIsTooLong() {
			Address address = Address.builder().name3("Name is too long containing (40) chars!!").name1("Name 1 is okay").name("name").build();
			OrderAddress orderAddressDto = mapper.mapOrderAddress(address, user);
			assertEquals("Name is too long containing (40) chars!!", orderAddressDto.getName3());
		}

		@Test
		void shouldMapOrderAddress() {

			Address address = Address.builder().name("Name").name1("Name 1").name2("Name 2").name3("Name 3").build();
			OrderAddress mapped = mapper.mapOrderAddress(address, user);
			assertEquals("Name 1", mapped.getName());
			assertEquals("Name 2", mapped.getName2());
			assertEquals("Name 3", mapped.getName3());
		}

		@Test
		void shouldMapOrderAddressWithLongName() {
			Address address = Address.builder().name1("Name is too long containing (40) chars!!").build();
			OrderAddress mapped = mapper.mapOrderAddress(address, user);
			assertEquals("Name is too long containing (40) chars!!", mapped.getName());
		}

		@Test
		void shouldMapOrderAddressWithName1() {
			Address address = Address.builder().name("Name").name1("Name 1").build();
			OrderAddress mapped = mapper.mapOrderAddress(address, user);
			assertEquals("Name 1", mapped.getName());
		}

		@Test
		void shouldMapOrderAddressWithValidGLN() {
			final Address address = Address.builder().name("Blub").gln("0").build();
			OrderAddress mapped = mapper.mapOrderAddress(address, user);
			assertNull(mapped.getGln());

			final Address address2 = Address.builder().name("Blub").gln("").build();
			OrderAddress mapped2 = mapper.mapOrderAddress(address2, user);
			assertNull(mapped2.getGln());

		}
	}

	@ParameterizedTest
	@MethodSource("divisions")
	void shouldMapDivisions(PlatformRoadCustomerV5.BusinessLineEnum input, Division expected) {
		Division mapped = mapper.mapDivision(input);
		assertEquals(expected, mapped);
	}

	@ParameterizedTest
	@ValueSource(booleans = { true, false })
	void shouldMapCustomerOptions(Boolean input) {
		CustomerSettingsDto mapped = mapper.mapCustomerOptions(buildOrderOptions(input));
		assertEquals(input, mapped.getManualNumberOfLabels());
		assertEquals(input, mapped.getFrostProtection());
		assertEquals(input, mapped.getPalletLocation());
		assertEquals(input, mapped.getSelfCollection());
	}

	@ParameterizedTest
	@ValueSource(booleans = { true, false })
	void shouldMapCustomerOptionsAsl(Boolean input) {
		AslCustomer aslCustomer = new AslCustomer();
		aslCustomer.setAirProducts(input);
		aslCustomer.setOrderOptions(buildOrderOptions(input));
		CustomerSettingsDto mapped = mapper.mapCustomerOptions(aslCustomer);
		assertEquals(input, mapped.getAirProducts());
		assertEquals(input, mapped.getPalletLocation());
	}

	private static Stream<Arguments> divisions() {
		return Stream.of(Arguments.of(PlatformRoadCustomerV5.BusinessLineEnum.EL, Division.T), Arguments.of(PlatformRoadCustomerV5.BusinessLineEnum.FL, Division.F));
	}

	// End setup methods
}
