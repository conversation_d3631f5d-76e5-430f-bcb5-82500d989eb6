package com.dachser.dfe.book.order.emissionforecast;

import com.dachser.dfe.emissionforecast.api.ForecastApi;
import com.dachser.dfe.emissionforecast.support.ApiClient;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
@ConfigurationProperties(prefix = "com.dachser.dfe.emissionforecast.configuration")
@Getter
@Setter
public class EmissionForecastConfig {

	@Value("${dfe.emissionforecast.configuration.baseURL}")
	private String baseURL;

	@Value("${dfe.emissionforecast.configuration.retryCount:10}")
	private Integer retryCount;

	private String username;

	private String password;

	private final RestTemplate bookRestTemplate;

	@Autowired
	EmissionForecastConfig(@Qualifier("bookRestTemplateNoAuth") RestTemplate bookRestTemplateNoAuth) {
		this.bookRestTemplate = bookRestTemplateNoAuth;
	}

	@Bean
	ForecastApi forecastApi() {
		return new ForecastApi(emissionForecastApiClient());
	}

	private ApiClient emissionForecastApiClient() {
		final ApiClient apiClient = new ApiClient(bookRestTemplate);
		apiClient.setBasePath(baseURL);
		apiClient.setPassword(password);
		apiClient.setUsername(username);
		return apiClient;
	}
}