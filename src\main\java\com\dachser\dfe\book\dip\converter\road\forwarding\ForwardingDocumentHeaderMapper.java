package com.dachser.dfe.book.dip.converter.road.forwarding;

import com.dachser.dfe.book.model.jaxb.order.road.forwarding.DocumentHeader;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.EDIReceiver;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.EDISender;
import com.dachser.dfe.book.dip.converter.shared.DateMapper;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Value;

@Mapper(componentModel = "spring", uses = { ForwardingTransportMapper.class, DateMapper.class })
public abstract class ForwardingDocumentHeaderMapper {

	public static final String EDI_SERVICE_GLN = "4023083000008";

	@Value("${ums.dip.ediTestFlagRoad}")
	protected boolean ediTestFlagRoad;

	@Mapping(target = "testFlag", expression = "java( java.lang.Boolean.FALSE == ediTestFlagRoad ? \"0\" : \"1\" )")
	@Mapping(target = "documentDate.date", source = ".", qualifiedByName = "createTimeZonedToGermanyAsZuluTime")
	@Mapping(target = "EDISender", source = ".")
	@Mapping(target = "EDIReceiver", source = ".")
	@Mapping(target = "documentID", constant = "0")
	public abstract DocumentHeader map(ForwardingOrder order);

	@Mapping(target = "partnerInformation.partnerID", source = "order.customerNumber")
	public abstract EDISender mapSender(ForwardingOrder order);

	@Mapping(target = "partnerInformation.partnerGLN", constant = EDI_SERVICE_GLN)
	public abstract EDIReceiver mapReceiver(ForwardingOrder order);
}
