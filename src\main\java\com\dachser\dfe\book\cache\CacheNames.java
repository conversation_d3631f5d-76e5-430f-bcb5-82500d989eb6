package com.dachser.dfe.book.cache;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CacheNames {

	/* Road Masterdata */
	public static final String ROAD_DELIVERY_TERMS = "rmd-delivery-terms";

	public static final String ROAD_ORDER_GROUPS = "rmd-order-groups";

	public static final String ROAD_PACKAGE_NAMES = "rmd-package-names";

	public static final String ROAD_PRODUCT_GROUPS = "rmd-product-groups";

	public static final String ROAD_PRODUCT_NAMES = "rmd-product-names";

	public static final String ROAD_SERVICE_TYPES = "rmd-service-types";

	public static final String ROAD_VALID_TERMS = "rmd-valid-terms";

	public static final String ROAD_BRANCH_DATA = "rmd-branch-data";

	public static final String ROAD_ADDRESS_TYPES = "rmd-adress-types";

	public static final String ROAD_SHIPPER_DATA = "rmd-shipper-data";

	public static final String ROAD_FORWARDING_DOMAINS = "rmd-forwarding-domains";

	/* AirSea Masterdata */
	public static final String AIRSEA_ALL_AIRPORTS = "asmd-all-airports";

	public static final String AIRSEA_AIRPORTS_FOR_COUNTRY = "asmd-airports-for-country";

	public static final String AIRSEA_AIRPORT_ROUTINGS_FOR_AIRPORT_CODE = "asmd-airport-routings-for-airport-code";

	public static final String AIRSEA_AIRPORT_ROUTINGS_FOR_COUNTRY_POSTCODE = "asmd-airport-routings-for-country-postcode";

	public static final String AIRSEA_SEAPORT_ROUTINGS_FOR_COUNTRY_POSTCODE = "asmd-seaport-routings-for-country-postcode";

	public static final String AIRSEA_EMBARGOS = "asmd-embargos";

	public static final String AIRSEA_FIND_SEAPORTS = "asmd-find-seaports";

	public static final String AIRSEA_ALL_SEAPORTS = "asmd-all-seaports";

	public static final String AIRSEA_ALL_PACKAGE_TYPES = "asmd-all-package-types";

	public static final String AIRSEA_PACKAGE_TYPES_FOR_CODE = "asmd-package-types-for-code";

	public static final String AIRSEA_PACKAGE_TYPES_FOR_ID = "asmd-package-types-for-id";

	public static final String AIRSEA_PACKAGE_TYPES_FOR_LANGUAGE = "asmd-package-types-for-language";

	public static final String GENERAL_DATA_CONTAINER_TYPES = "gd-container-types";

	/* Shared Service Masterdata */

	public static final String MASTERDATA_COUNTRIES = "md-countries";

	public static final String MASTERDATA_CURRENCIES = "md-currencies";

	public static final String MASTERDATA_LOCATIONS = "md-locations";

	/* Platform */

	public static final String PLATFORM_USER = "platform-user";

	/* Labels */
	public static final String PRINT_LABELS = "print-labels";

	/* Order Overview */
	public static final String OVERVIEW_DOCUMENT_TYPES = "overview-document-types";

	public static final String OVERVIEW_PRODUCT_NAMES = "overview-product-names";

	public static final String OVERVIEW_AIR_PRODUCT_NAMES = "overview-air-product-names";

	/* General Data */
	public static final String GENERAL_DATA_TERMS = "general-data-terms";

	public static final String GENERAL_DATA_COUNTRIES = "general-data-countries";

	public static final String GENERAL_DATA_PACKAGING_AIR = "general-data-packaging-air";

	public static final String GENERAL_DATA_PACKAGING_ROAD = "general-data-packaging-road";

	public static final String GENERAL_DATA_DELIVERY_PRODUCTS_ROAD = "general-data-delivery-product-road";

	public static final String GENERAL_DATA_DELIVERY_PRODUCTS_AIR = "general-data-delivery-product-air";

	public static final String GENERAL_DATA_MASTERDATA_LANGUAGES = "general-data-masterdata-languages";

	public static List<String> getValues() {
		return Arrays.stream(CacheNames.class.getDeclaredFields()).map(field -> {
			try {
				return field.get(null).toString();
			} catch (IllegalAccessException ex) {
				return StringUtils.EMPTY;
			}
		}).filter(value -> !value.isBlank()).toList();
	}
}
