package com.dachser.dfe.book.product.air;

import com.dachser.dfe.book.generaldata.DeliveryProductMapper;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class AirProductService {

	private final GeneralDataService generalDataService;

	private final DeliveryProductMapper deliveryProductMapper;

	public List<DeliveryProductDto> getAirDeliveryProducts() {
		return deliveryProductMapper.mapAirProductListToDeliveryProductList(generalDataService.getAirProducts(false));
	}

	public List<AirProductDto> getAirProducts(boolean includeInactive) {
		return generalDataService.getAirProducts(includeInactive);
	}

	public Optional<AirProductDto> getAirProductByProductCode(String productCode) {
		return generalDataService.getAirProducts(true).stream().filter(product -> product.getCode().equals(productCode)).findFirst();
	}
}
