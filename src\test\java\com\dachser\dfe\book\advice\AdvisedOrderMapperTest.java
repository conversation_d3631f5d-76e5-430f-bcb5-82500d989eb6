package com.dachser.dfe.book.advice;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.common.dangerousgood.ADRDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGoodDataItem;
import com.dachser.dfe.book.order.common.dangerousgood.EQDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.LQDangerousGood;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.legacy.advice.bean.AdvisedOrder;
import com.dachser.dfe.legacy.advice.bean.OrderLine;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

import static com.dachser.dfe.book.TestUtil.generateRoadOrderLine;
import static com.dachser.dfe.book.advice.AdvisedOrderTestUtil.createValidAdvisedOrder;
import static com.dachser.dfe.book.advice.AdvisedOrderTestUtil.createValidForwardingOrderForAdvice;
import static com.dachser.dfe.book.advice.AdvisedOrderTestUtil.dangerousGoodAsADR;
import static com.dachser.dfe.book.advice.AdvisedOrderTestUtil.dangerousGoodAsEQ;
import static com.dachser.dfe.book.advice.AdvisedOrderTestUtil.dangerousGoodAsLQ;
import static com.dachser.dfe.book.advice.AdvisedOrderTestUtil.orderLine;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@Nested
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class AdvisedOrderMapperTest {
	final AdvisedOrderMapper mapper = new AdvisedOrderMapperImpl();

	final AdvisedOrder sampleAdvisedOrder = createValidAdvisedOrder();

	final ForwardingOrder forwardingOrder = createValidForwardingOrderForAdvice();

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	@BeforeAll
	void setUp() {
		mapper.ediTestFlagRoad = true;
	}

	@Nested
	class ValidMapping {

		AdvisedOrder mappedOrder = mapper.map(forwardingOrder);

		@Test
		void shouldMapRequiredFields() {
			assertNotNull(mappedOrder);
			assertEquals(sampleAdvisedOrder.getConsignmentNumber(), mappedOrder.getConsignmentNumber());
			assertEquals(sampleAdvisedOrder.getPrincipalNumber(), mappedOrder.getPrincipalNumber());
			assertEquals(sampleAdvisedOrder.getConsolidatorNumber(), mappedOrder.getConsolidatorNumber());
			assertEquals(sampleAdvisedOrder.getConsignorNumber(), mappedOrder.getConsignorNumber());
			assertEquals(sampleAdvisedOrder.getConsignee().getName1(), mappedOrder.getConsignee().getName1());
			assertEquals(sampleAdvisedOrder.getDivisionCode(), mappedOrder.getDivisionCode());
			assertEquals(sampleAdvisedOrder.getProductCode(), mappedOrder.getProductCode());
			assertEquals(sampleAdvisedOrder.getTermsCode(), mappedOrder.getTermsCode());
			assertEquals(sampleAdvisedOrder.getDate(), mappedOrder.getDate());
			assertEquals(sampleAdvisedOrder.getSsccCount(), mappedOrder.getSsccCount());
			assertFalse(mappedOrder.getOrderLines().isEmpty());
			assertEquals(sampleAdvisedOrder.getOrderLines().get(0).getId(), mappedOrder.getOrderLines().get(0).getId());
			assertEquals(sampleAdvisedOrder.getOrderLines().get(0).getPackaging(), mappedOrder.getOrderLines().get(0).getPackaging());
			assertEquals(sampleAdvisedOrder.getOrderLines().get(0).getQuantityOfPackages(), mappedOrder.getOrderLines().get(0).getQuantityOfPackages());
			assertEquals(sampleAdvisedOrder.getWeight(), mappedOrder.getWeight());
		}

		@Test
		void shouldMapSpecialFormattingCorrectly() {
			assertEquals(sampleAdvisedOrder.getOrderLines().get(0).getVolume(), mappedOrder.getOrderLines().get(0).getVolume());
			assertEquals(sampleAdvisedOrder.getOrderLines().get(0).getWeight(), mappedOrder.getOrderLines().get(0).getWeight());
			assertEquals(sampleAdvisedOrder.getOrderLines().get(0).getLength(), mappedOrder.getOrderLines().get(0).getLength());
			assertEquals(sampleAdvisedOrder.getOrderLines().get(0).getLoadingMeter(), mappedOrder.getOrderLines().get(0).getLoadingMeter());
			assertEquals(sampleAdvisedOrder.getConsignee().getCountryCode(), mappedOrder.getConsignee().getCountryCode());
			assertEquals(sampleAdvisedOrder.getCollectionDateTimeFrom(), mappedOrder.getCollectionDateTimeFrom());
			assertEquals(sampleAdvisedOrder.getInsuranceGoodsValue().getValue(), mappedOrder.getInsuranceGoodsValue().getValue());
			assertEquals(sampleAdvisedOrder.getDeliveryNotification().getCode(), mappedOrder.getDeliveryNotification().getCode());
			assertEquals(sampleAdvisedOrder.getDeliveryNotification().getMail(), mappedOrder.getDeliveryNotification().getMail());
			assertEquals(sampleAdvisedOrder.getDeliveryNotification().getMobilePhone(), mappedOrder.getDeliveryNotification().getMobilePhone());
			assertTrue(mappedOrder.getFurtherAddresses().containsKey("DA"));
			assertTrue(mappedOrder.getConsignee().getSupplement().length() >= 30);
		}

		@Test
		void shouldRemoveInsuranceGoodsValueIfValueIsNotPresent() {
			final ForwardingOrder order = testUtil.generateForwardingOrder();
			order.setGoodsValue(null);
			final AdvisedOrder map = mapper.map(order);
			assertNull(map.getInsuranceGoodsValue());
		}

		@Test
		void shouldRemoveGoodsGroupIfValuesNotPresent() {
			final ForwardingOrder order = testUtil.generateForwardingOrder();
			order.getOrderLines().get(0).setGoodsGroup(null);
			final AdvisedOrder map = mapper.map(order);
			assertNull(map.getOrderLines().get(0).getGoodsGroup());

			order.getOrderLines().get(0).setGoodsGroup("MC");
			order.getOrderLines().get(0).setGoodsGroupQuantity(null);

			final AdvisedOrder map2 = mapper.map(order);
			assertNull(map2.getOrderLines().get(0).getGoodsGroup());
		}

		@Test
		void shouldRemoveFurtherAddressesWhenAddressInformationIsMissing() {
			final ForwardingOrder order = testUtil.generateForwardingOrder();
			OrderFurtherAddress furtherAddress = new OrderFurtherAddress();
			furtherAddress.setName("");
			order.setAddresses(List.of(furtherAddress));
			final AdvisedOrder map = mapper.map(order);
			assertTrue(map.getFurtherAddresses().isEmpty());
		}

		@Test
		void shouldRemoveFurtherAddressesWhenAddressTypeIsNull() {
			final ForwardingOrder order = testUtil.generateForwardingOrder();
			OrderFurtherAddress furtherAddress = new OrderFurtherAddress();
			furtherAddress.setAddressType(null);
			order.setAddresses(List.of(furtherAddress));
			final AdvisedOrder map = mapper.map(order);
			assertTrue(map.getFurtherAddresses().isEmpty());
		}

		@Test
		void shouldSetTestBranchIfBookIsInTestMode() {
			final AdvisedOrder map = mapper.map(forwardingOrder);
			assertEquals(918, map.getConsolidatorNumber());
		}

		@Test
		void shouldNotMapDeliveryOptionWhenNotPresent() {
			final ForwardingOrder order = testUtil.generateForwardingOrder();

			order.setDeliveryOption(null);
			final AdvisedOrder map = mapper.map(order);
			assertNull(map.getDeliveryNotification());
		}

		@Test
		void shouldNotMapDeliverOptionWhenNoneIsSelected() {
			final ForwardingOrder order = testUtil.generateForwardingOrder();

			order.setDeliveryOption(DeliveryOptions.NO);
			final AdvisedOrder map = mapper.map(order);
			assertNull(map.getDeliveryNotification());
		}

		@Test
		void shouldNotMapDeliveryOptionWhenContactIsMissing() {
			final ForwardingOrder order = testUtil.generateForwardingOrder();

			order.setDeliveryOption(DeliveryOptions.AP);
			order.setDeliveryContact(null);
			final AdvisedOrder map2 = mapper.map(order);
			assertNull(map2.getDeliveryNotification());
		}

		@Test
		void shouldShortenPhoneNumberToMaxLength() {
			final ForwardingOrder order = testUtil.generateForwardingOrder();
			order.getDeliveryContact().setTelephone("+1234567890123456789012345678901234567890");
			final AdvisedOrder map = mapper.map(order);
			assertEquals(25, map.getDeliveryNotification().getPhone().length());
			assertTrue(map.getDeliveryNotification().getPhone().startsWith("+"));
		}

		@Test
		void shouldNotMapCollectionTimeFromWhenNotPresent() {
			final ForwardingOrder order = testUtil.generateForwardingOrder();
			order.setCollectionFrom(null);
			final AdvisedOrder map = mapper.map(order);
			assertNull(map.getCollectionDateTimeFrom());
		}

		@Test
		void shouldNotMapCollectionTimeToWhenNotPresent() {
			final ForwardingOrder order = testUtil.generateForwardingOrder();
			order.setCollectionTo(null);
			final AdvisedOrder map = mapper.map(order);
			assertNull(map.getCollectionDateTimeTo());
		}

		@Test
		void shouldMapNullWeightInSecondOrderLine() {
			final ForwardingOrder order = testUtil.generateForwardingOrder();
			final RoadOrderLine orderLine = generateRoadOrderLine();
			orderLine.setWeight(null);
			order.getOrderLines().add(orderLine);
			final AdvisedOrder map = mapper.map(order);
			assertEquals(0, map.getOrderLines().get(1).getWeight());
		}

		@Test
		void shouldMapPackingPosition() {
			assertFalse(mappedOrder.getPackingPositions().isEmpty());
			assertEquals(1, mappedOrder.getPackingPositions().size());
			assertEquals(1, mappedOrder.getPackingPositions().get(0).getId());
			assertNotEquals(mappedOrder.getPackingPositions().get(0).getId(), forwardingOrder.getPackingPositions().get(0).getId().intValue());
			assertEquals(sampleAdvisedOrder.getPackingPositions().get(0).getId(), mappedOrder.getPackingPositions().get(0).getId());
			assertEquals(sampleAdvisedOrder.getPackingPositions().get(0).getQuantity(), mappedOrder.getPackingPositions().get(0).getQuantity());
			assertEquals(sampleAdvisedOrder.getPackingPositions().get(0).getPackaging(), mappedOrder.getPackingPositions().get(0).getPackaging());
			assertEquals(sampleAdvisedOrder.getOrderLines().get(2).getPackingPositionId(), mappedOrder.getOrderLines().get(2).getPackingPositionId());
		}

		@Test
		void shouldMapDangerousGoods() {
			// given a forwarding road order
			ForwardingOrder order = createValidForwardingOrderForAdvice();
			// having an order line
			Integer orderLinePositionId = order.getOrderLines().size();
			Long orderLineId = orderLinePositionId.longValue();
			RoadOrderLine orderLine = orderLine(orderLineId);
			order.addOrderLine(orderLine);
			// that has some dangerous goods
			ADRDangerousGood dangerousGoodAsADR = dangerousGoodAsADR(100L, 1, "1987");
			LQDangerousGood dangerousGoodAsLQ = dangerousGoodAsLQ(200L, 2);
			EQDangerousGood dangerousGoodAsEQ = dangerousGoodAsEQ(300L, 3);
			DangerousGoodDataItem dangerousGoodDataItem = dangerousGoodAsADR.getDangerousGoodDataItem();
			dangerousGoodAsLQ.setDangerousGoodDataItem(dangerousGoodDataItem);
			assertNotNull(orderLine.getDangerousGoods());
			orderLine.getDangerousGoods().addAll(List.of(dangerousGoodAsADR, dangerousGoodAsLQ, dangerousGoodAsEQ));
			orderLine.getDangerousGoods().forEach(dangerousGood -> dangerousGood.setRoadOrderLine(orderLine));
			// when mapped to a legacy advised order
			AdvisedOrder advisedOrder = mapper.map(order);
			// then our order line is mapped
			assertNotNull(advisedOrder.getOrderLines());
			assertFalse(advisedOrder.getOrderLines().isEmpty());
			Optional<OrderLine> advisedOrderLine = advisedOrder.getOrderLines().stream().filter(line -> orderLinePositionId.equals(line.getId())).findFirst();
			assertTrue(advisedOrderLine.isPresent());
			// and dangerous goods are mapped too
			assertNotNull(advisedOrderLine.get().getDangerousGoods());
			assertFalse(advisedOrderLine.get().getDangerousGoods().isEmpty());
			assertEquals(orderLine.getDangerousGoods().size(), advisedOrderLine.get().getDangerousGoods().size());
			advisedOrderLine.get().getDangerousGoods().forEach(advisedDangerousGood -> {
				assertAdvisedDangerousGood(advisedDangerousGood);
				log.debug("advice {} dangerous good at position {} = {}", advisedDangerousGood.getType(), advisedDangerousGood.getId(), advisedDangerousGood);
			});
			// and ADR dangerous good is well mapped
			assertAdvisedDangerousGoodAsADR(dangerousGoodAsADR, advisedOrderLine.get().getDangerousGoods().get(dangerousGoodAsADR.getSortingPosition()-1));
			// and LQ dangerous good is well mapped
			assertAdvisedDangerousGoodAsLQ(dangerousGoodAsLQ, advisedOrderLine.get().getDangerousGoods().get(dangerousGoodAsLQ.getSortingPosition()-1));
			// and EQ dangerous good is well mapped
			assertAdvisedDangerousGoodAsEQ(dangerousGoodAsEQ, advisedOrderLine.get().getDangerousGoods().get(dangerousGoodAsEQ.getSortingPosition()-1));
			// and shared fields are well mapped for ADR + LQ dangerous goods
			assertDangerousGoodDataItem(dangerousGoodDataItem, advisedOrderLine.get().getDangerousGoods().get(dangerousGoodAsADR.getSortingPosition()-1));
			assertDangerousGoodDataItem(dangerousGoodDataItem, advisedOrderLine.get().getDangerousGoods().get(dangerousGoodAsLQ.getSortingPosition()-1));
		}

		private static void assertAdvisedDangerousGood(com.dachser.dfe.legacy.advice.bean.DangerousGood advisedDangerousGood) {
			assertNotNull(advisedDangerousGood);
			assertNotNull(advisedDangerousGood.getType());
			assertNotNull(advisedDangerousGood.getId());
			assertNotEquals(BigDecimal.ZERO.intValue(), advisedDangerousGood.getId());
		}

		private static void assertAdvisedDangerousGoodAsEQ(EQDangerousGood dangerousGood, com.dachser.dfe.legacy.advice.bean.DangerousGood advisedDangerousGood) {
			assertEquals(com.dachser.dfe.legacy.advice.bean.DangerousGoodType.EQ, advisedDangerousGood.getType());
			assertEquals(dangerousGood.getSortingPosition(), advisedDangerousGood.getId());
			assertEquals(dangerousGood.getNoOfPackages(), advisedDangerousGood.getQuantityOfPackages());
			assertEquals(dangerousGood.getPackagingKey(), advisedDangerousGood.getPackaging());
		}

		private static void assertAdvisedDangerousGoodAsLQ(LQDangerousGood dangerousGood, com.dachser.dfe.legacy.advice.bean.DangerousGood advisedDangerousGood) {
			assertEquals(com.dachser.dfe.legacy.advice.bean.DangerousGoodType.LQ, advisedDangerousGood.getType());
			assertEquals(dangerousGood.getSortingPosition(), advisedDangerousGood.getId());
			assertEquals(dangerousGood.getNos(), advisedDangerousGood.getNos());
			assertEquals(dangerousGood.getGrossMass().intValue(), advisedDangerousGood.getWeight().intValue());
		}

		private static void assertAdvisedDangerousGoodAsADR(ADRDangerousGood dangerousGood, com.dachser.dfe.legacy.advice.bean.DangerousGood advisedDangerousGood) {
			assertEquals(com.dachser.dfe.legacy.advice.bean.DangerousGoodType.ADR, advisedDangerousGood.getType());
			assertEquals(dangerousGood.getSortingPosition(), advisedDangerousGood.getId());
			assertEquals(dangerousGood.getNos(), advisedDangerousGood.getNos());
			assertEquals(dangerousGood.getNoOfPackages(), advisedDangerousGood.getQuantityOfPackages());
			assertEquals(dangerousGood.getPackagingKey(), advisedDangerousGood.getPackaging());
			assertEquals(dangerousGood.getEnvironmentallyHazardous(), advisedDangerousGood.getEnvironmentallyHazardous());
			assertEquals(dangerousGood.getGrossMass().intValue(), advisedDangerousGood.getWeight().intValue());
		}

		private static void assertDangerousGoodDataItem(DangerousGoodDataItem data, com.dachser.dfe.legacy.advice.bean.DangerousGood advisedDangerousGood) {
			assertEquals(data.getExternalDgmId(), advisedDangerousGood.getCustomerId());
			assertEquals(data.getDescription(), advisedDangerousGood.getDescription());
			assertEquals(data.getUnNumber(), advisedDangerousGood.getUnNumber());
			assertEquals(data.getPackingGroup(), advisedDangerousGood.getPackingGroup());
			assertEquals(data.getClassificationCode(), advisedDangerousGood.getClassificationCode());
			assertEquals(data.getMainDanger(), advisedDangerousGood.getMainDanger());
			assertNotNull(advisedDangerousGood.getSubsidiaryHazards());
			assertTrue(List.of(advisedDangerousGood.getSubsidiaryHazards()).containsAll(Stream.of(data.getSubsidiaryHazardOne(), data.getSubsidiaryHazardTwo(), data.getSubsidiaryHazardThree()).filter(Objects::nonNull).toList()));
		}

	}

	@Nested
	class InvalidMapping {

		@Test
		void shouldReturnNullForNullInput() {
			AdvisedOrder mappedNullOrder = mapper.map(null);
			assertNull(mappedNullOrder);
		}

		@Test
		void shouldHandleIncompleteOrderInput() {
			ForwardingOrder incompleteForwardingOrder = testUtil.generateForwardingOrder();
			incompleteForwardingOrder.setCustomerNumber(null);
			incompleteForwardingOrder.setBranchId(null);
			incompleteForwardingOrder.setProduct(null);
			incompleteForwardingOrder.setFreightTerm(null);
			incompleteForwardingOrder.setOrderLines(null);
			incompleteForwardingOrder.setDivision(null);
			incompleteForwardingOrder.setConsigneeAddress(null);
			AdvisedOrder mappedIncompleteOrder = mapper.map(incompleteForwardingOrder);
			assertNotNull(mappedIncompleteOrder);
			assertNull(mappedIncompleteOrder.getDivisionCode());
			assertNull(mappedIncompleteOrder.getConsignee());
		}
	}
}
