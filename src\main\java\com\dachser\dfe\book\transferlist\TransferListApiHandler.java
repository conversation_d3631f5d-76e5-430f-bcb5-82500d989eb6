package com.dachser.dfe.book.transferlist;

import com.dachser.dfe.book.api.TransferListApiDelegate;
import com.dachser.dfe.book.exception.ProblemUtils;
import com.dachser.dfe.book.exception.generic.NotFoundException;
import com.dachser.dfe.book.model.DocumentDownloadDto;
import com.dachser.dfe.book.model.TransferListFilterDto;
import com.dachser.dfe.book.model.TransferListPdfQueryObjectDto;
import com.dachser.dfe.book.model.TransferListQueryObjectDto;
import com.dachser.dfe.book.model.TransferListResponseDto;
import com.dachser.dfe.book.transferlist.exception.PdfTransferListNoDataException;
import com.dachser.dfe.book.transferlist.service.TransferListPdfService;
import com.dachser.dfe.book.transferlist.service.TransferListService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class TransferListApiHandler implements TransferListApiDelegate {

	private final TransferListService transferListService;

	private final TransferListPdfService transferListPdfService;

	@Override
	public ResponseEntity<TransferListResponseDto> getTransferList(TransferListQueryObjectDto queryObjectDto) {
		return ResponseEntity.ok(transferListService.getTransferList(queryObjectDto));
	}

	@Override
	public ResponseEntity<List<TransferListFilterDto>> getTransferListFilter() {
		return ResponseEntity.ok(transferListService.getTransferListFilter());
	}

	@Override
	public ResponseEntity<DocumentDownloadDto> getTransferListPdf(TransferListPdfQueryObjectDto pdfQueryObject) {
		try {
			return ResponseEntity.ok(transferListPdfService.getTransferListPdf(pdfQueryObject));
		} catch (PdfTransferListNoDataException e) {
			throw new NotFoundException(ProblemUtils.LABEL_TITLE_NF_DATA);
		}
	}
}