package com.dachser.dfe.book.order.generator;

import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.SourceOfOrder;
import com.dachser.dfe.book.order.OrderDefaults;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderBaseAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.common.OrderText;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaImportOrder;
import com.dachser.dfe.book.order.sea.SeaOrderLine;
import com.dachser.dfe.book.order.sea.SeaOrderLineHsCode;
import com.dachser.dfe.book.order.sea.SeaOrderReference;
import com.dachser.dfe.book.user.UserContextService;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

@Component
@RequiredArgsConstructor
public class OrderGenerator {

	static final String VALID_CUST_NO_ROAD = "00000001";

	static final String VALID_CUST_NO_ASL = "00000100";

	static final int BRANCH_ID = 10;

	private final UserContextService userContextService;

	public Supplier<CollectionOrder> collectionOrderSupplier = CollectionOrder::new;

	public SeaExportOrder generateSeaExportOrder() {
		final SeaExportOrder seaExportOrder = new SeaExportOrder();
		seaExportOrder.setDatasource(SourceOfOrder.BOOK);
		seaExportOrder.setCustomerNumber(VALID_CUST_NO_ASL);
		seaExportOrder.setBranchId(BRANCH_ID);
		seaExportOrder.setStatus(OrderStatus.DRAFT);
		seaExportOrder.setToPort("DEHAM");
		seaExportOrder.setFromPort("HKHKG");
		seaExportOrder.setSendAt(LocalDateTime.of(2022, 12, 10, 1, 0, 0).toInstant(ZoneOffset.UTC));
		generateSeaOrderReferences().forEach(seaExportOrder::addOrderReference);
		seaExportOrder.setTailLiftCollection(true);
		seaExportOrder.setGoodsValue(BigDecimal.valueOf(99));
		seaExportOrder.setCurrency("EUR");
		final SeaOrderLine orderLine = generateSeaOrderLine();
		seaExportOrder.addOrderLine(orderLine);
		seaExportOrder.setCollectionDate(LocalDate.now().plusDays(2));
		seaExportOrder.setCollectionFrom(OffsetDateTime.now().plusDays(2).plusHours(2));
		seaExportOrder.setCollectionTo(OffsetDateTime.now().plusDays(2).plusHours(6));
		seaExportOrder.setConsigneeAddress(generateOrderAddress("_consignee", "DE", true, null, true, "tax1234"));
		OrderAddress shipperAddress = generateOrderAddress("_shipper", "DE", false, "9999920");
		shipperAddress.setOrderContact(generateDefaultContact("shipper_contact"));
		seaExportOrder.setShipperAddress(shipperAddress);
		final OrderAddress deliveryAddress = generateOrderAddress("_delivery", "DE", false, null);
		deliveryAddress.setOrderContact(generateDefaultContact("delivery_contact"));
		seaExportOrder.setDeliveryAddress(deliveryAddress);
		seaExportOrder.setPickupAddress(generateOrderAddress("_pickup", "DE", false, null, true, "tax4567"));
		seaExportOrder.addAddress(generateOrderFurtherAddress("_notification", "DE", "N1"));
		generateOrderText().forEach(seaExportOrder::addOrderText);
		seaExportOrder.setShipperReference("shipper_reference123");
		seaExportOrder.addOrderReference(
				SeaOrderReference.builder().referenceType(AirSeaOrderReferenceType.SHIPPERS_REFERENCE).referenceValue("shipper_reference123").loading(false).unloading(true)
						.build());
		seaExportOrder.setIncoTerm("EXW");
		seaExportOrder.setShipmentNumber(4711L);
		OrderDefaults.fillCreationFieldsOfAllModels(seaExportOrder, userContextService);
		return seaExportOrder;
	}

	private OrderContact generateDefaultContact(String contactName) {
		return OrderContact.builder().name(contactName).email("email").mobile("12345").telephone("54321").fax("fax23").build();
	}

	public SeaImportOrder generateSeaImportOrder() {
		final SeaImportOrder seaImport = new SeaImportOrder();
		seaImport.setDatasource(SourceOfOrder.BOOK);
		seaImport.setCustomerNumber(VALID_CUST_NO_ASL);
		seaImport.setBranchId(BRANCH_ID);
		seaImport.setToPort("DEHAM");
		seaImport.setFromPort("HKHKG");
		seaImport.setSendAt(LocalDateTime.of(2022, 12, 10, 1, 0, 0).toInstant(ZoneOffset.UTC));
		generateSeaOrderReferences().forEach(seaImport::addOrderReference);
		seaImport.setTailLiftCollection(true);
		seaImport.setGoodsValue(BigDecimal.valueOf(99));
		seaImport.setCurrency("EUR");
		final SeaOrderLine orderLine = generateSeaOrderLine();
		seaImport.addOrderLine(orderLine);
		seaImport.setCollectionDate(LocalDate.now().plusDays(2));
		seaImport.setCollectionFrom(OffsetDateTime.now().plusDays(2).plusHours(2));
		seaImport.setCollectionTo(OffsetDateTime.now().plusDays(2).plusHours(6));
		seaImport.setConsigneeAddress(generateOrderAddress("_consignee", "DE", true, null, true, "tax1234"));
		OrderAddress shipperAddress = generateOrderAddress("_shipper", "DE", false, "9999920");
		shipperAddress.setOrderContact(generateDefaultContact("shipper_contact"));
		seaImport.setShipperAddress(shipperAddress);
		final OrderAddress deliveryAddress = generateOrderAddress("_delivery", "DE", false, null);
		deliveryAddress.setOrderContact(generateDefaultContact("delivery_contact"));
		seaImport.setDeliveryAddress(deliveryAddress);
		seaImport.setPickupAddress(generateOrderAddress("_pickup", "DE", false, null, true, "tax4567"));
		seaImport.addAddress(generateOrderFurtherAddress("_notification", "DE", "N1"));
		generateOrderText().forEach(seaImport::addOrderText);
		seaImport.setShipperReference("shipper_reference123");
		seaImport.addOrderReference(
				SeaOrderReference.builder().referenceType(AirSeaOrderReferenceType.SHIPPERS_REFERENCE).referenceValue("shipper_reference123").loading(false).unloading(true)
						.build());
		seaImport.setIncoTerm("EXW");
		seaImport.setShipmentNumber(4711L);
		OrderDefaults.fillCreationFieldsOfAllModels(seaImport, userContextService);
		return seaImport;
	}

	public static SeaOrderLine generateSeaOrderLine() {
		final SeaOrderLine orderLine = new SeaOrderLine();
		orderLine.setWeight(BigDecimal.valueOf(50L));
		orderLine.setHeight(240);
		orderLine.setWidth(120);
		orderLine.setLength(260);
		orderLine.setVolume(BigDecimal.valueOf(2L));
		orderLine.setPackagingType("EU");
		orderLine.setQuantity(2);
		orderLine.setNumber(1);
		orderLine.setMarkAndNumbers("markAndNumbers - text - content");
		final ArrayList<SeaOrderLineHsCode> hsCodes = new ArrayList<>();
		final SeaOrderLineHsCode seaOrderLineHsCode1 = new SeaOrderLineHsCode();
		seaOrderLineHsCode1.setHsCode("4564654");
		seaOrderLineHsCode1.setGoods("goods1");
		seaOrderLineHsCode1.setOrderLine(orderLine);
		hsCodes.add(seaOrderLineHsCode1);
		final SeaOrderLineHsCode seaOrderLineHsCode2 = new SeaOrderLineHsCode();
		seaOrderLineHsCode2.setHsCode("1234567");
		seaOrderLineHsCode2.setGoods("goods2");
		seaOrderLineHsCode2.setOrderLine(orderLine);
		hsCodes.add(seaOrderLineHsCode2);
		final SeaOrderLineHsCode seaOrderLineHsCode3 = new SeaOrderLineHsCode();
		seaOrderLineHsCode3.setHsCode(null);
		seaOrderLineHsCode3.setGoods("goods3");
		seaOrderLineHsCode3.setOrderLine(orderLine);
		hsCodes.add(seaOrderLineHsCode3);
		orderLine.setHsCodes(hsCodes);
		return orderLine;
	}

	private static List<OrderText> generateOrderText() {
		final OrderText orderText = new OrderText();
		orderText.setTextType("ZU");
		orderText.setText("Ein Zustelltext");
		return List.of(orderText);
	}

	public static List<SeaOrderReference> generateSeaOrderReferences() {
		final ArrayList<SeaOrderReference> seaOrderReferences = new ArrayList<>();
		seaOrderReferences.add(
				(SeaOrderReference) buildFurtherSeaReference(AirSeaOrderReferenceType.INVOICE_NUMBER, "FurtherRef1-InvoiceNumber").loading(true).loading(false).build());
		seaOrderReferences.add(buildFurtherSeaReference(AirSeaOrderReferenceType.PURCHASE_ORDER_NUMBER, "PurchaseOrderNo").build());
		seaOrderReferences.add((SeaOrderReference) buildFurtherSeaReference(AirSeaOrderReferenceType.CONSIGNEE_REFERENCE_NUMBER, "ConsigneeRefNo").unloading(false).build());
		seaOrderReferences.add(
				(SeaOrderReference) buildFurtherSeaReference(AirSeaOrderReferenceType.COMMERCIAL_INVOICE_NUMBER, "Commercial").unloading(false).loading(false).build());
		seaOrderReferences.add(buildFurtherSeaReference(AirSeaOrderReferenceType.CUSTOMS_DOC_NUMBER, "Customs doc").build());
		seaOrderReferences.add((SeaOrderReference) buildFurtherSeaReference(AirSeaOrderReferenceType.QUOTATION_REFERENCE, "QU127766").unloading(false).loading(false).build());
		seaOrderReferences.add(
				(SeaOrderReference) buildFurtherSeaReference(AirSeaOrderReferenceType.MARKS_AND_NUMBERS, "Marks and numbers").unloading(false).loading(false).build());
		seaOrderReferences.add(
				(SeaOrderReference) buildFurtherSeaReference(AirSeaOrderReferenceType.DELIVERY_NOTE_NUMBER, "Delivery note number").unloading(false).loading(false).build());
		seaOrderReferences.add(
				(SeaOrderReference) buildFurtherSeaReference(AirSeaOrderReferenceType.PACKAGING_LIST_NUMBER, "Packaging list").unloading(false).loading(false).build());
		seaOrderReferences.add(
				(SeaOrderReference) buildFurtherSeaReference(AirSeaOrderReferenceType.PROVIDER_SHIPMENT_NUMBER, "Provider shipment").unloading(true).loading(false).build());
		seaOrderReferences.add(
				(SeaOrderReference) buildFurtherSeaReference(AirSeaOrderReferenceType.SUPPLIER_SHIPMENT_NUMBER, "Supplier shipment").unloading(false).loading(true).build());
		seaOrderReferences.add((SeaOrderReference) buildFurtherSeaReference(AirSeaOrderReferenceType.OTHERS, "Other reference").unloading(false).loading(false).build());
		return seaOrderReferences;
	}

	@NotNull
	private static SeaOrderReference.SeaOrderReferenceBuilder buildFurtherSeaReference(AirSeaOrderReferenceType type, String value) {
		final SeaOrderReference.SeaOrderReferenceBuilder e = SeaOrderReference.builder();
		e.referenceValue(value);
		e.referenceType(type);
		e.loading(true);
		e.unloading(true);
		return e;
	}

	public static OrderAddress generateOrderAddress(String suffix, String countryCode, boolean withContact, String customerNumber, boolean emptyThirdName, String taxID) {
		final OrderAddress address = new OrderAddress();
		fillOrderAddress(address, suffix, countryCode);
		if (emptyThirdName) {
			address.setName3("");
		}
		if (withContact) {
			fillOrderContact(address, suffix);
		}
		if (taxID != null) {
			address.setTaxID(taxID);
		}
		address.setCustomerNumber(customerNumber);
		return address;
	}

	public static OrderAddress generateOrderAddress(String suffix, String countryCode, boolean withContact, String customerNumber) {
		return generateOrderAddress(suffix, countryCode, withContact, customerNumber, false, null);
	}

	public static OrderFurtherAddress generateOrderFurtherAddress(String suffix, String countryCode, String type) {
		final OrderFurtherAddress furtherAddress = new OrderFurtherAddress();
		fillOrderAddress(furtherAddress, suffix, countryCode);
		fillOrderContact(furtherAddress, suffix);
		furtherAddress.setAddressType(type);
		return furtherAddress;
	}

	private static void fillOrderAddress(OrderBaseAddress orderAddress, String suffix, String countryCode) {
		orderAddress.setName("Example GmbH " + suffix);
		orderAddress.setName2("name2");
		orderAddress.setName3("name3");
		orderAddress.setCity("Newtown");
		orderAddress.setPostcode("88888");
		orderAddress.setCountryCode(countryCode);
		orderAddress.setGln("9012345000004");
		orderAddress.setStreet("Con Street 11");
		orderAddress.setSupplement("supplement" + suffix);
	}

	private static void fillOrderContact(OrderBaseAddress orderAddress, String suffix) {
		final OrderContact contact = new OrderContact();
		contact.setEmail("<EMAIL>");
		contact.setName("Consignora" + suffix);
		contact.setMobile("0000777777778");
		contact.setTelephone("0000777777777");
		orderAddress.setOrderContact(contact);
	}
}
