package com.dachser.dfe.book.mgmt.hikari;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/mgmt/hikari")
@Profile({ "apitest", "local", "dev", "test" })
public class HikariController {

	private final HikariService hikariService;

	@PostMapping("/minimum-idle")
	public ResponseEntity<String> setMinimumIdle(@RequestParam int minimumIdle) {
		hikariService.setMinimumIdle(minimumIdle);
		return ResponseEntity.ok("Minimum idle set to " + minimumIdle);
	}

	@PostMapping("/connection-timeout")
	public ResponseEntity<String> setConnectionTimeout(@RequestParam long timeoutMs) {
		hikariService.setConnectionTimeout(timeoutMs);
		return ResponseEntity.ok("Connection timeout set to " + timeoutMs + "ms");
	}

	@PostMapping("/leak-detection-threshold")
	public ResponseEntity<String> setLeakDetectionThreshold(@RequestParam long thresholdMs) {
		hikariService.setLeakDetectionThreshold(thresholdMs);
		return ResponseEntity.ok("Leak detection threshold set to " + thresholdMs + "ms");
	}

	@GetMapping("/minimum-idle")
	public ResponseEntity<String> getMinimumIdle() {
		return ResponseEntity.ok("Minimum idle is " + hikariService.getMinimumIdle());
	}

	@GetMapping("/connection-timeout")
	public ResponseEntity<String> getConnectionTimeout() {
		return ResponseEntity.ok("Connection timeout is " + hikariService.getConnectionTimeout() + "ms");
	}

	@GetMapping("/reset-connection-timeout")
	public ResponseEntity<String> resetConnectionTimeout() {
		hikariService.setConnectionTimeout(30000);
		return ResponseEntity.ok("Connection timeout reset to default (30000ms)");
	}

	@GetMapping("/leak-detection-threshold")
	public ResponseEntity<String> getLeakDetectionThreshold() {
		return ResponseEntity.ok("Leak detection threshold is " + hikariService.getLeakDetectionThreshold() + "ms");
	}

	@GetMapping("/reset-leak-detection-threshold")
	public ResponseEntity<String> resetLeakDetectionThreshold() {
		hikariService.setLeakDetectionThreshold(0);
		return ResponseEntity.ok("Leak detection threshold turned off");
	}

	@GetMapping("/metrics/pool")
	public ResponseEntity<HikariPoolMetrics> getPoolMetrics() {
		return ResponseEntity.ok(hikariService.getPoolMetrics());
	}

}
