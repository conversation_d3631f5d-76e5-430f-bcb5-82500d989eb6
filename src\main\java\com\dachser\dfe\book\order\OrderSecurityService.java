package com.dachser.dfe.book.order;

import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.BasicQuoteInformationDto;
import com.dachser.dfe.book.model.OrderRequestBodyDto;
import com.dachser.dfe.book.order.exception.OrderNotFoundException;
import com.dachser.dfe.book.user.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component("orderSecuritySecurityService")
@RequiredArgsConstructor
@Slf4j
public class OrderSecurityService {

	private final OrderRepository orderRepository;

	private final PermissionService permissionService;

	public boolean isOrderAccessValid(final Long orderId, final BasicOrderDto basicOrderDto, final OrderRequestBodyDto orderRequestBodyDto,
			final BasicQuoteInformationDto basicQuoteInformationDto) {
		Long orderIdToUse = resetNegativeOrderId(orderId);
		if (orderIdToUse == null && basicOrderDto == null && orderRequestBodyDto == null && basicQuoteInformationDto == null) {
			log.warn("OrderAccessValid: orderId, orderDto and orderRequestBodyDto and quoteInformationDto are null - access is not order based");
			return true;
		}
		Optional<String> customerNumber = Optional.empty();
		final BasicOrderDto basicOrder = Optional.ofNullable(orderRequestBodyDto).map(BasicOrderDto.class::cast).orElse(basicOrderDto);

		if (basicOrder != null) {
			customerNumber = Optional.of(basicOrder.getCustomerNumber());
			if (basicOrder.getOrderId() != null) {
				orderIdToUse = basicOrder.getOrderId();
			} else {
				final OrderType orderType = OrderType.getByName(basicOrder.getOrderType());
				return permissionService.isCustomerNumberValid(customerNumber.get(), orderType.getSegment());
			}
		}
		if (orderIdToUse != null) {
			final OrderTypeStatusAndOwner typeOwnerAndStatus = orderRepository.findTypeOwnerAndStatus(orderIdToUse);
			if (typeOwnerAndStatus == null) {
				log.warn("Could not check orderId {} will let request pass for later error handling", orderIdToUse);
				throw new OrderNotFoundException();
			}
			if (customerNumber.isPresent() && !customerNumber.get().equals(typeOwnerAndStatus.getCustomerNumber())) {
				log.info("Customer number switch for order id #{}: from {} to {}", orderIdToUse, typeOwnerAndStatus.getCustomerNumber(), customerNumber.get());
				return permissionService.isCustomerNumberValid(customerNumber.get(), typeOwnerAndStatus.getOrderType().getSegment()) && permissionService.isCustomerNumberValid(
						typeOwnerAndStatus.getCustomerNumber(), typeOwnerAndStatus.getOrderType().getSegment());
			}
			return permissionService.isCustomerNumberValid(typeOwnerAndStatus.getCustomerNumber(), typeOwnerAndStatus.getOrderType().getSegment());
		}
		if (basicQuoteInformationDto != null) {
			return permissionService.isCustomerNumberValid(basicQuoteInformationDto.getCustomerNumber(),
					OrderType.getByName(basicQuoteInformationDto.getOrderType().getValue()).getSegment());
		}
		log.warn("Could not resolve orderId ");
		return false;
	}

	private Long resetNegativeOrderId(final Long orderId) {
		if (orderId != null && orderId < 0) {
			return null;
		}
		return orderId;
	}
}
