package com.dachser.dfe.book.term;

import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.model.IncoTermDto;
import jakarta.annotation.Nullable;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

@Service
@AllArgsConstructor
public class IncoTermService {

	private GeneralDataService generalDataService;

	public IncoTermDto getIncoTermByCode(String code) {
		Optional<IncoTermDto> first = generalDataService.getIncoTerms().stream().filter(filterByIncoTerm(code)).findFirst();
		return first.orElseThrow();
	}

	public List<IncoTermDto> getAllActiveIncoTerms() {
		return generalDataService.getIncoTerms().stream().sorted(Comparator.comparing(IncoTermDto::getCode)).toList();
	}

	@Nullable
	public IncoTermDto getIncoTermByDachserCode(String code) {
		Optional<IncoTermDto> first = generalDataService.getIncoTerms().stream().filter(filterByDachserCode(code)).findFirst();
		return first.orElse(null);
	}

	// Is used within validation, not excluding it leads to persistence exceptions
	public boolean isIncoTermActiveByDachserCode(String code) {
		return getAllActiveIncoTerms().stream().anyMatch(filterByDachserCode(code));
	}

	private Predicate<IncoTermDto> filterByDachserCode(String dachserCode) {
		return term -> term.getDachserCode() != null && term.getDachserCode().equals(dachserCode);
	}

	private Predicate<IncoTermDto> filterByIncoTerm(String incoTerm) {
		return term -> term.getCode() != null && term.getCode().equals(incoTerm);
	}

}
