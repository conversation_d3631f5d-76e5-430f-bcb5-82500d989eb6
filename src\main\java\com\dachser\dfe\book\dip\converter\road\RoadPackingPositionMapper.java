package com.dachser.dfe.book.dip.converter.road;

import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.order.road.RoadOrder;
import org.mapstruct.Mapping;

import java.util.List;

public interface RoadPackingPositionMapper<M, P, O extends RoadOrder> {

	default List<P> mapPackingPositions(O order) {
		// map all packing positions that have order lines
		return order.getPackingPositions().stream().filter(packingPosition -> !packingPosition.getOrderLines().isEmpty()).map(this::mapPackingPosition).toList();
	}

	@Mapping(target = "packagingAidsPosition", source = "id")
	@Mapping(target = "packagesQuantity", source = "quantity")
	@Mapping(target = "packingType", source = "packagingType")
	P mapPackingPosition(PackingPosition packingPosition);

	void mapPackingPositionIds(M mappedOrder);
}
