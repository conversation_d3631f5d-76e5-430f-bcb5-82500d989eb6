package com.dachser.dfe.book.dip.converter.air;

import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.dip.converter.shared.AirSeaDocumentHeaderMapper;
import com.dachser.dfe.book.dip.converter.shared.DateMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.SubclassExhaustiveStrategy;

@Mapper(uses = { AirFreightMapper.class, DateMapper.class, AirSeaDocumentHeaderMapper.class }, componentModel = "spring", subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION)
@Named("AirOrderMapper")
public interface AirOrderMapper {

	@Mapping(target = "documentHeader", source = ".")
	@Mapping(target = "airFreightShipment", source = ".")
	com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder map(AirExportOrder order);

	@Mapping(target = "documentHeader", source = ".")
	@Mapping(target = "airFreightShipment", source = ".")
	com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder map(AirImportOrder order);
}
