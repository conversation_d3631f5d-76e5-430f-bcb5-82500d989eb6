package com.dachser.dfe.book.dataset;

import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.dataset.model.CountryDataset;
import com.dachser.dfe.book.dataset.model.ForwardingOrderDataset;
import com.dachser.dfe.book.dataset.model.IncoTermDataset;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.mapper.order.ConsignmentPrintBeanMapper;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.IncoTermDto;
import com.dachser.dfe.book.product.road.RoadProductsAdapter;
import com.dachser.dfe.book.term.IncoTermService;
import com.dachser.dfe.masterdata.geo.api.LocationsApi;
import com.dachser.dfe.masterdata.geo.model.GMDPostalCodeValidation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DatasetServiceTest {

	@Mock
	private LocationsApi locationsApi;

	@Mock
	private ConsignmentPrintBeanMapper consignmentPrintBeanMapper;

	@Mock
	private RoadProductsAdapter roadProductsAdapter;

	@Mock
	private IncoTermService incoTermService;

	@Mock
	private WhiteListFilterConfiguration whiteListFilterConfiguration;

	@Mock
	private CountryService countryService;

	@InjectMocks
	private DatasetService datasetService;

	@BeforeEach
	void setUp() {
		// Stub for CountriesApi used by enrichWithDachserCode
		when(countryService.mapToDachserCountryCode(anyString())).thenReturn("DACH123");

		// Stub for LocationsApi used by validateAndSetPostcode
		GMDPostalCodeValidation dummyZipValidation = new GMDPostalCodeValidation();
		dummyZipValidation.setExampleZipCode("10115");
		// Beispiel: in diesem Test setzen wir den ValidRegex auf true (oder einen echten boolean-Wert, falls so implementiert)
		dummyZipValidation.setValidRegex(true);
		when(locationsApi.validatePostalCode(anyString(), anyString())).thenReturn(dummyZipValidation);

		// Stub for ConsignmentPrintBeanMapper used to clean up post codes
		when(consignmentPrintBeanMapper.cleanPostCode(anyString())).thenAnswer(invocation -> {
			String postcode = invocation.getArgument(0, String.class);
			return postcode.trim();
		});

		// Standard-Stubs für RoadProductsAdapter:
		DeliveryProductDto productT = new DeliveryProductDto();
		productT.setCode("PT1");
		productT.setDescription("Product T");
		when(roadProductsAdapter.getOnlyValidDeliveryProducts(eq(Division.T), anyInt(), any(), anyString(), anyString(), anyString(), anyString())).thenReturn(List.of(productT));

		DeliveryProductDto productF = new DeliveryProductDto();
		productF.setCode("PF1");
		productF.setDescription("Product F");
		when(roadProductsAdapter.getOnlyValidDeliveryProducts(eq(Division.F), anyInt(), any(), anyString(), anyString(), anyString(), anyString())).thenReturn(List.of(productF));

		// Stub for IncoTermService used by getIncotermDatasets
		IncoTermDto dummyIncoTerm = new IncoTermDto("INCO1", "IncoTerm 1", "DACHTERM");
		when(incoTermService.getAllActiveIncoTerms()).thenReturn(List.of(dummyIncoTerm));

		// Stub for whiteListFilterConfiguration
		WhiteListFilterConfiguration.DeliveryProductFilter.Road roadConfig = new WhiteListFilterConfiguration.DeliveryProductFilter.Road();
		// Für den Test konfigurieren wir einfach leere Listen oder Dummy-Werte
		roadConfig.setEuropeanLogistics(new ArrayList<>());
		roadConfig.setFoodLogistics(new ArrayList<>());
		WhiteListFilterConfiguration.DeliveryProductFilter deliveryProductFilter = new WhiteListFilterConfiguration.DeliveryProductFilter();
		deliveryProductFilter.setRoad(roadConfig);
		when(whiteListFilterConfiguration.getDeliveryProducts()).thenReturn(deliveryProductFilter);
	}

	@Test
	void testGetForwardingOrderDataset_standard() {
		String consigneeCountryCode = "FR";
		String consigneePostalCode = "75001";
		String shipperCountryCode = "DE";
		String shipperPostalCode = "10115";

		ForwardingOrderDataset dataset = datasetService.getForwardingOrderDataset(consigneeCountryCode, consigneePostalCode, shipperCountryCode, shipperPostalCode);

		assertNotNull(dataset, "Dataset should not be null");
		assertFalse(dataset.getCountries().isEmpty(), "Countries should not be empty");
		assertFalse(dataset.getProducts().isEmpty(), "Products should not be empty");
		assertFalse(dataset.getIncoTerms().isEmpty(), "IncoTerms should not be empty");

		CountryDataset countryDataset = dataset.getCountries().getFirst();
		assertEquals("DACH123", countryDataset.getDachserCountryCode());
		assertEquals("AD500", countryDataset.getPostCodeFromDataset());
		assertEquals("AD500", countryDataset.getCleanedUpPostcode());
		assertTrue(countryDataset.getPostcodeValid());

		boolean productTExists = dataset.getProducts().stream().anyMatch(p -> "PT1".equals(p.getProductCode()));
		boolean productFExists = dataset.getProducts().stream().anyMatch(p -> "PF1".equals(p.getProductCode()));
		assertTrue(productTExists, "Product from Division T should exist");
		assertTrue(productFExists, "Product from Division F should exist");

		IncoTermDataset incoTerm = dataset.getIncoTerms().getFirst();
		assertEquals("IncoTerm 1", incoTerm.getDachserTerm());
		assertEquals("INCO1", incoTerm.getIncoTerm());
	}

	@Test
	void testGetForwardingOrderDataset_emptyIncoTerms() {
		// Stub IncoTermService to return an empty list.
		when(incoTermService.getAllActiveIncoTerms()).thenReturn(Collections.emptyList());

		String consigneeCountryCode = "FR";
		String consigneePostalCode = "75001";
		String shipperCountryCode = "DE";
		String shipperPostalCode = "10115";

		ForwardingOrderDataset dataset = datasetService.getForwardingOrderDataset(consigneeCountryCode, consigneePostalCode, shipperCountryCode, shipperPostalCode);

		// Expect incoTerms list to be empty, while countries and products are still present.
		assertNotNull(dataset, "Dataset should not be null");
		assertFalse(dataset.getCountries().isEmpty(), "Countries should not be empty");
		assertFalse(dataset.getProducts().isEmpty(), "Products should not be empty");
		assertTrue(dataset.getIncoTerms().isEmpty(), "IncoTerms should be empty");
	}

}