package com.dachser.dfe.book.country;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Optional;

@RequiredArgsConstructor
public enum CountryCode {

	// TODO: Refactor and extract to library - https://dil-itd.atlassian.net/browse/DFE-1355

	// DFE relevant Countries (WIP)
	AUSTRIA("A", "AT", " ", "de"), BELGIUM("B", "BE", "006", "nl"), CZECH_REPUBLIC("CZ", "CZ", "009", "cs"), DENMARK("DK", "DK", "007", "dk"), FINLAND("FIN", "FI", "021",
			"fi"), FRANCE("F", "FR", "002", "fr"), GERMANY("D", "DE", " ", "de"), HUNGARY("H", "HU", "008", "hu"), ITALY("I", "IT", "005", "it"), POLAND("PL", "PL", "011",
			"pl"), PORTUGAL("P", "PT", "004", "pt"), ROMANIA("RO", "RO", "018", "ro"), NETHERLANDS("NL", "NL", "006", "nl"), NORWAY("N", "NO", "013", "no"), SWEDEN("S", "SE",
			"012", "sv"), SWITZERLAND("CH", "CH", " ", "de"), SLOVENIA("SLO", "SI", "010", "sl"), SPAIN("E", "ES", "003", "es"),

	// Foreign languages pt, es, ro
	ARGENTINA("RA", "AR", "004", "pt"), BRASIL("BR", "BR", "004", "pt"), CHILE("RCH", "CL", "003", "es"), COLUMBIA("CO", "CO", "003", "es"), COSTA_RICA("CR", "CR", "003",
			"es"), CUBA("C", "CU", "003", "es"), MALTA("M", "MT", "003", "es"), MEXICO("MEX", "MX", "003", "es"), MOLDOVA("MD", "MD", "018", "ro"), PERU("PE", "PE", "003",
			"es"), VENEZUELA("YV", "VE", "003", "es"),

	// Default language en
	EGYPT("ET", "EG", "001", "en"), ALBANIA("AL", "AL", "001", "en"), ALGERIA("DZ", "DZ", "001", "en"), ANDORRA("AND", "AD", "001", "en"), ARMENIA("ARM", "AM", "001",
			"en"), AZERBAIJAN("AZ", "AZ", "001", "en"), AUSTRALIA("AUS", "AU", "001", "en"), BAHRAIN("BRN", "BH", "001", "en"), BOSNIA("BIH", "BA", "001", "en"), BULGARIA("BG",
			"BG", "001", "en"), BURUNDI("RU", "BI", "001", "en"), TAIWAN("RC", "TW", "001", "en"), CHINA("VRC", "CN", "001", "en"), ESTONIA("EST", "EE", "001", "en"), FAEROEER(
			"FO", "FO", "001", "en"), GEORGIA("GE", "GE", "001", "en"), GHANA("GH", "GH", "001", "en"), GREECE("GR", "GR", "001", "en"), GREENLAND("KN", "GL", "001", "en"), UNITED_KINGDOM("GB", "GB", "001",
			"en"), HONGKONG("HKG", "HK", "001", "en"), INDIA("IND", "IN", "001", "en"), INDONESIA("RI", "ID", "001", "en"), IRAQ("IRQ", "IQ", "001", "en"), IRAN("IR", "IR", "001",
			"en"), IRELAND("IRL", "IE", "001", "en"), ICELAND("IS", "IS", "001", "en"), ISRAEL("IL", "IL", "001", "en"), JAPAN("J", "JP", "001", "en"), JORDAN("JOR", "JO", "001",
			"en"), CANADA("CDN", "CA", "001", "en"), KAZAKHSTAN("KZ", "KZ", "001", "en"), QATAR("Q", "QA", "001", "en"), KENIA("EAK", "KE", "001", "en"), KIRGIZSTAN("KS", "KG",
			"001", "en"), KOREA("ROK", "KR", "001", "en"), KOSOVO("KOS", "XK", "001", "en"), CROATIA("HR", "HR", "001", "en"), KUWAIT("KWT", "KW", "001", "en"), LATVIA("LV",
			"LV", "001", "en"), LEBANON("RL", "LB", "001", "en"), LIBERIA("LB", "LR", "001", "en"), LYBIA("LAR", "LY", "001", "en"), LIECHTENSTEIN("FL", "LI", "001",
			"en"), LITHUANIA("LT", "LT", "001", "en"), LUXEMBOURG("L", "LU", "001", "en"), MALAYSIA("MAL", "MY", "001", "en"), MOROCCO("MA", "MA", "001", "en"), MARTINIQUE("MQ",
			"MQ", "001", "en"), MAURITANIA("RIM", "MR", "001", "en"), MACEDONIA("MK", "MK", "001", "en"), MONTENEGRO("MNE", "ME", "001", "en"), NAMIBIA("NAM", "NA", "001",
			"en"), NEW_ZEALAND("NZ", "NZ", "001", "en"), NIGERIA("WAN", "NG", "001", "en"), OMAN("OMN", "OM", "001", "en"), PAKISTAN("PAK", "PK", "001", "en"), SAN_MARINO("RSM",
			"SM", "001", "en"), SAUDI_ARABIA("SA", "SA", "001", "en"), SERBIA("SRB", "RS", "001", "en"), SINGAPORE("SGP", "SG", "001", "en"), SLOVAKIA("SK", "SK", "001",
			"en"), SOMALIA("SP", "SO", "001", "en"), SRI_LANKA("CL", "LK", "001", "en"), ST_BARTHELEMY("BL", "BL", "001", "en"), SOUTH_AFRICA("ZA", "ZA", "001", "en"), THAILAND(
			"THA", "TH", "001", "en"), TURKEY("TR", "TR", "001", "en"), TUNIS("TN", "TN", "001", "en"), TURKMENISTAN("TM", "TM", "001", "en"), USA("USA", "US", "001",
			"en"), UKRAINE("UA", "UA", "001", "en"), UZBEKISTAN("UZ", "UZ", "001", "en"), UNITED_ARAB_EMIRATES("UAE", "AE", "001", "en"), VIETNAM("VN", "VN", "001", "en"), CYPRUS(
			"CY", "CY", "001", "en"), VATICAN("V", "VA", "001", "en");

	@Getter
	private final String dachserCode;

	@Getter
	private final String isoCode;

	@Getter
	private final String dachserLanguageCode;

	@Getter
	private final String isoLanguageCode;

	public static Optional<CountryCode> getFromIsoCode(String isoCode) {
		return Arrays.stream(values()).filter(entry -> entry.isoCode.equals(isoCode)).findFirst();
	}

	public static Optional<CountryCode> getFromDachserCode(String dachserCode) {
		return Arrays.stream(values()).filter(entry -> entry.dachserCode.equals(dachserCode)).findFirst();
	}

}
