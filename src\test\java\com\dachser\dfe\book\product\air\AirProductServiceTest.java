package com.dachser.dfe.book.product.air;

import com.dachser.dfe.book.SpringBasedLocalMockTest;
import com.dachser.dfe.book.model.AirProductDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

@SpringBasedLocalMockTest
class AirProductServiceTest {

	@Autowired
	private AirProductService service;

	@Test
	void getAirProducts() {
		List<AirProductDto> airProducts = service.getAirProducts(false);
		assertNotNull(airProducts);
		assertTrue(airProducts.size() > 1);
		Optional<AirProductDto> no1 = airProducts.stream().filter(p -> p.getCode().equals("1")).findFirst();
		if (no1.isPresent()) {
			assertEquals("Fastest possible service - 1 to 3 days", no1.get().getHint());
			assertEquals("avigoexpress", no1.get().getDescription());
		} else {
			fail("Tx1 should be available");
		}
	}

	@Test
	void shouldHideNotActiveProducts() {
		List<AirProductDto> airProducts = service.getAirProducts(false);
		assertNotNull(airProducts);
		assertEquals(3, airProducts.size());
	}

	@Test
	void shouldFindAirProductByValidCode() {
		Optional<AirProductDto> optionalAirProduct = service.getAirProductByProductCode("1");
		assertTrue(optionalAirProduct.isPresent());
		assertEquals("avigoexpress", optionalAirProduct.get().getDescription());
	}

	@Test
	void shouldReturnEmptyOptionalForInvalidProductCode() {
		Optional<AirProductDto> optionalAirProduct = service.getAirProductByProductCode("500");
		assertFalse(optionalAirProduct.isPresent());
	}
}