package com.dachser.dfe.book.masterdata.geo;

import com.dachser.dfe.book.masterdata.MasterdataGatewayConfig;
import com.dachser.dfe.book.masterdata.geo.cache.CachingLocationsApi;
import com.dachser.dfe.masterdata.geo.ApiClient;
import com.dachser.dfe.masterdata.geo.api.LocationsApi;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
@Getter
@Setter
class MasterdataGatewayGeoApiGatewayConfig {

	private final MasterdataGatewayConfig masterdataGatewayConfig;

	private final RestTemplate bookRestTemplateNoAuth;

	@Autowired
	public MasterdataGatewayGeoApiGatewayConfig(@Qualifier("bookRestTemplateNoAuth") RestTemplate bookRestTemplateNoAuth, MasterdataGatewayConfig masterdataGatewayConfig) {
		this.bookRestTemplateNoAuth = bookRestTemplateNoAuth;
		this.masterdataGatewayConfig = masterdataGatewayConfig;
	}

	@Bean
	public LocationsApi locationsApi() {
		if (masterdataGatewayConfig.isUseCache()) {
			return new CachingLocationsApi(masterDataGeoApiClient());
		}
		return new LocationsApi(masterDataGeoApiClient());
	}

	private ApiClient masterDataGeoApiClient() {
		final ApiClient apiClient = new ApiClient(bookRestTemplateNoAuth);
		apiClient.setBasePath(masterdataGatewayConfig.getBaseURL());
		apiClient.setUsername(masterdataGatewayConfig.getUsername());
		apiClient.setPassword(masterdataGatewayConfig.getPassword());
		return apiClient;
	}
}
