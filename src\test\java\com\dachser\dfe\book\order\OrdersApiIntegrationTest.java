package com.dachser.dfe.book.order;

import com.dachser.dfe.book.BaseWebClientTest;
import com.dachser.dfe.book.OrderTestPayloadBuilder;
import com.dachser.dfe.book.country.CountryCode;
import com.dachser.dfe.book.model.TestMockData;
import com.dachser.dfe.book.order.address.AddressTestHelper;
import com.dachser.dfe.book.order.common.OrderBaseAddress;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import lombok.extern.slf4j.Slf4j;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.EntityExchangeResult;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

import static com.dachser.dfe.book.MockConstants.ACTIVE_COMPANY_ACCESS_TOKEN;

@Slf4j
class OrdersApiIntegrationTest extends BaseWebClientTest {

	// @formatter:off
	private static final Map<CountryCode, OrderBaseAddress> ADDRESSES =  Map.of(
		CountryCode.GERMANY, AddressTestHelper.addressInKempten(),
		CountryCode.ROMANIA, AddressTestHelper.addressInBucarest()
	);
	// @formatter:on

	@Value("classpath:/orders/new-forwarding-order-having-reference.json")
	private Resource orderHavingReferencePayload;

	@ParameterizedTest(name = "#{index} - order for {0} having reference {1} with value {2} can be saved")
	@MethodSource("scenarios")
	void orderHavingReference(String countryIsoCode, ReferenceType referenceType, String referenceValue) throws IOException {
		// @formatter:off
		// given a road order
		String payload = OrderTestPayloadBuilder.resourceBuilder(orderHavingReferencePayload)
		// having a legal shipment for a specific country
		.shipper(assertAddress(countryIsoCode))
		// and having a specific reference
		.reference(referenceType, referenceValue)
		.build();
		// when saving it
		EntityExchangeResult<byte[]> response = client
		.put()
		.uri("/orders/validate")
		.header(HttpHeaders.AUTHORIZATION, "Bearer ".concat(ACTIVE_COMPANY_ACCESS_TOKEN))
		.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
		.bodyValue(payload)
		.exchange()
		// then we obtain a JSON response
		.expectStatus().isOk()
		.expectHeader().contentType(MediaType.APPLICATION_JSON)
		.expectBody()
		// and no technical error
		.jsonPath("$.error").value(Matchers.nullValue())
		// and no validation errors
		.jsonPath("$.validationResult.valid").isEqualTo(true)
		// and an order storage identifier
		.jsonPath("$.order.orderId").isNumber()
		.jsonPath("$.order.orderId").value(Matchers.greaterThan(0))
		// and specific reference(s)
		.jsonPath("$.order.references").isArray()
		.jsonPath("$.order.references").isNotEmpty()
		.jsonPath("$.order.references[*].referenceType").value(Matchers.hasItem(referenceType.name()))
		.returnResult();
		// @formatter:on
		if (response.getResponseBodyContent() != null) {
			log.debug("order save - HTTP {} response = {}", response.getStatus(), new String(response.getResponseBodyContent(), StandardCharsets.UTF_8));
		}
	}

	private static Stream<Arguments> scenarios() {
		// @formatter:off
		return Stream.of(
			Arguments.of(CountryCode.GERMANY.getIsoCode(), ReferenceType.BOOKING_REFERENCE, "Amazon: my booking 1"),
			Arguments.of(CountryCode.ROMANIA.getIsoCode(), ReferenceType.IDENTIFICATION_CODE_TRANSPORT, OrderReferenceHelper.prefix(RoadOrderReferenceSubtype.UIT).concat(TestMockData.UIT_REFERENCE_VALUE)),
			Arguments.of(CountryCode.ROMANIA.getIsoCode(), ReferenceType.IDENTIFICATION_CODE_TRANSPORT, OrderReferenceHelper.prefix(RoadOrderReferenceSubtype.UIT).toLowerCase().concat(TestMockData.UIT_REFERENCE_VALUE))
		);
		// @formatter:on
	}

	private static OrderBaseAddress assertAddress(String countryIsoCode) {
		Optional<CountryCode> country = CountryCode.getFromIsoCode(countryIsoCode);
		Assertions.assertTrue(country.isPresent());
		Assertions.assertTrue(ADDRESSES.containsKey(country.get()));
		OrderBaseAddress address = ADDRESSES.get(country.get());
		Assertions.assertNotNull(address);
		return address;
	}

}