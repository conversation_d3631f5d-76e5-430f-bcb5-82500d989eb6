package com.dachser.dfe.book.order;

import com.dachser.dfe.book.jpa.entity.BaseEntity;
import com.dachser.dfe.book.jpa.entity.CustomsType;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.PreviousOrderStatusConverter;
import com.dachser.dfe.book.model.SourceOfOrder;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.common.OrderText;
import com.dachser.dfe.book.order.validation.CompleteOrderValidation;
import com.dachser.dfe.book.order.validation.air.CompleteOrderValidationAir;
import com.dachser.dfe.book.order.validation.common.ValidOrder;
import com.dachser.dfe.book.quote.QuoteInformation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.ColumnResult;
import jakarta.persistence.ConstructorResult;
import jakarta.persistence.Convert;
import jakarta.persistence.DiscriminatorColumn;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.NamedNativeQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SqlResultSetMapping;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Data
@Inheritance(strategy = InheritanceType.JOINED)
@DiscriminatorColumn(name = "order_type")
@Table(name = "order_base")
@NamedNativeQuery(name = "Order.findTypeOwnerAndStatus", query = "SELECT customer_number as customerNumber, status as status, order_type as orderType FROM order_base WHERE order_id = :orderId", resultSetMapping = "order_status_owner_mapping")
@SqlResultSetMapping(name = "order_status_owner_mapping", classes = @ConstructorResult(targetClass = OrderTypeStatusAndOwnerPojo.class, columns = {
		@ColumnResult(name = "customerNumber", type = String.class), @ColumnResult(name = "status", type = String.class),
		@ColumnResult(name = "orderType", type = Integer.class) }))
@EqualsAndHashCode(callSuper = true)
@ValidOrder(groups = { CompleteOrderValidation.class, CompleteOrderValidationAir.class })
public abstract class Order extends BaseEntity implements OrderTypeStatusAndOwner {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long orderId;

	@NotNull(groups = CompleteOrderValidation.class, message = "{label.text.invalid_input}")
	private Long shipmentNumber;

	@NotNull
	@Size(max = 8, min = 8)
	private String customerNumber;

	@NotNull
	private String customerNumberWithSegment;

	@NotNull
	// Also known as consolidator in eLogistics
	private Integer branchId;

	@Enumerated(EnumType.STRING)
	@NotNull
	private OrderStatus status;

	// Will be updated when setStatus is used to update the status
	private Integer statusSort;

	// Store the status of the order before deletion for rollback
	@Convert(converter = PreviousOrderStatusConverter.class)
	private OrderStatus previousStatus;

	private String sendUser;

	private Instant sendAt;

	private String shipmentTransferId;

	private SourceOfOrder datasource;

	@DecimalMin(value = "0.01")
	@DecimalMax(value = "9999999.99")
	private BigDecimal goodsValue;


	@Column(insertable = true, updatable = false)
	private OffsetDateTime orderExpiryDate;

	@JoinColumn(name = "quote_information_id")
	@OneToOne(cascade = CascadeType.ALL)
	private QuoteInformation quoteInformation;

	private OffsetDateTime collectionFrom;

	private OffsetDateTime collectionTo;

	// Is also used for delivery date at airport/port (handOverDate)
	@NotNull(groups = CompleteOrderValidation.class, message = "{label.text.invalid_input}")
	private LocalDate collectionDate;

	@Size(max = 3)
	private String currency;

	@JoinColumn(name = "consignee_address_id")
	@OneToOne(cascade = CascadeType.ALL)
	@Valid
	@NotNull(groups = CompleteOrderValidation.class, message = "{label.text.invalid_input}")
	private OrderAddress consigneeAddress;

	@JoinColumn(name = "shipper_address_id")
	@OneToOne(cascade = CascadeType.ALL)
	@Valid
	@NotNull(groups = CompleteOrderValidation.class, message = "{label.text.invalid_input}")
	private OrderAddress shipperAddress;

	@JoinColumn(name = "principal_address_id")
	@OneToOne(cascade = CascadeType.ALL)
	private OrderAddress principalAddress;

	@Enumerated(EnumType.STRING)
	private CustomsType customsType;

	@OneToMany(mappedBy = "order", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@ToString.Exclude
	@Valid
	private List<OrderFurtherAddress> addresses;

	@OneToMany(mappedBy = "order", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@ToString.Exclude
	@Valid
	private List<OrderText> orderTexts;

	private Double emissionForecast;

	private Integer emissionForecastRetryCount;

	@Transient
	private List<Long> documentIds;

	private boolean principalLocked;

	public void addAddress(final OrderFurtherAddress address) {
		if (addresses == null) {
			addresses = new ArrayList<>();
		}
		addresses.add(address);
		address.setOrder(this);
	}

	public void addOrderText(final OrderText orderText) {
		if (orderTexts == null) {
			orderTexts = new ArrayList<>();
		}
		orderTexts.add(orderText);
		orderText.setOrder(this);
	}

	public void validationSucceeded() {
		setStatus(OrderStatus.COMPLETE);
	}

	public OrderStatus validationSucceededStatus() {
		return OrderStatus.COMPLETE;
	}

	public boolean isSuccessfullyValidated() {
		return OrderStatus.COMPLETE == getStatus();
	}

	public boolean isOrderReadyForSubmission() {
		return OrderStatus.COMPLETE == getStatus();
	}

	public boolean isLabelPrintRequired() {
		return false;
	}

	public abstract OrderType getOrderType();

	public abstract Number getTotalOrderWeight();

	public abstract double getTotalOrderVolume();

	public abstract int getTotalAmountPackages();

	@Transient
	public boolean isOrderExpired() {
		return OrderStatus.EXPIRED == getStatus() || checkByTimestampIfExpired();
	}

	public void setStatus(OrderStatus status) {
		this.status = status;
		if (status != null) {
			setStatusSort(status.getSorting());
		}
	}

	public void setCustomerNumber(String customerNumber) {
		this.customerNumber = customerNumber;
		if (customerNumber != null && getOrderType() != null) {
			setCustomerNumberWithSegment(customerNumber + getOrderType().getSegment().getGroup());
		}
	}

	private boolean checkByTimestampIfExpired() {
		return orderExpiryDate != null && orderExpiryDate.isBefore(OffsetDateTime.now());
	}
}
