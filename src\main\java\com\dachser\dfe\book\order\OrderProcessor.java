package com.dachser.dfe.book.order;

import com.dachser.dfe.book.advice.AdviceService;
import com.dachser.dfe.book.dangerousgoods.DangerousGoodsTransferListService;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ErrorIdBaseException;
import com.dachser.dfe.book.exception.ErrorIdOrderExpiredException;
import com.dachser.dfe.book.exception.generic.BadRequestException;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.OrderActionWithoutValidationDto;
import com.dachser.dfe.book.model.OrderSaveActionDto;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.PrintLabelStartPosition;
import com.dachser.dfe.book.model.PrintLabelStartPositionDto;
import com.dachser.dfe.book.order.exception.MissingImplementationException;
import com.dachser.dfe.book.order.exception.OrderSaveProcessException;
import com.dachser.dfe.book.order.exception.OrderSubmissionFailedException;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import jakarta.transaction.Transactional;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Slf4j
abstract class OrderProcessor<O> {

	// -- state variables

	private final ThreadLocal<OrderProcessor<O>.ProcessState> state = new ThreadLocal<>();

	// ----

	final List<OrderSubmitter<?>> orderSubmitters;

	final AdviceService adviceService;

	final OrderRepositoryFacade orderRepository;

	final OrderMapper orderMapper;

	final OrderLabelGenerator orderLabelGenerator;

	final OrderLabelPrinter labelPrinter;

	final DangerousGoodsTransferListService dangerousGoodsTransferListService;

	final OrderDefaults orderDefaults;

	final List<OrderPostProcessor<?>> postProcessors;

	private void initState(InternalOrderProcessAction action) {
		state.set(new ProcessState(action));
	}

	@Transactional(Transactional.TxType.SUPPORTS)
	public PrintProcessResultDto process(O order, OrderSaveActionDto action, PrintLabelStartPositionDto startPosition) {
		return process(order, InternalOrderProcessAction.from(action), PrintLabelStartPosition.from(startPosition));
	}

	@Transactional(Transactional.TxType.SUPPORTS)
	public PrintProcessResultDto process(O order, OrderActionWithoutValidationDto action, PrintLabelStartPositionDto startPosition) {
		return process(order, InternalOrderProcessAction.from(action), PrintLabelStartPosition.from(startPosition));
	}

	private PrintProcessResultDto process(O order, InternalOrderProcessAction action, PrintLabelStartPosition startPosition) {
		initState(action);
		try {
			fillStateWithOrder(order);
			switch (action) {
			case VALIDATE -> {
				return getProcessResult();
			}
			case PRINT_LABELS -> {
				printLabelsOfForwardingOrder();
				return getProcessResult();
			}
			case SUBMIT -> {
				executeSubmit(startPosition);
				return getProcessResult();
			}
			default -> {
				log.warn("Unknown action: {} in Orderprocessor, cannot fulfill request", action);
				throw new IllegalArgumentException("Unknown action: " + action);
			}
			}
		} finally {
			removeCurrentState();
		}
	}

	/**
	 * Fill the state with the current order, but make sure it has a valid state before. If state is not valid do not hesitate to fail fast.
	 *
	 * @param order - input to resolve the order
	 */
	abstract void fillStateWithOrder(O order);

	ProcessState getState() {
		return state.get();
	}

	protected void setCurrentOrder(Order order) {
		getState().currentOrder = order;
	}

	protected void setOrderDto(BasicOrderDto orderDto) {
		getState().orderDto = orderDto;
	}

	protected PrintProcessResultDto getProcessResult() {
		return getState().getProcessResult();
	}

	protected void checkCurrentOrderForExpiredQuoteOrder() {
		Order order = getState().getCurrentOrder();
		InternalOrderProcessAction action = getOrderSaveAction();
		if (order.isOrderExpired()) {
			throw new ErrorIdOrderExpiredException(action.getBookErrorIdOrderExpired(),
					String.format("Order with id %d is expired. Action %s cannot be performed", order.getOrderId(), action));
		}
	}

	private void executeSubmit(PrintLabelStartPosition startPosition) {
		Order currentOrder = getCurrentOrder();
		OrderTransitions.ensureValidTargetStatus(OrderStatus.SENT, currentOrder);

		if (currentOrder.isLabelPrintRequired()) {
			printLabelsOfForwardingOrder();
			// In case of an submit we need to print immediately to avoid later errors during print
			getProcessResult().print(labelPrinter, startPosition);
		}

		final Optional<OrderSubmitter<Order>> orderSubmitter = getOrderSubmitter(getCurrentOrder());
		if (orderSubmitter.isPresent()) {
			try {
				orderSubmitter.get().submitOrder(getCurrentOrder());
			} catch (OrderSubmissionFailedException orderSaveFailedException) {
				handleSubmissionError(orderSaveFailedException);
			}
		} else {
			throw new MissingImplementationException("No submitter found for order " + getCurrentOrder().getOrderType());
		}

		doDangerousGoodCalculations(currentOrder);
		writeCurrentOrderToState();
	}

	private void doDangerousGoodCalculations(Order currentOrder) {
		if (currentOrder instanceof ForwardingOrder forwardingOrder) {
			try {
				dangerousGoodsTransferListService.calculateDangerousGoodsPoints(forwardingOrder);
			} catch (Exception e) {
				log.warn("Error while calculating dangerous goods points for order {}", forwardingOrder.getOrderId(), e);
				// We do not want to fail the submission if dangerous goods calculation fails
			}
		}
	}

	// Simple rethrow but can be overridden to change behaviour
	void handleSubmissionError(OrderSubmissionFailedException orderSaveFailedException) {
		throw orderSaveFailedException;
	}

	void handlePrintLabelError(Exception exception) {
		log.debug("Error while printing labels", exception);
		BookErrorId errorId = BookErrorId.ERR_LB_01;
		if (exception instanceof ErrorIdBaseException errorIdException) {
			errorId = errorIdException.getErrorId();
		} else if (exception instanceof BadRequestException bad) {
			throw bad;
		}
		throw new OrderSaveProcessException(exception.getMessage(), exception, getProcessResult().getValidationResult(), (BasicOrderDto) getProcessResult().getOrder(), errorId);
	}

	private void printLabelsOfForwardingOrder() {
		try {
			checkIfOrderIsForwardingOrder(getCurrentOrder());
			OrderLabelContainer orderLabelContainer = orderLabelGenerator.generateLabels((ForwardingOrder) getCurrentOrder());
			getProcessResult().setOrderLabelContainer(orderLabelContainer);
			setCurrentOrder(orderLabelContainer.getForwardingOrder());
			writeCurrentOrderToState();
		} catch (Exception exception) {
			handlePrintLabelError(exception);
		}
	}

	void checkIfOrderIsForwardingOrder(Order order) {
		if (!(order instanceof ForwardingOrder)) {
			throw new BadRequestException("Order " + order.getOrderId() + " is not a forwarding order " + getCurrentOrder().getClass().getName());
		}
	}

	protected void writeCurrentOrderToState() {
		state.get().processResult.order(orderMapper.map(getCurrentOrder()));
	}

	protected Order getCurrentOrder() {
		return state.get().currentOrder;
	}

	@NotNull
	private Optional<OrderSubmitter<Order>> getOrderSubmitter(Order order) {
		// Unfortunate cast to Order is necessary because of the generic type of EdiOrderConverter and List<Ed..<Order>> will not receive all possible implementations
		return orderSubmitters.stream().filter(c -> c.supports(order)).findFirst().map(c -> (OrderSubmitter<Order>) c);
	}

	@NotNull
	protected <T extends Order> Optional<OrderPostProcessor<T>> getOrderPostProcessor(T order) {
		// Unfortunate cast is necessary because of the generic type of OrderPostProcessor and List<OrderPostProcessor..<Order>> will not receive all possible implementations
		return postProcessors.stream().filter(c -> c.supports(order)).findFirst().map(c -> (OrderPostProcessor<T>) c);
	}

	private void removeCurrentState() {
		state.remove();
	}

	protected InternalOrderProcessAction getOrderSaveAction() {
		return getState().action;
	}

	protected void prefillDefaults(Order currentOrder) {
		orderDefaults.prefillDefaults(currentOrder);
	}

	@Data
	class ProcessState {
		final PrintProcessResultDto processResult = new PrintProcessResultDto();

		Order currentOrder;

		BasicOrderDto orderDto;

		InternalOrderProcessAction action;

		public ProcessState(InternalOrderProcessAction action) {
			this.action = action;
		}

		public void updateOrderInProcessResult() {
			processResult.order(orderMapper.map(currentOrder));
		}
	}

	@Getter
	enum InternalOrderProcessAction {
		VALIDATE(BookErrorId.ERR_SV_04), PRINT_LABELS(BookErrorId.ERR_LB_04), SUBMIT(BookErrorId.ERR_SE_04);

		private final BookErrorId bookErrorIdOrderExpired;

		InternalOrderProcessAction(BookErrorId bookErrorIdOrderExpired) {
			this.bookErrorIdOrderExpired = bookErrorIdOrderExpired;
		}

		static InternalOrderProcessAction from(OrderSaveActionDto orderSaveAction) {
			return InternalOrderProcessAction.valueOf(orderSaveAction.name());
		}

		static InternalOrderProcessAction from(OrderActionWithoutValidationDto orderSaveAction) {
			return InternalOrderProcessAction.valueOf(orderSaveAction.name());
		}
	}
}
