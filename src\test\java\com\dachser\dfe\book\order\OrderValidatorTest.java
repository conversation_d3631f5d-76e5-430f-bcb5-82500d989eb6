package com.dachser.dfe.book.order;

import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.SpringBasedLocalMockTest;
import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.document.DocumentsTestUtil;
import com.dachser.dfe.book.document.FileStorageCleanup;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.AirExportOrderDto;
import com.dachser.dfe.book.model.AirImportOrderDto;
import com.dachser.dfe.book.model.Constants;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.DocumentResponseDto;
import com.dachser.dfe.book.model.DocumentTypeDto;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.OrderStatusDto;
import com.dachser.dfe.book.model.OrderTypeDto;
import com.dachser.dfe.book.model.OrderValidationResultDto;
import com.dachser.dfe.book.model.RoadCollectionOrderDto;
import com.dachser.dfe.book.model.RoadForwardingOrderDto;
import com.dachser.dfe.book.model.ValidationResultEntryDto;
import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.air.AirOrderLine;
import com.dachser.dfe.book.order.air.AirOrderLineHsCode;
import com.dachser.dfe.book.order.air.AirOrderReference;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.common.OrderText;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.sea.SeaOrderLine;
import com.dachser.dfe.book.product.road.RoadProductsService;
import com.dachser.dfe.book.service.FixedDateValidationService;
import com.dachser.dfe.book.service.freightterm.FreightTermService;
import com.dachser.dfe.book.service.ordergroup.OrderGroupService;
import com.dachser.dfe.book.user.UserContextService;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Valid;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.Set;

import static com.dachser.dfe.book.order.OrderApiTestUtil.CUSTOMER_NUMBER_ASL;
import static com.dachser.dfe.book.order.OrderApiTestUtil.CUSTOMER_NUMBER_ASL_AIR_PRODUCTS;
import static com.dachser.dfe.book.order.generator.OrderGenerator.generateSeaOrderLine;
import static com.dachser.dfe.book.user.UserServiceMock.INVALID_CUST_NO;
import static com.dachser.dfe.book.user.UserServiceMock.VALID_CUST_NO_ROAD;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@SpringBasedLocalMockTest
class OrderValidatorTest implements ResourceLoadingTest {

	@Autowired
	OrderValidator orderValidator;

	@Autowired
	OrderService orderService;

	@Autowired
	DocumentService documentService;

	@Autowired
	FileStorageCleanup fileStorageCleanup;

	@Autowired
	OrderMapper orderMapper;

	@Autowired
	UserContextService userContextService;

	@Autowired
	OrderDefaults orderDefaults;

	@Autowired
	TestUtil testUtil;

	@MockBean
	FreightTermService freightTermService;

	@MockBean
	OrderGroupService orderGroupService;

	@MockBean
	FixedDateValidationService fixedDateValidationService;

	@MockBean
	RoadProductsService roadProductsService;

	@Autowired
	WhiteListFilterConfiguration whiteListFilterConfiguration;

	private final Random random = new Random();

	private static final String MISSING_INPUT = "Missing or incorrect input";

	private static final String INVALID_COLLECTION_TIME_SLOT_IN_PAST = "Collection time is in the past";

	private static final String USERNAME = "username";

	@BeforeEach
	public void beforeEach() {
		when(roadProductsService.validateProduct(any(RoadOrder.class), anyInt())).thenReturn(Boolean.TRUE);
		when(fixedDateValidationService.validateFixedDate(any(RoadOrder.class), anyInt())).thenReturn(Boolean.TRUE);
		when(orderGroupService.validateOrderGroup(any(RoadOrder.class), anyInt())).thenReturn(Boolean.TRUE);
		when(freightTermService.validateFreightTermForOrder(any(RoadOrder.class), anyInt())).thenReturn(Boolean.TRUE);
		WhiteListFilterConfiguration.FileTypeFilter fileTypeFilter = new WhiteListFilterConfiguration.FileTypeFilter();
		fileTypeFilter.setUpload(List.of("PDF"));
		whiteListFilterConfiguration.setFileTypes(fileTypeFilter);
	}

	@AfterEach
	public void afterEach() {
		fileStorageCleanup.deleteAllFoldersInBasePath();
	}

	@Nested
	class RoadOrder_ {

		@Nested
		@WithMockUser(username = "e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
		class Success {

			@Test
			void shouldNotFailOnMissingFreightTermCollectionOrder() throws IOException {
				final CollectionOrder order = loadCollectionOrder(VALID_CUST_NO_ROAD, "orders/new-collection-order-complete-valid.json");
				orderDefaults.prefillDefaults(order);
				order.setFixDate(LocalDate.now().plusDays(4));
				order.setFreightTerm(null);
				OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
				assertNotNull(validationResult);
				assertTrue(validationResult.getResults().isEmpty());
			}

			@Test
			void shouldUpdateStatusOnValidForwardingOrder() throws IOException {
				final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
				orderDefaults.prefillDefaults(order);
				final OrderValidationResultDto validationResultDto = orderValidator.validateOrder(order);
				assertTrue(validationResultDto.getValid());
				assertEquals(OrderStatus.LABEL_PENDING, order.getStatus());
			}

			@Test
			void shouldUpdateStatusOnValidCollectionOrder() throws IOException {
				final CollectionOrder order = loadCollectionOrder(VALID_CUST_NO_ROAD, "orders/new-collection-order-complete-valid.json");
				orderDefaults.prefillDefaults(order);
				order.setStatus(OrderStatus.DRAFT);
				order.setFixDate(null);
				order.setCollectionDate(LocalDate.now().plusDays(2));
				final OrderValidationResultDto validationResultDto = orderValidator.validateOrder(order);
				assertTrue(validationResultDto.getValid());
				assertEquals(OrderStatus.COMPLETE, order.getStatus());
			}
		}

		@Nested
		@WithMockUser(username = "e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
		class Failing {

			@Nested
			class OnOrderLine {

				@ParameterizedTest
				@EmptySource
				@ValueSource(strings = { "", " ", "   " })
				void shouldFailOnRequiredFieldContentInOrderLine(String content) throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.getOrderLines().get(1).setContent(content);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertFalse(validationResult.getValid());
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("orderLines[1].content", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnRequiredFieldPackagingInOrderLine() throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.getOrderLines().get(0).setPackagingType(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("orderLines[0].packagingType", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnRequiredFieldLengthInOrderLine() throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.getOrderLines().get(0).setLength(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("orderLines[0].length", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnRequiredFieldWidthInOrderLine() throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.getOrderLines().get(0).setWidth(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("orderLines[0].width", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnRequiredFieldHeightInOrderLine() throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.getOrderLines().get(0).setHeight(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("orderLines[0].height", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnRequiredFieldVolumeInOrderLine() throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.getOrderLines().get(0).setVolume(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("orderLines[0].volume", validationResult.getResults().get(0).getField());
				}

				@ParameterizedTest
				@ValueSource(doubles = { -1, 0.00, 0.0001, 10000, 999.9999 })
				void shouldFailWithInvalidVolume(double value) throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.getOrderLines().get(0).setVolume(BigDecimal.valueOf(value));
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertEquals("orderLines[0].volume", validationResult.getResults().get(0).getField());

				}

				@ParameterizedTest
				@ValueSource(ints = { -1, 1000, })
				void shouldFailOnWrongQuantityINnOrderLine(Integer quantity) throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.getOrderLines().get(0).setQuantity(quantity);

					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("orderLines[0].quantity", validationResult.getResults().get(0).getField());
				}

				@ParameterizedTest
				@ValueSource(strings = { "Test", "", "1234", "1234567890TTT" })
				void shouldFailOnWrongGLNInAddress(String gln) throws IOException {
					final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.getShipperAddress().setGln(gln);

					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("shipperAddress.gln", validationResult.getResults().get(0).getField());
				}

				@ParameterizedTest
				@ValueSource(doubles = { -0.1, 100.0, 99.9999 })
				void shouldFailOnExceedingLoadingMeter(Double loadingMeter) throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					final RoadOrderLine orderLine = order.getOrderLines().get(0);
					orderLine.setLoadingMeter(BigDecimal.valueOf(loadingMeter));

					OrderValidationResultDto orderValidationResultDto = orderValidator.validateOrder(order);
					assertNotNull(orderValidationResultDto);
					Optional<@Valid ValidationResultEntryDto> loadingMeterResult = orderValidationResultDto.getResults().stream()
							.filter(result -> result.getField().equals("orderLines[0].loadingMeter")).findFirst();
					assertTrue(loadingMeterResult.isPresent());
				}
			}

			@Nested
			class OnOrder {
				@Test
				void shouldFailWhenCombinationOfCountryCodesHasEmbargo() throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.getShipperAddress().setCountryCode("DE");
					order.getConsigneeAddress().setCountryCode("US");
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
				}

				@Test
				void shouldFailOnMissingCurrencyInOrder() throws IOException {
					final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setCurrency(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("currency", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailWhenCollectionTimeSlotIsNotBiggerThanTwoHoursApart() throws IOException {
					final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setCollectionTo(OffsetDateTime.now().plusDays(1).withHour(9));
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);

					assertTrue(validationResult.getResults().toString().contains("collectionFrom"));
					assertTrue(validationResult.getResults().toString().contains("collectionTo"));
				}

				@Test
				void shouldFailWhenCollectionTimeSlotIsInThePast() throws IOException {
					final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setCollectionFrom(OffsetDateTime.now().minusHours(1));
					order.setCollectionFrom(OffsetDateTime.now().minusHours(2));
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);

					assertTrue(validationResult.getResults().toString().contains("collectionFrom"));
					assertTrue(validationResult.getResults().toString().contains("collectionTo"));
				}

				@Test
				void shouldFailWhenCollectionDateIsMoreThen15DaysInTheFuture() throws IOException {
					final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setCollectionDate(LocalDate.now().plusDays(15));
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);

					assertTrue(validationResult.getResults().toString().contains("collectionDate"));
				}

				@Test
				void shouldFailOnDeliveryNoteMissingIfEDNDocIsSet() throws IOException {
					final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					final Order newOrder = orderService.createNewTestOrder(order);
					byte[] content = "test".getBytes();
					MultipartFile mockFile = new MockMultipartFile("file", "orig", null, content);

					List<DocumentTypeDto> documents = documentService.getDocumentTypes(OrderTypeDto.ROAD_FORWARDING_ORDER);
					Optional<DocumentTypeDto> edn = documents.stream().filter(document -> document.getDescription().equalsIgnoreCase("Electronic Delivery Note")).findFirst();
					int documentTypeID = edn.get().getTypeId();// must be there otherwhise the exception is fine

					DocumentResponseDto document = documentService.uploadDocument(VALID_CUST_NO_ROAD, newOrder.getOrderId(), USERNAME, documentTypeID, "harmless_Test",
							MediaType.APPLICATION_PDF, mockFile);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(newOrder);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("orderReferences.referenceType.delivery_note_number", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnDeliveryNoteMissingIfEDNDocIsSetInUpdate() throws IOException {
					final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					final Order newOrder = orderService.createNewTestOrder(order);
					byte[] content = "test".getBytes();
					MultipartFile mockFile = new MockMultipartFile("file", "orig", null, content);

					List<DocumentTypeDto> documents = documentService.getDocumentTypes(OrderTypeDto.ROAD_FORWARDING_ORDER);
					Optional<DocumentTypeDto> edn = documents.stream().filter(document -> document.getDescription().equalsIgnoreCase("Electronic Delivery Note")).findFirst();
					int documentTypeID = edn.get().getTypeId();// must be there otherwhise the exception is fine

					DocumentResponseDto document = documentService.uploadDocument(VALID_CUST_NO_ROAD, null, USERNAME, documentTypeID, "harmless_Test", MediaType.APPLICATION_PDF,
							mockFile);
					newOrder.setDocumentIds(List.of(document.getDocumentId()));
					OrderValidationResultDto validationResult = orderValidator.validateOrder(newOrder);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("orderReferences.referenceType.delivery_note_number", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnMissingCustomsSettingIfCustomsDocIsAvailable() throws IOException {
					final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					final Order newOrder = orderService.createNewTestOrder(order);
					newOrder.setCustomsType(null);
					byte[] content = "test".getBytes();
					MultipartFile mockFile = new MockMultipartFile("file", "orig", null, content);
					DocumentResponseDto document = documentService.uploadDocument(VALID_CUST_NO_ROAD, newOrder.getOrderId(), USERNAME, 2, "harmless_Test",
							MediaType.APPLICATION_PDF, mockFile);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(newOrder);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("customsType", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnMissingShipmentNumber() throws IOException {
					final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setShipmentNumber(null);
					OrderValidationResultDto validation = orderValidator.validateOrder(order);
					assertEquals(false, validation.getValid());
					assertNotNull(validation.getResults());
					assertEquals(new ValidationResultEntryDto().field("shipmentNumber").errorType(ValidationResultEntryDto.ErrorTypeEnum.REQUIRED_FIELD_MISSING)
							.description(MISSING_INPUT), validation.getResults().get(0));
				}

				@Test
				void shouldFailOnMissingOrderNumber() throws IOException {
					final com.dachser.dfe.book.order.road.ForwardingOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setOrderNumber(null);
					OrderValidationResultDto validation = orderValidator.validateOrder(order);
					assertEquals(false, validation.getValid());
					assertNotNull(validation.getResults());
					assertEquals(
							new ValidationResultEntryDto().field("orderNumber").errorType(ValidationResultEntryDto.ErrorTypeEnum.REQUIRED_FIELD_MISSING).description(MISSING_INPUT),
							validation.getResults().get(0));
				}

				@Test
				void shouldFailOnMissingCountryInAddress() throws IOException {
					final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.getShipperAddress().setCountryCode(null);

					OrderValidationResultDto validation = orderValidator.validateOrder(order);
					assertEquals(false, validation.getValid());
					assertNotNull(validation.getResults());
					assertEquals(new ValidationResultEntryDto().field("shipperAddress.countryCode").errorType(ValidationResultEntryDto.ErrorTypeEnum.REQUIRED_FIELD_MISSING)
							.description(MISSING_INPUT), validation.getResults().get(0));
				}

				@Test
				void shouldFailOnShipperAddressNameNull() throws IOException {
					final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.getShipperAddress().setName(null);

					OrderValidationResultDto validation = orderValidator.validateOrder(order);
					assertEquals(false, validation.getValid());
					assertNotNull(validation.getResults());
					assertEquals(OrderStatusDto.DRAFT, validation.getNewOrderStatus());
					assertEquals(new ValidationResultEntryDto().field("shipperAddress.name").errorType(ValidationResultEntryDto.ErrorTypeEnum.REQUIRED_FIELD_MISSING)
							.description(MISSING_INPUT), validation.getResults().get(0));
				}

				@Test
				void shouldFailOnCollectionDateMissing() throws IOException {
					final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setCollectionDate(null);

					OrderValidationResultDto validation = orderValidator.validateOrder(order);
					assertEquals(false, validation.getValid());
					assertNotNull(validation.getResults());
					assertEquals(OrderStatusDto.DRAFT, validation.getNewOrderStatus());
					assertEquals(new ValidationResultEntryDto().field("collectionDate").errorType(ValidationResultEntryDto.ErrorTypeEnum.REQUIRED_FIELD_MISSING)
							.description(MISSING_INPUT), validation.getResults().get(0));
				}

				@Test
				void shouldFailWhenCollectionDateIsInThePast() throws IOException {
					final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setCollectionDate(LocalDate.now().minusDays(1));
					order.setCollectionTo(null);
					order.setCollectionFrom(null);

					OrderValidationResultDto validation = orderValidator.validateOrder(order);
					assertEquals(false, validation.getValid());
					assertNotNull(validation.getResults());
					assertEquals(OrderStatusDto.DRAFT, validation.getNewOrderStatus());
					assertTrue(validation.getResults().stream().anyMatch(entry -> entry.getField().contains("collectionDate")));
					assertTrue(validation.getResults().stream().anyMatch(entry -> entry.getDescription().equals(INVALID_COLLECTION_TIME_SLOT_IN_PAST)));
				}

				@Test
				void shouldFailWhenCollectionToDateIsInThePast() throws IOException {
					final Order order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setCollectionDate(LocalDate.now());
					order.setCollectionTo(OffsetDateTime.now().minusHours(2));
					order.setCollectionFrom(null);

					OrderValidationResultDto validation = orderValidator.validateOrder(order);
					assertEquals(false, validation.getValid());
					assertNotNull(validation.getResults());
					assertEquals(OrderStatusDto.DRAFT, validation.getNewOrderStatus());
					assertTrue(validation.getResults().stream().anyMatch(entry -> entry.getField().contains("collectionTo")));
					assertTrue(validation.getResults().stream().anyMatch(entry -> entry.getDescription().equals(INVALID_COLLECTION_TIME_SLOT_IN_PAST)));
				}
			}

			@Nested
			class OnRoadOrder {
				@Test
				void shouldFailOnTooSmallCollectionTimeFrame() throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setCollectionTo(OffsetDateTime.now().plusDays(1).withHour(8));
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					List<@Valid ValidationResultEntryDto> results = validationResult.getResults();
					Optional<@Valid ValidationResultEntryDto> firstResult = results.stream().filter(entry -> entry.getField().contains("collectionTo")).findFirst();
					assertTrue(firstResult.isPresent());
					assertTrue(firstResult.get().getDescription().contains("Collection time slot must be at least 2 hours apart"));
				}

				@Test
				void shouldFailOnMissingProduct() throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setProduct(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("product", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnMissingFreightTermForwardingOrder() throws IOException {
					final ForwardingOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setFreightTerm(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("freightTerm", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnInvalidFreightTerm() throws IOException {
					when(freightTermService.validateFreightTermForOrder(any(RoadOrder.class), anyInt())).thenReturn(Boolean.FALSE);
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					when(freightTermService.validateFreightTermForOrder(order, 1)).thenReturn(Boolean.FALSE);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					final ValidationResultEntryDto validationResultEntry = validationResult.getResults().get(0);
					assertEquals("freightTerm", validationResultEntry.getField());
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals(ValidationResultEntryDto.ErrorTypeEnum.CONSTRAINT_VIOLATION, validationResultEntry.getErrorType());
				}

				@Test
				void shouldFailOnInvalidOrderGroup() throws IOException {
					when(orderGroupService.validateOrderGroup(any(RoadOrder.class), anyInt())).thenReturn(Boolean.FALSE);
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setOrderGroup("A");
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					final ValidationResultEntryDto validationResultEntry = validationResult.getResults().get(0);
					assertEquals("orderGroup", validationResultEntry.getField());
					assertEquals(ValidationResultEntryDto.ErrorTypeEnum.CONSTRAINT_VIOLATION, validationResultEntry.getErrorType());
				}

				@Test
				void shouldFailOnRejectedFixedDate() throws IOException {
					when(fixedDateValidationService.validateFixedDate(any(RoadOrder.class), anyInt())).thenReturn(Boolean.FALSE);
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					final ValidationResultEntryDto validationResultEntry = validationResult.getResults().get(0);
					assertEquals("fixDate", validationResultEntry.getField());
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals(ValidationResultEntryDto.ErrorTypeEnum.CONSTRAINT_VIOLATION, validationResultEntry.getErrorType());
				}

				@Test
				void shouldFailOnMissingContactWhenDeliveryOptionIsSet() throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setDeliveryOption(DeliveryOptions.AP);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("deliveryContact", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnMissingContactNameWhenDeliveryOptionIsSet() throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setDeliveryOption(DeliveryOptions.AP);
					final OrderContact orderContact = new OrderContact();
					orderContact.setEmail("sadasda");
					order.setDeliveryContact(orderContact);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("deliveryContact.name", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnMissingEmailAndMobileWhenDeliveryOptionIsSetToAP_AS() throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setDeliveryOption(DeliveryOptions.AP);
					final OrderContact orderContact = new OrderContact();
					orderContact.setName("Test");
					order.setDeliveryContact(orderContact);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains("Mobile number or email is mandatory"));
					assertEquals("deliveryContact.email", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnMissingEmailAndMobileWhenDeliveryOptionIsSetToAC_AT() throws IOException {
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setDeliveryOption(DeliveryOptions.AC);
					final OrderContact orderContact = new OrderContact();
					orderContact.setName("Test");
					order.setDeliveryContact(orderContact);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains("Mobile or phone number is mandatory"));
					assertEquals("deliveryContact.phone", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnMissingDeliveryDate() throws IOException {
					when(roadProductsService.getProductByKey(anyString(), eq(Division.T))).thenReturn(
							Optional.of(new DeliveryProductDto().code("E").description("Express").fixedDeliveryDate(Boolean.TRUE)));
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertFalse(validationResult.getResults().isEmpty());
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					assertEquals("fixDate", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnMissingEkaer() throws IOException {

					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.getConsigneeAddress().setCountryCode(Constants.HUNGARY_COUNTRY_CODE);
					order.getOrderReferences().clear();
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertEquals("orderReferences", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailOnInvalidProduct() throws IOException {
					when(roadProductsService.validateProduct(any(RoadOrder.class), anyInt())).thenReturn(Boolean.FALSE);
					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					final ValidationResultEntryDto validationResultEntry = validationResult.getResults().get(0);
					assertEquals("product", validationResultEntry.getField());
					assertEquals(ValidationResultEntryDto.ErrorTypeEnum.CONSTRAINT_VIOLATION, validationResultEntry.getErrorType());
				}

				@Test
				void shouldFailOnJustOneOfCollectionTimeIsSet() throws IOException {

					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setCollectionTo(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().stream().anyMatch(field -> field.getField().equals("collectionTo")));
				}

				@Test
				void shouldFailIfFixDateIsSetAndBeforeCollectionDate() throws IOException {

					final RoadOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setFixDate(LocalDate.now().plusDays(1));
					order.setCollectionDate(LocalDate.now().plusDays(2));
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertEquals("fixDate", validationResult.getResults().get(0).getField());
				}

				@Test
				void shouldFailIfCustomerIsNotAllowedForDGAndHasGSTextType() throws IOException {

					final RoadOrder order = loadForwardingOrder(INVALID_CUST_NO, "orders/new-forwarding-order-complete-valid.json");
					OrderText orderText = new OrderText();
					orderText.setTextType("GS");
					orderText.setText("Testing");
					orderText.setOrder(order);
					order.addOrderText(orderText);

					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().stream().map(ValidationResultEntryDto::getField).anyMatch("orderTexts"::equals));
				}

				@ParameterizedTest
				@ValueSource(doubles = { 0.000, 100, 99.999 })
				void shouldFailOnInvalidPalletLocations(Double palletLocations) throws IOException {
					final ForwardingOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setPalletLocations(BigDecimal.valueOf(palletLocations));
					OrderValidationResultDto orderValidationResultDto = orderValidator.validateOrder(order);
					assertNotNull(orderValidationResultDto);
					Optional<@Valid ValidationResultEntryDto> loadingMeterResult = orderValidationResultDto.getResults().stream()
							.filter(result -> result.getField().equals("palletLocations")).findFirst();
					assertTrue(loadingMeterResult.isPresent());
				}

				@ParameterizedTest
				@ValueSource(doubles = { -1, 0.000, 99999.999, 100000.0 })
				void shouldFailOnInvalidCashOnDeliveryAmount(Double amount) throws IOException {
					final ForwardingOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setCashOnDeliveryAmount(BigDecimal.valueOf(amount));
					order.setCashOnDelivery(true);
					OrderValidationResultDto orderValidationResultDto = orderValidator.validateOrder(order);
					assertNotNull(orderValidationResultDto);
					Optional<@Valid ValidationResultEntryDto> loadingMeterResult = orderValidationResultDto.getResults().stream()
							.filter(result -> result.getField().equals("cashOnDeliveryAmount")).findFirst();
					assertTrue(loadingMeterResult.isPresent());

				}

				@Test
				void shouldFailOnMissingCashOnDelivery() throws IOException {
					final ForwardingOrder order = loadForwardingOrder(VALID_CUST_NO_ROAD, "orders/new-forwarding-order-complete-valid.json");
					order.setCashOnDelivery(true);
					OrderValidationResultDto orderValidationResultDto = orderValidator.validateOrder(order);
					assertNotNull(orderValidationResultDto);
					Optional<@Valid ValidationResultEntryDto> loadingMeterResult = orderValidationResultDto.getResults().stream()
							.filter(result -> result.getField().equals("cashOnDelivery")).findFirst();
					assertTrue(loadingMeterResult.isPresent());
				}

			}

		}

		private com.dachser.dfe.book.order.road.ForwardingOrder loadForwardingOrder(String customerNumber, String resourceName) throws IOException {
			RoadForwardingOrderDto roadOrder = loadResourceAndConvert(resourceName, new TypeReference<>() {
			});

			roadOrder.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
			roadOrder.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
			roadOrder.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));

			final com.dachser.dfe.book.order.road.ForwardingOrder order = orderMapper.mapForwardingOrderToEntity(roadOrder);
			order.setStatus(OrderStatus.DRAFT);
			order.setBranchId(6);
			setRandomShipmentNumber(order);
			order.setCustomerNumber(customerNumber);
			order.setOrderNumber("123456");
			OrderDefaults.fillCreationFieldsOfAllModels(order, userContextService);
			return order;
		}

		private com.dachser.dfe.book.order.road.CollectionOrder loadCollectionOrder(String customerNumber, String resourceName) throws IOException {
			RoadCollectionOrderDto roadOrder = loadResourceAndConvert(resourceName, new TypeReference<>() {
			});

			roadOrder.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
			roadOrder.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
			roadOrder.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));

			final com.dachser.dfe.book.order.road.CollectionOrder order = orderMapper.mapCollectionOrderToEntity(roadOrder);
			setRandomShipmentNumber(order);
			order.setCustomerNumber(customerNumber);
			order.setOrderNumber("123456");
			OrderDefaults.fillCreationFieldsOfAllModels(order, userContextService);
			return order;
		}

	}

	@Nested
	@WithMockUser(username = "e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
	class AirExportOrder {

		@Nested
		class Success {
			@Test
			void shouldSucceedValidation() {
				final com.dachser.dfe.book.order.air.AirExportOrder airExportOrder = generateAirExportOrder();
				final Long commercialInvoice = DocumentsTestUtil.createCommercialInvoiceAir(documentService);
				airExportOrder.setDocumentIds(List.of(commercialInvoice));
				final OrderValidationResultDto validationResultDto = orderValidator.validateOrder(airExportOrder);
				assertTrue(validationResultDto.getValid());
			}

			@Test
			void shouldSucceedValidationWhenDeliverToAirportAndInvalidCollectionTimeSlots() {
				final com.dachser.dfe.book.order.air.AirExportOrder airExportOrder = generateAirExportOrder();
				final Long commercialInvoice = DocumentsTestUtil.createCommercialInvoiceAir(documentService);
				airExportOrder.setDocumentIds(List.of(commercialInvoice));
				airExportOrder.setDeliverToAirport(true);
				airExportOrder.setCollectionTo(OffsetDateTime.now());
				airExportOrder.setCollectionFrom(OffsetDateTime.now().plusHours(1));
				final OrderValidationResultDto validationResultDto = orderValidator.validateOrder(airExportOrder);
				assertTrue(validationResultDto.getValid());
			}

			@Test
			void shouldSucceedValidationWhenRequestArrangementIsSetAndCollectionTimeIsNull() throws IOException {
				com.dachser.dfe.book.order.air.AirExportOrder airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.setRequestArrangement(true);
				airOrder.setCollectionFrom(null);
				airOrder.setCollectionTo(null);
				airOrder.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertEquals(0, validationResult.getResults().size());
			}

			@ParameterizedTest
			@ValueSource(doubles = { 0.1, 999.99 })
			void validOrderLineVolumeRange(double volume) throws IOException {
				final AirOrder airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
				airOrder.getOrderLines().get(0).setVolume(BigDecimal.valueOf(volume));
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertTrue(validationResult.getResults().isEmpty());
			}

			@Test
			void shouldSucceedValidationWhenAirProductsEnabledAndConfigurationValid() throws IOException {
				com.dachser.dfe.book.order.air.AirExportOrder airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL_AIR_PRODUCTS);
				airOrder.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
				airOrder.setProductCode(1);
				airOrder.setFromIATA("MUC");
				airOrder.setToIATA("FRA");
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertTrue(validationResult.getResults().isEmpty());
				assertTrue(validationResult.getValid());
			}

			@Test
			void shouldSucceedValidationWhenAirProductsDisabledAndAirProductIsNull() throws IOException {
				com.dachser.dfe.book.order.air.AirExportOrder airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
				airOrder.setProductCode(null);
				airOrder.setFromIATA("MUC");
				airOrder.setToIATA("FRA");
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertTrue(validationResult.getResults().isEmpty());
				assertTrue(validationResult.getValid());
			}

			@Test
			void shouldSucceedValidationWhenAirProductsEnabledAndAirProductIsNullAndProductRoutingConfigurationIsNotValid() throws IOException {
				com.dachser.dfe.book.order.air.AirExportOrder airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL_AIR_PRODUCTS);
				airOrder.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
				airOrder.setProductCode(null);
				airOrder.setFromIATA("MUC");
				airOrder.setToIATA("BCN");
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertTrue(validationResult.getResults().isEmpty());
				assertTrue(validationResult.getValid());
			}

			@Test
			void shouldSucceedWhenInputNotExeeds35CharactersForShipperAddressName() throws IOException {
				var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.getShipperAddress().setName("12345678901234567890123456789012345");
				airOrder.getShipperAddress().setName2("12345678901234567890123456789012345");
				airOrder.getShipperAddress().setName3("12345678901234567890123456789012345");
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertTrue(validationResult.getResults().isEmpty());
				assertTrue(validationResult.getValid());
			}

			@Test
			void shouldSucceedValidationWhenNoDupleEntryIsMadeForOrderReference() throws IOException {
				com.dachser.dfe.book.order.air.AirExportOrder airExportOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);

				// Add none duplicate but same type
				AirOrderReference duplicate = new AirOrderReference();
				duplicate.setReferenceType(AirSeaOrderReferenceType.QUOTATION_REFERENCE);
				duplicate.setReferenceValue("1234534");
				duplicate.setLoading(true);
				duplicate.setUnloading(false);
				airExportOrder.addOrderReference(duplicate);

				OrderValidationResultDto validationResult = orderValidator.validateOrder(airExportOrder);
				assertTrue(validationResult.getResults().isEmpty());
				assertTrue(validationResult.getValid());
			}

			@Test
			void shouldSucceedOnMissingAirOrderLineWeight() throws IOException {
				var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.getOrderLines().get(1).setWeight(null);
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertTrue(validationResult.getResults().isEmpty());
				assertTrue(validationResult.getValid());
			}

			@Test
			void shouldSucceedOnOneDecimalOrderLineWeight() throws IOException {
				var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.getOrderLines().get(1).setWeight(BigDecimal.valueOf(0.1));
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertTrue(validationResult.getResults().isEmpty());
				assertTrue(validationResult.getValid());
			}
		}

		@Nested
		class Failure {

			private Validator validator;

			@BeforeEach
			public void setup() {
				ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
				validator = factory.getValidator();
			}

			@Test
			void shouldFailWhenNoCommercialInvoiceIsGiven() {
				final com.dachser.dfe.book.order.air.AirImportOrder airImportOrder = testUtil.generateAirImportOrder();
				setRandomShipmentNumber(airImportOrder);
				airImportOrder.setCreator(userContextService.getCurrentUserId());
				final Long commercialInvoice = DocumentsTestUtil.createCommercialInvoiceAir(documentService);
				airImportOrder.setDocumentIds(List.of(commercialInvoice));
				final OrderValidationResultDto validationResultDto = orderValidator.validateOrder(airImportOrder);
				assertTrue(validationResultDto.getValid());

				airImportOrder.setDocumentIds(List.of());
				final OrderValidationResultDto validation2 = orderValidator.validateOrder(airImportOrder);
				assertFalse(validation2.getValid());
				assertTrue(validation2.getResults().toString().contains("orderDocuments"));
			}

			@Test
			void shouldFailWhenCollectionDateAirIsMoreThen15DaysInTheFuture() {
				final AirOrder order = generateAirExportOrder();
				order.setDeliverToAirport(true);
				order.setCollectionDate(LocalDate.now().plusDays(15));

				OrderValidationResultDto validationResult = orderValidator.validateOrder(order);

				assertNotNull(validationResult);
				assertTrue(validationResult.getResults().toString().contains("collectionDate"));
			}

			@Test
			void shouldFailOnToLongFromIATA() throws IOException {
				var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.setFromIATA("21312321");
				Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
				assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("fromIATA")));
				assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 5")));
			}

			@Test
			void shouldFailOnToLongToIATA() throws IOException {
				var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.setToIATA("21312321");
				Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
				assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("toIATA")));
				assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 5")));
			}

			@Test
			void shouldFailOnIncoTermNotSet() throws IOException {
				var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.setIncoTerm(null);
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertNotNull(validationResult.getResults());
				assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("incoTerm")));
			}

			@Test
			void shouldFailOnIncoTermToLong() throws IOException {
				var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.setIncoTerm("1234");
				Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
				assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("incoTerm")));
				assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 3")));
			}

			@Test
			void shouldFailWithValidationErrorsEvenOnEmptyOrder() {
				final OrderValidationResultDto validationResultDto = orderValidator.validateOrder(new com.dachser.dfe.book.order.air.AirExportOrder());
				assertNotNull(validationResultDto);
				assertFalse(validationResultDto.getResults().isEmpty());
			}

			@Test
			void shouldFailWithInactiveIncoTerm() {
				final com.dachser.dfe.book.order.air.AirExportOrder airExportOrder = generateAirExportOrder();
				airExportOrder.setIncoTerm("XXX");
				final OrderValidationResultDto validationResultDto = orderValidator.validateOrder(airExportOrder);
				assertFalse(validationResultDto.getValid());
				assertNotNull(validationResultDto.getResults());
				Optional<ValidationResultEntryDto> incoTerm = validationResultDto.getResults().stream().filter(v -> v.getField().equals("incoTerm")).findFirst();
				assertTrue(incoTerm.isPresent());
			}

			@Test
			void shouldFailWhenAirProductsDisabledAndAirProductNotNull() throws IOException {
				com.dachser.dfe.book.order.air.AirExportOrder airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
				airOrder.setProductCode(1);
				airOrder.setFromIATA("MUC");
				airOrder.setToIATA("FRA");
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertFalse(validationResult.getValid());
				assertNotNull(validationResult.getResults());
				assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("productCode")));
			}

			@Test
			void shouldFailWhenAirProductsEnabledAndAirProductIsNull() throws IOException {
				com.dachser.dfe.book.order.air.AirExportOrder airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL_AIR_PRODUCTS);
				airOrder.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
				airOrder.setProductCode(null);
				airOrder.setFromIATA("MUC");
				airOrder.setToIATA("FRA");
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertFalse(validationResult.getValid());
				assertNotNull(validationResult.getResults());
				assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("productCode")));
			}

			@Test
			void shouldFailValidaitonWhenFromIATAIsSetAndToIATAIsNull() throws IOException {
				com.dachser.dfe.book.order.air.AirExportOrder airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
				airOrder.setProductCode(1);
				airOrder.setFromIATA("MUC");
				airOrder.setToIATA(null);
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertFalse(validationResult.getValid());
				assertNotNull(validationResult.getResults());
				assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("toIATA")));
			}

			@Test
			void shouldFailValidaitonWhenFromIATAIsNullAndToIATAIsSet() throws IOException {
				com.dachser.dfe.book.order.air.AirExportOrder airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
				airOrder.setProductCode(1);
				airOrder.setFromIATA(null);
				airOrder.setToIATA("FRA");
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertFalse(validationResult.getValid());
				assertNotNull(validationResult.getResults());
				assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("fromIATA")));
			}

			@Test
			void shouldFailOnMissingShippersReferenceLoadingOrUnloading() throws IOException {
				com.dachser.dfe.book.order.air.AirExportOrder airExportOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airExportOrder.getOrderReferences().stream().filter(ref -> ref.getReferenceType().equals(AirSeaOrderReferenceType.SHIPPERS_REFERENCE)).forEach(ref -> {
					ref.setLoading(false);
					ref.setUnloading(false);
				});
				OrderValidationResultDto orderValidationResultDto = orderValidator.validateOrder(airExportOrder);
				assertNotNull(orderValidationResultDto);
				assertFalse(orderValidationResultDto.getValid());
				assertTrue(orderValidationResultDto.getResults().stream().anyMatch(v -> v.getField().equals("shippersReference")));
			}

			@Test
			void shouldFailValidationWhenDupleEntryIsMadeForOrderReference() throws IOException {
				com.dachser.dfe.book.order.air.AirExportOrder airExportOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);

				// Add duplicate
				AirOrderReference duplicate = new AirOrderReference();
				duplicate.setReferenceType(AirSeaOrderReferenceType.QUOTATION_REFERENCE);
				duplicate.setReferenceValue("12345");
				duplicate.setLoading(true);
				duplicate.setUnloading(false);
				airExportOrder.addOrderReference(duplicate);

				OrderValidationResultDto orderValidationResultDto = orderValidator.validateOrder(airExportOrder);
				assertNotNull(orderValidationResultDto);
				assertFalse(orderValidationResultDto.getValid());
				assertTrue(orderValidationResultDto.getResults().stream().anyMatch(v -> v.getField().equals("orderReferences.referenceType.PR")));
			}

			@Test
			void shouldFailOnTwoDecimalOrderLineWeight() throws IOException {
				var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.getOrderLines().get(1).setWeight(BigDecimal.valueOf(0.01));
				OrderValidationResultDto orderValidationResultDto = orderValidator.validateOrder(airOrder);
				assertNotNull(orderValidationResultDto);
				assertFalse(orderValidationResultDto.getValid());
			}

			@Nested
			class OrderContact {

				@Test
				void shouldFailOnMissingOrderContactName() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderContact().setName(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("orderContact.name")));
				}

				@Test
				void shouldFailWhenInputExceeds40CharactersForOrderContactName() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderContact().setName("12345678901234567890123456789011232544111");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("orderContact.name")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 40")));
				}

				@Test
				void shouldFailWhenInputExceeds150CharactersForOrderContactEmail() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderContact().setEmail(
							"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901123456789012345678901234567890123456789012345678901234567890");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("orderContact.email")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 150")));
				}

				@Test
				void shouldFailWhenInputExceeds30CharactersForOrderContactTelephone() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderContact().setTelephone("12345678901234567890123456123456");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("orderContact.telephone")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 30")));
				}

				@Test
				void shouldFailWhenInputExceeds30CharactersForOrderContactMobile() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderContact().setMobile("12345678901234567890123456123456");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("orderContact.mobile")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 30")));
				}

				@Test
				void shouldFailWhenInputExceeds30CharactersForOrderContactFax() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderContact().setFax("1234567890123456789012345678901");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("orderContact.fax")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 30")));
				}
			}

			@Nested
			class OrderAddress {

				@Test
				void shouldFailOnMissingOrderBaseAddressName() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getShipperAddress().setName(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertFalse(validationResult.getValid());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("shipperAddress.name")));
				}

				@Test
				void shouldFailWhenInputExceeds40CharactersForShipperAddressName() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getShipperAddress().setName("12345678901234567890123456789012345X12345678912");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("shipperAddress.name")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 40")));
				}

				@Test
				void shouldFailWhenInputExceeds40CharactersForShipperAddressName2() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getShipperAddress().setName2("12345678901234567890123456789012345X12345678912");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("shipperAddress.name2")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 40")));
				}

				@Test
				void shouldFailWhenInputExceeds40CharactersForShipperAddressName3() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getShipperAddress().setName3("12345678901234567890123456789012345X12345678912");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("shipperAddress.name3")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 40")));
				}

				@Test
				void shouldFailWhenInputExceeds40CharactersForShipperAddressStreet() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getShipperAddress().setStreet("123456789012345678901234567890112345678912");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("shipperAddress.street")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 40")));
				}

				@Test
				void shouldFailOnMissingOrderBaseAddressStreet() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getShipperAddress().setStreet(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertFalse(validationResult.getValid());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("shipperAddress.street")));
				}

				@Test
				void shouldFailWhenInputExceeds12CharactersForShipperAddressPostCode() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.setStatus(OrderStatus.COMPLETE);
					airOrder.getShipperAddress().setPostcode("12345678912342");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("shipperAddress.postcode")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 12")));
				}

				@Test
				void shouldFailWhenInputExceeds40CharactersForShipperAddressCity() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getShipperAddress().setCity("12345678901234567890123456789012345X12345678912");
					airOrder.setStatus(OrderStatus.COMPLETE);
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("shipperAddress.city")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 40")));
				}

				@Test
				void shouldFailOnMissingOrderBaseAddressCity() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getShipperAddress().setCity(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertFalse(validationResult.getValid());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("shipperAddress.city")));
				}

				@Test
				void shouldFailWhenInputExceeds3CharactersForShipperAddressCountryCode() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getShipperAddress().setCountryCode("1234");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("shipperAddress.countryCode")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 3")));
				}

				@Test
				void shouldFailOnMissingOrderBaseAddressCountryCode() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getShipperAddress().setCountryCode(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertFalse(validationResult.getValid());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("shipperAddress.countryCode")));
				}

				@Test
				void shouldFailWhenInputExceeds50CharactersForShipperAddressSupplement() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getShipperAddress().setSupplement("123456789012345678901234567890123456789012345678901234567890");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("shipperAddress.supplement")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 50")));
				}

				@Test
				void shouldFailWhenInputExceeds13CharactersForShipperAddressGln() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getShipperAddress().setGln("12345678901233");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("shipperAddress.gln")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 13")));
				}

				@Test
				void shouldFailOnMissingOrderContactForAir() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getShipperAddress().setOrderContact(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertFalse(validationResult.getValid());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("contact")));
				}

				@Test
				void shouldFailOnIncorrectAddress() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getShipperAddress().setOrderContact(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertFalse(validationResult.getValid());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("contact")));
				}

			}

			@Nested
			class OrderCollection {

				@Test
				void shouldFailWhenCollectionTimeSlotIsNotBiggerThanTwoHoursApart() throws IOException {
					final AirOrder order = loadAirOrder(CUSTOMER_NUMBER_ASL);

					order.setCollectionFrom(OffsetDateTime.now().plusDays(1));
					order.setCollectionTo(OffsetDateTime.now().plusDays(1).plusHours(1));
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);

					assertTrue(validationResult.getResults().toString().contains("collectionFrom"));
					assertTrue(validationResult.getResults().toString().contains("collectionTo"));
				}

				@Test
				void shouldFailWhenCollectionTimeSlotDifferenceIsNegative() throws IOException {
					final AirOrder order = loadAirOrder(CUSTOMER_NUMBER_ASL);
					order.setCollectionFrom(OffsetDateTime.now().plusDays(1));
					order.setCollectionTo(OffsetDateTime.now().plusDays(1).minusHours(1));
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);

					assertTrue(validationResult.getResults().toString().contains("collectionFrom"));
					assertTrue(validationResult.getResults().toString().contains("collectionTo"));
				}

				@Test
				void shouldFailWhenCollectionTimeSlotIsInThePast() throws IOException {
					final AirOrder order = loadAirOrder(CUSTOMER_NUMBER_ASL);
					order.setCollectionFrom(OffsetDateTime.now().minusHours(1));
					order.setCollectionFrom(OffsetDateTime.now().minusHours(2));
					OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
					assertNotNull(validationResult);

					assertTrue(validationResult.getResults().toString().contains("collectionFrom"));
					assertTrue(validationResult.getResults().toString().contains("collectionTo"));
				}
			}

			@Nested
			class OrderReference {

				@Test
				void shouldFailWhenInputExceeds35CharactersForReferenceValue() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderReferences().get(0).setReferenceValue("Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("orderReferences[0].referenceValue")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 35")));
				}
			}

			@Nested
			class OrderLines {

				@Test
				void shouldFailWithNullGoodsDescription() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderLines().get(0).getHsCodes().get(0).setGoods(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("orderLines[0].hsCodes[0].good")));
				}

				@Test
				void shouldFailWithZeroOrderLines() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.setOrderLines(new ArrayList<>());
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("orderLines")));
				}

				@Test
				void shouldFailOnShipperReferenceNotSet() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.setShipperReference(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("shipperReference")));

				}

				@Test
				void shouldFailOnShipperReferenceToLong() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.setShipperReference("String over 35 characters testing 1 test");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("shipperReference")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 35")));
				}

				@Test
				void shouldFailOnMissingAirOrderLineWeight() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderLines().get(0).setWeight(null);
					airOrder.getOrderLines().get(1).setWeight(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("orderLines[0].weight")));
				}

				@Test
				void shouldFailOnMissingAirOrderLineHeight() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderLines().get(0).setHeight(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("orderLines[0].height")));
				}

				@Test
				void shouldFailOnMissingAirOrderLineWidth() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderLines().get(0).setWidth(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("orderLines[0].width")));
				}

				@Test
				void shouldFailOnMissingAirOrderLineLength() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderLines().get(0).setLength(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("orderLines[0].length")));
				}

				@Test
				void shouldFailOnMissingAirOrderLineVolume() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderLines().get(0).setVolume(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertNotNull(validationResult.getResults());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("orderLines[0].volume")));
				}

				@Test
				void shouldFailOnMissingAirOrderQuantity() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.setStatus(OrderStatus.COMPLETE);
					airOrder.getOrderLines().get(0).setQuantity(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("orderLines[0].quantity")));
				}

				@Test
				void shouldFailOnQuantityLowerThan1() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.setStatus(OrderStatus.COMPLETE);
					airOrder.getOrderLines().get(0).setQuantity(0);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("orderLines[0].quantity")));
				}

				@Test
				void shouldFailOnQuantityHigherThan999() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.setStatus(OrderStatus.COMPLETE);
					airOrder.getOrderLines().get(0).setQuantity(1000);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertFalse(validationResult.getValid());
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("orderLines[0].quantity")));
				}

				@Test
				void shouldFailOnMissingNumber() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.setStatus(OrderStatus.COMPLETE);
					airOrder.getOrderLines().get(0).setNumber(null);
					OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
					assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("orderLines[0].number")));
				}

				@Test
				void shouldFailWhenInputExceeds1500CharacterForMarkAndNumbers() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderLines().get(0).setMarkAndNumbers("""
							Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.\s
							
							Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.\s
							
							Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit e\
							""");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("orderLines[0].markAndNumbers")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 1500")));
				}

				@Test
				void shouldFailWhenInputExceeds3CharactersForPackagingType() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderLines().get(0).setPackagingType("1234");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("orderLines[0].packagingType")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 3")));
				}

				@Test
				void shouldFailWhenInputExceeds50CharactersForPackagingTypeDescription() throws IOException {
					var airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					airOrder.getOrderLines().get(0)
							.setPackagingTypeDescription("Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut");
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(airOrder);
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("orderLines[0].packagingTypeDescription")));
					assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("size must be between 0 and 50")));
				}

				@ParameterizedTest
				@ValueSource(doubles = { -1.0, -100.0, 1000.1 })
				void invalidOrderLineVolumeRange(double volume) throws IOException {
					var order = loadAirOrder(CUSTOMER_NUMBER_ASL);
					order.getOrderLines().get(0).setVolume(BigDecimal.valueOf(volume));
					Set<ConstraintViolation<com.dachser.dfe.book.order.air.AirExportOrder>> violations = validator.validate(order);
					assertFalse(violations.isEmpty());
					assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("orderLines[0].volume")));
				}

				@Test
				void shouldFailOnTooMuchInputInGoods() throws IOException {
					final AirOrder airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
					final AirOrderLine airOrderLine = airOrder.getOrderLines().get(0);
					for (int i = 0; i < 20; i++) {
						AirOrderLineHsCode airOrderLineHsCode = new AirOrderLineHsCode();
						airOrderLineHsCode.setGoods("12345678901234567890123456789012345678901234567890123456789012345678901234567890");
						airOrderLine.getHsCodes().add(airOrderLineHsCode);
					}
					final OrderValidationResultDto validationResultDto = orderValidator.validateOrder(airOrder);
					final List<ValidationResultEntryDto> results = validationResultDto.getResults();
					assertFalse(results.isEmpty());
					final Optional<ValidationResultEntryDto> first = results.stream().filter(result -> result.getField().equals("orderLines")).findFirst();
					assertTrue(first.isPresent(), "Should find an validation result on orderLines");

				}

				@ParameterizedTest
				@ValueSource(doubles = { -1.0, -100.0, 1000.1 })
				void invalidOrderLineVolumeRangeDefaultGroup(double volume) throws IOException {
					var order = loadAirOrder(CUSTOMER_NUMBER_ASL);
					order.getOrderLines().get(0).setVolume(BigDecimal.valueOf(volume));
					OrderValidationResultDto orderValidationResultDto = orderValidator.validateOrder(order);
					List<@Valid ValidationResultEntryDto> results = orderValidationResultDto.getResults();
					assertFalse(results.isEmpty());
					assertTrue(results.stream().anyMatch(v -> v.getField().equals("orderLines[0].volume")));
				}

			}

		}

		private com.dachser.dfe.book.order.air.AirExportOrder loadAirOrder(String customerNumber) throws IOException {
			final AirExportOrderDto order = loadResourceAndConvert("orders/new-air-export-order.json", new TypeReference<>() {
			});
			order.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
			order.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
			order.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));

			final Order order1 = orderMapper.map(order);

			setRandomShipmentNumber(order1);
			order1.setCustomerNumber(customerNumber);
			order1.setBranchId(1);
			order1.setStatus(OrderStatus.DRAFT);
			order1.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
			OrderDefaults.fillCreationFieldsOfAllModels(order1, userContextService);
			return (com.dachser.dfe.book.order.air.AirExportOrder) order1;
		}
	}

	private com.dachser.dfe.book.order.air.AirExportOrder generateAirExportOrder() {
		com.dachser.dfe.book.order.air.AirExportOrder airExportOrder = testUtil.generateAirExportOrder();
		setRandomShipmentNumber(airExportOrder);
		return airExportOrder;
	}

	@Nested
	@WithMockUser(username = "e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
	class AirImportOrder {

		@Nested
		class Success {
			@Test
			void shouldSucceedValidation() {
				final com.dachser.dfe.book.order.air.AirImportOrder airImportOrder = testUtil.generateAirImportOrder();
				setRandomShipmentNumber(airImportOrder);
				airImportOrder.setCreator(userContextService.getCurrentUserId());
				final Long commercialInvoice = DocumentsTestUtil.createCommercialInvoiceAir(documentService);
				airImportOrder.setDocumentIds(List.of(commercialInvoice));
				final OrderValidationResultDto validationResultDto = orderValidator.validateOrder(airImportOrder);
				assertTrue(validationResultDto.getValid());
			}

			@Test
			void shouldSucceedValidationWhenRequestArrangementIsSetAndCollectionTimeIsNull() throws IOException {
				com.dachser.dfe.book.order.air.AirImportOrder airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.setRequestArrangement(true);
				airOrder.setCollectionFrom(null);
				airOrder.setCollectionTo(null);
				airOrder.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertEquals(0, validationResult.getResults().size());
			}

			@ParameterizedTest
			@ValueSource(doubles = { 0.1, 999.99 })
			void validOrderLineVolumeRange(double volume) throws IOException {
				final AirOrder airOrder = loadAirOrder(CUSTOMER_NUMBER_ASL);
				airOrder.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
				airOrder.getOrderLines().get(0).setVolume(BigDecimal.valueOf(volume));
				OrderValidationResultDto validationResult = orderValidator.validateOrder(airOrder);
				assertTrue(validationResult.getResults().isEmpty());
			}

		}

		private com.dachser.dfe.book.order.air.AirImportOrder loadAirOrder(String customerNumber) throws IOException {
			final AirImportOrderDto order = loadResourceAndConvert("orders/new-air-import-order.json", new TypeReference<>() {
			});
			order.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
			order.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
			order.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));

			final Order order1 = orderMapper.map(order);
			setRandomShipmentNumber(order1);
			order1.setCustomerNumber(customerNumber);
			order1.setBranchId(1);
			order1.setStatus(OrderStatus.DRAFT);
			OrderDefaults.fillCreationFieldsOfAllModels(order1, userContextService);
			return (com.dachser.dfe.book.order.air.AirImportOrder) order1;
		}
	}

	@Nested
	@WithMockUser(username = "e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
	class SeaOrder {

		@Nested
		class Success {
			@Test
			void shouldSucceedValidationWhenDeliverToPortAndInvalidCollectionTimeSlots() {
				final com.dachser.dfe.book.order.sea.SeaOrder order = testUtil.generateSeaExportOrder();
				order.setShipmentNumber(12322222L);
				order.setDeliverToPort(true);
				order.setCollectionTo(OffsetDateTime.now());
				order.setCollectionFrom(OffsetDateTime.now().plusHours(1));
				final OrderValidationResultDto validationResultDto = orderValidator.validateOrder(order);
				assertTrue(validationResultDto.getValid());
			}

			@Test
			void shouldSucceedOnMissingSeaOrderLineWeight() {
				final com.dachser.dfe.book.order.sea.SeaOrder order = testUtil.generateSeaExportOrder();
				order.setShipmentNumber(1232222L);
				SeaOrderLine seaOrderLine = generateSeaOrderLine();
				seaOrderLine.setWeight(null);
				order.addOrderLine(seaOrderLine);
				order.setDeliverToPort(true);
				order.setCollectionTo(OffsetDateTime.now());
				order.setCollectionFrom(OffsetDateTime.now().plusHours(1));
				final OrderValidationResultDto validationResultDto = orderValidator.validateOrder(order);
				assertTrue(validationResultDto.getResults().isEmpty());
				assertTrue(validationResultDto.getValid());
			}

			@Test
			void shouldSucceedOnThreeDecimalOrderLineWeightSea() {
				final com.dachser.dfe.book.order.sea.SeaOrder order = testUtil.generateSeaExportOrder();
				SeaOrderLine seaOrderLine = generateSeaOrderLine();
				seaOrderLine.setWeight(BigDecimal.valueOf(0.001));
				order.addOrderLine(seaOrderLine);
				order.setDeliverToPort(true);
				order.setCollectionTo(OffsetDateTime.now());
				order.setCollectionFrom(OffsetDateTime.now().plusHours(1));
				final OrderValidationResultDto validationResultDto = orderValidator.validateOrder(order);
				assertTrue(validationResultDto.getResults().isEmpty());
				assertTrue(validationResultDto.getValid());
			}
		}

		@Nested
		class Failure {
			@Test
			void shouldFailWhenCollectionDateSeaIsMoreThen15DaysInTheFuture() {
				final com.dachser.dfe.book.order.sea.SeaOrder order = testUtil.generateSeaExportOrder();
				order.setDeliverToPort(true);
				order.setCollectionDate(LocalDate.now().plusDays(15));

				OrderValidationResultDto validationResult = orderValidator.validateOrder(order);

				assertNotNull(validationResult);
				assertTrue(validationResult.getResults().toString().contains("collectionDate"));
			}

			@Test
			void shouldFailValidationOnMissingOrderLineForSeaFcl() {
				final com.dachser.dfe.book.order.sea.SeaOrder order = testUtil.generateSeaExportOrder();
				order.setShipmentNumber(1232222L);
				order.setOrderLines(new ArrayList<>());
				order.setFullContainerLoad(false);
				order.setDeliverToPort(true);
				order.setCollectionTo(OffsetDateTime.now());
				order.setCollectionFrom(OffsetDateTime.now().plusHours(1));
				OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
				assertFalse(validationResult.getValid());
				assertNotNull(validationResult.getResults());
				assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("orderLines")));
			}

			@Test
			void shouldFailValidaitonWhenFromPortIsSetAndToPortIsNull() throws IOException {
				final com.dachser.dfe.book.order.sea.SeaOrder seaOrder = testUtil.generateSeaExportOrder();
				seaOrder.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
				seaOrder.setFromPort("FRA");
				seaOrder.setToPort(null);
				OrderValidationResultDto validationResult = orderValidator.validateOrder(seaOrder);
				assertFalse(validationResult.getValid());
				assertNotNull(validationResult.getResults());
				assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("toPort")));
			}

			@Test
			void shouldFailValidationWhenFromPortIsNullAndToPortIsSet() throws IOException {
				final com.dachser.dfe.book.order.sea.SeaOrder seaOrder = testUtil.generateSeaExportOrder();
				seaOrder.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
				seaOrder.setFromPort(null);
				seaOrder.setToPort("FRA");
				OrderValidationResultDto validationResult = orderValidator.validateOrder(seaOrder);
				assertFalse(validationResult.getValid());
				assertNotNull(validationResult.getResults());
				assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("fromPort")));
			}

			@Test
			void shouldFailValidationWhenFourDecimalOrderLineWeightSea() {
				final com.dachser.dfe.book.order.sea.SeaOrder order = testUtil.generateSeaExportOrder();
				SeaOrderLine seaOrderLine = generateSeaOrderLine();
				seaOrderLine.setWeight(BigDecimal.valueOf(0.0001));
				order.addOrderLine(seaOrderLine);
				final Long commercialInvoice = DocumentsTestUtil.createCommercialInvoiceAir(documentService);
				order.setDocumentIds(List.of(commercialInvoice));
				order.setDeliverToPort(true);
				order.setCollectionTo(OffsetDateTime.now());
				order.setCollectionFrom(OffsetDateTime.now().plusHours(1));
				OrderValidationResultDto validationResult = orderValidator.validateOrder(order);
				assertFalse(validationResult.getValid());
				assertNotNull(validationResult.getResults());
				assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getField().equals("orderLines[1].weight")));
				assertTrue(validationResult.getResults().stream().anyMatch(v -> v.getDescription().equals("must be greater than or equal to 0.001")));
			}
		}

	}

	private void setRandomShipmentNumber(Order order) {
		order.setShipmentNumber(Math.abs(Long.valueOf(random.nextInt())));
	}

}
