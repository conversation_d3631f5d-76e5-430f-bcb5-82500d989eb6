package com.dachser.dfe.book.dip.converter.road.collection;

import com.dachser.dfe.book.dip.converter.road.RoadShipmentLineMapper;
import com.dachser.dfe.book.model.jaxb.order.road.collection.Height;
import com.dachser.dfe.book.model.jaxb.order.road.collection.Length;
import com.dachser.dfe.book.model.jaxb.order.road.collection.LoadingMeter;
import com.dachser.dfe.book.model.jaxb.order.road.collection.Measurements;
import com.dachser.dfe.book.model.jaxb.order.road.collection.ShipmentLine;
import com.dachser.dfe.book.model.jaxb.order.road.collection.Volume;
import com.dachser.dfe.book.model.jaxb.order.road.collection.Weight;
import com.dachser.dfe.book.model.jaxb.order.road.collection.Width;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, uses = { CollectionDangerousGoodsMapper.class }, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface CollectionShipmentLineMapper extends RoadShipmentLineMapper<ShipmentLine, Measurements, Weight, Height, Length, Width, Volume, LoadingMeter> {
}
