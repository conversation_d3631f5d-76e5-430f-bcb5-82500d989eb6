package com.dachser.dfe.book.packaging;

import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.generaldata.GeneralDataAdapterMock;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.jpa.query.QueryService;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.Segment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PackagingOptionsServiceTest {

	private static final int BUSINESS_DOMAIN = 1;

	@Spy
	GeneralDataAdapterMock generalDataAdapterMock = new GeneralDataAdapterMock();

	@Mock
	GeneralDataService generalDataService;

	@Spy
	WhiteListFilterConfiguration whiteListFilterConfiguration = new WhiteListFilterConfiguration();

	@Mock
	QueryService queryService;

	@InjectMocks
	PackagingOptionsService sut;

	@BeforeEach
	void setUp() {
		WhiteListFilterConfiguration.PackagingOptionsFilter packagingOptionsFilter = new WhiteListFilterConfiguration.PackagingOptionsFilter();
		whiteListFilterConfiguration.setPackagingOptionsFilter(packagingOptionsFilter);
		packagingOptionsFilter.setAir(List.of("PE", "CT", "BX"));
		packagingOptionsFilter.setRoad(List.of("KT", "C2", "ST"));
	}

	@Nested
	class PackagingOptionsSea {
		@Test()
		void receiveNonEmptyList() {
			List<OptionDto> packagingOptionsAir = generalDataAdapterMock.getPackagingOptionsAir("en");
			when(generalDataService.getPackagingOptionsAir(anyString())).thenReturn(packagingOptionsAir);

			final List<OptionDto> serviceResponse = sut.getPackagingOptions(Segment.SEA);
			assertNotNull(serviceResponse);
			assertFalse(serviceResponse.isEmpty());
			assertEquals(3, serviceResponse.size());
			assertEquals("Box", serviceResponse.get(0).getDescription());
			assertEquals("Carton", serviceResponse.get(1).getDescription());
			assertEquals("Europallet", serviceResponse.get(2).getDescription());
		}

		@Test()
		void receiveEmptyListOnInvalidResponse() {

			final List<OptionDto> serviceResponse = sut.getPackagingOptions(Segment.SEA);
			assertNotNull(serviceResponse);
			assertTrue(serviceResponse.isEmpty());
		}

		@Test()
		void receiveEmptyList() {
			final List<OptionDto> serviceResponse = sut.getPackagingOptions(Segment.SEA);
			assertTrue(serviceResponse.isEmpty());
		}
	}

	@Nested
	class PackagingOptionsWithFavorites {

		@ParameterizedTest
		@MethodSource("providePackagingOptionsWithFavoritesParams")
		void receiveFavorites(String customerNumber, Segment segment, int businessDomain) {
			if (segment == Segment.AIR) {
				List<OptionDto> packagingOptionsAir = generalDataAdapterMock.getPackagingOptionsAir("en");
				when(generalDataService.getPackagingOptionsAir(anyString())).thenReturn(packagingOptionsAir);
				when(queryService.performNativeQuery(anyString())).thenReturn(List.of("BX", "CT", "PE"));
			} else {
				List<OptionDto> packagingOptionsRoad = generalDataAdapterMock.getPackagingOptionsRoad("en");
				when(generalDataService.getPackagingOptionsRoad(anyString())).thenReturn(packagingOptionsRoad);
				when(queryService.performNativeQuery(anyString())).thenReturn(List.of("KT", "C2", "ST"));
			}
			List<OptionDto> favorites = sut.getPackagingOptionsFavoritesForCustomer(customerNumber, segment);
			assertNotNull(favorites);
			assertFalse(favorites.isEmpty());
			assertEquals(3, favorites.size());

			if (segment == Segment.AIR) {
				assertEquals("BX", favorites.get(0).getCode());
				assertEquals("Box", favorites.get(0).getDescription());
				assertEquals("CT", favorites.get(1).getCode());
				assertEquals("PE", favorites.get(2).getCode());
			} else {
				assertEquals("KT", favorites.get(0).getCode());
				assertEquals("C2", favorites.get(1).getCode());
				assertEquals("ST", favorites.get(2).getCode());
			}
		}

		private static Stream<Arguments> providePackagingOptionsWithFavoritesParams() {
			return Stream.of(Arguments.of("1234567890", Segment.ROAD, BUSINESS_DOMAIN), Arguments.of("1234567890", Segment.AIR, BUSINESS_DOMAIN));
		}

	}

}