package com.dachser.dfe.book.exception;

import com.dachser.dfe.book.document.DocumentExceptionType;
import com.dachser.dfe.book.document.exception.CannotDeleteFileException;
import com.dachser.dfe.book.document.exception.CustomerDoesNotOwnDocumentException;
import com.dachser.dfe.book.document.exception.CustomerDoesNotOwnOrderException;
import com.dachser.dfe.book.document.exception.DocumentDownloadFailedException;
import com.dachser.dfe.book.document.exception.ErrorIdDocumentException;
import com.dachser.dfe.book.document.exception.FileExtensionInvalidException;
import com.dachser.dfe.book.document.exception.FileIsMaliciousException;
import com.dachser.dfe.book.document.exception.MediaTypeNotAllowedException;
import com.dachser.dfe.book.exception.generic.BadRequestException;
import com.dachser.dfe.book.exception.generic.ForbiddenException;
import com.dachser.dfe.book.exception.generic.NotAuthenticatedException;
import com.dachser.dfe.book.exception.generic.NotFoundException;
import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.model.BadGatewayProblemDto;
import com.dachser.dfe.book.model.BadRequestProblemDto;
import com.dachser.dfe.book.model.BooleanResultDto;
import com.dachser.dfe.book.model.DocumentResponseDto;
import com.dachser.dfe.book.model.ForbiddenProblemDto;
import com.dachser.dfe.book.model.GeneralProblemDto;
import com.dachser.dfe.book.model.NotFoundProblemDto;
import com.dachser.dfe.book.model.OrderProcessResultDto;
import com.dachser.dfe.book.model.OrderResponseBodyDto;
import com.dachser.dfe.book.model.OrderValidationResultDto;
import com.dachser.dfe.book.model.UnauthorizedProblemDto;
import com.dachser.dfe.book.model.ValidationResultDto;
import com.dachser.dfe.book.order.exception.DocumentZipDownloadException;
import com.dachser.dfe.book.order.exception.OrderAccessNotPermittedException;
import com.dachser.dfe.book.order.exception.OrderAddressValidationException;
import com.dachser.dfe.book.order.exception.OrderCreationFromQuoteException;
import com.dachser.dfe.book.order.exception.OrderDeletionFailedException;
import com.dachser.dfe.book.order.exception.OrderOverviewDatabaseException;
import com.dachser.dfe.book.order.exception.OrderSaveProcessException;
import com.dachser.dfe.book.order.exception.OrderStatusInvalidForDeletionException;
import com.dachser.dfe.book.order.exception.OrderSubmissionFailedException;
import com.dachser.dfe.book.transferlist.exception.TransferListDatabaseException;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.List;
import java.util.Objects;

import static com.dachser.dfe.book.exception.BookErrorId.ERR_KC_02;
import static com.dachser.dfe.book.exception.BookErrorId.ERR_UN_04;
import static com.dachser.dfe.book.exception.BookErrorId.ERR_UN_06;
import static com.dachser.dfe.book.exception.BookErrorId.ERR_UN_07;
import static com.dachser.dfe.book.exception.BookErrorId.ERR_UN_09;
import static com.dachser.dfe.book.exception.ProblemUtils.PROBLEM_URI_PREFIX;

/**
 * Adds an exception handler to each controller, all exceptions to a certain type are intercepted and response can be transformed.
 */
@ControllerAdvice
@Slf4j
@RequiredArgsConstructor
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

	private final OrderValidationErrorConverter errorConverter;

	private final Translator translate;

	@ExceptionHandler(value = { ConstraintViolationException.class })
	protected ResponseEntity<Object> handleBadRequest(RuntimeException ex, WebRequest request) {
		String bodyOfResponse = ex.getMessage();
		return handleExceptionInternal(ex, bodyOfResponse, new HttpHeaders(), HttpStatus.BAD_REQUEST, request);
	}

	@NotNull
	@Override
	protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, @NotNull HttpHeaders headers, @NotNull HttpStatusCode status,
			@NotNull WebRequest request) {
		final List<ObjectError> allErrors = ex.getAllErrors();
		if (request instanceof ServletWebRequest webRequest && webRequest.getRequest().getRequestURI().contains("/orders/")) {
			OrderProcessResultDto validationResultDto = errorConverter.convertToValidationResults(allErrors);
			if (validationResultDto != null) {
				return handleExceptionInternal(ex, validationResultDto, headers, status, request);
			}

		} else {
			log.debug("Not a ServletWebRequest, cannot convert to ValidationResultDto");
		}

		return handleExceptionInternal(ex, allErrors, headers, status, request);
	}

	@ExceptionHandler(value = { MediaTypeNotAllowedException.class })
	protected ResponseEntity<Object> handleExtensionNotAllowedException(RuntimeException ex, WebRequest request) {
		DocumentResponseDto documentResponse = new DocumentResponseDto().error("Filetype is not allowed")
				.uploadStatus(DocumentResponseDto.UploadStatusEnum.ERROR_EXTENSION_NOT_ALLOWED);
		return handleExceptionInternal(ex, documentResponse, new HttpHeaders(), HttpStatus.BAD_REQUEST, request);
	}

	@ExceptionHandler(value = { FileExtensionInvalidException.class })
	protected ResponseEntity<Object> handleInvalidFileExtensionException(RuntimeException ex, WebRequest request) {
		DocumentResponseDto documentResponse = new DocumentResponseDto().error("Filetype is not valid").uploadStatus(DocumentResponseDto.UploadStatusEnum.ERROR_INVALID_EXTENSION);
		return handleExceptionInternal(ex, documentResponse, new HttpHeaders(), HttpStatus.BAD_REQUEST, request);
	}

	@ExceptionHandler(value = { FileIsMaliciousException.class })
	protected ResponseEntity<Object> handleFileIsMaliciousException(RuntimeException ex, WebRequest request) {
		DocumentResponseDto documentResponse = new DocumentResponseDto().error("File was flagged as malicious").uploadStatus(DocumentResponseDto.UploadStatusEnum.FILE_MALICIOUS);
		return handleExceptionInternal(ex, documentResponse, new HttpHeaders(), HttpStatus.BAD_REQUEST, request);
	}

	@ExceptionHandler(value = { OrderAccessNotPermittedException.class, AccessDeniedException.class })
	protected ResponseEntity<Object> handleNotPermitted(RuntimeException ex) {
		log.error(ex.getMessage(), ex);
		final var why = ProblemUtils.populateProblemData(new NotFoundProblemDto(), ERR_UN_07, PROBLEM_URI_PREFIX).title("Not Found")
				.detail(translate.toLocale(ERR_UN_07.getI18nDetailsLabel()));
		return ResponseEntity.status(HttpStatus.NOT_FOUND).body(why);
	}

	@ExceptionHandler(value = { CannotDeleteFileException.class })
	protected ResponseEntity<Object> handleCannotDeleteFile(RuntimeException ex, WebRequest request) {
		return ResponseEntity.ok(new BooleanResultDto().result(false));
	}

	@ExceptionHandler(value = { CustomerDoesNotOwnOrderException.class })
	protected ResponseEntity<Object> handleUnknownExtensionException(CustomerDoesNotOwnOrderException ex, WebRequest request) {
		DocumentExceptionType type = ex.getType();
		if (type == DocumentExceptionType.UPDATE) {
			return ResponseEntity.ok(new BooleanResultDto().result(false));
		} else {
			return handleExceptionInternal(ex.getMessage(), DocumentResponseDto.UploadStatusEnum.UNSPECIFIC_ERROR);
		}
	}

	@ExceptionHandler(value = { CustomerDoesNotOwnDocumentException.class })
	protected ResponseEntity<Object> handlePrincipalDoesNotOwnDocumentException(RuntimeException ex, WebRequest request) {
		return ResponseEntity.ok(new BooleanResultDto().result(false));
	}

	@ExceptionHandler(value = { LabelsCouldNotBeCreatedException.class })
	protected ResponseEntity<Object> handleGeneralRestAPIException(RuntimeException ex, WebRequest request) {
		log.error(ex.getMessage());
		String bodyOfResponse = ex.getMessage();
		return handleExceptionInternal(ex, bodyOfResponse, new HttpHeaders(), HttpStatus.BAD_GATEWAY, request);
	}

	@ExceptionHandler(value = { ErrorIdExternalServiceNotAvailableException.class, ErrorIdDocumentException.class })
	protected ResponseEntity<Object> handleErrorIdBadGateWayException(ErrorIdBaseException ex, WebRequest request) {
		return handleErrorIdException(ex, request, new BadGatewayProblemDto(), HttpStatus.BAD_GATEWAY);
	}

	@ExceptionHandler(value = { ErrorIdConstraintValidatorBaseException.class })
	protected ResponseEntity<Object> handleErrorIdConstraintValidatorException(ErrorIdConstraintValidatorBaseException ex, WebRequest request) {
		return handleErrorIdException(ex, request, new BadGatewayProblemDto(), HttpStatus.BAD_GATEWAY);
	}

	@ExceptionHandler(value = { DocumentZipDownloadException.class, DocumentDownloadFailedException.class, ExtDeliveryProductServiceNotAvailable.class,
			LabelsCouldNotBeMergedException.class, OrderOverviewDatabaseException.class, ExtRelationLabelPrintingServiceNotAvailable.class, TransferListDatabaseException.class })
	protected ResponseEntity<Object> handleErrorIdGeneralException(ErrorIdBaseException ex, WebRequest request) {
		return handleErrorIdException(ex, request, new GeneralProblemDto(), HttpStatus.INTERNAL_SERVER_ERROR);
	}

	@ExceptionHandler(value = { OrderCreationFromQuoteException.class, OrderStatusInvalidForDeletionException.class })
	protected ResponseEntity<Object> handleErrorIdBadRequestException(ErrorIdBaseException ex, WebRequest request) {
		return handleErrorIdException(ex, request, new BadRequestProblemDto(), HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(value = { OrderDeletionFailedException.class })
	protected ResponseEntity<Object> handleErrorIdOrderDeletionException(ErrorIdBaseException ex, WebRequest request) {
		return handleErrorIdException(ex, request, new GeneralProblemDto(), HttpStatus.INTERNAL_SERVER_ERROR);
	}

	@ExceptionHandler(value = { ErrorIdBaseException.class })
	protected ResponseEntity<Object> handleBaseErrorIdFallback(ErrorIdBaseException ex, WebRequest request) {
		return handleErrorIdException(ex, request, new GeneralProblemDto().status(HttpStatus.INTERNAL_SERVER_ERROR.value()), HttpStatus.INTERNAL_SERVER_ERROR);
	}

	@ExceptionHandler(value = { OrderSaveProcessException.class })
	protected ResponseEntity<Object> handleOrderSubmissionFailedException(OrderSaveProcessException ex, WebRequest request) {
		final OrderProcessResultDto orderProcessResult = new OrderProcessResultDto();
		if (ex.getValidationResult() != null) {
			orderProcessResult.setValidationResult(ex.getValidationResult());
		} else {
			orderProcessResult.setValidationResult(new OrderValidationResultDto().valid(true).newOrderStatus(ex.getOrderDto().getStatus()));
		}
		orderProcessResult.order((OrderResponseBodyDto) ex.getOrderDto());
		if (ex.getErrorId() != null) {
			orderProcessResult.error(
					ProblemUtils.populateProblemData(new GeneralProblemDto(), ex.getErrorId(), PROBLEM_URI_PREFIX).detail(ex.getErrorId().getI18nDetailsLabel()));
		}
		HttpStatus returnStatus = resolveHttpStatusForOrderSaveProcess(ex);
		return handleExceptionInternal(ex, orderProcessResult, new HttpHeaders(), returnStatus, request);
	}

	@ExceptionHandler(value = { OrderAddressValidationException.class })
	protected ResponseEntity<Object> handleOrderSubmissionFailedException(OrderAddressValidationException ex, WebRequest request) {
		final ValidationResultDto orderProcessResult = new ValidationResultDto();
		if (ex.getValidationResult().getResults() != null) {
			orderProcessResult.setResults(ex.getValidationResult().getResults());
			orderProcessResult.setValid(false);
		} else {
			orderProcessResult.setValid(true);
		}
		return handleExceptionInternal(ex, orderProcessResult, new HttpHeaders(), HttpStatus.BAD_REQUEST, request);
	}

	@ExceptionHandler(value = { ErrorIdOrderExpiredException.class })
	protected ResponseEntity<Object> handleConflictException(ErrorIdOrderExpiredException ex, WebRequest request) {
		return handleErrorIdException(ex, request, new GeneralProblemDto().status(HttpStatus.CONFLICT.value()), HttpStatus.CONFLICT);
	}

	// 400
	@ExceptionHandler({ BadRequestException.class })
	@ResponseBody
	public ResponseEntity<BadRequestProblemDto> handleThrowable(BadRequestException e) {
		log.error(e.getMessage(), e);
		final var why = ProblemUtils.populateProblemData(new BadRequestProblemDto(), ERR_UN_04, PROBLEM_URI_PREFIX).title(e.getMessage())
				.detail(translate.toLocale(ERR_UN_04.getI18nDetailsLabel()));
		return ResponseEntity.status(HttpStatus.BAD_REQUEST).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_PROBLEM_JSON_VALUE).body(why);
	}

	// 401
	@ExceptionHandler({ NotAuthenticatedException.class })
	@ResponseBody
	public ResponseEntity<UnauthorizedProblemDto> handleThrowable(NotAuthenticatedException e) {
		log.error(e.getMessage(), e);
		final var why = ProblemUtils.populateProblemData(new UnauthorizedProblemDto(), ERR_KC_02, PROBLEM_URI_PREFIX).title("Unauthorized")
				.detail(translate.toLocale(ERR_KC_02.getI18nDetailsLabel()));
		return ResponseEntity.status(HttpStatus.UNAUTHORIZED).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_PROBLEM_JSON_VALUE).body(why);
	}

	// 403
	@ExceptionHandler({ ForbiddenException.class })
	@ResponseBody
	public ResponseEntity<ForbiddenProblemDto> handleThrowable(ForbiddenException e) {
		log.error(e.getMessage(), e);
		final var why = ProblemUtils.populateProblemData(new ForbiddenProblemDto(), ERR_UN_06, PROBLEM_URI_PREFIX).title("Forbidden")
				.detail(translate.toLocale(ERR_UN_06.getI18nDetailsLabel()));
		return ResponseEntity.status(HttpStatus.FORBIDDEN).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_PROBLEM_JSON_VALUE).body(why);
	}

	// 404
	@ExceptionHandler({ NotFoundException.class })
	@ResponseBody
	public ResponseEntity<NotFoundProblemDto> handleThrowable(NotFoundException e) {
		log.error(e.getMessage(), e);
		final var why = ProblemUtils.populateProblemData(new NotFoundProblemDto(), ERR_UN_07, PROBLEM_URI_PREFIX).title(e.getMessage())
				.detail(translate.toLocale(ERR_UN_07.getI18nDetailsLabel()));
		return ResponseEntity.status(HttpStatus.NOT_FOUND).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_PROBLEM_JSON_VALUE).body(why);
	}

	// 500
	@ExceptionHandler({ RuntimeException.class })
	@ResponseBody
	public ResponseEntity<GeneralProblemDto> handleThrowable(RuntimeException e) {
		log.error(e.getMessage(), e);
		final var why = ProblemUtils.populateProblemData(new GeneralProblemDto(), ERR_UN_09, PROBLEM_URI_PREFIX).title(e.getMessage())
				.detail(translate.toLocale(ERR_UN_09.getI18nDetailsLabel()));
		return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_PROBLEM_JSON_VALUE).body(why);
	}

	@NotNull
	private static HttpStatus resolveHttpStatusForOrderSaveProcess(OrderSaveProcessException ex) {
		HttpStatus returnStatus = HttpStatus.BAD_REQUEST;
		if (ex.getCause() instanceof LabelsCouldNotBeCreatedException) {
			returnStatus = HttpStatus.BAD_GATEWAY;
		} else if (ex.getCause() instanceof LabelsCouldNotBeMergedException) {
			returnStatus = HttpStatus.INTERNAL_SERVER_ERROR;
		} else if (ex instanceof OrderSubmissionFailedException) {
			returnStatus = HttpStatus.BAD_GATEWAY;
		}
		return returnStatus;
	}

	private ResponseEntity<Object> handleErrorIdException(ErrorIdBaseException ex, WebRequest request, GeneralProblemDto problem, HttpStatus defaultStatus) {
		return handleErrorId(ex.getErrorId(), problem, ex.getMessage(), handleExceptionInternal(ex, ex.getMessage(), new HttpHeaders(), defaultStatus, request));
	}

	private ResponseEntity<Object> handleErrorIdException(ErrorIdConstraintValidatorBaseException ex, WebRequest request, GeneralProblemDto problem, HttpStatus defaultStatus) {
		return handleErrorId(ex.getErrorId(), problem, ex.getMessage(), handleExceptionInternal(ex, ex.getMessage(), new HttpHeaders(), defaultStatus, request));
	}

	private ResponseEntity<Object> handleErrorId(BookErrorId ex, GeneralProblemDto problem, String ex1, ResponseEntity<Object> ex2) {
		if (Objects.nonNull(ex)) {
			problem = ProblemUtils.populateProblemData(problem, ex, PROBLEM_URI_PREFIX).detail(ex.getI18nDetailsLabel());
			log.error("%s - %s".formatted(ex, ex1));
			return new ResponseEntity<>(problem, HttpStatus.valueOf(problem.getStatus()));
		}
		return ex2;
	}

	private ResponseEntity<Object> handleExceptionInternal(String errorMessage, DocumentResponseDto.UploadStatusEnum uploadStatusEnum) {
		DocumentResponseDto response = new DocumentResponseDto();
		response.setError(errorMessage);
		response.setUploadStatus(uploadStatusEnum);
		return ResponseEntity.ok(response);
	}

}
