package com.dachser.dfe.book.dip.converter.sea;

import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.config.term.TermSorter;
import com.dachser.dfe.book.dip.converter.DipOrderConverter;
import com.dachser.dfe.book.dip.converter.shared.AirSeaDocumentHeaderMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.AirSeaGoodsDescriptionMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderContactCommunicationsMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.DateMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.StringNotEmptyConditionMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.dip.xml.XmlConverter;
import com.dachser.dfe.book.generaldata.ContainerTypeMapperImpl;
import com.dachser.dfe.book.generaldata.GeneralDataAdapterMock;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.generaldata.term.TermMapperImpl;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.common.fcl.FullContainerLoad;
import com.dachser.dfe.book.order.sea.BaseFullContainerLoadRepository;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaImportOrder;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.book.order.sea.SeaOrderLine;
import com.dachser.dfe.book.order.sea.SeaOrderLineHsCode;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
@SpringBootTest(properties = "dfe.book.mock=true", classes = { SeaExportDipOrderConverter.class, SeaImportDipOrderConverter.class, XmlConverter.class, SeaOrderMapperImpl.class,
		DateMapperImpl.class, AirSeaDocumentHeaderMapperImpl.class, SeaFreightMapperImpl.class, SeaPickupOrderMapperImpl.class, AirSeaOrderContactCommunicationsMapperImpl.class,
		SeaOrderLineMapperImpl.class, SeaOrderReferenceMapperImpl.class, SeaDeliveryOrderMapperImpl.class, StringNotEmptyConditionMapperImpl.class,
		AirSeaGoodsDescriptionMapperImpl.class, SeaOrderCategoryMapperImpl.class, SeaContainerMapperImpl.class, GeneralDataAdapterMock.class, GeneralDataService.class,
		ContainerTypeMapperImpl.class, WhiteListFilterConfiguration.class, TermMapperImpl.class, TermSorter.class })
class SeaDipOrderConverterTest implements ResourceLoadingTest {

	@Autowired
	private SeaExportDipOrderConverter seaExportEdiOrderConverter;

	@Autowired
	private SeaImportDipOrderConverter seaImportEdiOrderConverter;

	@Autowired
	private XmlConverter xmlConverter;

	// Dependency of Generaldata service
	@MockBean
	BaseFullContainerLoadRepository repository;

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	@Nested
	class ConvertToXML {

		SeaExportOrder seaExportOrder = testUtil.generateSeaExportOrder();

		SeaImportOrder seaImportOrder = testUtil.generateSeaImportOrder();

		@Nested
		class SeaExport {
			@Test
			void shouldConvertSeaExportOrderToXML() throws IOException {
				setValidCollectionDates(seaExportOrder);
				final String convertToXML = seaExportEdiOrderConverter.convertToXML(seaExportOrder);
				String expectedXML = loadXMLResourceToString("orders/send/sea/valid-generated-sea-order-export.xml");
				assertEquals(expectedXML, convertToXML);
			}

			@Test
			void shouldConvertSeaExportOrderToXMLWithTwoOrderLines() throws IOException {
				setValidCollectionDates(seaExportOrder);
				SeaOrderLine seaOrderLine = testUtil.generateSeaOrderLine();
				seaOrderLine.setMarkAndNumbers("Another sea orderline marks and numbers");
				seaExportOrder.addOrderLine(seaOrderLine);
				final String convertToXML = seaExportEdiOrderConverter.convertToXML(seaExportOrder);
				String expectedXML = loadXMLResourceToString("orders/send/sea/valid-generated-sea-order-export-two-orderlines.xml");
				assertEquals(expectedXML, convertToXML);
			}

			@Test
			void shouldConvertSeaExportDeliveryOrderToXML() throws IOException {
				String expectedXML = loadXMLResourceToString("orders/send/sea/valid-generated-sea-delivery-order-export.xml");
				final SeaExportOrder seaExportOrder = testUtil.generateSeaExportOrder();
				appendHSCodeWithDescription(seaExportOrder);
				// Should use from iata for mapping but this field changes generate type to delivery order
				seaExportOrder.setDeliverToPort(true);
				seaExportOrder.setShockSensitive(true);
				seaExportOrder.setStackable(false);
				setValidCollectionDates(seaExportOrder);

				final String convertToXML = seaExportEdiOrderConverter.convertToXML(seaExportOrder);
				assertEquals(expectedXML, convertToXML);
			}

			@Test
			void shouldConvertSeaExportDeliveryOrderWithoutDatesToXML() throws IOException {
				String expectedXML = loadXMLResourceToString("orders/send/sea/valid-generated-sea-delivery-order-request-arrangement-export.xml");
				final SeaExportOrder seaExportOrder = testUtil.generateSeaExportOrder();
				appendHSCodeWithDescription(seaExportOrder);
				seaExportOrder.setRequestArrangement(true);
				seaExportOrder.setDeliverToPort(true);
				seaExportOrder.setCollectionDate(null);
				seaExportOrder.setCollectionFrom(null);
				seaExportOrder.setCollectionTo(null);
				// Should use from iata for mapping but this field changes generate type to delivery order

				final String convertToXML = seaExportEdiOrderConverter.convertToXML(seaExportOrder);
				assertEquals(expectedXML, convertToXML);
			}

			@Test
			void shouldConvertFCLSeaExportOrderToXML() throws IOException {
				setValidCollectionDates(seaExportOrder);
				asFCLOrder(seaExportOrder);
				final String convertToXML = seaExportEdiOrderConverter.convertToXML(seaExportOrder);

				String expectedXML = loadXMLResourceToString("orders/send/sea/valid-generated-sea-order-export-fcl.xml");
				assertEquals(expectedXML, convertToXML);
			}

			@Test
			void shouldSkipAdditionInformationIfMarksAndNumbersIsEmptyToXML() throws IOException {
				String expectedXML = loadXMLResourceToString("orders/send/sea/valid-generated-sea-delivery-order-export-without-marks-numbers.xml");
				setValidCollectionDates(seaExportOrder);
				seaExportOrder.setDeliverToPort(true);
				seaExportOrder.getOrderLines().forEach(orderLine -> {
					orderLine.setMarkAndNumbers("");
				});

				final String convertToXML = seaExportEdiOrderConverter.convertToXML(seaExportOrder);
				assertEquals(expectedXML, convertToXML);
			}
		}

		private static void asFCLOrder(SeaOrder seaOrder) {
			FullContainerLoad fullContainerLoad = new FullContainerLoad();
			fullContainerLoad.setOrderLines(seaOrder.getOrderLines());
			fullContainerLoad.setId(15L);
			fullContainerLoad.getOrderLines().forEach(orderLine -> orderLine.setFullContainerLoadId(fullContainerLoad.getId()));
			fullContainerLoad.setContainerNumber("ABCJ1236541");
			fullContainerLoad.setContainerType("GP-20");
			fullContainerLoad.setVerifiedGrossMass(1000);
			fullContainerLoad.setSortingPosition(0);
			seaOrder.setFullContainerLoads(List.of(fullContainerLoad));
			seaOrder.setFullContainerLoad(true);
			seaOrder.setOrderLines(List.of());
		}

		@Nested
		class SeaImport {
			@Test
			void shouldConvertSeaImportOrderToXML() throws IOException {
				setValidCollectionDates(seaImportOrder);
				final String convertToXML = seaImportEdiOrderConverter.convertToXML(seaImportOrder);
				String expectedXML = loadXMLResourceToString("orders/send/sea/valid-generated-sea-order-import.xml");
				assertEquals(expectedXML, convertToXML);
			}

			@Test
			void shouldConvertSeaImportDeliveryOrderToXML() throws IOException {
				String expectedXML = loadXMLResourceToString("orders/send/sea/valid-generated-sea-delivery-order-import.xml");
				final SeaImportOrder seaImport = testUtil.generateSeaImportOrder();
				appendHSCodeWithDescription(seaImport);
				// Should use from iata for mapping but this field changes generate type to delivery order
				seaImport.setDeliverToPort(true);
				seaImport.setShockSensitive(true);
				seaImport.setStackable(false);
				setValidCollectionDates(seaImport);

				final String convertToXML = seaImportEdiOrderConverter.convertToXML(seaImport);
				assertEquals(expectedXML, convertToXML);
			}

			@Test
			void shouldConvertSeaImportDeliveryOrderWithoutDatesToXML() throws IOException {
				String expectedXML = loadXMLResourceToString("orders/send/sea/valid-generated-sea-delivery-order-request-arrangement-import.xml");
				final SeaImportOrder seaImportOrder = testUtil.generateSeaImportOrder();
				appendHSCodeWithDescription(seaImportOrder);
				seaImportOrder.setRequestArrangement(true);
				seaImportOrder.setDeliverToPort(true);
				seaImportOrder.setCollectionDate(null);
				seaImportOrder.setCollectionFrom(null);
				seaImportOrder.setCollectionTo(null);
				// Should use from iata for mapping but this field changes generate type to delivery order

				final String convertToXML = seaImportEdiOrderConverter.convertToXML(seaImportOrder);
				assertEquals(expectedXML, convertToXML);
			}

			@Test
			void shouldUseImporterFromFurtherAddress() throws IOException {
				String expectedXML = loadXMLResourceToString("orders/send/sea/valid-generated-sea-order-with-importer.xml");
				final SeaImportOrder seaImportOrder = testUtil.generateSeaImportOrder();
				setValidCollectionDates(seaImportOrder);
				seaImportOrder.addAddress(TestUtil.buildSeaImporterAddress());
				appendHSCodeWithDescription(seaImportOrder);
				seaImportOrder.setDeliverToPort(true);

				final String convertToXML = seaImportEdiOrderConverter.convertToXML(seaImportOrder);
				assertEquals(expectedXML, convertToXML);
			}

			@Test
			void shouldConvertFCLSeaImportOrderToXML() throws IOException {
				setValidCollectionDates(seaImportOrder);
				asFCLOrder(seaImportOrder);
				final String convertToXML = seaImportEdiOrderConverter.convertToXML(seaImportOrder);

				String expectedXML = loadXMLResourceToString("orders/send/sea/valid-generated-sea-order-import-fcl.xml");
				assertEquals(expectedXML, convertToXML);
			}

			@Test
			void shouldMapSeaShipperAddressIfCustNoMissing() {
				final SeaImportOrder seaImportOrder = testUtil.generateSeaImportOrder();

				seaImportOrder.getShipperAddress().setCustomerNumber(null);

				seaImportOrder.getShipperAddress().setName("Shipper Name");
				seaImportOrder.getShipperAddress().setStreet("Shipper Street");
				seaImportOrder.getShipperAddress().setPostcode("Shipper Zip");
				seaImportOrder.getShipperAddress().setCity("Shipper City");
				seaImportOrder.getShipperAddress().setCountryCode("DE");

				final String convertedXml = seaImportEdiOrderConverter.convertToXML(seaImportOrder);

				assertTrue(convertedXml.contains("<ShipmentAddress TypeOfAddress=\"Consignor\">"));
				assertTrue(convertedXml.contains("<Name1>Shipper Name</Name1>"));
				assertTrue(convertedXml.contains("<Street1>Shipper Street</Street1>"));
				assertTrue(convertedXml.contains("<PostalCode>Shipper Zip</PostalCode>"));
				assertTrue(convertedXml.contains("<CityName>Shipper City</CityName>"));
				assertTrue(convertedXml.contains("<CountryCode>DE</CountryCode>"));
			}

			@Test
			void shouldMapSeaShipperCustNo() {
				final SeaImportOrder seaImportOrder = testUtil.generateSeaImportOrder();

				seaImportOrder.getShipperAddress().setName("Shipper Name");

				final String convertedXml = seaImportEdiOrderConverter.convertToXML(seaImportOrder);

				assertTrue(convertedXml.contains("<ShipmentAddress TypeOfAddress=\"Consignor\">"));
				assertTrue(convertedXml.contains("<AddressID>"));
				assertFalse(convertedXml.contains("<Name1>Shipper Name</Name1>"));
			}

		}

		@Nested
		class AddressTests {
			@Test
			void useConsigneeAsDeliveryAddress() throws IOException, JAXBException {
				final ForwardingOrder forwardingOrder = loadValidBase();
				seaExportOrder.setDeliveryAddress(null);
				seaExportOrder.setStackable(false);
				setValidCollectionDates(seaExportOrder);
				final ForwardingOrder.SeaFreightShipment airFreightShipment = forwardingOrder.getSeaFreightShipment().get(0);
				final Optional<ForwardingOrder.SeaFreightShipment.ShipmentAddress> newShipmentList = airFreightShipment.getShipmentAddress().stream()
						.filter(address -> address.getTypeOfAddress().equals(Types.AddressTypes.ULTIMATE_CONSIGNEE)).findFirst();
				newShipmentList.ifPresent(shipmentAddress -> airFreightShipment.getShipmentAddress().remove(shipmentAddress));
				airFreightShipment.getPickupOrder().get(0).getOrderReference().stream()
						.filter(orderReference -> orderReference.getTypeOfReference().equals(AirSeaOrderReferenceType.SHIPPERS_REFERENCE.getEdiReferenceType()))
						.forEach(orderReference -> orderReference.setHandlingInstruction("NST"));

				assertByXMLComparison(forwardingOrder, seaExportOrder);
			}

			private void assertByXMLComparison(ForwardingOrder forwardingOrder, SeaExportOrder airExportOrder) {
				final String airExportBasedXML = seaExportEdiOrderConverter.convertToXML(airExportOrder);
				final String targetObjectSerialized = xmlConverter.convertToXml(forwardingOrder);
				assertEquals(targetObjectSerialized, airExportBasedXML);
			}

			private ForwardingOrder loadValidBase() throws JAXBException, IOException {
				JAXBContext jaxbContext = JAXBContext.newInstance(ForwardingOrder.class);
				Unmarshaller jaxbMarshaller = jaxbContext.createUnmarshaller();
				return (ForwardingOrder) jaxbMarshaller.unmarshal(new StringReader(loadXMLResourceToString("orders/send/sea/valid-generated-sea-order-export.xml")));
			}
		}
	}

	private static void appendHSCodeWithDescription(SeaOrder airExportOrder) {
		final List<SeaOrderLineHsCode> hsCodes = airExportOrder.getOrderLines().get(0).getHsCodes();

		for (int i = 0; i < 10; i++) {
			SeaOrderLineHsCode seaOrderLineHsCode = new SeaOrderLineHsCode();
			seaOrderLineHsCode.setHsCode("123456");
			seaOrderLineHsCode.setGoods("Lorem ipsum text with some more chars (" + i + ") to come");
			hsCodes.add(seaOrderLineHsCode);
		}
	}

	private static void setValidCollectionDates(SeaOrder airExportOrder) {
		airExportOrder.setCollectionDate(LocalDate.of(2022, 9, 1).atStartOfDay().toLocalDate());
		airExportOrder.setCollectionFrom(OffsetDateTime.of(2022, 9, 1, 10, 0, 0, 0, ZoneOffset.UTC));
		airExportOrder.setCollectionTo(OffsetDateTime.of(2022, 9, 1, 15, 0, 0, 0, ZoneOffset.UTC));
	}

	@Nested
	class Supports {

		@Test
		void shouldSupportAirExportOrder() {
			final boolean supports = seaExportEdiOrderConverter.supports(new SeaExportOrder());
			assertTrue(supports);
		}

		@Test
		void shouldNotSupportForwardingOrder() {
			final DipOrderConverter airOrderConverter1 = seaExportEdiOrderConverter;
			final boolean supports = airOrderConverter1.supports(new com.dachser.dfe.book.order.road.ForwardingOrder());
			assertFalse(supports);
		}
	}

	@Nested
	class OrderPosition {

		@Test
		void shouldSkipZeroValuesForOrderPositionAndDimension() {
			final SeaExportOrder seaExportOrder = testUtil.generateSeaExportOrder();

			seaExportOrder.getOrderLines().get(0).setLength(0);
			seaExportOrder.getOrderLines().get(0).setWidth(0);
			seaExportOrder.getOrderLines().get(0).setHeight(0);
			seaExportOrder.getOrderLines().get(0).setWeight(BigDecimal.valueOf(0L));
			seaExportOrder.getOrderLines().get(0).setVolume(BigDecimal.valueOf(0));
			seaExportOrder.getOrderLines().get(0).setQuantity(0);

			final String convertToXML = seaExportEdiOrderConverter.convertToXML(seaExportOrder);

			assertFalse(convertToXML.contains("<Length>"));
			assertFalse(convertToXML.contains("<Width>"));
			assertFalse(convertToXML.contains("<Height>"));
			assertFalse(convertToXML.contains("<GrossWeight"));
			assertFalse(convertToXML.contains("<Volume"));
			assertFalse(convertToXML.contains("<Pieces>"));
		}
	}

}