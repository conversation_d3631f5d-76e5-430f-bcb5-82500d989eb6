package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.hscode.HsCode;
import com.dachser.dfe.book.hscode.HsCodeService;
import com.dachser.dfe.book.mapper.OrderStatusMapperImpl;
import com.dachser.dfe.book.mapper.custom.TranslationMapper;
import com.dachser.dfe.book.model.AirExportOrderDto;
import com.dachser.dfe.book.model.AirExportQuoteInformationDto;
import com.dachser.dfe.book.model.AirImportOrderDto;
import com.dachser.dfe.book.model.AirOrderLineDto;
import com.dachser.dfe.book.model.AirSeaOrderLineHsCodeDto;
import com.dachser.dfe.book.model.AirSeaOrderReferenceDto;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.CollectionOptionDto;
import com.dachser.dfe.book.model.CollectionTimeSlotDto;
import com.dachser.dfe.book.model.CustomsTypeDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.DisplayableOrderStatusDto;
import com.dachser.dfe.book.model.FreightTermDto;
import com.dachser.dfe.book.model.GoodsGroupDto;
import com.dachser.dfe.book.model.IncoTermDto;
import com.dachser.dfe.book.model.Interpreter;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderAddressDto;
import com.dachser.dfe.book.model.OrderContactDataDto;
import com.dachser.dfe.book.model.OrderReferenceTypeDto;
import com.dachser.dfe.book.model.OrderResponseBodyDto;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.OrderStatusDto;
import com.dachser.dfe.book.model.OrderTextDto;
import com.dachser.dfe.book.model.PortDto;
import com.dachser.dfe.book.model.RoadCollectionOrderDto;
import com.dachser.dfe.book.model.RoadForwardingOrderDto;
import com.dachser.dfe.book.model.RoadForwardingQuoteInformationDto;
import com.dachser.dfe.book.model.RoadOrderLineDto;
import com.dachser.dfe.book.model.RoadOrderReferenceDto;
import com.dachser.dfe.book.model.SeaExportOrderDto;
import com.dachser.dfe.book.model.SeaImportOrderDto;
import com.dachser.dfe.book.model.SeaOrderLineDto;
import com.dachser.dfe.book.model.SourceOfOrder;
import com.dachser.dfe.book.model.options.CollectionOptions;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.air.AirOrderLine;
import com.dachser.dfe.book.order.air.AirOrderLineHsCode;
import com.dachser.dfe.book.order.air.AirOrderReference;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.common.LockedByQuoteEnum;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderBaseAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.common.OrderSscc;
import com.dachser.dfe.book.order.common.OrderText;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.common.fcl.FullContainerLoad;
import com.dachser.dfe.book.order.common.orderline.OrderLine;
import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.order.generator.OrderGenerator;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaImportOrder;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.book.order.sea.SeaOrderLine;
import com.dachser.dfe.book.order.sea.SeaOrderLineHsCode;
import com.dachser.dfe.book.order.sea.SeaOrderReference;
import com.dachser.dfe.book.port.PortMapperImpl;
import com.dachser.dfe.book.port.PortRepository;
import com.dachser.dfe.book.port.entity.Port;
import com.dachser.dfe.book.port.entity.Type;
import com.dachser.dfe.book.product.air.AirProductService;
import com.dachser.dfe.book.quote.AirQuoteInformation;
import com.dachser.dfe.book.quote.QuoteMapperImpl;
import com.dachser.dfe.book.quote.QuotePackingPositionMapperImpl;
import com.dachser.dfe.book.quote.SeaQuoteInformation;
import com.dachser.dfe.book.service.freightterm.FreightTermService;
import com.dachser.dfe.book.term.IncoTermService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.dachser.dfe.book.model.OrderReferenceTypeDto.PURCHASE_ORDER_NUMBER;
import static com.dachser.dfe.book.model.OrderReferenceTypeDto.SHIPPERS_REFERENCE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = { OrderTypeMapperImpl.class, OrderMapperImpl.class, FreightTermCodeMapperImpl.class, IncoTermCodeMapperImpl.class, OrderTextMapperImpl.class,
		OrderReferenceMapperImpl.class, OrderLineMapperImpl.class, OrderContactMapperImpl.class, OrderStatusMapperImpl.class, PortMapperImpl.class, OrderLineHsCodeMapperImpl.class,
		OrderAddressMapperImpl.class, QuoteMapperImpl.class, DeliveryOptionMapperImpl.class, QuotePackingPositionMapperImpl.class, PackingPositionMapperImpl.class,
		FullContainerLoadMapperImpl.class, FullContainerLoadMapperImpl.class })
public class OrderMapperTest {

	@Autowired
	private OrderMapper orderMapper;

	@Autowired
	private OrderAddressMapper orderAddressMapper;

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	@MockBean
	private FreightTermService freightTermService;

	@MockBean
	private GeneralDataService generalDataService;

	@MockBean
	private PortRepository portRepository;

	@MockBean
	private IncoTermService incoTermService;

	@MockBean
	private TranslationMapper translationMapper;

	@MockBean
	private HsCodeService hsCodeService;

	@MockBean
	private AirProductService airProductService;

	@MockBean
	private DangerousGoodsMapper dangerousGoodsMapper;

	@BeforeEach
	void setUp() {
		FreightTermDto freightTerm = new FreightTermDto();
		freightTerm.setDachserTermKey("011");
		freightTerm.setIncoTermKey("EXW");
		freightTerm.setHeadline("Ex Works");

		Port airportMuc = new Port();
		airportMuc.setCode("MUC");
		airportMuc.setName("München");
		airportMuc.setCountryCode("DE");
		airportMuc.setType(Type.AIRPORT);

		Port airportFra = new Port();
		airportFra.setCode("FRA");
		airportFra.setName("Frankfurt");
		airportFra.setCountryCode("DE");
		airportFra.setType(Type.AIRPORT);

		Port airportLHR = new Port();
		airportLHR.setCode("LHR");
		airportLHR.setName("London Heathrow");
		airportLHR.setCountryCode("GB");
		airportLHR.setType(Type.AIRPORT);

		Port seaPortHam = new Port();
		seaPortHam.setCode("DEHAM");
		seaPortHam.setName("HAMBURG");
		seaPortHam.setCountryCode("DE");
		seaPortHam.setType(Type.SEAPORT);

		Port seaPortHongKong = new Port();
		seaPortHongKong.setCode("HKHKG");
		seaPortHongKong.setName("HONGKONG");
		seaPortHongKong.setCountryCode("DE");
		seaPortHongKong.setType(Type.SEAPORT);

		HsCode hsCode = new HsCode();
		hsCode.setId("42");
		hsCode.setDescription("hs code description");
		when(hsCodeService.searchHsCodes(anyString())).thenReturn(List.of(hsCode));

		IncoTermDto incoTerm = new IncoTermDto().code("DPA").id(1L).dachserCode("TEST").description("Test code");
		when(incoTermService.getIncoTermByDachserCode("DPA")).thenReturn(incoTerm);

		when(freightTermService.getFreightTermByDachserKey("011")).thenReturn(freightTerm);
		when(portRepository.findByCode("MUC")).thenReturn(Optional.of(airportMuc));
		when(portRepository.findByCode("FRA")).thenReturn(Optional.of(airportFra));
		when(portRepository.findByCode("LHR")).thenReturn(Optional.of(airportLHR));
		when(portRepository.findByCode("DEHAM")).thenReturn(Optional.of(seaPortHam));
		when(portRepository.findByCode("HKHKG")).thenReturn(Optional.of(seaPortHongKong));
		when(translationMapper.translatedLabel(anyString())).thenAnswer(i -> i.getArguments()[0]);
	}

	@Nested
	class MapCollectionOrder {

		@Nested
		class ToEntity {

			@Test
			void shouldMapFields() {
				final OffsetDateTime fromTime = OffsetDateTime.of(2022, 9, 1, 10, 0, 0, 0, ZoneOffset.UTC);
				final RoadCollectionOrderDto collectionOrderDto = getJsonRoadCollectionOrder(fromTime);
				final CollectionOrder collectionOrder = (CollectionOrder) orderMapper.map(collectionOrderDto);
				assertEquals("OG", collectionOrder.getOrderGroup());
				assertNotNull(collectionOrder.getCustomerNumber());
				assertEquals("name_delivery", collectionOrder.getDeliveryContact().getName());
				assertEquals("name_different_consignee_address", collectionOrder.getDifferentConsigneeAddress().getName());
				assertEquals(CollectionOptions.BOOKING, collectionOrder.getCollectionOption());
				assertEquals(fromTime, collectionOrder.getCollectionFrom());
				assertEquals(Interpreter.FIX, collectionOrder.getInterpreter());
				assertEquals(OrderStatus.DRAFT, collectionOrder.getStatus());
				final OrderLine firstOrderLine = collectionOrder.getOrderLines().get(0);
				assertEquals(8, firstOrderLine.getHeight());
				assertEquals("EUP", firstOrderLine.getPackagingType());
				assertEquals(1, collectionOrder.getOrderTexts().size());
				assertEquals(2, collectionOrder.getOrderReferences().size());
				assertEquals("textType", collectionOrder.getOrderTexts().get(0).getTextType());
				assertEquals("orderNumber", collectionOrder.getOrderNumber());
				assertEquals(ReferenceType.PURCHASE_ORDER_NUMBER.getCoreSystemsValue(), collectionOrder.getOrderReferences().get(0).getReferenceType().getCoreSystemsValue());
				assertNull(collectionOrder.getFreightTerm());
				assertEquals("Rear entrance", collectionOrder.getConsigneeAddress().getDropOfLocation());
				assertTrue(collectionOrder.isTailLiftCollection());
				// lastModified should be ignored since its read-only
				assertNull(collectionOrder.getLastModified());
				assertNull(collectionOrder.getDatasource());
			}

			@Test
			void shouldBeNullSafeMapToEntity() {
				final CollectionOrder map = orderMapper.mapCollectionOrderToEntity(null);
				assertNull(map);
			}

			@Test
			void shouldThrowException() {
				final BasicOrderDto forwardingOrderDto = new BasicOrderDto();
				final IllegalArgumentException illegalArgumentException = assertThrows(IllegalArgumentException.class, () -> orderMapper.map(forwardingOrderDto));
				assertTrue(illegalArgumentException.getMessage().startsWith("Not all subclasses are supported for this mapping. Missing for"));

			}

		}

		@Nested
		class UpdateEntity {

			@Test
			void shouldUpdateFieldsOnEmptyObject() {
				final OffsetDateTime fromTime = OffsetDateTime.of(2022, 9, 1, 10, 0, 0, 0, ZoneOffset.UTC);
				final RoadCollectionOrderDto collectionOrderDto = getJsonRoadCollectionOrder(fromTime);
				final CollectionOrder collectionOrder = new CollectionOrder();
				orderMapper.update(collectionOrderDto, collectionOrder);
				assertEquals("OG", collectionOrder.getOrderGroup());
				assertEquals("name_delivery", collectionOrder.getDeliveryContact().getName());
				assertEquals("name_shipper_address", collectionOrder.getShipperAddress().getName());
				assertEquals(fromTime, collectionOrder.getCollectionFrom());
				assertEquals(Interpreter.FIX, collectionOrder.getInterpreter());
				assertNull(collectionOrder.getStatus());
				assertEquals(8, collectionOrder.getOrderLines().get(0).getHeight());
				assertNull(collectionOrder.getFreightTerm());
			}

			@Test
			void shouldUpdateFieldsOnPrefilledObject() {
				final OffsetDateTime fromTime = OffsetDateTime.of(2022, 9, 1, 10, 0, 0, 0, ZoneOffset.UTC);
				final RoadCollectionOrderDto collectionOrderDto = getJsonRoadCollectionOrder(fromTime);
				final CollectionOrder collectionOrder = (CollectionOrder) orderMapper.map(collectionOrderDto);
				collectionOrder.setStatus(OrderStatus.LABEL_PENDING);
				collectionOrder.setInterpreter(Interpreter.FIX);
				collectionOrder.setAddresses(null);
				orderMapper.update(collectionOrderDto, collectionOrder);
				assertEquals("OG", collectionOrder.getOrderGroup());
				assertEquals("name_delivery", collectionOrder.getDeliveryContact().getName());
				assertEquals(fromTime, collectionOrder.getCollectionFrom());
				assertEquals(Interpreter.FIX, collectionOrder.getInterpreter());
				assertEquals(OrderStatus.LABEL_PENDING, collectionOrder.getStatus());
				assertEquals(8, collectionOrder.getOrderLines().get(0).getHeight());
				assertNull(collectionOrder.getFreightTerm());
			}

			@Test
			void shouldFailOnWrongTypes() {
				final RoadForwardingOrderDto forwardingOrderDto = new RoadForwardingOrderDto();
				final CollectionOrder order = new CollectionOrder();
				assertThrows(IllegalArgumentException.class, () -> orderMapper.update(forwardingOrderDto, order));
			}

			@Test
			void shouldFailOnWrongTypes2() {
				final AirImportOrderDto forwardingOrderDto = new AirImportOrderDto();
				final CollectionOrder order = new CollectionOrder();
				assertThrows(IllegalArgumentException.class, () -> orderMapper.update(forwardingOrderDto, order));
			}

			@Test
			void shouldFailOnWrongTypes3() {
				final RoadCollectionOrderDto forwardingOrderDto = new RoadCollectionOrderDto();
				final ForwardingOrder order = new ForwardingOrder();
				assertThrows(IllegalArgumentException.class, () -> orderMapper.update(forwardingOrderDto, order));
			}
		}

		private RoadCollectionOrderDto getJsonRoadCollectionOrder(OffsetDateTime fromTime) {
			final RoadCollectionOrderDto collectionOrderDto = new RoadCollectionOrderDto();
			collectionOrderDto.setCollectionOption(CollectionOptionDto.BOOKING);
			collectionOrderDto.setDifferentConsigneeAddress(generateAddress("different_consignee_address"));
			collectionOrderDto.setShipperAddress(generateAddress("shipper_address"));
			collectionOrderDto.setOrderGroup("OG");
			collectionOrderDto.setCustomerNumber("10");
			collectionOrderDto.addOrderLineItemsItem(new RoadOrderLineDto().content("content").weight(10).height(8).length(5).loadingMeter(12.1)
					.packaging(new OptionDto().code("EUP").description("Euro" + "-Palette")).quantity(2).goodsGroup(new GoodsGroupDto().code("GG")));
			collectionOrderDto.setDeliveryContact(generateContact("delivery"));
			collectionOrderDto.setCollectionTime(new CollectionTimeSlotDto().from(fromTime).to(OffsetDateTime.of(2022, 9, 1, 15, 0, 0, 0, ZoneOffset.UTC)));
			collectionOrderDto.setConsigneeAddress(generateAddress("consignee"));
			collectionOrderDto.setFixDate(LocalDate.now());
			collectionOrderDto.setStatus(OrderStatusDto.DRAFT);
			collectionOrderDto.setTailLiftCollection(true);
			DisplayableOrderStatusDto orderStatus = new DisplayableOrderStatusDto();
			orderStatus.setStatus(OrderStatusDto.DRAFT);
			orderStatus.setDescription("orderStatusDescription");
			collectionOrderDto.setOrderStatus(orderStatus);
			collectionOrderDto.setInterpreter(RoadCollectionOrderDto.InterpreterEnum.FIX);
			collectionOrderDto.addFurtherAddressesItem(generateAddress("further_address"));
			collectionOrderDto.addTextsItem(new OrderTextDto().textType("textType").value("content"));
			collectionOrderDto.setOrderNumber("orderNumber");
			collectionOrderDto.setFreightTerm(new FreightTermDto().dachserTermKey("011").incoTermKey("EXW").headline("Ex Works"));
			collectionOrderDto.addReferencesItem(new RoadOrderReferenceDto().referenceType(PURCHASE_ORDER_NUMBER).referenceValue("ref_value"));
			return collectionOrderDto;
		}

		@Nested
		class ToJson {
			@Test
			void shouldMapFields() {
				final Instant lastModified = Instant.now();
				final Instant sendAt = Instant.now();
				final Instant createdAt = Instant.now();

				String user = "fbb5a7d9-cd5c-436c-9b66-1425feb58b80";

				final CollectionOrder collectionOrder = new CollectionOrder();
				final OrderAddress shipperAddress = generateOrderAddress("shipper_address");
				collectionOrder.setShipperAddress(shipperAddress);
				final OrderFurtherAddress collection = generateOrderFurtherAddress("collection_address");
				collection.setAddressType("CA");
				collectionOrder.addAddress(collection);
				collectionOrder.setStatus(OrderStatus.DRAFT);
				collectionOrder.setOrderGroup("OG");
				collectionOrder.setCustomerNumber("10");
				collectionOrder.setLastModified(lastModified);

				collectionOrder.setSendAt(sendAt);
				collectionOrder.setSendUser(user);
				collectionOrder.setCreatedAt(createdAt);
				collectionOrder.setCreator(user);

				final RoadOrderLine orderLine = new RoadOrderLine();
				orderLine.setNumber(1);
				orderLine.setContent("content");
				orderLine.setWeight(BigDecimal.valueOf(10L));
				orderLine.setHeight(8);
				orderLine.setLength(5);
				orderLine.setLoadingMeter(BigDecimal.valueOf(10.2));
				orderLine.setPackagingType("EU");
				orderLine.setQuantity(2);
				orderLine.setGoodsGroup("GG");
				collectionOrder.addOrderLine(orderLine);
				collectionOrder.setDeliveryContact(generateOrderContact("delivery"));
				collectionOrder.setCollectionFrom(OffsetDateTime.of(2022, 9, 1, 10, 0, 0, 0, ZoneOffset.UTC));
				collectionOrder.setCollectionTo(OffsetDateTime.of(2022, 9, 1, 15, 0, 0, 0, ZoneOffset.UTC));
				collectionOrder.setConsigneeAddress(generateOrderAddress("consignee_address"));
				collectionOrder.setFixDate(LocalDate.now());
				collectionOrder.addAddress(generateOrderFurtherAddress("further_address"));
				final RoadOrderReference roadOrderReference = new RoadOrderReference();
				roadOrderReference.setReferenceType(ReferenceType.PURCHASE_ORDER_NUMBER);
				collectionOrder.addOrderReference(roadOrderReference);
				final OrderText orderText = new OrderText();
				orderText.setTextType("textType");
				collectionOrder.addOrderText(orderText);
				collectionOrder.setFreightTerm("011");
				final RoadCollectionOrderDto collectionOrderDto = (RoadCollectionOrderDto) orderMapper.map(collectionOrder);

				// assert
				assertEquals("OG", collectionOrderDto.getOrderGroup());
				assertEquals("name_shipper_address", collectionOrderDto.getShipperAddress().getName());
				assertEquals("name_consignee_address", collectionOrderDto.getConsigneeAddress().getName());
				assertEquals(OrderStatusDto.DRAFT, collectionOrderDto.getStatus());
				assertEquals(OrderStatusDto.DRAFT, collectionOrderDto.getOrderStatus().getStatus());
				assertEquals(OrderStatus.DRAFT.getLabel(), collectionOrderDto.getOrderStatus().getDescription());
				assertEquals(8, collectionOrderDto.getOrderLineItems().get(0).getHeight());
				assertEquals(1, collectionOrderDto.getOrderLineItems().get(0).getNumber());
				assertEquals(1, collectionOrderDto.getTexts().size());
				assertEquals(1, collectionOrderDto.getReferences().size());
				assertEquals("textType", collectionOrderDto.getTexts().get(0).getTextType());
				assertNotNull(collectionOrderDto.getFixDate());
				assertEquals(PURCHASE_ORDER_NUMBER.getValue(), collectionOrderDto.getReferences().get(0).getReferenceType().getValue());
				assertNull(collectionOrderDto.getFreightTerm());
				assertEquals(OffsetDateTime.ofInstant(lastModified, ZoneId.of("UTC")), collectionOrderDto.getLastModified());
				assertEquals(user, collectionOrderDto.getSendBy().toString());
				assertEquals(sendAt, collectionOrderDto.getSendAt().toInstant());
				assertEquals(user, collectionOrderDto.getCreatedBy().toString());
				assertEquals(createdAt, collectionOrderDto.getCreatedAt().toInstant());

			}

			@Test
			void shouldMapRoadOrderCalculatedFields() {
				final CollectionOrder collectionOrder = new CollectionOrder();

				final RoadOrderLine orderLine = new RoadOrderLine();
				orderLine.setNumber(1);
				orderLine.setContent("content");
				orderLine.setWeight(BigDecimal.valueOf(10L));
				orderLine.setHeight(8);
				orderLine.setLength(5);
				orderLine.setVolume(BigDecimal.valueOf(10L));
				orderLine.setLoadingMeter(BigDecimal.valueOf(10.2));
				orderLine.setPackagingType("EU");
				orderLine.setQuantity(2);
				orderLine.setGoodsGroup("GG");
				collectionOrder.addOrderLine(orderLine);

				final RoadOrderLine orderLine2 = new RoadOrderLine();
				orderLine2.setNumber(1);
				orderLine2.setContent("content");
				orderLine2.setWeight(BigDecimal.valueOf(10L));
				orderLine2.setHeight(8);
				orderLine2.setLength(5);
				orderLine2.setVolume(BigDecimal.valueOf(10L));
				orderLine2.setLoadingMeter(BigDecimal.valueOf(10.2));
				orderLine2.setPackagingType("EU");
				orderLine2.setQuantity(2);
				orderLine2.setGoodsGroup("GG");
				collectionOrder.addOrderLine(orderLine2);

				final RoadCollectionOrderDto collectionOrderDto = (RoadCollectionOrderDto) orderMapper.map(collectionOrder);

				// assert
				assertEquals(20, collectionOrderDto.getTotalOrderWeight());
				assertEquals(20, collectionOrderDto.getTotalOrderVolume());
				assertEquals(4, collectionOrderDto.getTotalAmountPackages());
			}

			@Test
			void shouldMapRoadOrderCalculatedFieldsWithPackingPosition() {
				final CollectionOrder collectionOrder = new CollectionOrder();

				final RoadOrderLine orderLine = new RoadOrderLine();
				orderLine.setNumber(1);
				orderLine.setContent("content");
				orderLine.setWeight(BigDecimal.valueOf(30L));
				orderLine.setHeight(8);
				orderLine.setLength(5);
				orderLine.setVolume(BigDecimal.valueOf(30L));
				orderLine.setLoadingMeter(BigDecimal.valueOf(10.2));
				orderLine.setPackagingType("EU");
				orderLine.setQuantity(2);
				orderLine.setGoodsGroup("GG");
				orderLine.setPackingPositionId(1L);
				collectionOrder.addOrderLine(orderLine);

				PackingPosition packingPosition = new PackingPosition();
				packingPosition.setQuantity(1);
				packingPosition.setOrderLines(List.of(orderLine));
				packingPosition.setPackagingType("EU");
				packingPosition.setOrder(collectionOrder);
				collectionOrder.setPackingPositions(List.of(packingPosition));

				final RoadOrderLine orderLine2 = new RoadOrderLine();
				orderLine2.setNumber(1);
				orderLine2.setContent("content");
				orderLine2.setWeight(BigDecimal.valueOf(10L));
				orderLine2.setHeight(8);
				orderLine2.setLength(5);
				orderLine2.setVolume(BigDecimal.valueOf(10L));
				orderLine2.setLoadingMeter(BigDecimal.valueOf(10.2));
				orderLine2.setPackagingType("EU");
				orderLine2.setQuantity(2);
				orderLine2.setGoodsGroup("GG");
				collectionOrder.addOrderLine(orderLine2);

				final RoadCollectionOrderDto collectionOrderDto = (RoadCollectionOrderDto) orderMapper.map(collectionOrder);

				// assert
				assertEquals(40, collectionOrderDto.getTotalOrderWeight());
				assertEquals(40, collectionOrderDto.getTotalOrderVolume());
				assertEquals(3, collectionOrderDto.getTotalAmountPackages());
			}

			@Test
			void shouldbeNullSafeMap() {
				final OrderResponseBodyDto map = orderMapper.map((CollectionOrder) null);
				assertNull(map);
			}

			@Test
			void shouldbeNullSafeMapToJson() {
				final OrderResponseBodyDto map = orderMapper.mapCollectionOrderToDto(null);
				assertNull(map);
			}

			@Test
			void shouldMapEmptyObject() {
				final OrderResponseBodyDto map = orderMapper.map(new CollectionOrder());
				assertNotNull(map);
			}

		}
	}

	@Nested
	class MapForwardingOrder {

		@Nested
		class ToEntity {

			@Test
			void shouldMapNotFailOnMissingOrderlineGoodsGroups() {
				final OffsetDateTime fromTime = OffsetDateTime.of(2022, 9, 1, 10, 0, 0, 0, ZoneOffset.UTC);
				final RoadForwardingOrderDto forwardingOrderDto = getJsonRoadForwardingOrder(fromTime);
				forwardingOrderDto.getOrderLineItems().forEach(line -> line.setGoodsGroup(null));
				final ForwardingOrder forwardingOrder = (ForwardingOrder) orderMapper.map(forwardingOrderDto);
				assertEquals("OG", forwardingOrder.getOrderGroup());
			}

			@Test
			void shouldMapFieldsUsingMap() {
				final OffsetDateTime fromTime = OffsetDateTime.of(2022, 9, 1, 10, 0, 0, 0, ZoneOffset.UTC);
				final RoadForwardingOrderDto forwardingOrderDto = getJsonRoadForwardingOrder(fromTime);

				final ForwardingOrder forwardingOrder = (ForwardingOrder) orderMapper.map(forwardingOrderDto);
				assertEquals("OG", forwardingOrder.getOrderGroup());
				assertNotNull(forwardingOrder.getCustomerNumber());
				assertEquals(1, forwardingOrder.getAddresses().size());
				assertEquals("name_further_address", forwardingOrder.getAddresses().get(0).getName());
				assertEquals("name_consignee_address", forwardingOrder.getConsigneeAddress().getName());
				assertEquals("street2", forwardingOrder.getConsigneeAddress().getStreet2());
				assertNotNull(forwardingOrder.getAddresses().get(0).getOrder());
				assertTrue(forwardingOrder.isCustomsGoods());
				assertTrue(forwardingOrder.getSelfCollection());
				assertEquals(fromTime, forwardingOrder.getCollectionFrom());
				assertEquals(8, forwardingOrder.getOrderLines().get(0).getHeight());
				assertEquals("011", forwardingOrder.getFreightTerm());
				assertNull(forwardingOrder.getDatasource());

			}

			@Test
			void shouldBeNullSafe() {
				final Order map = orderMapper.map((RoadForwardingOrderDto) null);
				assertNull(map);
			}

			@Test
			void shouldBeNullSafeToEntity() {
				final Order map = orderMapper.mapForwardingOrderToEntity(null);
				assertNull(map);
			}

			@Test
			void shouldNotMapQuoteInformationFromJson() {
				final RoadForwardingOrderDto orderDto = new RoadForwardingOrderDto();
				RoadForwardingQuoteInformationDto quoteInformation = new RoadForwardingQuoteInformationDto();
				quoteInformation.setQuoteRequestId(1234L);
				orderDto.setQuoteInformation(quoteInformation);
				final ForwardingOrder forwardingOrder = (ForwardingOrder) orderMapper.map(orderDto);
				assertNull(forwardingOrder.getQuoteInformation());
			}

			@Test
			void shouldMapCashOnDelivery() {
				final RoadForwardingOrderDto orderDto = new RoadForwardingOrderDto();
				orderDto.setCashOnDeliveryAmount(1000.0);
				orderDto.setCashOnDelivery(true);
				final ForwardingOrder forwardingOrder = (ForwardingOrder) orderMapper.map(orderDto);
				assertTrue(forwardingOrder.isCashOnDelivery());
				assertEquals(BigDecimal.valueOf(1000.0), forwardingOrder.getCashOnDeliveryAmount());
			}
		}

		@Nested
		class UpdateEntity {
			@Test
			void shouldUpdateFieldsUsingMap() {
				final OffsetDateTime fromTime = OffsetDateTime.of(2022, 9, 1, 10, 0, 0, 0, ZoneOffset.UTC);
				final RoadForwardingOrderDto forwardingOrderDto = getJsonRoadForwardingOrder(fromTime);
				final ForwardingOrder forwardingOrder = new ForwardingOrder();
				orderMapper.update(forwardingOrderDto, forwardingOrder);
				assertEquals("OG", forwardingOrder.getOrderGroup());
				assertEquals(1, forwardingOrder.getAddresses().size());
				assertEquals("name_further_address", forwardingOrder.getAddresses().get(0).getName());
				assertEquals("name_consignee_address", forwardingOrder.getConsigneeAddress().getName());
				assertNotNull(forwardingOrder.getAddresses().get(0).getOrder());
				assertTrue(forwardingOrder.isCustomsGoods());
				assertTrue(forwardingOrder.getSelfCollection());
				assertEquals(fromTime, forwardingOrder.getCollectionFrom());
				assertEquals(8, forwardingOrder.getOrderLines().get(0).getHeight());
				assertEquals("011", forwardingOrder.getFreightTerm());
			}

			@Test
			void shouldRemoveObsoleteSsccsIfKeepListIsAvailable() {
				final RoadForwardingOrderDto forwardingOrderDto = getJsonRoadForwardingOrder(OffsetDateTime.now());
				final ForwardingOrder forwardingOrder = new ForwardingOrder();
				ArrayList<OrderSscc> ssccs = new ArrayList<>();
				ssccs.add(OrderSscc.builder().sscc("12345").build());
				ssccs.add(OrderSscc.builder().sscc("123456").build());
				forwardingOrderDto.setGeneratedSsccs(List.of("12345"));
				forwardingOrder.setSsccs(ssccs);
				orderMapper.update(forwardingOrderDto, forwardingOrder);
				assertEquals(1, forwardingOrder.getSsccs().size());
				assertEquals("12345", forwardingOrder.getSsccs().get(0).getSscc());
			}

			@Test
			void shouldKeepSsccsIfKeepListIsEmpty() {
				final RoadForwardingOrderDto forwardingOrderDto = getJsonRoadForwardingOrder(OffsetDateTime.now());
				final ForwardingOrder forwardingOrder = new ForwardingOrder();
				ArrayList<OrderSscc> ssccs = new ArrayList<>();
				ssccs.add(OrderSscc.builder().sscc("12345").build());
				ssccs.add(OrderSscc.builder().sscc("123456").build());
				forwardingOrder.setSsccs(ssccs);
				forwardingOrderDto.setGeneratedSsccs(new ArrayList<>());
				orderMapper.update(forwardingOrderDto, forwardingOrder);
				assertEquals(2, forwardingOrder.getSsccs().size());
			}

			@Test
			void shouldKeepSsccsIfKeepListIsNull() {
				final RoadForwardingOrderDto forwardingOrderDto = getJsonRoadForwardingOrder(OffsetDateTime.now());
				final ForwardingOrder forwardingOrder = new ForwardingOrder();
				ArrayList<OrderSscc> ssccs = new ArrayList<>();
				ssccs.add(OrderSscc.builder().sscc("12345").build());
				ssccs.add(OrderSscc.builder().sscc("123456").build());
				forwardingOrder.setSsccs(ssccs);
				orderMapper.update(forwardingOrderDto, forwardingOrder);
				assertEquals(2, forwardingOrder.getSsccs().size());
			}
		}

		private RoadForwardingOrderDto getJsonRoadForwardingOrder(OffsetDateTime fromTime) {

			final RoadForwardingOrderDto forwardingOrderDto = new RoadForwardingOrderDto();
			forwardingOrderDto.setSelfCollection(true);
			forwardingOrderDto.setCustomsGoods(true);
			forwardingOrderDto.setOrderGroup("OG");
			forwardingOrderDto.setCustomerNumber("10");
			forwardingOrderDto.addOrderLineItemsItem(new RoadOrderLineDto().content("content").weight(10).height(8).length(5).loadingMeter(12.1)
					.packaging(new OptionDto().code("EU").description("Euro" + "-Palette")).quantity(2).goodsGroup(new GoodsGroupDto().code("GG")));
			forwardingOrderDto.setCollectionTime(new CollectionTimeSlotDto().from(fromTime).to(OffsetDateTime.of(2022, 9, 1, 15, 0, 0, 0, ZoneOffset.UTC)));
			final OrderAddressDto consignee = generateAddress("consignee_address");
			consignee.setAddressType("UC");
			forwardingOrderDto.setConsigneeAddress(consignee);
			forwardingOrderDto.setCustomsType(CustomsTypeDto.CUSTOMER);
			forwardingOrderDto.setFixDate(LocalDate.now());
			forwardingOrderDto.addFurtherAddressesItem(generateAddress("further_address"));
			forwardingOrderDto.setFreightTerm(new FreightTermDto().dachserTermKey("011").incoTermKey("EXW").headline("Ex Works"));
			return forwardingOrderDto;
		}

		@Nested
		class ToJson {
			@Test
			void shouldMapFields() {

				String user = "fbb5a7d9-cd5c-436c-9b66-1425feb58b80";

				final ForwardingOrder forwardingOrder = new ForwardingOrder();
				forwardingOrder.setOrderGroup("OG");
				final RoadOrderLine orderLine = new RoadOrderLine();
				orderLine.setContent("content");
				orderLine.setWeight(BigDecimal.valueOf(10L));
				orderLine.setHeight(8);
				orderLine.setLength(5);
				orderLine.setLoadingMeter(BigDecimal.valueOf(12.1));
				orderLine.setPackagingType("EU");
				orderLine.setQuantity(2);
				orderLine.setGoodsGroup("GG");
				forwardingOrder.addOrderLine(orderLine);
				forwardingOrder.setSelfCollection(true);
				forwardingOrder.setCollectionFrom(OffsetDateTime.of(2022, 9, 1, 10, 0, 0, 0, ZoneOffset.UTC));
				forwardingOrder.setCollectionTo(OffsetDateTime.of(2022, 9, 1, 15, 0, 0, 0, ZoneOffset.UTC));
				forwardingOrder.addAddress(generateOrderFurtherAddress("consignee"));
				forwardingOrder.setFixDate(LocalDate.now());
				forwardingOrder.addAddress(generateOrderFurtherAddress("further_address"));
				forwardingOrder.setFreightTerm("011");

				final Instant sendAt = Instant.now();
				final Instant createdAt = Instant.now();
				final Instant labelsPrintedAt = Instant.now();
				final Instant adviceSentAt = Instant.now();
				forwardingOrder.setSendAt(sendAt);

				forwardingOrder.setSendUser(user);
				forwardingOrder.setCreatedAt(createdAt);
				forwardingOrder.setCreator(user);
				forwardingOrder.setLabelsPrintedAt(labelsPrintedAt);
				forwardingOrder.setAdviceSent(adviceSentAt);

				final RoadForwardingOrderDto forwardingOrderDto = (RoadForwardingOrderDto) orderMapper.map(forwardingOrder);
				// assert
				assertEquals("OG", forwardingOrderDto.getOrderGroup());
				assertEquals("name_further_address", forwardingOrderDto.getFurtherAddresses().get(1).getName());
				assertEquals(8, forwardingOrderDto.getOrderLineItems().get(0).getHeight());
				assertEquals("011", forwardingOrderDto.getFreightTerm().getDachserTermKey());
				assertTrue(forwardingOrder.getSelfCollection());

				assertEquals(user, forwardingOrderDto.getSendBy().toString());
				assertEquals(sendAt, forwardingOrderDto.getSendAt().toInstant());
				assertEquals(user, forwardingOrderDto.getCreatedBy().toString());
				assertEquals(createdAt, forwardingOrderDto.getCreatedAt().toInstant());
				assertEquals(labelsPrintedAt, forwardingOrderDto.getLabelsPrintedAt().toInstant());
				assertEquals(adviceSentAt, forwardingOrderDto.getAdviceSentAt().toInstant());
			}

			@Test
			void shouldMapTotalLabelCount() {
				final ForwardingOrder forwardingOrder = new ForwardingOrder();

				final RoadOrderLine orderLine = new RoadOrderLine();
				orderLine.setContent("content");
				orderLine.setWeight(BigDecimal.valueOf(10L));
				orderLine.setHeight(8);
				orderLine.setLength(5);
				orderLine.setLoadingMeter(BigDecimal.valueOf(12.1));
				orderLine.setPackagingType("EU");
				orderLine.setQuantity(2);
				orderLine.setGoodsGroup("GG");
				forwardingOrder.addOrderLine(orderLine);

				final RoadForwardingOrderDto mappedWithoutManualSSCC = (RoadForwardingOrderDto) orderMapper.map(forwardingOrder);

				forwardingOrder.setManualNumberSscc(5);

				final RoadForwardingOrderDto mappedWithManualSSCC = (RoadForwardingOrderDto) orderMapper.map(forwardingOrder);

				// assert
				assertEquals(2, mappedWithoutManualSSCC.getTotalLabelCount());
				assertEquals(5, mappedWithManualSSCC.getTotalLabelCount());
			}

			@Test
			void shouldBeNullSafe() {
				final OrderResponseBodyDto map = orderMapper.map((ForwardingOrder) null);
				assertNull(map);
			}

			@Test
			void shouldBeNullSafeToJson() {
				final RoadForwardingOrderDto map = orderMapper.mapForwardingOrderToDto(null);
				assertNull(map);
			}
		}
	}

	@Nested
	class MapAir {
		@Nested
		class ToEntity {
			@Test
			void shouldMapFields() {
				final AirImportOrderDto airOrderDto = new AirImportOrderDto();
				airOrderDto.setOrderNumber("orderNumber");
				airOrderDto.setFromIATA(new PortDto().code("MUC").name("Munich"));
				airOrderDto.setToIATA(new PortDto().code("FRA").name("Paris"));
				airOrderDto.setOrderLineItems(List.of(generateAirOrderLine()));
				airOrderDto.setIncoTerm(new IncoTermDto().code("code").dachserCode("sadf"));
				airOrderDto.setStackable(false);
				airOrderDto.setShockSensitive(true);
				final AirOrder airOrder = (AirOrder) orderMapper.map(airOrderDto);
				assertEquals("MUC", airOrder.getFromIATA());
				assertEquals("FRA", airOrder.getToIATA());
				assertEquals(1, airOrder.getOrderLines().size());
				assertEquals("sadf", airOrder.getIncoTerm());
				assertFalse(airOrder.isStackable());
				assertTrue(airOrder.isShockSensitive());
				assertNull(airOrder.getDatasource());
			}

			@Test
			void shouldMapPortRouting() {
				final AirImportOrderDto airOrderDto = new AirImportOrderDto();
				airOrderDto.setDeliverToAirport(true);
				airOrderDto.setCollectFromAirport(true);

				final AirOrder airOrder = (AirOrder) orderMapper.map(airOrderDto);

				assertTrue(airOrder.isDeliverToAirport());
				assertTrue(airOrder.isCollectFromAirport());
			}

			@Test
			void shouldMapHSCodes() {
				final AirExportOrderDto airExportOrderDTO = new AirExportOrderDto();
				final AirOrderLineDto orderLineItemsItem = generateAirOrderLine();
				airExportOrderDTO.addOrderLineItemsItem(orderLineItemsItem);

				orderLineItemsItem.setHsCodes(
						List.of(new AirSeaOrderLineHsCodeDto().hsCode("122134").goods("description"), new AirSeaOrderLineHsCodeDto().hsCode("122132").goods("description2")));

				final AirExportOrder airExportOrder = orderMapper.mapAir(airExportOrderDTO);
				assertEquals(2, airExportOrder.getOrderLines().get(0).getHsCodes().size());
				assertEquals("122134", airExportOrder.getOrderLines().get(0).getHsCodes().get(0).getHsCode());
			}

			@Test
			void shouldMapProduct() {
				final AirExportOrderDto airExportOrderDTO = new AirExportOrderDto();
				airExportOrderDTO.setProduct(new DeliveryProductDto().code("1").description("description"));

				final AirExportOrder airExportOrder = orderMapper.mapAir(airExportOrderDTO);
				assertEquals(1, airExportOrder.getProductCode());
				assertEquals("description", airExportOrder.getProductName());
			}

			@Test
			void shouldMapAirReferences() {
				final AirExportOrderDto airExportOrderDTO = new AirExportOrderDto();
				airExportOrderDTO.setReferences(List.of(generateReference(OrderReferenceTypeDto.QUOTATION_REFERENCE, "quot1234"),
						generateReference(OrderReferenceTypeDto.SHIPPING_ORDER_NUMBER, "ship123")));

				final AirExportOrder airExportOrder = orderMapper.mapAir(airExportOrderDTO);

				assertEquals(3, airExportOrder.getOrderReferences().size());
				final AirOrderReference airOrderReference = airExportOrder.getOrderReferences().get(0);
				assertEquals("quot1234", airOrderReference.getReferenceValue());
				Assertions.assertEquals(AirSeaOrderReferenceType.QUOTATION_REFERENCE, airOrderReference.getReferenceType());
			}

			@Test
			void shouldFail() {
				final Order order = TestUtil.generateBasicOrder(OrderType.ROADFORWARDINGORDER);
				order.setStatus(OrderStatus.DRAFT);
				order.setCustomerNumber("10");

				final IllegalArgumentException illegalArgumentException = assertThrows(IllegalArgumentException.class, () -> orderMapper.map(order));
				assertTrue(illegalArgumentException.getMessage().startsWith("Not all subclasses are supported for this mapping. Missing for"));

			}

			@Test
			void shouldBeNullSafe() {
				final Order map = orderMapper.map((AirImportOrderDto) null);
				assertNull(map);
			}

			@Test
			void shouldBeNullSafeToJson() {
				final AirOrder map = orderMapper.mapAir((AirImportOrderDto) null);
				assertNull(map);
			}

			@Test
			void shouldMapCollectionDateToNullWhenNotSet() {
				final AirImportOrderDto airOrderDto = new AirImportOrderDto();
				airOrderDto.setCollectionTime(new CollectionTimeSlotDto().collectionDate(null));
				final AirOrder airOrder = (AirOrder) orderMapper.map(airOrderDto);
				assertNull(airOrder.getCollectionDate());
			}

			@Test
			void shouldMapShippersReference() {
				final AirImportOrderDto airOrderDto = new AirImportOrderDto();
				airOrderDto.setShipperReference("Test");
				airOrderDto.setLoading(true);
				airOrderDto.setUnloading(true);
				final AirOrder airOrder = (AirOrder) orderMapper.map(airOrderDto);
				assertNotNull(airOrder.getOrderReferences());
				Optional<AirOrderReference> first = airOrder.getOrderReferences().stream().filter(ref -> ref.getReferenceType() == AirSeaOrderReferenceType.SHIPPERS_REFERENCE)
						.findFirst();
				assertTrue(first.isPresent());
				assertEquals("Test", first.get().getReferenceValue());
				assertTrue(first.get().isLoading());
				assertTrue(first.get().isUnloading());
			}

			@Test
			void shouldMapShippersReferenceNeitherLoadingOrUnloading() {
				final AirImportOrderDto airOrderDto = new AirImportOrderDto();
				airOrderDto.setShipperReference("Test");
				airOrderDto.setLoading(false);
				airOrderDto.setUnloading(false);
				final AirOrder airOrder = (AirOrder) orderMapper.map(airOrderDto);
				assertNotNull(airOrder.getOrderReferences());
				Optional<AirOrderReference> first = airOrder.getOrderReferences().stream().filter(ref -> ref.getReferenceType() == AirSeaOrderReferenceType.SHIPPERS_REFERENCE)
						.findFirst();
				assertTrue(first.isPresent());
				assertEquals("Test", first.get().getReferenceValue());
				assertFalse(first.get().isLoading());
				assertFalse(first.get().isUnloading());
			}

			@Test
			void shouldNotMapQuoteInformationFromJson() {
				final AirImportOrderDto airOrderDto = new AirImportOrderDto();
				AirExportQuoteInformationDto quoteInformation = new AirExportQuoteInformationDto();
				quoteInformation.setQuoteRequestId(1234L);
				airOrderDto.setQuoteInformation(quoteInformation);
				final AirOrder airOrder = (AirOrder) orderMapper.map(airOrderDto);
				assertNull(airOrder.getQuoteInformation());
			}

			@Nested
			class ToJson {
				@Test
				void shouldMapFieldsExport() {
					final AirOrder airOrder = testUtil.generateAirExportOrder();
					airOrder.setFromIATA("MUC");
					airOrder.setToIATA("FRA");
					final AirExportOrderDto airOrderDto = (AirExportOrderDto) orderMapper.map(airOrder);
					assertEquals("MUC", airOrderDto.getFromIATA().getCode());
					assertEquals("FRA", airOrderDto.getToIATA().getCode());
					assertEquals(1, airOrderDto.getOrderLineItems().size());
					assertEquals("AIREXPORTORDER", airOrderDto.getOrderType());
				}

				@Test
				void shouldMapFieldsImport() {
					final AirOrder airOrder = testUtil.generateAirImportOrder();
					airOrder.setFromIATA("MUC");
					airOrder.setToIATA("FRA");
					final AirImportOrderDto airOrderDto = (AirImportOrderDto) orderMapper.map(airOrder);
					assertEquals("MUC", airOrderDto.getFromIATA().getCode());
					assertEquals("FRA", airOrderDto.getToIATA().getCode());
					assertEquals(1, airOrderDto.getOrderLineItems().size());
					assertEquals("AIRIMPORTORDER", airOrderDto.getOrderType());
				}

				@Test
				void shouldMapProduct() {
					final AirImportOrder airOrder = testUtil.generateAirImportOrder();
					airOrder.setProductCode(1);
					airOrder.setProductName("name");

					final AirImportOrderDto airExportOrder = orderMapper.mapAirOrderToDto(airOrder);
					assertEquals("1", airExportOrder.getProduct().getCode());
					assertEquals("name", airExportOrder.getProduct().getDescription());
				}

				@Test
				void shouldMapAirOrderCalculatedFields() {
					final AirImportOrder airOrder = testUtil.generateAirImportOrder();

					final AirOrderLine orderLine = new AirOrderLine();
					orderLine.setWeight(BigDecimal.valueOf(50.0));
					orderLine.setHeight(240);
					orderLine.setWidth(120);
					orderLine.setLength(260);
					orderLine.setVolume(BigDecimal.valueOf(2.0));
					orderLine.setPackagingType("EU");
					orderLine.setQuantity(2);

					AirOrderLine orderLine2 = new AirOrderLine();
					orderLine2.setWeight(BigDecimal.valueOf(50.0));
					orderLine2.setHeight(240);
					orderLine2.setWidth(120);
					orderLine2.setLength(260);
					orderLine2.setVolume(BigDecimal.valueOf(2.0));
					orderLine2.setPackagingType("EU");
					orderLine2.setQuantity(2);

					airOrder.setOrderLines(List.of(orderLine, orderLine2));

					final AirImportOrderDto airImportOrderDto = orderMapper.mapAirOrderToDto(airOrder);

					// assert
					assertEquals(100.0, airImportOrderDto.getTotalOrderWeight());
					assertEquals(4.0, airImportOrderDto.getTotalOrderVolume());
					assertEquals(4, airImportOrderDto.getTotalAmountPackages());
				}

				@Test
				void shouldBeNullSafe() {
					final OrderResponseBodyDto map = orderMapper.map((AirExportOrder) null);
					assertNull(map);
				}

				@Test
				void shouldBeNullSafeToJson() {
					final AirOrder map = orderMapper.mapAir((AirExportOrderDto) null);
					assertNull(map);
				}
			}

		}

		private AirOrderLineDto generateAirOrderLine() {
			final AirOrderLineDto orderLine = new AirOrderLineDto();
			orderLine.setWeight(50.0);
			orderLine.setHeight(240);
			orderLine.setWidth(120);
			orderLine.setLength(260);
			orderLine.setVolume(2.0);
			orderLine.setPackaging(new OptionDto().code("EU"));
			orderLine.setMarkAndNumbers("markAndNumbers");
			orderLine.setQuantity(2);
			final AirSeaOrderLineHsCodeDto hsCode1 = new AirSeaOrderLineHsCodeDto();
			final AirSeaOrderLineHsCodeDto hsCode2 = new AirSeaOrderLineHsCodeDto();
			hsCode1.setHsCode("123456");
			hsCode2.setHsCode("223456");
			orderLine.setHsCodes(List.of(hsCode1, hsCode2));
			return orderLine;
		}

		@Nested
		class UpdateEntity {

			@Test
			void shouldUpdateFields() {
				final AirImportOrderDto airOrderDto = new AirImportOrderDto();
				airOrderDto.setOrderNumber("orderNumber");
				airOrderDto.setFromIATA(new PortDto().code("MUC"));
				airOrderDto.setToIATA(new PortDto().code("FRA"));
				final AirOrder airOrder = new AirImportOrder();
				orderMapper.update(airOrderDto, airOrder);
				assertEquals("MUC", airOrder.getFromIATA());
				assertEquals("FRA", airOrder.getToIATA());
			}

			@Test
			void shouldUpdateRegardingDeliveryAddressSwitch() {

				final AirOrder existingAirExportOrder = new AirExportOrder();
				AirQuoteInformation quoteInformation = new AirQuoteInformation();
				existingAirExportOrder.setQuoteInformation(quoteInformation);

				OrderAddress existingConsignee = new OrderAddress();
				existingConsignee.setOrderAddressId(1L);
				existingConsignee.setCountryCode("AT");
				existingConsignee.setPostcode("1010");
				existingConsignee.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
				existingAirExportOrder.setConsigneeAddress(existingConsignee);

				final AirExportOrderDto airOrderDto = new AirExportOrderDto();
				airOrderDto.setOrderId(1L);

				OrderAddressDto consignee = new OrderAddressDto();
				consignee.setCountryCode("DE");
				consignee.setPostcode("82131");
				airOrderDto.setConsigneeAddress(consignee);

				OrderAddressDto delivery = new OrderAddressDto();
				delivery.setId(1L);
				delivery.setCountryCode("AT");
				delivery.setPostcode("1010");
				airOrderDto.setDeliveryAddress(delivery);

				orderMapper.update(airOrderDto, existingAirExportOrder);

				assertEquals("1010", existingAirExportOrder.getDeliveryAddress().getPostcode());
				assertEquals("AT", existingAirExportOrder.getDeliveryAddress().getCountryCode());

				assertEquals("82131", existingAirExportOrder.getConsigneeAddress().getPostcode());
				assertEquals("DE", existingAirExportOrder.getConsigneeAddress().getCountryCode());

			}

			@Test
			void shouldUpdateRegardingPickupAddressSwitch() {

				final AirOrder existingAirImportOrder = new AirImportOrder();
				AirQuoteInformation quoteInformation = new AirQuoteInformation();
				existingAirImportOrder.setQuoteInformation(quoteInformation);

				OrderAddress existingShipper = new OrderAddress();
				existingShipper.setOrderAddressId(1L);
				existingShipper.setCountryCode("AT");
				existingShipper.setPostcode("1010");
				existingShipper.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
				existingAirImportOrder.setShipperAddress(existingShipper);

				final AirImportOrderDto airOrderDto = new AirImportOrderDto();
				airOrderDto.setOrderId(1L);

				OrderAddressDto shipper = new OrderAddressDto();
				shipper.setCountryCode("DE");
				shipper.setPostcode("82131");
				airOrderDto.setShipperAddress(shipper);

				OrderAddressDto pickup = new OrderAddressDto();
				pickup.setId(1L);
				pickup.setCountryCode("AT");
				pickup.setPostcode("1010");
				airOrderDto.setPickupAddress(pickup);

				orderMapper.update(airOrderDto, existingAirImportOrder);

				assertEquals("1010", existingAirImportOrder.getPickupAddress().getPostcode());
				assertEquals("AT", existingAirImportOrder.getPickupAddress().getCountryCode());

				assertEquals("82131", existingAirImportOrder.getShipperAddress().getPostcode());
				assertEquals("DE", existingAirImportOrder.getShipperAddress().getCountryCode());

			}

			@Test
			void switchSeaAddressesConditionallySwitchesDeliveryAddress() {
				final SeaExportOrder existingSeaExportOrder = new SeaExportOrder();
				SeaQuoteInformation quoteInformation = new SeaQuoteInformation();
				existingSeaExportOrder.setQuoteInformation(quoteInformation);

				OrderAddress existingConsignee = new OrderAddress();
				existingConsignee.setOrderAddressId(1L);
				existingConsignee.setCountryCode("AT");
				existingConsignee.setPostcode("1010");
				existingConsignee.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
				existingSeaExportOrder.setConsigneeAddress(existingConsignee);

				final SeaExportOrderDto seaOrderDto = new SeaExportOrderDto();
				seaOrderDto.setOrderId(1L);

				OrderAddressDto consignee = new OrderAddressDto();
				consignee.setCountryCode("DE");
				consignee.setPostcode("82131");
				seaOrderDto.setConsigneeAddress(consignee);

				OrderAddressDto delivery = new OrderAddressDto();
				delivery.setId(1L);
				delivery.setCountryCode("AT");
				delivery.setPostcode("1010");
				seaOrderDto.setDeliveryAddress(delivery);

				orderMapper.switchSeaAddressesConditionally(seaOrderDto, existingSeaExportOrder);

				assertEquals("1010", existingSeaExportOrder.getDeliveryAddress().getPostcode());
				assertEquals("AT", existingSeaExportOrder.getDeliveryAddress().getCountryCode());

			}

			@Test
			void switchSeaAddressesConditionallySwitchesDeliveryAddressBack() {
				final SeaExportOrder existingSeaExportOrder = new SeaExportOrder();
				SeaQuoteInformation quoteInformation = new SeaQuoteInformation();
				existingSeaExportOrder.setQuoteInformation(quoteInformation);

				OrderAddress existingConsignee = new OrderAddress();
				existingConsignee.setOrderAddressId(3L);
				existingConsignee.setCountryCode("AT");
				existingConsignee.setPostcode("1010");
				existingSeaExportOrder.setConsigneeAddress(existingConsignee);

				OrderAddress existingDelivery = new OrderAddress();
				existingDelivery.setOrderAddressId(4L);
				existingDelivery.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
				existingDelivery.setCountryCode("DE");
				existingDelivery.setPostcode("82131");
				existingSeaExportOrder.setDeliveryAddress(existingDelivery);

				final SeaExportOrderDto seaOrderDto = new SeaExportOrderDto();
				seaOrderDto.setOrderId(1L);

				OrderAddressDto consignee = new OrderAddressDto();
				consignee.setId(4L);
				consignee.setCountryCode("DE");
				consignee.setPostcode("82131");
				seaOrderDto.setConsigneeAddress(consignee);

				orderMapper.switchSeaAddressesConditionally(seaOrderDto, existingSeaExportOrder);

				assertEquals("82131", existingSeaExportOrder.getConsigneeAddress().getPostcode());
				assertEquals("DE", existingSeaExportOrder.getConsigneeAddress().getCountryCode());

				assertNull(existingSeaExportOrder.getDeliveryAddress());
			}

			@Test
			void switchSeaAddressesConditionallySwitchesPickupAddress() {
				final SeaImportOrder existingSeaImportOrder = new SeaImportOrder();
				SeaQuoteInformation quoteInformation = new SeaQuoteInformation();
				existingSeaImportOrder.setQuoteInformation(quoteInformation);

				OrderAddress existingShipper = new OrderAddress();
				existingShipper.setOrderAddressId(1L);
				existingShipper.setCountryCode("AT");
				existingShipper.setPostcode("1010");
				existingShipper.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
				existingSeaImportOrder.setShipperAddress(existingShipper);

				final SeaImportOrderDto seaOrderDto = new SeaImportOrderDto();
				seaOrderDto.setOrderId(1L);

				OrderAddressDto shipper = new OrderAddressDto();
				shipper.setCountryCode("DE");
				shipper.setPostcode("82131");
				seaOrderDto.setShipperAddress(shipper);

				OrderAddressDto pickup = new OrderAddressDto();
				pickup.setId(1L);
				pickup.setCountryCode("AT");
				pickup.setPostcode("1010");
				seaOrderDto.setPickupAddress(pickup);

				orderMapper.switchSeaAddressesConditionally(seaOrderDto, existingSeaImportOrder);

				assertEquals("1010", existingSeaImportOrder.getPickupAddress().getPostcode());
				assertEquals("AT", existingSeaImportOrder.getPickupAddress().getCountryCode());

			}

			@Test
			void switchSeaAddressesConditionallySwitchesPickupAddressBack() {
				final SeaImportOrder existingSeaImportOrder = new SeaImportOrder();
				SeaQuoteInformation quoteInformation = new SeaQuoteInformation();
				existingSeaImportOrder.setQuoteInformation(quoteInformation);

				OrderAddress existingShipper = new OrderAddress();
				existingShipper.setOrderAddressId(3L);
				existingShipper.setCountryCode("AT");
				existingShipper.setPostcode("1010");
				existingSeaImportOrder.setShipperAddress(existingShipper);

				OrderAddress existingPickup = new OrderAddress();
				existingPickup.setOrderAddressId(4L);
				existingPickup.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
				existingPickup.setCountryCode("DE");
				existingPickup.setPostcode("82131");
				existingSeaImportOrder.setPickupAddress(existingPickup);

				final SeaImportOrderDto seaOrderDto = new SeaImportOrderDto();
				seaOrderDto.setOrderId(1L);

				OrderAddressDto shipper = new OrderAddressDto();
				shipper.setId(4L);
				shipper.setCountryCode("DE");
				shipper.setPostcode("82131");
				seaOrderDto.setShipperAddress(shipper);

				orderMapper.switchSeaAddressesConditionally(seaOrderDto, existingSeaImportOrder);

				assertEquals("82131", existingSeaImportOrder.getShipperAddress().getPostcode());
				assertEquals("DE", existingSeaImportOrder.getShipperAddress().getCountryCode());

				assertNull(existingSeaImportOrder.getPickupAddress());
			}

			@Test
			void switchAirAddressesConditionallySwitchesDeliveryAddress() {
				final AirExportOrder existingAirExportOrder = new AirExportOrder();
				AirQuoteInformation quoteInformation = new AirQuoteInformation();
				existingAirExportOrder.setQuoteInformation(quoteInformation);

				OrderAddress existingConsignee = new OrderAddress();
				existingConsignee.setOrderAddressId(1L);
				existingConsignee.setCountryCode("AT");
				existingConsignee.setPostcode("1010");
				existingConsignee.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
				existingAirExportOrder.setConsigneeAddress(existingConsignee);

				final AirExportOrderDto airOrderDto = new AirExportOrderDto();
				airOrderDto.setOrderId(1L);

				OrderAddressDto consignee = new OrderAddressDto();
				consignee.setCountryCode("DE");
				consignee.setPostcode("82131");
				airOrderDto.setConsigneeAddress(consignee);

				OrderAddressDto delivery = new OrderAddressDto();
				delivery.setId(1L);
				delivery.setCountryCode("AT");
				delivery.setPostcode("1010");
				airOrderDto.setDeliveryAddress(delivery);

				orderMapper.switchAirAddressesConditionally(airOrderDto, existingAirExportOrder);

				assertEquals("1010", existingAirExportOrder.getDeliveryAddress().getPostcode());
				assertEquals("AT", existingAirExportOrder.getDeliveryAddress().getCountryCode());

			}

			@Test
			void switchAirAddressesConditionallySwitchesDeliveryAddressBack() {
				final AirExportOrder existingAirExportOrder = new AirExportOrder();
				AirQuoteInformation quoteInformation = new AirQuoteInformation();
				existingAirExportOrder.setQuoteInformation(quoteInformation);

				OrderAddress existingConsignee = new OrderAddress();
				existingConsignee.setOrderAddressId(3L);
				existingConsignee.setCountryCode("AT");
				existingConsignee.setPostcode("1010");
				existingAirExportOrder.setConsigneeAddress(existingConsignee);

				OrderAddress existingDelivery = new OrderAddress();
				existingDelivery.setOrderAddressId(4L);
				existingDelivery.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
				existingDelivery.setCountryCode("DE");
				existingDelivery.setPostcode("82131");
				existingAirExportOrder.setDeliveryAddress(existingDelivery);

				final AirExportOrderDto airOrderDto = new AirExportOrderDto();
				airOrderDto.setOrderId(1L);

				OrderAddressDto consignee = new OrderAddressDto();
				consignee.setId(4L);
				consignee.setCountryCode("DE");
				consignee.setPostcode("82131");
				airOrderDto.setConsigneeAddress(consignee);

				orderMapper.switchAirAddressesConditionally(airOrderDto, existingAirExportOrder);

				assertEquals("82131", existingAirExportOrder.getConsigneeAddress().getPostcode());
				assertEquals("DE", existingAirExportOrder.getConsigneeAddress().getCountryCode());

				assertNull(existingAirExportOrder.getDeliveryAddress());
			}

			@Test
			void switchAirAddressesConditionallySwitchesPickupAddress() {
				final AirImportOrder existingAirImportOrder = new AirImportOrder();
				AirQuoteInformation quoteInformation = new AirQuoteInformation();
				existingAirImportOrder.setQuoteInformation(quoteInformation);

				OrderAddress existingShipper = new OrderAddress();
				existingShipper.setOrderAddressId(1L);
				existingShipper.setCountryCode("AT");
				existingShipper.setPostcode("1010");
				existingShipper.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
				existingAirImportOrder.setShipperAddress(existingShipper);

				final AirImportOrderDto airOrderDto = new AirImportOrderDto();
				airOrderDto.setOrderId(1L);

				OrderAddressDto shipper = new OrderAddressDto();
				shipper.setCountryCode("DE");
				shipper.setPostcode("82131");
				airOrderDto.setShipperAddress(shipper);

				OrderAddressDto pickup = new OrderAddressDto();
				pickup.setId(1L);
				pickup.setCountryCode("AT");
				pickup.setPostcode("1010");
				airOrderDto.setPickupAddress(pickup);

				orderMapper.switchAirAddressesConditionally(airOrderDto, existingAirImportOrder);

				assertEquals("1010", existingAirImportOrder.getPickupAddress().getPostcode());
				assertEquals("AT", existingAirImportOrder.getPickupAddress().getCountryCode());

			}

			@Test
			void switchAirAddressesConditionallySwitchesPickupAddressBack() {
				final AirImportOrder existingAirImportOrder = new AirImportOrder();
				AirQuoteInformation quoteInformation = new AirQuoteInformation();
				existingAirImportOrder.setQuoteInformation(quoteInformation);

				OrderAddress existingShipper = new OrderAddress();
				existingShipper.setOrderAddressId(3L);
				existingShipper.setCountryCode("AT");
				existingShipper.setPostcode("1010");
				existingAirImportOrder.setShipperAddress(existingShipper);

				OrderAddress existingPickup = new OrderAddress();
				existingPickup.setOrderAddressId(4L);
				existingPickup.setLockedByQuote(LockedByQuoteEnum.PARTIAL);
				existingPickup.setCountryCode("DE");
				existingPickup.setPostcode("82131");
				existingAirImportOrder.setPickupAddress(existingPickup);

				final AirImportOrderDto airOrderDto = new AirImportOrderDto();
				airOrderDto.setOrderId(1L);

				OrderAddressDto shipper = new OrderAddressDto();
				shipper.setId(4L);
				shipper.setCountryCode("DE");
				shipper.setPostcode("82131");
				airOrderDto.setShipperAddress(shipper);

				orderMapper.switchAirAddressesConditionally(airOrderDto, existingAirImportOrder);

				assertEquals("82131", existingAirImportOrder.getShipperAddress().getPostcode());
				assertEquals("DE", existingAirImportOrder.getShipperAddress().getCountryCode());

				assertNull(existingAirImportOrder.getPickupAddress());
			}

			@Test
			void shouldUpdateBasicAirExportFields() {
				final AirExportOrderDto airOrderDto = new AirExportOrderDto();
				airOrderDto.setOrderNumber("orderNumber");
				airOrderDto.setConsigneeAddress(generateAddress("consignee"));
				airOrderDto.setFromIATA(new PortDto().code("MUC"));
				airOrderDto.setToIATA(new PortDto().code("FRA"));
				final AirOrder airOrder = new AirExportOrder();
				orderMapper.update(airOrderDto, airOrder);
				assertEquals("MUC", airOrder.getFromIATA());
				assertEquals("FRA", airOrder.getToIATA());
				assertEquals("name_consignee", airOrder.getConsigneeAddress().getName());
			}

			@Test
			void shouldUpdateHSCodes() {
				final AirExportOrderDto airOrderDto = new AirExportOrderDto();
				airOrderDto.setOrderNumber("orderNumber");
				final AirOrderLineDto orderLineItemsItem = generateAirOrderLine();
				airOrderDto.addOrderLineItemsItem(orderLineItemsItem);
				airOrderDto.getOrderLineItems().get(0).setHsCodes(List.of(new AirSeaOrderLineHsCodeDto().id(1L).hsCode("122134").goods("description"),
						new AirSeaOrderLineHsCodeDto().id(2L).hsCode("122132").goods("description2")));

				final AirOrder airOrder = new AirExportOrder();
				final AirOrderLine airOrderLine1 = new AirOrderLine();
				AirOrderLineHsCode airOrderLineHsCode = new AirOrderLineHsCode();
				airOrderLineHsCode.setHsCode("012345");
				airOrderLineHsCode.setGoods("goods1");
				airOrderLine1.setHsCodes(new ArrayList<>(List.of(airOrderLineHsCode)));
				airOrder.setOrderLines(new ArrayList<>(List.of(airOrderLine1)));

				orderMapper.update(airOrderDto, airOrder);

				final AirOrderLine airOrderLine = airOrder.getOrderLines().get(0);
				assertEquals(2, airOrderLine.getHsCodes().size());
				assertEquals("122134", airOrderLine.getHsCodes().get(0).getHsCode());
				assertEquals("122132", airOrderLine.getHsCodes().get(1).getHsCode());
			}

			@Test
			void shouldUpdateExistingOrderLine() {
				final AirExportOrderDto airOrderDto = new AirExportOrderDto();
				airOrderDto.setOrderNumber("orderNumber");
				final AirOrderLineDto orderLineItemsItem = generateAirOrderLine();
				airOrderDto.addOrderLineItemsItem(orderLineItemsItem);
				orderLineItemsItem.setId(100L);

				final AirOrder airOrder = new AirExportOrder();
				final AirOrderLine airOrderLine1 = new AirOrderLine();
				airOrderLine1.setOrderLineId(100L);
				airOrderLine1.setPackagingType("PKT");
				airOrderLine1.setVolume(BigDecimal.TEN);
				airOrderLine1.setMarkAndNumbers("MarsAndNumbersOLD");
				airOrder.setOrderLines(new ArrayList(List.of(airOrderLine1)));

				orderMapper.update(airOrderDto, airOrder);

				final AirOrderLine airOrderLine = airOrder.getOrderLines().get(0);
				assertEquals("markAndNumbers", airOrderLine.getMarkAndNumbers());
				assertEquals(100L, airOrderLine.getOrderLineId());
				assertEquals(BigDecimal.valueOf(50.0), airOrderLine.getWeight());
				assertEquals(260, airOrderLine.getLength());
				assertEquals(120, airOrderLine.getWidth());
				assertEquals(BigDecimal.valueOf(2).setScale(4), airOrderLine.getVolume());
			}

			@Test
			void shouldUpdateShippersReference() {
				final AirExportOrderDto airExportOrderDto = new AirExportOrderDto();
				airExportOrderDto.setOrderNumber("orderNumber");
				airExportOrderDto.addReferencesItem(new AirSeaOrderReferenceDto().referenceType(SHIPPERS_REFERENCE).referenceValue("SR1").loading(false).unloading(false));
				airExportOrderDto.setShipperReference("SR1");
				airExportOrderDto.setLoading(true);
				airExportOrderDto.setUnloading(true);
				airExportOrderDto.setConsigneeAddress(generateAddress("consignee"));
				final AirOrder airExportOrder = new AirExportOrder();
				orderMapper.update(airExportOrderDto, airExportOrder);
				assertEquals("name_consignee", airExportOrder.getConsigneeAddress().getName());
				Optional<AirOrderReference> shippersRef = airExportOrder.getOrderReferences().stream()
						.filter(reference -> reference.getReferenceType().equals(AirSeaOrderReferenceType.SHIPPERS_REFERENCE)).findFirst();
				assertTrue(shippersRef.isPresent());
				assertTrue(shippersRef.get().isLoading());
				assertTrue(shippersRef.get().isUnloading());
				assertEquals("SR1", airExportOrder.getShipperReference());
				assertEquals("SR1", shippersRef.get().getReferenceValue());
			}
		}

		@Nested
		class ToJson {
			@Test
			void shouldMapFields() {
				final AirOrder airOrder = new AirImportOrder();
				airOrder.setOrderReferences(
						List.of(AirOrderReference.builder().orderReferenceId(10L).referenceValue("ref1").referenceType(AirSeaOrderReferenceType.PURCHASE_ORDER_NUMBER).build()));
				airOrder.setFromIATA("MUC");
				airOrder.setToIATA("FRA");
				airOrder.setIncoTerm("DPA");
				airOrder.setStackable(false);
				airOrder.setShockSensitive(true);
				final AirImportOrderDto airOrderDto = (AirImportOrderDto) orderMapper.map(airOrder);
				assertEquals("MUC", airOrderDto.getFromIATA().getCode());
				assertEquals("FRA", airOrderDto.getToIATA().getCode());
				assertNotNull(airOrderDto.getIncoTerm());
				assertEquals("DPA", airOrderDto.getIncoTerm().getCode());
				final List<AirSeaOrderReferenceDto> references = airOrderDto.getReferences();
				assertNotNull(references);
				assertEquals(1, references.size());
				final AirSeaOrderReferenceDto airOrderReferenceDto = references.get(0);
				assertEquals("ref1", airOrderReferenceDto.getReferenceValue());
				assertEquals(PURCHASE_ORDER_NUMBER, airOrderReferenceDto.getReferenceType());
				assertEquals(10L, airOrderReferenceDto.getId());
				assertFalse(airOrderDto.getStackable());
				assertTrue(airOrderDto.getShockSensitive());
			}

			@Test
			void shouldMapPortRouting() {
				final AirOrder airOrder = new AirImportOrder();
				airOrder.setDeliverToAirport(true);
				airOrder.setCollectFromAirport(true);

				final AirImportOrderDto airOrderDto = (AirImportOrderDto) orderMapper.map(airOrder);

				assertTrue(airOrderDto.getDeliverToAirport());
				assertTrue(airOrderDto.getCollectFromAirport());
			}

			@Test
			void shouldBeNullSafe() {
				final OrderResponseBodyDto map = orderMapper.map((AirOrder) null);
				assertNull(map);
			}

			@Test
			void shouldMapLoadingUnloading() {
				final AirOrder airOrder = new AirImportOrder();
				airOrder.setOrderReferences(
						List.of(AirOrderReference.builder().orderReferenceId(10L).referenceValue("ref1").referenceType(AirSeaOrderReferenceType.SHIPPERS_REFERENCE).loading(true)
								.unloading(true).build()));
				airOrder.setShipperReference("SHIP1");
				airOrder.setStackable(false);
				airOrder.setShockSensitive(true);
				final AirImportOrderDto airOrderDto = (AirImportOrderDto) orderMapper.map(airOrder);
				assertNotNull(airOrderDto.getLoading());
				assertNotNull(airOrderDto.getUnloading());
				assertTrue(airOrderDto.getLoading());
				assertTrue(airOrderDto.getUnloading());
			}

			@Test
			void shouldMapClonedOrder() {
				final AirOrder airOrder = testUtil.generateAirImportOrder();
				airOrder.setDatasource(SourceOfOrder.CLONE);
				final AirImportOrderDto airOrderDto = (AirImportOrderDto) orderMapper.map(airOrder);
				assertTrue(airOrderDto.getClonedOrder());
			}

		}

	}

	@Nested
	class MapSea {
		@Nested
		class ToEntity {
			@Test
			void shouldMapFields() {
				final SeaImportOrderDto seaImportOrderDto = new SeaImportOrderDto();
				seaImportOrderDto.setOrderNumber("orderNumber");
				seaImportOrderDto.setFromPort(new PortDto().code("MUC").name("Munich"));
				seaImportOrderDto.setToPort(new PortDto().code("FRA").name("Paris"));
				seaImportOrderDto.setOrderLineItems(List.of(generateSeaOrderLine()));
				seaImportOrderDto.setIncoTerm(new IncoTermDto().code("code").dachserCode("sadf"));
				seaImportOrderDto.setStackable(false);
				seaImportOrderDto.setShockSensitive(true);
				final SeaOrder seaOrder = (SeaOrder) orderMapper.map(seaImportOrderDto);
				assertEquals("MUC", seaOrder.getFromPort());
				assertEquals("FRA", seaOrder.getToPort());
				assertEquals(1, seaOrder.getOrderLines().size());
				assertEquals("sadf", seaOrder.getIncoTerm());
				assertFalse(seaOrder.isStackable());
				assertTrue(seaOrder.isShockSensitive());
				assertNull(seaOrder.getDatasource());
			}

			@Test
			void shouldMapFieldsFullContainerLoad() {
				final SeaImportOrderDto seaImportOrderDto = new SeaImportOrderDto();
				seaImportOrderDto.setOrderNumber("orderNumber");
				seaImportOrderDto.setFromPort(new PortDto().code("MUC").name("Munich"));
				seaImportOrderDto.setToPort(new PortDto().code("FRA").name("Paris"));
				seaImportOrderDto.setOrderLineItems(List.of(generateSeaOrderLine()));
				seaImportOrderDto.setIncoTerm(new IncoTermDto().code("code").dachserCode("sadf"));
				seaImportOrderDto.setStackable(true);
				seaImportOrderDto.setShockSensitive(true);
				seaImportOrderDto.setFullContainerLoad(true);
				final SeaOrder seaOrder = (SeaOrder) orderMapper.map(seaImportOrderDto);
				assertEquals("MUC", seaOrder.getFromPort());
				assertEquals("FRA", seaOrder.getToPort());
				assertEquals(1, seaOrder.getOrderLines().size());
				assertEquals("sadf", seaOrder.getIncoTerm());
				assertFalse(seaOrder.isStackable());
				assertFalse(seaOrder.isShockSensitive());
				assertNull(seaOrder.getDatasource());
			}

			@Test
			void shouldMapPortRouting() {
				final SeaImportOrderDto seaOrderDto = new SeaImportOrderDto();
				seaOrderDto.setDeliverToPort(true);
				seaOrderDto.setCollectFromPort(true);

				final SeaOrder seaOrder = (SeaOrder) orderMapper.map(seaOrderDto);

				assertTrue(seaOrder.isDeliverToPort());
				assertTrue(seaOrder.isCollectFromPort());
			}

			@Test
			void shouldMapHSCodes() {
				final SeaExportOrderDto seaExportOrderDto = new SeaExportOrderDto();
				final SeaOrderLineDto orderLineItemsItem = generateSeaOrderLine();
				seaExportOrderDto.addOrderLineItemsItem(orderLineItemsItem);

				orderLineItemsItem.setHsCodes(
						List.of(new AirSeaOrderLineHsCodeDto().hsCode("122134").goods("description"), new AirSeaOrderLineHsCodeDto().hsCode("122132").goods("description2")));

				final SeaExportOrder seaExportOrder = orderMapper.mapSea(seaExportOrderDto);
				assertEquals(2, seaExportOrder.getOrderLines().get(0).getHsCodes().size());
				assertEquals("122134", seaExportOrder.getOrderLines().get(0).getHsCodes().get(0).getHsCode());
			}

			@Test
			void shouldMapSeaReferences() {
				final SeaExportOrderDto seaExportOrderDTO = new SeaExportOrderDto();
				seaExportOrderDTO.setReferences(List.of(generateReference(OrderReferenceTypeDto.QUOTATION_REFERENCE, "quot1234"),
						generateReference(OrderReferenceTypeDto.SHIPPING_ORDER_NUMBER, "ship123")));

				final SeaExportOrder seaExportOrder = orderMapper.mapSea(seaExportOrderDTO);

				assertEquals(2, seaExportOrder.getOrderReferences().size());
				final SeaOrderReference seaOrderReference = seaExportOrder.getOrderReferences().get(0);
				assertEquals("quot1234", seaOrderReference.getReferenceValue());
				Assertions.assertEquals(AirSeaOrderReferenceType.QUOTATION_REFERENCE, seaOrderReference.getReferenceType());
			}

			@Test
			void shouldFail() {
				final Order order = TestUtil.generateBasicOrder(OrderType.AIREXPORTORDER);
				order.setStatus(OrderStatus.DRAFT);
				order.setCustomerNumber("10");

				final IllegalArgumentException illegalArgumentException = assertThrows(IllegalArgumentException.class, () -> orderMapper.map(order));
				assertTrue(illegalArgumentException.getMessage().startsWith("Not all subclasses are supported for this mapping. Missing for"));

			}

			@Test
			void shouldBeNullSafe() {
				final Order map = orderMapper.map((SeaImportOrderDto) null);
				assertNull(map);
			}

			@Test
			void shouldBeNullSafeToJson() {
				final SeaOrder map = orderMapper.mapSea((SeaImportOrderDto) null);
				assertNull(map);
			}

			@Test
			void shouldMapCollectionDateToNullWhenNotSet() {
				final SeaImportOrderDto seaImportOrderDto = new SeaImportOrderDto();
				seaImportOrderDto.setCollectionTime(new CollectionTimeSlotDto().collectionDate(null));
				final SeaOrder seaOrder = (SeaOrder) orderMapper.map(seaImportOrderDto);
				assertNull(seaOrder.getCollectionDate());
			}

			@Test
			void shouldMapShippersReference() {
				final SeaImportOrderDto seaOrderDto = new SeaImportOrderDto();
				seaOrderDto.setShipperReference("Test");
				seaOrderDto.setLoading(true);
				seaOrderDto.setUnloading(true);
				final SeaOrder seaOrder = (SeaOrder) orderMapper.map(seaOrderDto);
				assertNotNull(seaOrder.getOrderReferences());
				Optional<SeaOrderReference> first = seaOrder.getOrderReferences().stream().filter(ref -> ref.getReferenceType() == AirSeaOrderReferenceType.SHIPPERS_REFERENCE)
						.findFirst();
				assertTrue(first.isPresent());
				assertEquals("Test", first.get().getReferenceValue());
				assertTrue(first.get().isLoading());
				assertTrue(first.get().isUnloading());
			}

			@Test
			void shouldMapShippersReferenceNeitherLoadingOrUnloading() {
				final SeaImportOrderDto seaOrderDto = new SeaImportOrderDto();
				seaOrderDto.setShipperReference("Test");
				seaOrderDto.setLoading(false);
				seaOrderDto.setUnloading(false);
				final SeaOrder seaOrder = (SeaOrder) orderMapper.map(seaOrderDto);
				assertNotNull(seaOrder.getOrderReferences());
				Optional<SeaOrderReference> first = seaOrder.getOrderReferences().stream().filter(ref -> ref.getReferenceType() == AirSeaOrderReferenceType.SHIPPERS_REFERENCE)
						.findFirst();
				assertTrue(first.isPresent());
				assertEquals("Test", first.get().getReferenceValue());
				assertFalse(first.get().isLoading());
				assertFalse(first.get().isUnloading());
			}

			@Nested
			class ToJson {
				@Test
				void shouldMapFields() {
					final SeaOrder seaOrder = testUtil.generateSeaExportOrder();
					seaOrder.setFromPort("DEHAM");
					seaOrder.setToPort("HKHKG");
					final SeaExportOrderDto seaExportOrderDto = (SeaExportOrderDto) orderMapper.map(seaOrder);
					assertEquals("DEHAM", seaExportOrderDto.getFromPort().getCode());
					assertEquals("HKHKG", seaExportOrderDto.getToPort().getCode());
					assertEquals(1, seaExportOrderDto.getOrderLineItems().size());
				}

				@Test
				void shouldMapHsCodes() {
					final SeaOrder seaOrder = testUtil.generateSeaExportOrder();

					final SeaExportOrderDto seaExportOrderDto = (SeaExportOrderDto) orderMapper.map(seaOrder);

					final AirSeaOrderLineHsCodeDto hsCodes = seaExportOrderDto.getOrderLineItems().get(0).getHsCodes().get(0);
					assertEquals("4564654", hsCodes.getHsCode());
					assertEquals("hs code description", hsCodes.getDescription());
				}

				@Test
				void shouldBeNullSafe() {
					final OrderResponseBodyDto map = orderMapper.map((SeaExportOrder) null);
					assertNull(map);
				}

				@Test
				void shouldBeNullSafeToJson() {
					final SeaOrder map = orderMapper.mapSea((SeaExportOrderDto) null);
					assertNull(map);
				}
			}

		}

		private SeaOrderLineDto generateSeaOrderLine() {
			final SeaOrderLineDto orderLine = new SeaOrderLineDto();
			orderLine.setWeight(50.0);
			orderLine.setHeight(240);
			orderLine.setWidth(120);
			orderLine.setLength(260);
			orderLine.setVolume(2.0);
			orderLine.setPackaging(new OptionDto().code("EU"));
			orderLine.setMarkAndNumbers("markAndNumbers");
			orderLine.setQuantity(2);
			final AirSeaOrderLineHsCodeDto hsCode1 = new AirSeaOrderLineHsCodeDto();
			final AirSeaOrderLineHsCodeDto hsCode2 = new AirSeaOrderLineHsCodeDto();
			hsCode1.setHsCode("123456");
			hsCode2.setHsCode("223456");
			orderLine.setHsCodes(List.of(hsCode1, hsCode2));
			return orderLine;
		}

		@Nested
		class UpdateEntity {
			@Test
			void shouldUpdateFields() {
				final SeaImportOrderDto seaOrderDto = new SeaImportOrderDto();
				seaOrderDto.setOrderNumber("orderNumber");
				seaOrderDto.setFromPort(new PortDto().code("MUC"));
				seaOrderDto.setToPort(new PortDto().code("FRA"));
				final SeaOrder seaImportOrder = new SeaImportOrder();
				orderMapper.update(seaOrderDto, seaImportOrder);
				assertEquals("MUC", seaImportOrder.getFromPort());
				assertEquals("FRA", seaImportOrder.getToPort());
			}

			@Test
			void shouldUpdateBasicSeaExportFields() {
				final SeaExportOrderDto seaOrderDto = new SeaExportOrderDto();
				seaOrderDto.setOrderNumber("orderNumber");
				seaOrderDto.setConsigneeAddress(generateAddress("consignee"));
				seaOrderDto.setFromPort(new PortDto().code("MUC"));
				seaOrderDto.setToPort(new PortDto().code("FRA"));
				final SeaOrder seaExportOrder = new SeaExportOrder();
				orderMapper.update(seaOrderDto, seaExportOrder);
				assertEquals("MUC", seaExportOrder.getFromPort());
				assertEquals("FRA", seaExportOrder.getToPort());
				assertEquals("name_consignee", seaExportOrder.getConsigneeAddress().getName());
			}

			@Test
			void shouldUpdateHSCodes() {
				final SeaExportOrderDto seaExportOrderDto = new SeaExportOrderDto();
				seaExportOrderDto.setOrderNumber("orderNumber");
				final SeaOrderLineDto orderLineItemsItem = generateSeaOrderLine();
				seaExportOrderDto.addOrderLineItemsItem(orderLineItemsItem);
				seaExportOrderDto.getOrderLineItems().get(0).setHsCodes(List.of(new AirSeaOrderLineHsCodeDto().id(1L).hsCode("122134").goods("description"),
						new AirSeaOrderLineHsCodeDto().id(2L).hsCode("122132").goods("description2")));

				final SeaOrder seaExportOrder = new SeaExportOrder();
				final SeaOrderLine seaOrderLine1 = new SeaOrderLine();
				SeaOrderLineHsCode seaOrderLineHsCode = new SeaOrderLineHsCode();
				seaOrderLineHsCode.setHsCode("012345");
				seaOrderLineHsCode.setGoods("goods1");
				seaOrderLine1.setHsCodes(new ArrayList<>(List.of(seaOrderLineHsCode)));
				seaExportOrder.setOrderLines(new ArrayList<>(List.of(seaOrderLine1)));

				orderMapper.update(seaExportOrderDto, seaExportOrder);

				final SeaOrderLine seaOrderLine = seaExportOrder.getOrderLines().get(0);
				assertEquals(2, seaOrderLine.getHsCodes().size());
				assertEquals("122134", seaOrderLine.getHsCodes().get(0).getHsCode());
				assertEquals("122132", seaOrderLine.getHsCodes().get(1).getHsCode());
			}

			@Test
			void shouldUpdateExistingOrderLine() {
				final SeaExportOrderDto seaExportOrderDto = new SeaExportOrderDto();
				seaExportOrderDto.setOrderNumber("orderNumber");
				final SeaOrderLineDto orderLineItemsItem = generateSeaOrderLine();
				seaExportOrderDto.addOrderLineItemsItem(orderLineItemsItem);
				orderLineItemsItem.setId(100L);

				final SeaOrder seaExportOrder = new SeaExportOrder();
				final SeaOrderLine airOrderLine1 = new SeaOrderLine();
				airOrderLine1.setOrderLineId(100L);
				airOrderLine1.setPackagingType("PKT");
				airOrderLine1.setVolume(BigDecimal.TEN);
				airOrderLine1.setMarkAndNumbers("MarsAndNumbersOLD");
				seaExportOrder.setOrderLines(new ArrayList(List.of(airOrderLine1)));

				orderMapper.update(seaExportOrderDto, seaExportOrder);

				final SeaOrderLine seaOrderLine = seaExportOrder.getOrderLines().get(0);
				assertEquals("markAndNumbers", seaOrderLine.getMarkAndNumbers());
				assertEquals(100L, seaOrderLine.getOrderLineId());
				assertEquals(BigDecimal.valueOf(50.0), seaOrderLine.getWeight());
				assertEquals(260, seaOrderLine.getLength());
				assertEquals(120, seaOrderLine.getWidth());
				assertEquals(BigDecimal.valueOf(2).setScale(4), seaOrderLine.getVolume());
			}

			@Test
			void shouldUpdateShippersReference() {
				final SeaExportOrderDto seaOrderDto = new SeaExportOrderDto();
				seaOrderDto.setOrderNumber("orderNumber");
				seaOrderDto.addReferencesItem(new AirSeaOrderReferenceDto().referenceType(SHIPPERS_REFERENCE).referenceValue("SR1").loading(false).unloading(false));
				seaOrderDto.setShipperReference("SR1");
				seaOrderDto.setLoading(true);
				seaOrderDto.setUnloading(true);
				seaOrderDto.setConsigneeAddress(generateAddress("consignee"));
				seaOrderDto.setFromPort(new PortDto().code("MUC"));
				seaOrderDto.setToPort(new PortDto().code("FRA"));
				final SeaOrder seaExportOrder = new SeaExportOrder();
				orderMapper.update(seaOrderDto, seaExportOrder);
				assertEquals("MUC", seaExportOrder.getFromPort());
				assertEquals("FRA", seaExportOrder.getToPort());
				assertEquals("name_consignee", seaExportOrder.getConsigneeAddress().getName());
				Optional<SeaOrderReference> shippersRef = seaExportOrder.getOrderReferences().stream()
						.filter(reference -> reference.getReferenceType().equals(AirSeaOrderReferenceType.SHIPPERS_REFERENCE)).findFirst();
				assertTrue(shippersRef.isPresent());
				assertTrue(shippersRef.get().isLoading());
				assertTrue(shippersRef.get().isUnloading());
				assertEquals("SR1", seaExportOrder.getShipperReference());
				assertEquals("SR1", shippersRef.get().getReferenceValue());
			}
		}

		@Nested
		class ToJson {
			@Test
			void shouldMapFields() {
				final SeaOrder order = new SeaImportOrder();
				order.setOrderReferences(
						List.of(SeaOrderReference.builder().orderReferenceId(10L).referenceValue("ref1").referenceType(AirSeaOrderReferenceType.PURCHASE_ORDER_NUMBER).build()));
				order.setFromPort("DEHAM");
				order.setToPort("HKHKG");
				order.setIncoTerm("DPA");
				order.setStackable(false);
				order.setShockSensitive(true);
				final SeaImportOrderDto seaImportOrder = (SeaImportOrderDto) orderMapper.map(order);
				assertEquals("DEHAM", seaImportOrder.getFromPort().getCode());
				assertEquals("HKHKG", seaImportOrder.getToPort().getCode());
				assertNotNull(seaImportOrder.getIncoTerm());
				assertEquals("DPA", seaImportOrder.getIncoTerm().getCode());
				final List<AirSeaOrderReferenceDto> references = seaImportOrder.getReferences();
				assertNotNull(references);
				assertEquals(1, references.size());
				final AirSeaOrderReferenceDto airOrderReferenceDto = references.get(0);
				assertEquals("ref1", airOrderReferenceDto.getReferenceValue());
				assertEquals(PURCHASE_ORDER_NUMBER, airOrderReferenceDto.getReferenceType());
				assertEquals(10L, airOrderReferenceDto.getId());
				assertFalse(seaImportOrder.getStackable());
				assertTrue(seaImportOrder.getShockSensitive());
			}

			@Test
			void shouldMapPortRouting() {
				final SeaOrder seaOrder = new SeaImportOrder();
				seaOrder.setDeliverToPort(true);
				seaOrder.setCollectFromPort(true);

				final SeaImportOrderDto airOrderDto = (SeaImportOrderDto) orderMapper.map(seaOrder);

				assertTrue(airOrderDto.getDeliverToPort());
				assertTrue(airOrderDto.getCollectFromPort());
			}

			@Test
			void shouldMapHsCodes() {
				final SeaOrder airOrder = new SeaImportOrder();
				airOrder.setOrderLines(List.of(OrderGenerator.generateSeaOrderLine()));

				final SeaImportOrderDto airOrderDto = (SeaImportOrderDto) orderMapper.map(airOrder);

				final AirSeaOrderLineHsCodeDto hsCodes = airOrderDto.getOrderLineItems().get(0).getHsCodes().get(0);
				assertEquals("4564654", hsCodes.getHsCode());
				assertEquals("hs code description", hsCodes.getDescription());
			}

			@Test
			void shouldBeNullSafe() {
				final OrderResponseBodyDto map = orderMapper.map((SeaOrder) null);
				assertNull(map);
			}

			@Test
			void shouldCalcTotalVolume() {
				SeaExportOrder seaExportOrder = testUtil.generateSeaExportOrder();
				SeaExportOrderDto map = (SeaExportOrderDto) orderMapper.map(seaExportOrder);
				assertEquals(2.0, map.getTotalOrderVolume());
			}

			@Test
			void shouldMapEmptyObject() {
				final OrderResponseBodyDto map = orderMapper.map(new AirExportOrder());
				assertNotNull(map);
			}

			@Test
			void shouldMapLoadingUnloading() {
				final SeaOrder airOrder = new SeaImportOrder();
				airOrder.setOrderReferences(
						List.of(SeaOrderReference.builder().orderReferenceId(10L).referenceValue("ref1").referenceType(AirSeaOrderReferenceType.SHIPPERS_REFERENCE).loading(true)
								.unloading(true).build()));
				airOrder.setShipperReference("SHIP1");
				airOrder.setStackable(false);
				airOrder.setShockSensitive(true);
				final SeaImportOrderDto seaOrderDto = (SeaImportOrderDto) orderMapper.map(airOrder);
				assertNotNull(seaOrderDto.getLoading());
				assertNotNull(seaOrderDto.getUnloading());
				assertTrue(seaOrderDto.getLoading());
				assertTrue(seaOrderDto.getUnloading());
			}

			@Test
			void shouldMapSeaOrderCalculations() {
				final SeaOrder seaOrder = testUtil.generateSeaExportOrder();

				final SeaExportOrderDto seaExportOrderDto = (SeaExportOrderDto) orderMapper.map(seaOrder);

				// assert
				assertEquals(2, seaExportOrderDto.getTotalAmountPackages());
				assertEquals(2.0, seaExportOrderDto.getTotalOrderVolume());
				assertEquals(50.0, seaExportOrderDto.getTotalOrderWeight());
				assertEquals(0, seaExportOrderDto.getTotalContainerQuantity());
				assertEquals(0, seaExportOrderDto.getTotalVerifiedGrossMass());
			}

			@Test
			void shouldMapSeaOrderCalculationsFcl() {
				final SeaOrder seaOrder = testUtil.generateSeaExportOrder();

				seaOrder.setFullContainerLoad(true);

				FullContainerLoad fullContainerLoad = new FullContainerLoad();
				fullContainerLoad.setContainerType("20DC");
				fullContainerLoad.setContainerNumber("123456");
				fullContainerLoad.setVerifiedGrossMass(500);

				SeaOrderLine seaOrderLine = OrderGenerator.generateSeaOrderLine();
				fullContainerLoad.setOrderLines(List.of(seaOrderLine, seaOrderLine));
				fullContainerLoad.setOrder(seaOrder);
				seaOrder.setFullContainerLoads(List.of(fullContainerLoad));

				final SeaExportOrderDto seaExportOrderDto = (SeaExportOrderDto) orderMapper.map(seaOrder);

				// assert
				assertEquals(4, seaExportOrderDto.getTotalAmountPackages());
				assertEquals(4.0, seaExportOrderDto.getTotalOrderVolume());
				assertEquals(100.0, seaExportOrderDto.getTotalOrderWeight());
				assertEquals(1, seaExportOrderDto.getTotalContainerQuantity());
				assertEquals(500, seaExportOrderDto.getTotalVerifiedGrossMass());
			}
		}

	}

	@Nested
	class MapAddressClass {
		@Nested
		class ToJson {

			@Test
			void shouldMapProperties() {
				final OrderFurtherAddress orderAddress = generateOrderFurtherAddress("test");
				orderAddress.setOrderFurtherAddressId(10L);
				final OrderAddressDto orderAddressDto = orderAddressMapper.mapFurtherAddress(orderAddress);
				assertEquals(10L, orderAddressDto.getId());
				assertEquals("name_test", orderAddressDto.getName());
				assertEquals("postcode", orderAddressDto.getPostcode());
				assertEquals("name2", orderAddressDto.getName2());
				assertEquals("name3", orderAddressDto.getName3());
				assertEquals("city", orderAddressDto.getCity());
				assertEquals("street", orderAddressDto.getStreet());
				assertEquals("street2", orderAddressDto.getStreet2());

			}

		}

		@Nested
		class toEntity {

			@ParameterizedTest
			@ValueSource(strings = { "   " })
			@EmptySource
			@NullSource
			void shouldSetBlankGlnToNull(String input) {
				final RoadForwardingOrderDto orderDto = new RoadForwardingOrderDto();

				final OrderAddressDto consignee = new OrderAddressDto();
				consignee.gln(input);

				final OrderAddressDto shipper = new OrderAddressDto();
				shipper.gln(input);

				final OrderAddressDto furtherAddress = new OrderAddressDto();
				furtherAddress.setGln(input);

				orderDto.setConsigneeAddress(consignee);
				orderDto.setShipperAddress(shipper);
				orderDto.setFurtherAddresses(List.of(furtherAddress));
				final Order order = orderMapper.map(orderDto);

				assertNull(order.getConsigneeAddress().getGln());
				assertNull(order.getShipperAddress().getGln());
				assertNull(order.getAddresses().get(0).getGln());
			}

		}
	}

	private OrderContactDataDto generateContact(String nameSuffix) {
		final OrderContactDataDto contactDataDto = new OrderContactDataDto();
		contactDataDto.email("email");
		contactDataDto.name("name_" + nameSuffix);
		contactDataDto.mobile("mobile");
		contactDataDto.setTelephone("telephone");
		return contactDataDto;
	}

	private OrderFurtherAddress generateOrderFurtherAddress(String suffix) {
		final OrderFurtherAddress furtherAddress = new OrderFurtherAddress();
		fillOrderAddress(furtherAddress, suffix);
		furtherAddress.setAddressType("AT");
		return furtherAddress;
	}

	private AirSeaOrderReferenceDto generateReference(OrderReferenceTypeDto type, String value) {
		final AirSeaOrderReferenceDto airOrderReferenceDto = new AirSeaOrderReferenceDto();
		airOrderReferenceDto.setReferenceValue(value);
		airOrderReferenceDto.setReferenceType(type);
		return airOrderReferenceDto;
	}

	private OrderAddress generateOrderAddress(String suffix) {
		final OrderAddress orderAddress = new OrderAddress();
		fillOrderAddress(orderAddress, suffix);
		return orderAddress;
	}

	private void fillOrderAddress(OrderBaseAddress orderAddress, String suffix) {
		orderAddress.setName("name_" + suffix);
		orderAddress.setName2("name2");
		orderAddress.setName3("name3");
		orderAddress.setCity("city");
		orderAddress.setPostcode("postcode");
		orderAddress.setCountryCode("DE");
		orderAddress.setGln("gln");
		orderAddress.setStreet("street");
		orderAddress.setStreet2("street2");
	}

	private OrderContact generateOrderContact(String nameSuffix) {
		final OrderContact contactDataDto = new OrderContact();
		contactDataDto.setEmail("email");
		contactDataDto.setName("name_" + nameSuffix);
		contactDataDto.setMobile("mobile");
		contactDataDto.setTelephone("telephone");
		return contactDataDto;
	}

	private OrderAddressDto generateAddress(String suffix) {
		final OrderAddressDto orderAddressDto = new OrderAddressDto();
		orderAddressDto.name("name_" + suffix);
		orderAddressDto.name2("name2");
		orderAddressDto.name3("name3");
		orderAddressDto.city("city");
		orderAddressDto.postcode("postcode");
		orderAddressDto.countryCode("DE");
		orderAddressDto.addressType("AT");
		orderAddressDto.gln("gln");
		orderAddressDto.setStreet2("street2");
		orderAddressDto.setDropOfLocation("Rear entrance");
		return orderAddressDto;
	}
}
