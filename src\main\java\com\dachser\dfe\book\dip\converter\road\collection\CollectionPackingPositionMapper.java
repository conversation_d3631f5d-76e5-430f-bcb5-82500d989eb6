package com.dachser.dfe.book.dip.converter.road.collection;

import com.dachser.dfe.book.dip.converter.road.RoadPackingPositionMapper;
import com.dachser.dfe.book.order.road.CollectionOrder;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

import java.math.BigInteger;
import java.util.stream.IntStream;

@Mapper(componentModel = "spring")
public interface CollectionPackingPositionMapper extends
		RoadPackingPositionMapper<com.dachser.dfe.book.model.jaxb.order.road.collection.ShipmentHeader, com.dachser.dfe.book.model.jaxb.order.road.collection.PackagingAids, CollectionOrder> {

	@Override
	@AfterMapping
	default void mapPackingPositionIds(@MappingTarget com.dachser.dfe.book.model.jaxb.order.road.collection.ShipmentHeader mapped) {
		IntStream.range(0, mapped.getPackagingAids().size()).forEachOrdered(index -> {
			int position = index + 1;
			com.dachser.dfe.book.model.jaxb.order.road.collection.PackagingAids packagingAid = mapped.getPackagingAids().get(index);
			BigInteger packingPositionId = packagingAid.getPackagingAidsPosition();
			packagingAid.setPackagingAidsPosition(BigInteger.valueOf(position));
			mapped.getShipmentLine().stream().filter(shipmentLine -> packingPositionId.equals(shipmentLine.getPackagingAidsPosition()))
					.forEach(shipmentLine -> shipmentLine.setPackagingAidsPosition(BigInteger.valueOf(position)));
		});
	}
}
