package com.dachser.dfe.book.quote.validation;

import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.OrderValidationResultDto;
import com.dachser.dfe.book.model.ValidationResultEntryDto;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.exception.OrderSaveProcessException;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.validation.Messages;
import com.dachser.dfe.book.quote.AirQuoteInformation;
import com.dachser.dfe.book.quote.QuoteInformation;
import com.dachser.dfe.book.quote.RoadQuoteInformation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class QuoteOrderUpdateValidator {

	private final PriceRelevantCommonFieldsValidator commonQuoteValidator;

	private final PriceRelevantAirExportOrderValidator airExportOrderValidator;

	private final PriceRelevantAirImportOrderValidator airImportOrderValidator;

	private final PriceRelevantForwardingOrderValidator forwardingOrderValidator;

	private final PriceRelevantCollectionOrderValidator collectionOrderValidator;

	private final OrderMapper orderMapper;

	private final Translator translator;

	public void validateQuoteUpdates(@NonNull final QuoteInformation quoteInformation, @NonNull final Order order) {
		final OrderValidationResultDto validationResult = new OrderValidationResultDto();
		ArrayList<PriceRelevantChange> priceRelevantChanges = new ArrayList<>();

		if (order instanceof AirOrder airOrder && quoteInformation instanceof AirQuoteInformation airQuoteInformation) {
			priceRelevantChanges.addAll(commonQuoteValidator.getPriceRelevantChanges(quoteInformation, order));
			if (airOrder instanceof AirExportOrder airExportOrder) {
				priceRelevantChanges.addAll(airExportOrderValidator.getOrderSpecificChanges(airQuoteInformation, airExportOrder));
			} else if (airOrder instanceof AirImportOrder airImportOrder) {
				priceRelevantChanges.addAll(airImportOrderValidator.getOrderSpecificChanges(airQuoteInformation, airImportOrder));
			}
		} else if (order instanceof RoadOrder roadOrder && quoteInformation instanceof RoadQuoteInformation roadQuoteInformation) {
			// If it is not an ePricing quote, we do not need to check for changes in price relevant fields
			if (roadQuoteInformation.getQuoteReference() != null) {
				priceRelevantChanges.addAll(commonQuoteValidator.getPriceRelevantChanges(quoteInformation, order));
				if (roadOrder instanceof ForwardingOrder forwardingOrder) {
					priceRelevantChanges.addAll(forwardingOrderValidator.getOrderSpecificChanges(roadQuoteInformation, forwardingOrder));
				} else if (roadOrder instanceof CollectionOrder collectionOrder) {
					priceRelevantChanges.addAll(collectionOrderValidator.getOrderSpecificChanges(roadQuoteInformation, collectionOrder));
				}
			}
		} else {
			log.debug("Order {} is not of AirOrderType or ForwardingOrder", order);
		}

		validationResult.setResults(addValidationResults(priceRelevantChanges));
		validationResult.setValid(validationResult.getResults().isEmpty());
		log.debug("QuoteOrderUpdateValidator.checkASLQuotePriceRelevantChanges: Quote Update Validation executed with Result: {}", validationResult);

		if (Boolean.FALSE == validationResult.getValid()) {
			log.warn("QuoteOrderUpdateValidator.checkQuotePriceRelevantChanges: Quote Update Validation Error: {}", validationResult);
			throw new OrderSaveProcessException("Order Update Validation Error", validationResult, (BasicOrderDto) orderMapper.map(order));
		}
	}

	private List<ValidationResultEntryDto> addValidationResults(@NonNull final List<PriceRelevantChange> priceRelevantChanges) {
		return priceRelevantChanges.stream()
				.map(change -> new ValidationResultEntryDto().field(change.fieldName()).errorType(ValidationResultEntryDto.ErrorTypeEnum.CONSTRAINT_VIOLATION)
						.description(translator.toLocale(Messages.FORBIDDEN_CHANGE_QUOTE_ORDER))).toList();
	}
}