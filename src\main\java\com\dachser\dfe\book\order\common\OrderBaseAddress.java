package com.dachser.dfe.book.order.common;

import com.dachser.dfe.book.model.options.CollectionOptions;
import com.dachser.dfe.book.order.validation.CompleteOrderValidation;
import com.dachser.dfe.book.order.validation.air.CompleteOrderValidationAir;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.OneToOne;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

@MappedSuperclass
@Data
public class OrderBaseAddress {

	@Size(max = 40)
	@NotNull(groups = { CompleteOrderValidation.class, CompleteOrderValidationAir.class }, message = "{label.text.invalid_input}")
	private String name;

	@Size(max = 40)
	private String name2;

	@Size(max = 40)
	private String name3;

	@Size(max = 40)
	@NotNull(groups = { CompleteOrderValidation.class, CompleteOrderValidationAir.class }, message = "{label.text.invalid_input}")
	private String street;

	@Size(max = 40)
	private String street2;

	@Size(max = 12)
	private String postcode;

	@Size(max = 40)
	@NotNull(groups = { CompleteOrderValidation.class, CompleteOrderValidationAir.class }, message = "{label.text.invalid_input}")
	private String city;

	@Size(max = 3)
	@NotNull(groups = { CompleteOrderValidation.class, CompleteOrderValidationAir.class }, message = "{label.text.invalid_input}")
	private String countryCode;

	@Size(max = 50)
	private String supplement;

	@Size(max = 13)
	@Pattern(groups = { CompleteOrderValidation.class, CompleteOrderValidationAir.class }, regexp = "\\d{13}", message = "{label.text.invalid_input}")
	private String gln;

	@Size(max = 30)
	@Column(name = "tax_id")
	private String taxID;

	private Boolean neutralize;

	private Boolean tailLift;

	@Enumerated(EnumType.STRING)
	private CollectionOptions collectionOption;

	private Long originAddressId;

	@JoinColumn(name = "address_contact_id")
	@OneToOne(cascade = CascadeType.ALL)
	private OrderContact orderContact;

	@Size(max = 17)
	@Column(name = "individual_id")
	private String individualId;
}
