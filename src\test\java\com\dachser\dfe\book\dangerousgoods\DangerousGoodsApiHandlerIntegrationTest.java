package com.dachser.dfe.book.dangerousgoods;

import com.dachser.dfe.book.DFEBookApplication;
import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@SpringBootTest(classes = { DFEBookApplication.class })
@ActiveProfiles("integrationtest")
@Tag("IntegrationTest")
class DangerousGoodsThirdPartyAdapterExtIntegrationTest {

	@Autowired
	private DangerousGoodsThirdPartyAdapterExt dangerousGoodsThirdPartyAdapterExt;

	@Nested
	class FindUnNumbers {

		@Test
		void shouldReturn200() {
			// given
			String unNumber = "1791";

			// when
			List<DangerousGoodDataItemDto> dangerousGoodDataItemDtos = dangerousGoodsThirdPartyAdapterExt.searchUnNumbersRoad(unNumber, "EN");

			// then
			assertEquals(2, dangerousGoodDataItemDtos.size());
			DangerousGoodDataItemDto first = dangerousGoodDataItemDtos.getFirst();
			assertEquals(unNumber, first.getUnNumber());
			assertEquals("C9", first.getClassificationCode());
			assertEquals(unNumber, first.getUnNumber());
			assertEquals("HYPOCHLORITE SOLUTION", first.getDescription());

		}

		@Test
		void shouldReturn200AndParseSubrisk() {
			// given
			String unNumber = "1714";

			// when
			List<DangerousGoodDataItemDto> dangerousGoodDataItemDtos = dangerousGoodsThirdPartyAdapterExt.searchUnNumbersRoad(unNumber, "EN");

			// then
			assertEquals(1, dangerousGoodDataItemDtos.size());
			DangerousGoodDataItemDto first = dangerousGoodDataItemDtos.getFirst();
			assertEquals(unNumber, first.getUnNumber());
			assertEquals("WT2", first.getClassificationCode());
			assertEquals(unNumber, first.getUnNumber());
			assertEquals("ZINC PHOSPHIDE", first.getDescription());
			assertEquals("6.1", first.getSubsidiaryHazardOne());
			assertEquals("I", first.getPackingGroup());
			assertEquals("1", first.getTransportCategory());
			assertEquals("146", first.getDgmId());

		}

		@Test
		void shouldReturnEmptyListOnNonExistingUnNumber() {
			// given
			String unNumber = "9999";

			// when
			List<DangerousGoodDataItemDto> dangerousGoodDataItemDtos = dangerousGoodsThirdPartyAdapterExt.searchUnNumbersRoad(unNumber, "EN");

			// then
			assertEquals(0, dangerousGoodDataItemDtos.size());

		}

	}

	@Nested
	class FetchLocalizedInformationForDgmIds {

		@Test
		void shouldReturn200() {
			// given
			List<String> dgmIds = List.of("146", "147");

			// when
			List<DangerousGoodDataItemDto> dangerousGoodDataItemDtos = dangerousGoodsThirdPartyAdapterExt.fetchLocalizedInformationForDgmIdsRoad(dgmIds, "EN");

			// then
			assertEquals(2, dangerousGoodDataItemDtos.size());
			DangerousGoodDataItemDto first = dangerousGoodDataItemDtos.getFirst();
			assertEquals("146", first.getDgmId());
			assertEquals("WT2", first.getClassificationCode());
			assertEquals("ZINC PHOSPHIDE", first.getDescription());
		}

		@Test
		void shouldReturn200WithLocalizedInformation() {
			// given
			List<String> dgmIds = List.of("146", "147");

			// when
			List<DangerousGoodDataItemDto> dangerousGoodDataItemDtos = dangerousGoodsThirdPartyAdapterExt.fetchLocalizedInformationForDgmIdsRoad(dgmIds, "DE");

			// then
			assertEquals(2, dangerousGoodDataItemDtos.size());
			DangerousGoodDataItemDto first = dangerousGoodDataItemDtos.getFirst();
			assertEquals("146", first.getDgmId());
			assertEquals("WT2", first.getClassificationCode());
			assertEquals("ZINKPHOSPHID", first.getDescription());
		}

		@Test
		void shouldReturnEmptyListOnNonExistingDgmIds() {
			// given
			List<String> dgmIds = List.of("9999", "8888");

			// when
			List<DangerousGoodDataItemDto> dangerousGoodDataItemDtos = dangerousGoodsThirdPartyAdapterExt.fetchLocalizedInformationForDgmIdsRoad(dgmIds, "EN");

			// then
			assertEquals(0, dangerousGoodDataItemDtos.size());
		}

	}
}