package com.dachser.dfe.book.config;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.DefaultUriBuilderFactory;

import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Configuration
public class BookRestTemplateConfig {

	private static final String REQUEST_ORIGIN_HEADER_NAME = "request_origin";

	private static final String REQUEST_ORIGIN_HEADER_VALUE = "dfe-book-backend";

	@Value("${dfe.book.rest.timeoutMS:#{120000}}")
	private Long timeoutMs;

	private static final List<String> USER_HEADERS_TO_INTERCEPT = List.of(HttpHeaders.AUTHORIZATION);

	@Bean(name = "bookRestTemplate")
	RestTemplate bookRestTemplate(final RestTemplateBuilder restTemplateBuilder) {
		final RestTemplate restTemplate = buildTemplate(restTemplateBuilder);
		restTemplate.getInterceptors().add(headerInterceptor(true));
		return restTemplate;
	}

	@Bean(name = "bookRestTemplateNoAuth")
	RestTemplate bookRestTemplateNoAuth(final RestTemplateBuilder restTemplateBuilder) {
		final RestTemplate restTemplate = buildTemplate(restTemplateBuilder);
		restTemplate.getInterceptors().add(headerInterceptor(false));
		return restTemplate;
	}

	private RestTemplate buildTemplate(final RestTemplateBuilder restTemplateBuilder) {
		final RestTemplate restTemplate = restTemplateBuilder.setConnectTimeout(Duration.ofMillis(timeoutMs)).setReadTimeout(Duration.ofMillis(timeoutMs)).build();
		restTemplate.setRequestFactory(new BufferingClientHttpRequestFactory(restTemplate.getRequestFactory()));

		final DefaultUriBuilderFactory uriBuilderFactory = new DefaultUriBuilderFactory();
		uriBuilderFactory.setEncodingMode(DefaultUriBuilderFactory.EncodingMode.VALUES_ONLY);
		restTemplate.setUriTemplateHandler(uriBuilderFactory);
		return restTemplate;
	}

	private ClientHttpRequestInterceptor headerInterceptor(boolean includeUserRequestHeaders) {
		return (request, body, execution) -> {
			// add headers from the original user request to the outgoing request (e.g. for OAuth)
			if (includeUserRequestHeaders) {
				final HttpServletRequest userRequest = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
				USER_HEADERS_TO_INTERCEPT.forEach(headerName -> Optional.ofNullable(userRequest.getHeader(headerName))
						.ifPresentOrElse(headerValue -> request.getHeaders().add(headerName, headerValue), () -> log.debug("Header {} not found in user request", headerName)));
			}
			// always add a custom header to identify the origin of the request
			request.getHeaders().add(REQUEST_ORIGIN_HEADER_NAME, REQUEST_ORIGIN_HEADER_VALUE);
			return execution.execute(request, body);
		};
	}
}
