package com.dachser.dfe.book.order;

import com.dachser.dfe.book.advice.AdviceService;
import com.dachser.dfe.book.dangerousgoods.DangerousGoodsTransferListService;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
class OrderCreateProcessor extends OrderProcessorWithUpdateBody {

	OrderCreateProcessor(OrderRepositoryFacade orderRepository, OrderMapper orderMapper, OrderLabelGenerator orderLabelGenerator, List<OrderSubmitter<?>> orderSubmitter,
			AdviceService adviceService, OrderProcessCheckSwitchAndSave orderProcessCheckSwitchAndSave, OrderDefaults orderDefaults, OrderLabelPrinter labelPrinter,
			List<OrderPostProcessor<?>> postProcessors, DangerousGoodsTransferListService dangerousGoodsTransferListService) {
		super(orderRepository, orderMapper, orderLabelGenerator, orderSubmitter, adviceService, orderProcessCheckSwitchAndSave, orderDefaults, labelPrinter,
				dangerousGoodsTransferListService, postProcessors);
	}

	@Override
	void prepareOrderAndCheckPreconditions(BasicOrderDto basicOrderDto) {
		setOrderDto(basicOrderDto);
		setCurrentOrder(orderMapper.map(basicOrderDto));
		final Order currentOrder = getCurrentOrder();
		checkCurrentOrderForExpiredQuoteOrder();
		prefillDefaults(currentOrder);
	}

}
