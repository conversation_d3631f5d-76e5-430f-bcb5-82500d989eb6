FROM  docker-all.dach041.dachser.com/ubi8/openjdk-21-runtime
COPY target/app.jar app.jar

USER 0
COPY certificates/ /certificates/
RUN keytool -cacerts -import -noprompt -alias cert-1 -file /certificates/cert-1.crt -storepass changeit;
RUN keytool -cacerts -import -noprompt -alias certnew -file /certificates/certnew.cer -storepass changeit;
RUN keytool -cacerts -import -noprompt -alias proxycert -file /certificates/proxycert.cer -storepass changeit;
RUN keytool -cacerts -import -noprompt -alias root-cert -file /certificates/root-cert.cer -storepass changeit;
USER 1001

ENTRYPOINT ["java","-XX:MaxRAMPercentage=75","-jar","app.jar"]
