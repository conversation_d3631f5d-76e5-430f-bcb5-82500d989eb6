package com.dachser.dfe.book.transferlist.service;

import com.dachser.dfe.book.jpa.OrderSsccRepository;
import com.dachser.dfe.book.model.OrderLineDetailDto;
import com.dachser.dfe.book.model.PackingPositionDto;
import com.dachser.dfe.book.model.TransferListFilterDto;
import com.dachser.dfe.book.model.TransferListItemDto;
import com.dachser.dfe.book.model.TransferListQueryObjectDto;
import com.dachser.dfe.book.model.TransferListResponseDto;
import com.dachser.dfe.book.overview.CachedOrderOverviewService;
import com.dachser.dfe.book.packaging.PackagingOptionsService;
import com.dachser.dfe.book.transferlist.entity.TransferListFilterItem;
import com.dachser.dfe.book.transferlist.entity.VTransferList;
import com.dachser.dfe.book.transferlist.exception.TransferListDatabaseException;
import com.dachser.dfe.book.transferlist.mapper.TransferListFilterMapper;
import com.dachser.dfe.book.transferlist.mapper.TransferListMapper;
import com.dachser.dfe.book.transferlist.repository.TransferListFilterRepository;
import com.dachser.dfe.book.transferlist.repository.VTransferListRepository;
import com.dachser.dfe.book.user.UserContextService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class TransferListServiceImpl extends TransferListBaseServiceImpl implements TransferListService {

	private final TransferListFilterRepository transferListFilterRepository;

	private final TransferListMapper transferListMapper;

	private final TransferListFilterMapper transferListFilterMapper;

	public TransferListServiceImpl(VTransferListRepository transferListRepository,
			UserContextService userContextService, CachedOrderOverviewService cachedOrderOverviewService,
			RoadOrderLineService roadOrderLineService,  PackingPositionService packingPositionService,TransferListFilterRepository transferListFilterRepository, TransferListMapper transferListMapper,
			TransferListFilterMapper transferListFilterMapper, PackagingOptionsService packagingOptionsService, OrderSsccRepository orderSsccRepository) {
		super(transferListRepository, userContextService, cachedOrderOverviewService, roadOrderLineService, packingPositionService,packagingOptionsService, orderSsccRepository);
		this.transferListFilterRepository = transferListFilterRepository;
		this.transferListMapper = transferListMapper;
		this.transferListFilterMapper = transferListFilterMapper;
	}

	@Override
	@Retryable(retryFor = { TransferListDatabaseException.class }, maxAttempts = 2, listeners = "loggerRetryListener")
	public TransferListResponseDto getTransferList(TransferListQueryObjectDto queryObject) {
		List<VTransferList> transferListItems = fetchTransferList(queryObject);
		return createTransferListResponse(transferListItems);
	}

	@Override
	@Retryable(retryFor = { TransferListDatabaseException.class }, maxAttempts = 2, listeners = "loggerRetryListener")
	public List<TransferListFilterDto> getTransferListFilter() {
		List<String> customerNumbersWithSegment = userContextService.getAllBookPermittedCustomerIdsWithSegment();
		return fetchTransferListFilters(customerNumbersWithSegment).stream().map(transferListFilterMapper::map).toList();
	}

	private List<TransferListFilterItem> fetchTransferListFilters(List<String> customerNumbersWithSegment) {
		try {
			return transferListFilterRepository.findAll(customerNumbersWithSegment);
		} catch (Exception ex) {
			String errorMsg = String.format("Transfer List (Filters): Error while retrieving data from database: %s.", ex.getMessage());
			log.error(errorMsg, ex);
			throw new TransferListDatabaseException(errorMsg);
		}
	}

	private TransferListResponseDto createTransferListResponse(List<VTransferList> transferList) {
		TransferListResponseDto responsePage = new TransferListResponseDto();
		responsePage.setContent(createTransferListResultSet(transferList));
		responsePage.setTotalElements(transferList.size());
		return responsePage;
	}

	private List<TransferListItemDto> createTransferListResultSet(List<VTransferList> items) {
		return items.stream().map(this::mapTransferList).sorted(compareByConsigneeAddressName(o -> {
			if (o.getConsigneeAddress() != null) {
				return o.getConsigneeAddress().getName();
			} else {
				return null;
			}
		})).toList();
	}

	private TransferListItemDto mapTransferList(VTransferList transferListItem) {
		mapProductName(transferListItem);
		TransferListItemDto transferListItemDto = transferListMapper.mapTransferList(transferListItem);
		mapOrderLine(transferListItemDto);
		mapPackingPosition(transferListItemDto);
		mapLabelCount(transferListItemDto);
		return transferListItemDto;
	}

	private void mapLabelCount(TransferListItemDto transferListItem) {
		long labelsPerOrder = getLabelCount(transferListItem.getOrderId());
		transferListItem.setQuantity(labelsPerOrder);
	}

	private void mapOrderLine(TransferListItemDto transferListItem) {
		List<OrderLineDetailDto> orderLineItems = getRoadOrderLines(transferListItem.getOrderId());
		replacePackagingCodeWithDescription(orderLineItems);
		transferListItem.setOrderLineItems(orderLineItems);
	}

	private void mapPackingPosition(TransferListItemDto transferListItem) {
		List<PackingPositionDto> packingPositions = getPackingPositions(transferListItem.getOrderId());
		mapPackingPosition(packingPositions);
		transferListItem.setPackingPositions(packingPositions);
	}

	private void replacePackagingCodeWithDescription(List<OrderLineDetailDto> orderLineDetails) {
		for (OrderLineDetailDto detail : orderLineDetails) {
			String description = lookupPackagingDescription(detail.getPackaging().getCode());
			detail.getPackaging().setDescription(description);
		}
	}
}
