package com.dachser.dfe.book.logging;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

@Slf4j
@ConditionalOnProperty(name = "logging.performancelogging.enabled", havingValue = "true")
@Component
@Aspect
public class LoggingAspect {
	private static final String LOG_MESSAGE_FORMAT = "%s.%s execution time: %dms";

	@Value("${logging.performancelogging.threshold}")
	private long msThreshold;

	// @formatter:off
	// Pointcut for all packages, classes and methods that should be performance logged
	@Pointcut(
			// API Handlers
			"""
			execution(* com.dachser.dfe.book.resthandler..*(..)) || \
			execution(* com.dachser.dfe.book.customer.CustomerListApiHandler.*(..)) ||\
			execution(* com.dachser.dfe.book.customer.CustomersApiHandler.*(..)) ||\
			execution(* com.dachser.dfe.book.document.DocumentApiHandler.*(..)) ||\
			execution(* com.dachser.dfe.book.order.OrderApiHandler.*(..)) ||\
			execution(* com.dachser.dfe.book.order.OrdersContentApiHandler.*(..)) ||\
			execution(* com.dachser.dfe.book.port.PortApiHandler.*(..)) ||\
			execution(* com.dachser.dfe.book.jpa..*(..)) || \
			execution(* com.dachser.dfe.book.user.UserService.*(..)) || \
			execution(* com.dachser.dfe.book.user.platform..*(..)) || \
			execution(* com.dachser.dfe.book.security..*(..)) ||\
			execution(* com.dachser.dfe.book.service.ext..*(..)) || \
			execution(* com.dachser.dfe.book.advice..*(..)) ||\
			execution(* com.dachser.dfe.book.country..*(..)) ||\
			execution(* com.dachser.dfe.book.dip.publisher..*(..)) ||\
			execution(* com.dachser.dfe.book.document.kta.KTAUploadServiceExt.*(..)) ||\
			execution(* com.dachser.dfe.book.document.archive.ArchiveServiceExt.*(..)) ||\
			execution(* com.dachser.dfe.book.service.forwardingdomain..*(..)) ||\
			execution(* com.dachser.dfe.book.service.freightterm..*(..)) ||\
			execution(* com.dachser.dfe.book.service.goodsgroup..*(..)) ||\
			execution(* com.dachser.dfe.book.service.loadingpoint..*(..)) ||\
			execution(* com.dachser.dfe.book.service.ordergroup..*(..)) ||\
			execution(* com.dachser.dfe.book.service.postalcode..*(..)) ||\
			execution(* com.dachser.dfe.book.order.OrderSubmitter.submitOrder(..)) ||\
			execution(* com.dachser.dfe.book.packaging.PackagingOptionsServiceExt.*(..))\
			"""
	)
	public void logPointcutWithExecution() {
		// this method is only declared to hold the pointcut configuration data
	}
	// @formatter:on

	@Around("logPointcutWithExecution()")
	public Object logTimeMethod(final ProceedingJoinPoint joinPoint) throws Throwable {
		final StopWatch stopWatch = new StopWatch();
		stopWatch.start();

		final Object retVal = joinPoint.proceed();

		stopWatch.stop();

		if (stopWatch.getTotalTimeMillis() > msThreshold) {
			log.debug(LOG_MESSAGE_FORMAT.formatted(joinPoint.getTarget().getClass().getName(), joinPoint.getSignature().getName(), stopWatch.getTotalTimeMillis()));
		}

		return retVal;
	}
}