package com.dachser.dfe.book.dangerousgoods;

import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.dfe.book.order.common.dangerousgood.ADRDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGoodDataItem;
import com.dachser.dfe.book.order.common.dangerousgood.LQDangerousGood;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.masterdata.thirdparty.model.MDTCalculationPoint;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class DangerousGoodsTransferListService {

	private static final Locale DEFAULT_LOCALE = Locale.US;

	private final DangerousGoodsThirdPartyAdapter adapter;

	/**
	 * Calculates and assigns ADR points for dangerous goods
	 * in the provided forwarding order.
	 *
	 * <p>This method performs the following steps:
	 * <ol>
	 *   <li>Checks whether the order contains any ADR-classified goods.</li>
	 *   <li>Collects initial calculation points based on the gross mass of each ADR item.</li>
	 *   <li>Retrieves unit point factors via an external adapter, based on country-specific rules.</li>
	 *   <li>Recalculates the total points for each item using the original quantities and the unit factors.</li>
	 *   <li>Applies the calculated points back to the corresponding ADR goods in the order.</li>
	 * </ol>
	 *
	 * @param order the forwarding order containing ADR dangerous goods
	 * @return {@code true} if all ADR goods in the order have calculated points assigned, {@code false} otherwise
	 */
	public boolean calculateDangerousGoodsPoints(ForwardingOrder order) {
		if (!order.isDangerousGoodsOrder()) {
			return true;
		}
		List<MDTCalculationPoint> rawPoints = collectCalculationPoints(order);
		if (rawPoints.isEmpty()) {
			log.debug("No ADR goods requiring calculation for order {}", order.getOrderId());
			return true;
		}

		Map<Integer, Integer> unitFactors = determineUnitFactors(rawPoints, order.getShipperAddress().getCountryCode());
		List<DgmIdAndQuantity> adjustedResults = recalculatePoints(rawPoints, unitFactors);

		applyResults(order, adjustedResults);
		return order.getOrderLines().stream().map(RoadOrderLine::getAdrDangerousGoods).flatMap(List::stream).noneMatch(adr -> Objects.isNull(adr.getCalculatedPoints()));
	}

	@Nonnull
	public ForwardingOrder fetchLocalizedDescriptionForDangerousGoods(@Nonnull final ForwardingOrder order, @Nonnull final String targetLanguage) {
		if (order.isDangerousGoodsOrder()) {
			// @formatter:off
			final List<ADRDangerousGood> adrGoods = order.getOrderLines()
					.stream()
					.map(RoadOrderLine::getAdrDangerousGoods)
					.flatMap(List::stream)
					.filter(dg -> Objects.nonNull(dg.getDangerousGoodDataItem()))
					.toList();
			final List<LQDangerousGood> lqGoods = order.getOrderLines()
					.stream()
					.map(RoadOrderLine::getLqDangerousGoods)
					.flatMap(List::stream)
					.filter(dg -> Objects.nonNull(dg.getDangerousGoodDataItem()))
					.toList();
			// @formatter:on

			final List<String> dgmIds = new ArrayList<>();

			dgmIds.addAll(adrGoods.stream().map(ADRDangerousGood::getDangerousGoodDataItem).map(DangerousGoodDataItem::getExternalDgmId).filter(StringUtils::isNotBlank).distinct()
					.toList());
			dgmIds.addAll(lqGoods.stream().map(LQDangerousGood::getDangerousGoodDataItem).map(DangerousGoodDataItem::getExternalDgmId).filter(StringUtils::isNotBlank).distinct()
					.toList());

			if (dgmIds.isEmpty()) {
				log.debug("No specific dangerous goods found in order {}", order.getOrderId());
				return order;
			}
			if (!(DEFAULT_LOCALE.getLanguage().equals(targetLanguage)) || hasDangerousGoodsWithoutDescription(adrGoods, lqGoods)) {
				final List<DangerousGoodDataItemDto> defaultDtos = adapter.fetchLocalizedInformationForDgmIdsRoad(dgmIds, DEFAULT_LOCALE.getCountry());
				// Country Code is unknown for us, we only have the language
				final List<DangerousGoodDataItemDto> localizedDtos = adapter.fetchLocalizedInformationForDgmIdsRoad(dgmIds, targetLanguage.toUpperCase());
				if (defaultDtos.isEmpty() || localizedDtos.isEmpty()) {
					log.debug("No localized dangerous goods information found for order {}", order.getOrderId());
					return order;
				}
				// Updates Descriptions for ADR- und LQ-Goods
				applyUpdatesFromDto(order, defaultDtos, true);
				applyUpdatesFromDto(order, localizedDtos, false);
			}
		}
		return order;
	}

	private boolean hasDangerousGoodsWithoutDescription(List<ADRDangerousGood> adrGoods, List<LQDangerousGood> lqGoods) {
		// @formatter:off
		return (adrGoods.stream().anyMatch(adr -> StringUtils.isBlank(adr.getDangerousGoodDataItem().getDescription()))
					|| lqGoods.stream().anyMatch(lq -> StringUtils.isBlank(lq.getDangerousGoodDataItem().getDescription())))
				|| (adrGoods.stream().anyMatch(adr -> StringUtils.isBlank(adr.getDangerousGoodDataItem().getDefaultDescription()))
					|| lqGoods.stream().anyMatch(lq -> StringUtils.isBlank(lq.getDangerousGoodDataItem().getDefaultDescription())));
		// @formatter:on
	}

	private void applyUpdatesFromDto(ForwardingOrder order, @Nonnull List<DangerousGoodDataItemDto> dtos, boolean isDefault) {
		// @formatter:off
		order.getOrderLines().stream()
				.filter(RoadOrderLine::hasDangerousGoods)
				.map(RoadOrderLine::getDangerousGoods)
				.flatMap(List::stream)
				.forEach(good -> dtos.stream()
						.distinct()
						.filter(dto -> dto.getDgmId().equals(getDgmId(good)))
						.findFirst()
						.ifPresent(dto -> {
							updateDescriptions(good, dto, isDefault);
				}));
		// @formatter:on
	}

	@Nullable
	private String getDgmId(DangerousGood dangerousGood) {
		switch (dangerousGood) {
		case ADRDangerousGood adr -> {
			final DangerousGoodDataItem dataItem = adr.getDangerousGoodDataItem();
			return Optional.ofNullable(dataItem).isPresent() ? dataItem.getExternalDgmId() : null;
		}
		case LQDangerousGood lq -> {
			final DangerousGoodDataItem dataItem = lq.getDangerousGoodDataItem();
			return Optional.ofNullable(dataItem).isPresent() ? dataItem.getExternalDgmId() : null;
		}
		default -> {
			log.warn("Unsupported DangerousGood type: {}", dangerousGood.getClass().getSimpleName());
			return null;
		}
		}
	}

	private void updateDescriptions(@Nonnull DangerousGood dangerousGood, @Nonnull DangerousGoodDataItemDto dto, boolean isDefault) {
		switch (dangerousGood) {
		case ADRDangerousGood adr -> Optional.ofNullable(adr.getDangerousGoodDataItem()).ifPresentOrElse(dataItem -> {
			if (isDefault) {
				dataItem.setDefaultDescription(dto.getDescription());
			} else {
				dataItem.setDescription(dto.getDescription());
			}
		}, () -> log.warn("ADRDangerousGood has no DangerousGoodDataItem to update: {}", adr));
		case LQDangerousGood lq -> Optional.ofNullable(lq.getDangerousGoodDataItem()).ifPresentOrElse(dataItem -> {
			if (isDefault) {
				dataItem.setDefaultDescription(dto.getDescription());
			} else {
				dataItem.setDescription(dto.getDescription());
			}
		}, () -> log.warn("LQDangerousGood has no DangerousGoodDataItem to update: {}", lq));
		default -> log.warn("Can not update description of DangerousGood type: {}", dangerousGood.getClass().getSimpleName());
		}
	}

	private List<MDTCalculationPoint> collectCalculationPoints(ForwardingOrder order) {
		return order.getOrderLines().stream().map(RoadOrderLine::getAdrDangerousGoods).flatMap(List::stream).filter(Objects::nonNull).map(this::toCalculationPoint)
				.flatMap(Optional::stream).toList();
	}

	private Optional<MDTCalculationPoint> toCalculationPoint(@Nonnull ADRDangerousGood adrDangerousGood) {
		Integer calculatedPoints = adrDangerousGood.getCalculatedPoints();
		if (calculatedPoints != null) {
			log.debug("Skipping ADR good {} as it already has calculated points", adrDangerousGood);
		} else {
			Optional<Integer> idOpt = parseDgmId(adrDangerousGood);
			BigDecimal grossMass = adrDangerousGood.getGrossMass();
			if (idOpt.isPresent() && Objects.nonNull(grossMass)) {
				return Optional.of(new MDTCalculationPoint().dgmId(idOpt.get()).quantity(grossMass.doubleValue()));
			}
			log.debug("Skipping ADR good with missing id or mass: {}", adrDangerousGood);
		}
		return Optional.empty();
	}

	private Optional<Integer> parseDgmId(@Nonnull ADRDangerousGood adrDangerousGood) {
		AtomicReference<Optional<Integer>> dgmIdOpt = new AtomicReference<>(Optional.empty());
		Optional.ofNullable(adrDangerousGood.getDangerousGoodDataItem()).ifPresent(dataItem -> {
			final String externalDgmId = dataItem.getExternalDgmId();
			if (!StringUtils.isBlank(externalDgmId)) {
				try {
					dgmIdOpt.set(Optional.of(Integer.parseInt(externalDgmId)));
				} catch (NumberFormatException e) {
					log.warn("Invalid DGM ID format: {}", externalDgmId);
				}
			}
		});
		return dgmIdOpt.get();
	}

	private Map<Integer, Integer> determineUnitFactors(List<MDTCalculationPoint> points, String countryCode) {
		List<MDTCalculationPoint> unitPoints = points.stream().map(p -> new MDTCalculationPoint().dgmId(p.getDgmId()).quantity(1.0)).distinct().toList();

		List<DgmIdAndQuantity> results = adapter.calculatePointsForSingleValues(countryCode, unitPoints);

		if (results == null) {
			log.debug("No unit factors found for country code: {}", countryCode);
			return Map.of();
		}
		return results.stream().filter(r -> r.resultValue() != null).collect(Collectors.toMap(DgmIdAndQuantity::dgmId, DgmIdAndQuantity::resultValue));
	}

	private List<DgmIdAndQuantity> recalculatePoints(List<MDTCalculationPoint> rawPoints, Map<Integer, Integer> unitFactors) {
		return rawPoints.stream().map(p -> {
			Integer factor = unitFactors.get(p.getDgmId());
			Integer newValue = (factor != null) ? (int) Math.round(factor * p.getQuantity()) : null;
			return new DgmIdAndQuantity(p.getDgmId(), p.getQuantity(), newValue);
		}).toList();
	}

	private void applyResults(ForwardingOrder order, List<DgmIdAndQuantity> results) {
		if (!results.isEmpty()) {
			order.getOrderLines().forEach(line -> line.getAdrDangerousGoods().stream().filter(Objects::nonNull).forEach(adr -> updateAdr(adr, results)));
		}
	}

	private void updateAdr(@Nonnull ADRDangerousGood adr, List<DgmIdAndQuantity> results) {
		parseDgmId(adr).ifPresent(id -> {
			results.stream().filter(r -> r.dgmId().equals(id)).findFirst().ifPresentOrElse(r -> {
				if (r.resultValue() == null) {
					log.warn("Skipping ADR good with missing result value: {}", adr);
				}
				adr.setCalculatedPoints(r.resultValue());
			}, () -> log.warn("No recalculation result for DGM ID {}", id));
		});
	}

	public record DgmIdAndQuantity(Integer dgmId, Double quantity, Integer resultValue) {
	}
}