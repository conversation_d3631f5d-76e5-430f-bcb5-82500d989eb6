server:
  port: 8090

logging:
  level:
    org.springframework.security.oauth2: DEBUG
    web: DEBUG
    org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping: TRACE
    liquibase.changelog: DEBUG
    com.dachser.dfe: DEBUG

dfe:
  services:
    document:
      basePath: application/upload/documents/
    order:
      basePath: application/upload/documents/orders/

oAuth:
  server-base-url: "https://login.platform-dev.dach041.dachser.com/realms/dfe/protocol/openid-connect"

ums:
  enabled: false
  # Set this in your local environment when enabling UMS
  # ENV_VAULT_PATH: dev
  connection:
    # Set when disabling UMS
    factory: SpringTestConnectionFactory
    # Set when enabling UMS
    # factory: "DipCF"
  provider:
    url: "nsp://APW041UMST01.dachser-ds.dachser.com:9000,nsp://APW041UMST02.dachser-ds.dachser.com:9000"
  dip:
    ediTestFlagRoad: true
    ediTestFlagAirSea: true
    destination: /com/dachser/bi/dfe/book/order

ENV_BOOK_TRIAL_MODE_ROAD: true
ENV_BOOK_TRIAL_MODE_AIR_SEA: true
ENV_BOOK_API_DFE_SHAREDSERVICES_MASTERDATA_BUSINESSPARTNER_BASEURL: "https://dach041y.dach041.dachser.com:21167/masterdata.businesspartner.backend"
ENV_BOOK_API_DFE_SHAREDSERVICES_MASTERDATA_GEO_BASEURL: "https://dach041y.dach041.dachser.com:21175/masterdata.geo.service"
ENV_BOOK_API_DFE_AIRSEA_MASTERDATA_BASEURL: "https://dach041x.dach041.dachser.com:21397/airsea.masterdata.service"
ENV_BOOK_API_DFE_ROAD_MASTERDATA_BASEURL: "https://dach041x.dach041.dachser.com:21365/road.masterdata.order.service"
ENV_BOOK_API_DFE_ROAD_CONSIGNMENT_LABEL_BASEURL: "https://dach041y.dach041.dachser.com:21957/road.consignmentlabel.service"
ENV_BOOK_API_DFE_PLATFORM_BACKEND_BASEURL: "https://dfe-platform-backend-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"
ENV_BOOK_API_CONFIGSERVER_BASEURL: "https://api.platform-dev.dach041.dachser.com/config/"
ENV_BOOK_API_GENERAL_DATA_SERVER_BASEURL: "https://dfe-general-data-backend-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"
ENV_BOOK_API_PDF_EXPORT_SERVER_BASEURL: "https://dfe-pdf-export-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"
ENV_BOOK_API_DFE_EMISSIONFORECAST_BASEURL: "https://dfe-emissionforecast-backend-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"
ENV_BOOK_API_MASTERDATA_GATEWAY_BASEURL: "https://masterdata-dev.api.dachser.com"

# LOCAL H2 DATABASE CONFIGURATION IN MEMORY
---
spring:
  config:
    activate:
      on-profile: local-h2,local-h2-disk
  datasource:
    driver-class-name: org.h2.Driver
    username: sa
    password:
    url: "jdbc:h2:mem:db1;MODE=MSSQLServer;DB_CLOSE_ON_EXIT=FALSE;INIT=CREATE SCHEMA IF NOT EXISTS dbo\\;SET SCHEMA dbo"
    hikari:
      register-mbeans: false
  h2:
    console:
      enabled: true
  liquibase:
    contexts: mock-data,h2
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
    show-sql: false

# LOCAL H2 DATABASE CONFIGURATION ON DISK
---
spring:
  config:
    activate:
      on-profile: local-h2-disk
  datasource:
    url: "jdbc:h2:file:./data/book;MODE=MSSQLServer;DB_CLOSE_ON_EXIT=FALSE;INIT=CREATE SCHEMA IF NOT EXISTS dbo\\;SET SCHEMA dbo"
  liquibase:
    contexts: h2

# LOCAL MSSQL DATABASE CONFIGURATION
---
spring:
  config:
    activate:
      on-profile: local-mssql
  datasource:
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    username: dfe_user
    password: Passw0rd
    url: "************************************************************************************************"
    hikari:
      register-mbeans: false
  liquibase:
    contexts: mssql
    parameters:
      default-schema: dbo
  jpa:
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.SQLServerDialect

# REMOTE MSSQL ON DEV CLUSTER AS SANDBOX DB
---
spring:
  config:
    activate:
      on-profile: dev-mssql-sandbox
  datasource:
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    url: "******************************************************************************************************************;"
    hikari:
      register-mbeans: false
  liquibase:
    contexts: mssql
    parameters:
      default-schema: ${USERDOMAIN}\${USERNAME}
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.SQLServerDialect

---
# ADVANCED DEBUG CONFIGURATION
spring:
  config:
    activate:
      on-profile: advanced-debug
  jpa:
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
logging:
  level:
    root: DEBUG
    sql: DEBUG
    web: DEBUG
    liquibase.changelog: DEBUG
    com.dachser.dfe: DEBUG
    org.hibernate.orm.jdbc.bind: TRACE
    org.hibernate.sql: DEBUG
    org.hibernate.stat: DEBUG
    org.hibernate.type: TRACE
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.jdbc.core.JdbcTemplate: DEBUG
    org.springframework.jdbc.core.StatementCreatorUtils: TRACE
    org.springframework.security.oauth2: DEBUG
    org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping: TRACE

ENV_BOOK_DB_LOGLEVEL: DEBUG
ENV_BOOK_APPLICATION_LOGLEVEL: DEBUG