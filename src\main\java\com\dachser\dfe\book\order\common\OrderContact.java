package com.dachser.dfe.book.order.common;

import com.dachser.dfe.book.order.validation.air.CompleteOrderValidationAir;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderContact {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long orderContactId;

	@Size(max = 40)
	@NotNull(groups = CompleteOrderValidationAir.class, message = "{label.text.invalid_input}")
	private String name;

	@Size(max = 150)
	private String email;

	@Size(max = 30)
	private String telephone;

	@Size(max = 30)
	private String mobile;

	@Size(max = 30)
	private String fax;
}
