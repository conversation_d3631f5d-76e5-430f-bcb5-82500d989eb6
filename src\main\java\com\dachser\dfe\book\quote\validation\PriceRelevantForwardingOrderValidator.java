package com.dachser.dfe.book.quote.validation;

import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.quote.RoadQuoteInformation;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
class PriceRelevantForwardingOrderValidator extends PriceRelevantRoadOrderValidator<ForwardingOrder> {

	private static final String PALLET_LOCATIONS = "palletLocations";

	private static final String SELFCOLLECT = "selfCollector";

	private static final String CASH_ON_DELIVERY = "cashOnDelivery";

	private static final String CASH_ON_DELIVERY_AMOUNT = "cashOnDeliveryAmount";

	PriceRelevantForwardingOrderValidator(PriceRelevantOrderLineFieldsValidator<RoadOrderLine> orderLineValidator,
			PriceRelevantPackingPositionFieldsValidator<PackingPosition> packingPositionValidator, PriceRelevantAddressFieldsValidator addressValidator) {
		super(orderLineValidator, packingPositionValidator, addressValidator);
	}

	List<PriceRelevantChange> getOrderSpecificChanges(RoadQuoteInformation quoteInformation, ForwardingOrder order) {
		final List<PriceRelevantChange> priceRelevantChanges = getPriceRelevantChanges(quoteInformation, order);

		//@formatter:off
		List<Pair<String, Boolean>> roadFields = new ArrayList<>(List.of(
				Pair.of(SELFCOLLECT, Objects.equals(quoteInformation.getSelfCollector(), order.getSelfCollection())),
				Pair.of(PALLET_LOCATIONS, Objects.equals(quoteInformation.getPalletLocations(), order.getPalletLocations())),
				Pair.of(CASH_ON_DELIVERY, Objects.equals(quoteInformation.isCashOnDelivery(), order.isCashOnDelivery())),
				Pair.of(CASH_ON_DELIVERY_AMOUNT, Objects.equals(quoteInformation.getCashOnDeliveryAmount(), order.getCashOnDeliveryAmount()))
		));
		//@formatter:on

		priceRelevantChanges.addAll(buildPriceRelevantChangesFromFields(roadFields));

		priceRelevantChanges.addAll(getAddressValidator().getPriceRelevantChanges(quoteInformation.getShipperAddress(), order.getShipperAddress()).stream()
				.map(change -> new PriceRelevantChange(PRINCIPAL_SELECTION + change.fieldName())).toList());

		return priceRelevantChanges;
	}

}
