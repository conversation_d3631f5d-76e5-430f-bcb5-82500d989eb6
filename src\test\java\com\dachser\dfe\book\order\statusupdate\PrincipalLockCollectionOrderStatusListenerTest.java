package com.dachser.dfe.book.order.statusupdate;

import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.order.OrderRepository;
import com.dachser.dfe.book.order.OrderStatusUpdater;
import com.dachser.dfe.book.order.road.CollectionOrder;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = { PrincipalLockCollectionOrderStatusListener.class, TypedStatusUpdateListenerDecorator.class, OrderStatusUpdater.class, LoggingOrderStatusUpdater.class })
class PrincipalLockCollectionOrderStatusListenerTest {

	@MockBean
	private OrderRepository orderRepository;

	@Autowired
	private OrderStatusUpdater orderStatusUpdater;

	@Test
	void shouldSetPrincipalLockWhenFlagIsSet() {
		CollectionOrder collectionOrder = new CollectionOrder();
		collectionOrder.setStatus(OrderStatus.DRAFT);
		orderStatusUpdater.updateOrderStatus(collectionOrder, OrderStatus.COMPLETE);
		assertTrue(collectionOrder.isPrincipalLocked());
	}

}