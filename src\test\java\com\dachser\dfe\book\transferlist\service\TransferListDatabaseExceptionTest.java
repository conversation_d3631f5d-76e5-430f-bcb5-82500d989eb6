package com.dachser.dfe.book.transferlist.service;

import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ErrorIdBaseException;
import com.dachser.dfe.book.exception.GlobalExceptionHandler;
import com.dachser.dfe.book.jpa.OrderSsccRepository;
import com.dachser.dfe.book.model.GeneralProblemDto;
import com.dachser.dfe.book.model.TransferListQueryObjectDto;
import com.dachser.dfe.book.overview.CachedOrderOverviewService;
import com.dachser.dfe.book.packaging.PackagingOptionsService;
import com.dachser.dfe.book.service.freightterm.FreightTermService;
import com.dachser.dfe.book.spring.LoggerRetryListener;
import com.dachser.dfe.book.transferlist.entity.VTransferList;
import com.dachser.dfe.book.transferlist.exception.TransferListDatabaseException;
import com.dachser.dfe.book.transferlist.mapper.TransferListFilterMapperImpl;
import com.dachser.dfe.book.transferlist.mapper.TransferListMapperImpl;
import com.dachser.dfe.book.transferlist.repository.TransferListFilterRepository;
import com.dachser.dfe.book.transferlist.repository.VTransferListRepository;
import com.dachser.dfe.book.user.UserContextService;
import com.dachser.dfe.dfefiltersorthelper.service.SortAndFilterService;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.web.context.request.WebRequest;

import java.lang.reflect.Method;
import java.time.LocalDate;

import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TRY_AGAIN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = { TransferListServiceImpl.class, LoggerRetryListener.class })
@EnableRetry
@ExtendWith(MockitoExtension.class)
class TransferListDatabaseExceptionTest {

	@Autowired
	private TransferListService sut;

	@MockBean
	private UserContextService userContextService;

	@MockBean
	private SortAndFilterService<VTransferList> sortAndFilterService;

	@MockBean
	private RoadOrderLineService roadOrderLineService;

	@MockBean
	private PackingPositionService packingPositionService;

	@MockBean
	private CachedOrderOverviewService cachedOrderOverviewService;

	@MockBean
	private VTransferListRepository vTransferListRepository;

	@MockBean
	private TransferListFilterRepository filterRepository;

	@MockBean
	private TransferListMapperImpl transferListMapper;

	@MockBean
	private TransferListFilterMapperImpl transferListFilterMapper;

	@MockBean
	private PackagingOptionsService packagingOptionsService;

	@MockBean
	private OrderSsccRepository orderSsccRepository;

	@MockBean
	private FreightTermService freightTermService;

	@Nested
	class GetTransferList {

		@Test
		void throwsTransferListDatabaseException() {
			TransferListQueryObjectDto queryObject= new TransferListQueryObjectDto();
			queryObject.setAddressHash("6D8EC4E80767201A87AD80A6BD6292765771281CE2AB9D0EBEAF0FABCE7B3B6F");
			queryObject.setPickupDate(LocalDate.parse("2024-05-02"));
			when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenThrow(RuntimeException.class);

			TransferListDatabaseException exception = assertThrows(TransferListDatabaseException.class, () -> sut.getTransferList(queryObject));

			assertThat(exception.getErrorId()).isEqualTo(BookErrorId.ERR_TL_01);
		}

		@Test
		void shouldRetryTransferListDatabaseQueryOnceAfterFailing() {
			TransferListQueryObjectDto queryObject= new TransferListQueryObjectDto();
			queryObject.setAddressHash("6D8EC4E80767201A87AD80A6BD6292765771281CE2AB9D0EBEAF0FABCE7B3B6F");
			queryObject.setPickupDate(LocalDate.parse("2024-05-02"));
			when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenThrow(RuntimeException.class).thenThrow(RuntimeException.class);

			TransferListDatabaseException exception = assertThrows(TransferListDatabaseException.class, () -> sut.getTransferList(queryObject));

			assertThat(exception.getErrorId()).isEqualTo(BookErrorId.ERR_TL_01);
			verify(vTransferListRepository, times(2)).findAll(anyList(), anyString(), any(LocalDate.class));
		}
	}

	@Nested
	class GetTransferListFilter {

		@Test
		void throwsTransferListDatabaseException() {
			when(filterRepository.findAll(anyList())).thenThrow(RuntimeException.class);

			TransferListDatabaseException exception = assertThrows(TransferListDatabaseException.class, () -> sut.getTransferListFilter());

			assertThat(exception.getErrorId()).isEqualTo(BookErrorId.ERR_TL_01);
		}

		@Test
		void shouldRetryTransferListDatabaseQueryOnceAfterFailing() {
			when(filterRepository.findAll(anyList())).thenThrow(RuntimeException.class).thenThrow(RuntimeException.class);

			TransferListDatabaseException exception = assertThrows(TransferListDatabaseException.class, () -> sut.getTransferListFilter());

			assertThat(exception.getErrorId()).isEqualTo(BookErrorId.ERR_TL_01);
			verify(filterRepository, times(2)).findAll(anyList());
		}
	}

	@Test
	void handleTransferListDatabaseException() throws Exception {
		TransferListDatabaseException exception = new TransferListDatabaseException("Test error message");
		WebRequest webRequest = mock(WebRequest.class);
		GlobalExceptionHandler globalExceptionHandler = new GlobalExceptionHandler(null, null);

		// Use reflection to access the protected method
		Method method = GlobalExceptionHandler.class.getDeclaredMethod("handleErrorIdGeneralException", ErrorIdBaseException.class, WebRequest.class);
		method.setAccessible(true);

		ResponseEntity<Object> responseEntity = (ResponseEntity<Object>) method.invoke(globalExceptionHandler, exception, webRequest);

		assertThat(responseEntity.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
		assertThat(responseEntity.getBody()).isInstanceOf(GeneralProblemDto.class);
		GeneralProblemDto problemDto = (GeneralProblemDto) responseEntity.getBody();
		assertThat(problemDto.getDetail()).isEqualTo(LABEL_TRY_AGAIN);
	}
}