package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.model.FreightTermDto;
import com.dachser.dfe.book.service.freightterm.FreightTermService;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public abstract class FreightTermCodeMapper {

    @Autowired
    private FreightTermService freightTermService;

	public FreightTermDto map(String dachserKey) {
        return freightTermService.getFreightTermByDachserKey(dachserKey);
    }

}
