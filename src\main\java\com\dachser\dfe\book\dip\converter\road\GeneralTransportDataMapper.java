package com.dachser.dfe.book.dip.converter.road;

import com.dachser.dfe.book.order.common.OrderBaseAddress;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

@Mapper(componentModel = "spring")
public interface GeneralTransportDataMapper {

	@Named("mapValidGlnOrNull")
	default BigInteger mapGLN(String gln) {
		BigInteger result = null;

		if (gln != null && !gln.startsWith("0")) {
			result = new BigInteger(gln);
		}
		return result;
	}

	@Named("mapInvalidGlnAsIdOrNull")
	default String mapGlnAsID(String gln) {
		if (gln != null && gln.startsWith("0")) {
			return gln;
		}
		return null;
	}

	@Named("mapAddressNames")
	default List<String> mapAddressNames(OrderBaseAddress address) {
		List<String> list = new ArrayList<>();

		if (address.getName() != null) {
			list.add(address.getName());
		}
		if (address.getName2() != null) {
			list.add(address.getName2());
		}
		if (address.getName3() != null) {
			list.add(address.getName3());
		}
		return list;
	}


}
