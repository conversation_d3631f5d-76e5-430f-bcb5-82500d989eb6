package com.dachser.dfe.book.transferlist.mapper;

import com.dachser.dfe.book.model.FreightTermDto;
import com.dachser.dfe.book.model.TransferListAddressDto;
import com.dachser.dfe.book.model.TransferListPdfItemDto;
import com.dachser.dfe.book.order.mapper.FreightTermCodeMapper;
import com.dachser.dfe.book.transferlist.entity.VTransferList;
import org.mapstruct.Context;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.Locale;

@Mapper(config = TransferListMapperConfig.class, componentModel = "spring", uses = { FreightTermCodeMapper.class }, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface TransferListPdfMapper {

	int TOUR_IDENTIFIER = 1;

	@Mapping(target = "product.code", source = "product")
	@Mapping(target = "product.description", source = "productName")
	@Mapping(target = "deliveryDate", qualifiedByName = "formattedDateFromOffsetDateTime")
	TransferListPdfItemDto mapTransferListPdfItem(VTransferList transferList, @Context String format, @Context Locale locale);

	FreightTermDto mapFreightTerm(FreightTermDto freightTerm);

	@Named("mapFromAddress")
	default TransferListAddressDto mapFromAddress(VTransferList transferList) {
		return switch (transferList.getAddressType()) {
			case LOADING_POINT -> new TransferListAddressDto().id(transferList.getLpAddress().getOrderFurtherAddressId()).name(transferList.getLpAddress().getName())
					.name2(transferList.getLpAddress().getName2()).name3(transferList.getLpAddress().getName3()).street(transferList.getLpAddress().getStreet())
					.street2(transferList.getLpAddress().getStreet2()).postcode(transferList.getLpAddress().getPostcode()).city(transferList.getLpAddress().getCity())
					.countryCode(transferList.getLpAddress().getCountryCode());
			case PRINCIPAL -> new TransferListAddressDto().id(transferList.getPrincipalAddress().getOrderAddressId()).name(transferList.getPrincipalAddress().getName())
					.name2(transferList.getPrincipalAddress().getName2()).name3(transferList.getPrincipalAddress().getName3())
					.street(transferList.getPrincipalAddress().getStreet()).street2(transferList.getPrincipalAddress().getStreet2())
					.postcode(transferList.getPrincipalAddress().getPostcode()).city(transferList.getPrincipalAddress().getCity())
					.countryCode(transferList.getPrincipalAddress().getCountryCode());
		};
	}

	/**
	 * Maps the document name for the transfer list.
	 *
	 * @param transferList the transfer list
	 * @return the document name
	 * Example:
	 * transfer-list-20241231-p12345-1.pdf (if principal address)
	 * transfer-list-20241231-l12345-1.pdf (if loading point address)
	 */
	default String mapDocumentName(VTransferList transferList, LocalDate date) {
		if (transferList == null || date == null) {
			return null;
		}
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
		String formattedDate = date.format(formatter);
		String addressTypeIndicator = switch (transferList.getAddressType()) {
			case LOADING_POINT -> "l";
			case PRINCIPAL -> "p";
		};
		String addressId = switch (transferList.getAddressType()) {
			case LOADING_POINT -> transferList.getLpAddress().getOrderFurtherAddressId().toString();
			case PRINCIPAL -> transferList.getPrincipalAddress().getOrderAddressId().toString();
		};
		return String.format("transfer-list-%s-%s%s-%d.pdf", formattedDate, addressTypeIndicator, addressId, TOUR_IDENTIFIER);
	}

	@Named("formattedDateFromOffsetDateTime")
	default String formattedDateFromOffsetDateTime(OffsetDateTime dateTime, @Context String format, @Context Locale locale) {
		return formatDateTime(dateTime, format, locale);
	}

	@Named("formattedDateFromLocalDate")
	default String formattedDateFromLocalDate(LocalDate date, @Context String format, @Context Locale locale) {
		return formatDateTime(date, format, locale);
	}

	@Named("formattedDateTimeFromOffsetDateTime")
	default String formattedDateTimeFromOffsetDateTime(OffsetDateTime date, @Context String format, @Context Locale locale) {
		return formatDateTime(date, format, locale);
	}

	private String formatDateTime(TemporalAccessor temporal, String format, Locale locale) {
		if (temporal == null) {
			return null;
		}
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format).withLocale(locale);
		return formatter.format(temporal);
	}

}
