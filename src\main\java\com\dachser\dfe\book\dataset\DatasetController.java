package com.dachser.dfe.book.dataset;

import com.dachser.dfe.book.dataset.model.ForwardingOrderDataset;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Profile({ "apitest", "local", "dev", "test" })
@RequiredArgsConstructor
@RequestMapping("/dataset")
public class DatasetController {

	private final DatasetService datasetService;

	@GetMapping("/forwarding-order")
	@Operation(
			summary = "Retrieve Forwarding Order Dataset",
			description = "This endpoint retrieves a comprehensive forwarding order dataset that includes country data, valid delivery products, and incoterms. " +
					"It uses the provided shipper and consignee country codes and postal codes to gather the necessary data from multiple external services " +
					"in parallel. Security is enforced via the 'dachserOAuth' mechanism.",
			security = { @SecurityRequirement(name = "dachserOAuth") }
	)
	ResponseEntity<ForwardingOrderDataset> getForwardingOrderDataset(String shipperCountryCode, String shipperPostalCode, String consigneeCountryCode, String consigneePostalCode) {
		final ForwardingOrderDataset dataset = datasetService.getForwardingOrderDataset(consigneeCountryCode, consigneePostalCode, shipperCountryCode, shipperPostalCode);
		return ResponseEntity.ok(dataset);
	}
}
