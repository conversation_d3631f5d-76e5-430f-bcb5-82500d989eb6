package com.dachser.dfe.book.annotation;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * This Annotation should be used to mark a method to be executed only if at least one of the given profiles is active.
 * Returns null if no profile is active.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RunOnProfile {
	String[] value();

	@Aspect
	@Configuration
	class RunOnProfileAspect {

		@Autowired
		Environment environment;

		@Around("@annotation(runOnProfile)")
		public Object around(ProceedingJoinPoint joinPoint, RunOnProfile runOnProfile) throws Throwable {
			final String[] profiles = runOnProfile.value();
			for (String profile : profiles) {
				if (environment.acceptsProfiles(Profiles.of(profile))) {
					return joinPoint.proceed();
				}
			}
			return null;
		}
	}
}
