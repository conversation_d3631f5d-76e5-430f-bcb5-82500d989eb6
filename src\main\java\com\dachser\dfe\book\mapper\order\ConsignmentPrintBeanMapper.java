package com.dachser.dfe.book.mapper.order;

import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderSscc;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.user.UserContextService;
import com.dachser.dfe.book.user.model.UserPreferences;
import com.dachser.dfe.road.consignmentlabel.model.RCLAddressInformation;
import com.dachser.dfe.road.consignmentlabel.model.RCLDmcInputDto;
import com.dachser.dfe.road.consignmentlabel.model.RCLIdentity;
import com.dachser.dfe.road.consignmentlabel.model.RCLReference;
import com.dachser.dfe.road.consignmentlabel.model.RCLShipment;
import com.dachser.dfe.road.consignmentlabel.model.RCLShipmentWeight;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.stream.Stream;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
public abstract class ConsignmentPrintBeanMapper {

	protected static final RCLDmcInputDto.FormatEnum LABEL_FORMAT = RCLDmcInputDto.FormatEnum.PDF;

	protected static final RCLShipmentWeight.UnitEnum WEIGHT_UNIT = RCLShipmentWeight.UnitEnum.KG;

	private final String defaultLabelSize = RCLDmcInputDto.LabelSizeEnum.TTD_102X152.getValue();

	@Autowired
	protected CountryService countryService;

	@Autowired
	protected UserContextService userContextService;

	@Mapping(target = "format", expression = "java(ConsignmentPrintBeanMapper.LABEL_FORMAT)")
	@Mapping(target = "labelSize", source = ".", qualifiedByName = "mapLabelSize")
	@Mapping(target = "ssccs", source = "ssccs", qualifiedByName = "mapSsccs")
	@Mapping(target = "count", expression = "java(order.getSsccs().size())")
	@Mapping(target = "shipment", source = ".")
	public abstract RCLDmcInputDto map(ForwardingOrder order);

	@Mapping(target = "division", source = "division.key")
	@Mapping(target = "shipmentDate", source = "collectionDate")
	@Mapping(target = "deliveryDate", source = "fixDate", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "forwarder.id", source = "branchId")
	@Mapping(target = "consignor", source = "shipperAddress")
	@Mapping(target = "consignee", source = "consigneeAddress")
	@Mapping(target = "references", source = "orderReferences")
	@Mapping(target = "shipmentWeight.weight", source = "totalOrderWeight")
	@Mapping(target = "shipmentWeight.unit", expression = "java(ConsignmentPrintBeanMapper.WEIGHT_UNIT)")
	abstract RCLShipment mapShipment(ForwardingOrder order);

	@Mapping(target = "language", expression = "java(org.springframework.context.i18n.LocaleContextHolder.getLocale().getLanguage())")
	@Mapping(target = "partnerGLN", source = "gln")
	@Mapping(target = "names", source = ".", qualifiedByName = "mapNames")
	@Mapping(target = "addressInformation", source = ".")
	abstract RCLIdentity mapIdentity(OrderAddress orderAddress);

	@Mapping(target = "streets", source = ".", qualifiedByName = "mapStreets")
	@Mapping(target = "postalCode", source = "postcode", qualifiedByName = "cleanPostCode")
	@Mapping(target = "countryCode", source = "countryCode", qualifiedByName = "mapCountryCodeToDachserCode")
	@Mapping(target = "supplementInformation", source = "supplement")
	abstract RCLAddressInformation mapAddressInformation(OrderAddress orderAddress);

	abstract List<RCLReference> mapReferences(List<RoadOrderReference> references);

	@Mapping(target = "code", source = "referenceType.coreSystemsValue")
	@Mapping(target = "value", source = "reference")
	abstract RCLReference mapReference(RoadOrderReference reference);

	@Named("mapSsccs")
	List<String> mapSsccs(List<OrderSscc> ssccs) {
		return ssccs.stream().map(OrderSscc::getSscc).toList();
	}

	@Named("mapNames")
	List<String> mapNames(OrderAddress orderAddress) {
		return Stream.of(orderAddress.getName(), orderAddress.getName2(), orderAddress.getName3()).filter(name -> name != null && !name.isBlank()).toList();
	}

	@Named("mapStreets")
	List<String> mapStreets(OrderAddress address) {
		return Stream.of(address.getStreet(), address.getStreet2() != null ? address.getStreet2() : "").filter(street -> street != null && !street.isBlank()).toList();
	}

	@Named("cleanPostCode")
	public String cleanPostCode(String postcode) {
		return postcode.replaceAll("\\s", "");
	}

	@Named("mapCountryCodeToDachserCode")
	String mapCountryCodeToDachserCode(String countryCode) {
		return countryService.mapToDachserCountryCode(countryCode);
	}

	@Named("mapLabelSize")
	String mapLabelSize(ForwardingOrder notNeeded) {
		String labelSize = defaultLabelSize;
		UserPreferences userPreferences = userContextService.getUserPreferences();
		if (userPreferences != null) {
			Integer printerHeight = userPreferences.getPrinterHeight();
			Integer printerWidth = userPreferences.getPrinterWidth();
			if (printerHeight != null && printerWidth != null) {
				labelSize = getLabelSizeFromUserSettings(printerWidth, printerHeight);
			}
		}
		// we need to use uppercase, since the enum names are all uppercase and mapping wont work otherwise
		return labelSize.toUpperCase();
	}

	private String getLabelSizeFromUserSettings(@NonNull Integer printerWidth, @NonNull Integer printerHeight) {
		if (printerWidth == 102 && printerHeight == 152) {
			return RCLDmcInputDto.LabelSizeEnum.TTD_102X152.getValue();
		} else if (printerWidth == 105 || printerHeight == 157) {
			return RCLDmcInputDto.LabelSizeEnum.TTD_105X157.getValue();
		} else if (printerWidth == 107 && printerHeight == 152) {
			return RCLDmcInputDto.LabelSizeEnum.TTD_107X152.getValue();
		} else if (printerWidth == 113 || printerHeight == 165) {
			return RCLDmcInputDto.LabelSizeEnum.TTD_113X165.getValue();
		}
		// Default label size
		return RCLDmcInputDto.LabelSizeEnum.TTD_102X152.getValue();
	}

}