package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.document.Document;
import com.dachser.dfe.book.document.DocumentRepositoryFacade;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.common.OrderText;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaImportOrder;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.book.service.ShipmentNumberService;
import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.SubclassExhaustiveStrategy;

import java.util.List;

@Mapper(componentModel = "spring", subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION)
@Named("OrderTypeConversionMapper")
public interface OrderTypeConversionMapper {

	@Mapping(source = "orderId", target = "orderId", ignore = true)
	@Mapping(source = "shipmentNumber", target = "shipmentNumber", ignore = true)
	@Mapping(source = "shipmentTransferId", target = "shipmentTransferId", ignore = true)
	@Mapping(source = "quoteInformation", target = "quoteInformation", ignore = true)
	@Mapping(constant = "DRAFT", target = "status")
	@Mapping(constant = "DRAFT", target = "previousStatus")
	@Mapping(source = "shipperAddress", target = "consigneeAddress")
	@Mapping(source = "consigneeAddress", target = "shipperAddress")
	@Mapping(source = "fromIATA", target = "toIATA")
	@Mapping(source = "toIATA", target = "fromIATA")
	AirImportOrder convertToAirImportOrder(AirExportOrder airExportOrder, @Context DocumentRepositoryFacade documentRepositoryFacade,
			@Context ShipmentNumberService shipmentNumberService);

	@Mapping(source = "orderId", target = "orderId", ignore = true)
	@Mapping(source = "shipmentNumber", target = "shipmentNumber", ignore = true)
	@Mapping(source = "shipmentTransferId", target = "shipmentTransferId", ignore = true)
	@Mapping(source = "quoteInformation", target = "quoteInformation", ignore = true)
	@Mapping(constant = "DRAFT", target = "status")
	@Mapping(constant = "DRAFT", target = "previousStatus")
	@Mapping(source = "shipperAddress", target = "consigneeAddress")
	@Mapping(source = "consigneeAddress", target = "shipperAddress")
	@Mapping(source = "fromIATA", target = "toIATA")
	@Mapping(source = "toIATA", target = "fromIATA")
	AirExportOrder convertToAirExportOrder(AirImportOrder airImportOrder, @Context DocumentRepositoryFacade documentRepositoryFacade,
			@Context ShipmentNumberService shipmentNumberService);

	@Mapping(source = "orderId", target = "orderId", ignore = true)
	@Mapping(source = "shipmentNumber", target = "shipmentNumber", ignore = true)
	@Mapping(source = "shipmentTransferId", target = "shipmentTransferId", ignore = true)
	@Mapping(source = "quoteInformation", target = "quoteInformation", ignore = true)
	@Mapping(source = "allOrderLinesOrFCLOrderLines", target = "allOrderLinesOrFCLOrderLines", ignore = true)
	@Mapping(constant = "DRAFT", target = "status")
	@Mapping(constant = "DRAFT", target = "previousStatus")
	@Mapping(source = "shipperAddress", target = "consigneeAddress")
	@Mapping(source = "consigneeAddress", target = "shipperAddress")
	@Mapping(source = "fromPort", target = "toPort")
	@Mapping(source = "toPort", target = "fromPort")
	SeaImportOrder convertToSeaImportOrder(SeaExportOrder seaExportOrder, @Context DocumentRepositoryFacade documentRepositoryFacade,
			@Context ShipmentNumberService shipmentNumberService);

	@Mapping(source = "orderId", target = "orderId", ignore = true)
	@Mapping(source = "shipmentNumber", target = "shipmentNumber", ignore = true)
	@Mapping(source = "shipmentTransferId", target = "shipmentTransferId", ignore = true)
	@Mapping(source = "quoteInformation", target = "quoteInformation", ignore = true)
	@Mapping(source = "allOrderLinesOrFCLOrderLines", target = "allOrderLinesOrFCLOrderLines", ignore = true)
	@Mapping(constant = "DRAFT", target = "status")
	@Mapping(constant = "DRAFT", target = "previousStatus")
	@Mapping(source = "shipperAddress", target = "consigneeAddress")
	@Mapping(source = "consigneeAddress", target = "shipperAddress")
	@Mapping(source = "fromPort", target = "toPort")
	@Mapping(source = "toPort", target = "fromPort")
	SeaExportOrder convertToSeaExportOrder(SeaImportOrder seaImportOrder, @Context DocumentRepositoryFacade documentRepositoryFacade,
			@Context ShipmentNumberService shipmentNumberService);

	@Mapping(source = "orderId", target = "orderId", ignore = true)
	@Mapping(source = "shipmentNumber", target = "shipmentNumber", ignore = true)
	@Mapping(source = "shipmentTransferId", target = "shipmentTransferId", ignore = true)
	@Mapping(source = "quoteInformation", target = "quoteInformation", ignore = true)
	@Mapping(source = "transferlistPrinted", target = "transferlistPrinted", ignore = true)
	@Mapping(constant = "DRAFT", target = "status")
	@Mapping(constant = "DRAFT", target = "previousStatus")
	@Mapping(source = "shipperAddress", target = "consigneeAddress")
	@Mapping(source = "consigneeAddress", target = "shipperAddress")
	CollectionOrder convertToRoadCollectionOrder(ForwardingOrder forwardingOrder, @Context DocumentRepositoryFacade documentRepositoryFacade,
			@Context ShipmentNumberService shipmentNumberService);

	@Mapping(source = "orderId", target = "orderId", ignore = true)
	@Mapping(source = "shipmentNumber", target = "shipmentNumber", ignore = true)
	@Mapping(source = "shipmentTransferId", target = "shipmentTransferId", ignore = true)
	@Mapping(source = "quoteInformation", target = "quoteInformation", ignore = true)
	@Mapping(source = "transferlistPrinted", target = "transferlistPrinted", ignore = true)
	@Mapping(constant = "DRAFT", target = "status")
	@Mapping(constant = "DRAFT", target = "previousStatus")
	@Mapping(source = "shipperAddress", target = "consigneeAddress")
	@Mapping(source = "consigneeAddress", target = "shipperAddress")
	ForwardingOrder convertToRoadForwardingOrder(CollectionOrder collectionOrder, @Context DocumentRepositoryFacade documentRepositoryFacade,
			@Context ShipmentNumberService shipmentNumberService);

	@AfterMapping
	default void moveIds(Order source, @MappingTarget Order target, @Context DocumentRepositoryFacade documentRepositoryFacade,
			@Context ShipmentNumberService shipmentNumberService) {
		moveDocumentIds(source, target, documentRepositoryFacade);
		moveFurtherAddresses(source, target);
		moveOrderTextIds(source, target);
		target.setShipmentNumber(shipmentNumberService.getShipmentNumber());
	}

	@AfterMapping
	default void moveIdsAir(AirImportOrder source, @MappingTarget AirExportOrder target) {
		updateReferencedOrderReferencesAir(source, target);
		updateReferencedOrderOrderTextsAir(source, target);
		updateReferencedOrderOrderLineAir(source, target);
	}

	@AfterMapping
	default void moveIdsAir(AirExportOrder source, @MappingTarget AirImportOrder target) {
		updateReferencedOrderReferencesAir(source, target);
		updateReferencedOrderOrderTextsAir(source, target);
		updateReferencedOrderOrderLineAir(source, target);
	}

	@AfterMapping
	default void moveIdsSea(SeaImportOrder source, @MappingTarget SeaExportOrder target) {
		updateReferencedOrderOrderLinesSea(source, target);
		updateReferencedOrderOrderReferencesSea(source, target);
		updateReferencedOrderOrderTextsSea(source, target);
		updateReferencedOrderOrderFullContainerSea(source, target);
	}

	@AfterMapping
	default void moveIdsSea(SeaExportOrder source, @MappingTarget SeaImportOrder target) {
		updateReferencedOrderOrderLinesSea(source, target);
		updateReferencedOrderOrderReferencesSea(source, target);
		updateReferencedOrderOrderTextsSea(source, target);
		updateReferencedOrderOrderFullContainerSea(source, target);
	}

	@AfterMapping
	default void moveIdsRoad(CollectionOrder source, @MappingTarget ForwardingOrder target) {
		updateReferencedOrderReferencesRoad(source, target);
		updateReferencedOrderOrderLinesRoad(source, target);
		updateReferencedOrderOrderTextsRoad(source, target);
		updateReferencedOrderPackingPositionRoad(source, target);
	}

	@AfterMapping
	default void moveIdsRoad(ForwardingOrder source, @MappingTarget CollectionOrder target) {
		updateReferencedOrderReferencesRoad(source, target);
		updateReferencedOrderOrderLinesRoad(source, target);
		updateReferencedOrderOrderTextsRoad(source, target);
		updateReferencedOrderPackingPositionRoad(source, target);
	}

	// cleanup Ids air
	@AfterMapping
	default void cleanupReferencesAirImport(AirImportOrder source, @MappingTarget AirExportOrder target) {
		cleanAddressIdsAir(source);
	}

	@AfterMapping
	default void cleanupReferencesAirExport(AirExportOrder source, @MappingTarget AirImportOrder target) {
		cleanAddressIdsAir(source);
	}

	// cleanup Ids sea
	@AfterMapping
	default void cleanupReferencesSeaImport(SeaImportOrder source, @MappingTarget SeaExportOrder target) {
		cleanAddressIdsSea(source);
	}

	@AfterMapping
	default void cleanupReferencesSeaExport(SeaExportOrder source, @MappingTarget SeaImportOrder target) {
		cleanAddressIdsSea(source);
	}

	// cleanup Ids road
	@AfterMapping
	default void cleanupReferencesRoadCollection(CollectionOrder source, @MappingTarget ForwardingOrder target) {
		cleanAddressIdsRoad(source);
	}

	@AfterMapping
	default void cleanupReferencesSeaExport(ForwardingOrder source, @MappingTarget CollectionOrder target) {
		cleanAddressIdsRoad(source);
	}

	default void cleanAddressIdsAir(AirOrder source) {
		source.setDeliveryAddress(null);
		source.setPickupAddress(null);
		source.setConsigneeAddress(null);
		source.setPrincipalAddress(null);
		source.setShipperAddress(null);
		source.setOrderContact(null);

		source.setStatus(OrderStatus.DELETED);
	}

	default void cleanAddressIdsSea(SeaOrder source) {
		source.setDeliveryAddress(null);
		source.setPickupAddress(null);
		source.setConsigneeAddress(null);
		source.setPrincipalAddress(null);
		source.setShipperAddress(null);
		source.setOrderContact(null);

		source.setStatus(OrderStatus.DELETED);
	}

	default void cleanAddressIdsRoad(RoadOrder source) {
		source.setConsigneeAddress(null);
		source.setPrincipalAddress(null);
		source.setShipperAddress(null);

		source.setStatus(OrderStatus.DELETED);
	}

	default void updateReferencedOrderOrderLineAir(AirOrder source, AirOrder target) {
		source.getOrderLines().clear();
		target.getOrderLines().forEach(orderLine -> orderLine.setOrder(target));
	}

	default void updateReferencedOrderOrderTextsAir(AirOrder source, AirOrder target) {
		source.getOrderTexts().clear();
		target.getOrderTexts().forEach(orderText -> orderText.setOrder(target));
	}

	default void updateReferencedOrderOrderTextsRoad(RoadOrder source, RoadOrder target) {
		source.getOrderTexts().clear();
		target.getOrderTexts().forEach(reference -> reference.setOrder(target));
	}

	default void updateReferencedOrderOrderLinesRoad(RoadOrder source, RoadOrder target) {
		source.getOrderLines().clear();
		target.getOrderLines().forEach(reference -> reference.setOrder(target));
	}

	default void updateReferencedOrderOrderTextsSea(SeaOrder source, SeaOrder target) {
		source.getOrderTexts().clear();
		target.getOrderTexts().forEach(reference -> reference.setOrder(target));
	}

	default void updateReferencedOrderOrderLinesSea(SeaOrder source, SeaOrder target) {
		source.getOrderLines().clear();
		target.getOrderLines().forEach(orderLine -> orderLine.setOrder(target));
	}

	default void updateReferencedOrderOrderReferencesSea(SeaOrder source, SeaOrder target) {
		source.getOrderReferences().clear();
		target.getOrderReferences().forEach(reference -> reference.setOrder(target));
	}

	default void updateReferencedOrderOrderFullContainerSea(SeaOrder source, SeaOrder target) {
		if (source.isFullContainerLoad()) {
			source.getFullContainerLoads().clear();
			target.getFullContainerLoads().forEach(reference -> reference.setOrder(target));
		}
	}

	default void updateReferencedOrderReferencesAir(AirOrder source, AirOrder target) {
		source.getOrderReferences().clear();
		target.getOrderReferences().forEach(reference -> reference.setOrder(target));
	}

	default void updateReferencedOrderReferencesRoad(RoadOrder source, RoadOrder target) {
		source.getOrderReferences().clear();
		target.getOrderReferences().forEach(reference -> reference.setOrder(target));
	}

	default void moveFurtherAddresses(Order source, Order target) {
		source.getAddresses().clear();
		target.getAddresses().forEach(address -> address.setOrder(target));
	}

	default void moveOrderTextIds(Order source, Order target) {
		List<OrderText> orderTexts = source.getOrderTexts();
		source.getOrderTexts().clear();

		orderTexts.forEach(text -> text.setOrder(target));
		target.getOrderTexts().addAll(orderTexts);
	}

	default void moveDocumentIds(Order source, Order target, DocumentRepositoryFacade documentRepositoryFacade) {
		target.setDocumentIds(source.getDocumentIds());
		List<Document> documents = documentRepositoryFacade.findByOrderId(source.getOrderId());
		for (Document document : documents) {
			document.setOrderId(target.getOrderId());
			documentRepositoryFacade.save(document);
		}
	}

	default void updateReferencedOrderPackingPositionRoad(RoadOrder source, RoadOrder target) {
		source.getPackingPositions().clear();
		target.getPackingPositions().forEach(reference -> reference.setOrder(target));
	}
}
