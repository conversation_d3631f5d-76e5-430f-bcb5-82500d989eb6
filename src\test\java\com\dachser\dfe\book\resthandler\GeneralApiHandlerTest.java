package com.dachser.dfe.book.resthandler;

import com.dachser.dfe.book.BaseOpenApiTest;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ExtDeliveryProductServiceNotAvailable;
import com.dachser.dfe.book.generaldata.GeneralDataAdapterMock;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.BadGatewayProblemDto;
import com.dachser.dfe.book.model.CountryDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.DivisionDto;
import com.dachser.dfe.book.model.IrelandPostalCodeDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.PackagingOptionsDto;
import com.dachser.dfe.book.model.PostcodeValidationDto;
import com.dachser.dfe.book.model.SegmentDto;
import io.restassured.common.mapper.TypeRef;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import io.restassured.module.mockmvc.specification.MockMvcRequestSpecification;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import java.util.List;
import java.util.Optional;

import static com.dachser.dfe.book.MockConstants.ACTIVE_COMPANY_TT_ACCESS_TOKEN;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

class GeneralApiHandlerTest extends BaseOpenApiTest {

	@Nested
	class GetHsCodes {
		@Nested
		class ValidRequests {
			@Test
			void shouldReturn200AndFindDomesticInDescription() {

				final MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");

				request.param("searchFor", "domestic");
				final MockMvcResponse response = request.get(buildUrl("/hs-code-options"));
				assertEquals(HttpStatus.SC_OK, response.statusCode());

				final List<OptionDto> hsCodes = response.getBody().as(new TypeRef<>() {
				});
				assertEquals(27, hsCodes.size());
				assertEquals("010511", hsCodes.get(0).getCode());
				assertEquals("Live fowls of the species Gallus domesticus, weighing <= 185 g (excl. turkeys and guinea fowls)", hsCodes.get(0).getDescription());

			}

			@Test
			void shouldReturn200AndFind724InID() {

				final MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");

				request.param("searchFor", "724");
				final MockMvcResponse response = request.get(buildUrl("/hs-code-options"));
				assertEquals(HttpStatus.SC_OK, response.statusCode());

				final List<OptionDto> hsCodes = response.getBody().as(new TypeRef<>() {
				});
				assertEquals(1, hsCodes.size());
				assertEquals("020724", hsCodes.get(0).getCode());

			}
		}

		@Nested
		class InvalidRequests {
			@Test
			void shouldReturn400_OnMissingParam() {

				final MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");

				final MockMvcResponse response = request.get(buildUrl("/hs-code-options"));
				assertEquals(HttpStatus.SC_BAD_REQUEST, response.statusCode());
			}

			@Test
			void shouldReturn400_OnEmptyParam() {
				final MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");

				request.param("searchFor", "");
				final MockMvcResponse response = request.get(buildUrl("/hs-code-options"));
				assertEquals(HttpStatus.SC_BAD_REQUEST, response.statusCode());
			}
		}
	}

	@Nested
	class GetCountries {
		@Test
		void shouldReturn200WithoutSegment() {
			MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");

			// Default segment is ROAD
			final MockMvcResponse response = request.get(buildUrl("/countries"));
			assertEquals(200, response.statusCode());
			final List<CountryDto> content = response.getBody().as(new TypeRef<>() {
			});
			assertFalse(content.isEmpty());
			assertEquals(GeneralDataAdapterMock.getCountriesCount() - 1, content.size());
			final CountryDto countryDto = content.get(0);
			assertNotNull(countryDto.getCountryCode());
			assertNotNull(countryDto.getLabel());
		}

		@Test
		void shouldReturn200ForAir() {
			MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
			request.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.AIR);
			final MockMvcResponse response = request.get(buildUrl("/countries"));
			assertEquals(200, response.statusCode());
			final List<CountryDto> content = response.getBody().as(new TypeRef<>() {
			});
			assertFalse(content.isEmpty());
			assertEquals(GeneralDataAdapterMock.getCountriesCount(), content.size());
			final CountryDto countryDto = content.get(0);
			assertNotNull(countryDto.getCountryCode());
			assertNotNull(countryDto.getLabel());
		}

		@Test
		void shouldReturn200ForRoad() {
			MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
			request.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.ROAD);
			final MockMvcResponse response = request.get(buildUrl("/countries"));
			assertEquals(200, response.statusCode());
			final List<CountryDto> content = response.getBody().as(new TypeRef<>() {
			});
			assertFalse(content.isEmpty());
			assertEquals(GeneralDataAdapterMock.getCountriesCount() - 1, content.size());
			final CountryDto countryDto = content.get(0);
			assertNotNull(countryDto.getCountryCode());
			assertNotNull(countryDto.getLabel());
		}
	}

	@Nested
	class CountriesException {

		@Test
		void shouldReturn502BadGatewayForExternalServiceFailure() {
			MockMvcRequestSpecification request = givenRequest();
			request.header("Accept-Language", "xx");
			request.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.ROAD);

			final MockMvcResponse response = request.get(buildUrl("/countries"));
			assertEquals(HttpStatus.SC_BAD_GATEWAY, response.statusCode());
			assertEquals(BookErrorId.ERR_AL_10.getErrorTitle(), response.getBody().as(BadGatewayProblemDto.class).getTitle());
		}
	}

	@Nested
	class GetDeliveryProducts {
		@Test
		void shouldReturn200() {
			when(roadProductsService.getDeliveryProductsForDivision(Division.T)).thenReturn(
					List.of(new DeliveryProductDto().code("code").description("description")));

			MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
			request.queryParam("division", DivisionDto.EUROPEAN_LOGISTICS.name());
			final MockMvcResponse response = request.get(buildUrl("/delivery-products"));
			assertEquals(200, response.statusCode());
			final List<DeliveryProductDto> content = response.getBody().as(new TypeRef<>() {
			});
			assertFalse(content.isEmpty());
			final DeliveryProductDto countryDto = content.get(0);
			assertNotNull(countryDto.getCode());
			assertNotNull(countryDto.getDescription());
		}

		@Test
		void shouldReturn500() {
			when(roadProductsService.getDeliveryProductsForDivision(Division.T)).thenThrow(ExtDeliveryProductServiceNotAvailable.class);

			MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
			request.queryParam("division", DivisionDto.EUROPEAN_LOGISTICS.name());
			final MockMvcResponse response = request.get(buildUrl("/delivery-products"));

			assertEquals(500, response.statusCode());
		}

		@Test
		void shouldReturn400() {
			MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");

			final MockMvcResponse response = request.get(buildUrl("/delivery-products"));
			assertEquals(400, response.statusCode());
		}
	}

	@Nested
	class HasEmbargo {

		@Test
		void shouldReturnTrue() {
			final MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
			request.queryParam("fromCountryCode", "DE");
			request.queryParam("toCountryCode", "US");
			final MockMvcResponse response = request.get(buildUrl("/v1/embargo"));

			assertEquals(HttpStatus.SC_OK, response.statusCode());
			final boolean hasEmbargo = response.getBody().as(new TypeRef<>() {
			});
			assertTrue(hasEmbargo);
		}

		@Test
		void shouldReturnBadRequestForMissingParameterFromCountryCode() {
			final MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
			request.queryParam("toCountryCode", "DE");
			final MockMvcResponse response = request.get(buildUrl("/v1/embargo"));
			assertEquals(HttpStatus.SC_BAD_REQUEST, response.statusCode());
		}

		@Test
		void shouldReturnBadRequestForMissingParameterToCountryCode() {
			final MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
			request.queryParam("fromCountryCode", "DE");
			final MockMvcResponse response = request.get(buildUrl("/v1/embargo"));
			assertEquals(HttpStatus.SC_BAD_REQUEST, response.statusCode());
		}

	}

	@Nested
	class AirProducts {
		@Test
		void shouldReturn200() {
			final MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
			final MockMvcResponse response = request.get(buildUrl("/v1/air-products"));
			assertEquals(HttpStatus.SC_OK, response.statusCode());
			List<AirProductDto> airProducts = response.as(new TypeRef<>() {
			});
			Optional<AirProductDto> first = airProducts.stream().findFirst();
			assertTrue(first.isPresent());
			assertEquals("1", first.get().getCode());
		}

		@Test
		void shouldReturn200TT() {
			final MockMvcRequestSpecification request = prepareNewRequestWithToken(ACTIVE_COMPANY_TT_ACCESS_TOKEN);
			final MockMvcResponse response = request.get(buildUrl("/v1/air-products"));
			assertEquals(HttpStatus.SC_OK, response.statusCode());
			List<AirProductDto> airProducts = response.as(new TypeRef<>() {
			});
			Optional<AirProductDto> first = airProducts.stream().findFirst();
			assertTrue(first.isPresent());
			assertEquals("1", first.get().getCode());
		}
	}

	@Nested
	class DachserPostalCode {

		@Test
		void shouldReturn200() {
			final MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
			request.queryParam("postalCode", "H54");
			final MockMvcResponse response = request.get(buildUrl("/v1/ireland/dachserpostalcodes"));
			assertEquals(HttpStatus.SC_OK, response.statusCode());
			List<IrelandPostalCodeDto> postalCodes = response.as(new TypeRef<>() {
			});
			Optional<IrelandPostalCodeDto> first = postalCodes.stream().findFirst();
			assertTrue(first.isPresent());
			assertEquals("Dublin", first.get().getTown());
		}

	}

	@Nested
	class ValidatePostcode {
		@Test
		void shouldReturn200() {
			final MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
			request.queryParam("countryCode", "DE");
			request.queryParam("postalCode", "91757");
			final MockMvcResponse response = request.get(buildUrl("/v1/validate-postcode"));
			assertEquals(HttpStatus.SC_OK, response.statusCode());
			final PostcodeValidationDto postcodeValidation = response.as(PostcodeValidationDto.class);
			assertTrue(postcodeValidation.getValid());
		}
	}

	@Nested
	class NatureOfGoods {

		@Nested
		class Road {
			@Test
			void shouldReturn200() {
				final MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
				request.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.ROAD);
				final MockMvcResponse response = request.get(buildUrl("/nature-of-goods"));
				assertEquals(HttpStatus.SC_OK, response.statusCode());
				List<String> lastUsedGoods = response.as(new TypeRef<>() {
				});
				// Only 4 because Test content 2 is a duplicate and logic should return distinct values
				assertEquals(4, lastUsedGoods.size());
				assertEquals("Test content 4", lastUsedGoods.get(0));
				assertEquals("Test content 3", lastUsedGoods.get(1));
				assertEquals("Test content 2", lastUsedGoods.get(2));
				assertEquals("Test content", lastUsedGoods.get(3));
			}
		}

		@Nested
		class Air {
			@Test
			void shouldReturn200() {
				final MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
				request.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.AIR);
				final MockMvcResponse response = request.get(buildUrl("/nature-of-goods"));
				assertEquals(HttpStatus.SC_OK, response.statusCode());
				List<String> lastUsedGoods = response.as(new TypeRef<>() {
				});
				// Only 4 because Air 4 is a duplicate and logic should return distinct values
				assertEquals(4, lastUsedGoods.size());
				assertEquals("Air 4", lastUsedGoods.get(0));
				assertEquals("Air 3", lastUsedGoods.get(1));
				assertEquals("Air 2", lastUsedGoods.get(2));
				assertEquals("Air 1", lastUsedGoods.get(3));
			}
		}

		@Nested
		class Sea {
			@Test
			void shouldReturn200() {
				final MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
				request.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.SEA);
				final MockMvcResponse response = request.get(buildUrl("/nature-of-goods"));
				assertEquals(HttpStatus.SC_OK, response.statusCode());
				List<String> lastUsedGoods = response.as(new TypeRef<>() {
				});
				// Only 4 because Sea 4 is a duplicate and logic should return distinct values
				assertEquals(4, lastUsedGoods.size());
				assertEquals("Sea 4", lastUsedGoods.get(0));
				assertEquals("Sea 3", lastUsedGoods.get(1));
				assertEquals("Sea 2", lastUsedGoods.get(2));
				assertEquals("Sea 1", lastUsedGoods.get(3));
			}
		}

	}

	@Nested
	class getPackagingOptions {
		@ParameterizedTest
		@EnumSource(value = SegmentDto.class, names = {"ROAD", "AIR", "SEA"})
		void shouldReturn200ForAllSegments(SegmentDto segment) {
			MockMvcRequestSpecification request = givenWriteRequest();
			request.queryParam(CUSTOMER_SEGMENT_PARAM, segment.getValue());
			MockMvcResponse response = request.get("/packaging-options");

			assertEquals(200, response.getStatusCode());

			final PackagingOptionsDto content = response.getBody().as(new TypeRef<PackagingOptionsDto>() {
			});

			assertNotNull(content.getPackagingOptions());
			assertFalse(content.getPackagingOptions().isEmpty());
		}

		@Test
		void shouldReturn400WhenSegmentIsMissing() {
			MockMvcRequestSpecification request = givenWriteRequest();
			MockMvcResponse response = request.get("/packaging-options");
			assertEquals(400, response.getStatusCode());
		}
	}

	@Nested
	class GetAllFurtherAddressTypes {
		@Test
		void shouldReturn200() {
			MockMvcRequestSpecification request = givenWriteRequest();
			MockMvcResponse response = request.get("/further-address-types");
			assertEquals(200, response.getStatusCode());
		}
	}
}