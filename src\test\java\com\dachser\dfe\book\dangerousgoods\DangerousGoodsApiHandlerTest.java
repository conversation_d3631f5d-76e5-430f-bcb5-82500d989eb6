package com.dachser.dfe.book.dangerousgoods;

import com.dachser.dfe.book.BaseOpenApiTest;
import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.SegmentDto;
import io.restassured.common.mapper.TypeRef;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import io.restassured.module.mockmvc.specification.MockMvcRequestSpecification;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

class DangerousGoodsApiHandlerTest extends BaseOpenApiTest {

	@Nested
	class FindUnNumbers {

		@Test
		void shouldReturn200() {
			// given
			String searchFor = "1791";
			MockMvcRequestSpecification request = givenRequest();
			request.queryParam("searchFor", searchFor);
			request.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.ROAD.getValue());

			// when
			MockMvcResponse response = request.get("/v1/dangerous-goods/un-search");

			// then
			assertEquals(200, response.getStatusCode());
			List<DangerousGoodDataItemDto> dangerousGoodDataItemDtos = response.getBody().as(new TypeRef<>() {
			});
			assertEquals(2, dangerousGoodDataItemDtos.size());
		}

		@Test
		void shouldReturn204OnEmptyResultSearchFor() {
			// given
			MockMvcRequestSpecification request = givenRequest();
			request.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.ROAD.getValue());
			String searchFor = "9999";
			request.queryParam("searchFor", searchFor);

			// when
			MockMvcResponse response = request.get("/v1/dangerous-goods/un-search");

			// then
			assertEquals(204, response.getStatusCode());
		}

	}

	@Nested
	class FindPackagingOptions {

		@Test
		void shouldReturn200() {
			// given
			MockMvcRequestSpecification request = givenRequest();
			request.queryParam(CUSTOMER_SEGMENT_PARAM, SegmentDto.ROAD.getValue());

			// when
			MockMvcResponse response = request.get("/v1/dangerous-goods/packaging-options");

			// then
			assertEquals(200, response.getStatusCode());
			List<OptionDto> packagingOptions = response.getBody().as(new TypeRef<>() {
			});
			assertEquals(7, packagingOptions.size());
			OptionDto firstPackage = packagingOptions.getFirst();
			assertEquals("KT", firstPackage.getCode());
			assertEquals("generalData.packagingOptionRoad.KT", firstPackage.getTranslationKey());
		}

	}

}