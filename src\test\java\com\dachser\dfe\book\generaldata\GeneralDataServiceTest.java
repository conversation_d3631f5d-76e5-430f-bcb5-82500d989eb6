package com.dachser.dfe.book.generaldata;

import com.dachser.dfe.book.SpringBasedLocalMockTest;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.ContainerTypeDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.TermDto;
import com.dachser.dfe.book.order.sea.BaseFullContainerLoadRepository;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.test.context.support.WithMockUser;

import java.util.List;
import java.util.Locale;

import static com.dachser.dfe.book.user.UserServiceMock.VALID_CUST_NO_ASL;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@SpringBasedLocalMockTest
public class GeneralDataServiceTest {

	@MockBean
	BaseFullContainerLoadRepository containerRepository;

	@Autowired
	GeneralDataService generalDataService;

	@Nested
	@WithMockUser("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
	class ContainerTypeServiceTest {
		@Test
		void shouldGetAllContainerTypes() {
			List<ContainerTypeDto> allContainerTypes = generalDataService.getAllWhitelistedContainerTypes();
			assertEquals(3, allContainerTypes.size());
		}

		@Test
		void shouldGetLastUsedContainerTypesForCustomer() {

			when(containerRepository.getMostFrequentlyUsedContainerTypes(Mockito.any())).thenReturn(List.of("HC-40", "GP-40"));

			List<ContainerTypeDto> lastUsedContainerTypesForCustomer = generalDataService.getMostFrequentlyUsedContainerTypesForCustomer(VALID_CUST_NO_ASL);
			assertEquals(2, lastUsedContainerTypesForCustomer.size());
		}

		@Test
		void shouldGetOnlyWhitelistedKeys() {
			List<ContainerTypeDto> allContainerTypes = generalDataService.getAllWhitelistedContainerTypes();
			assertTrue(allContainerTypes.stream().anyMatch(new GeneralDataService.ContainerTypeFilter(List.of("HC-40"))));
			assertFalse(allContainerTypes.stream().anyMatch(new GeneralDataService.ContainerTypeFilter(List.of("PC-40"))));
		}
	}

	@Nested
	class TermsServiceTest {
		@Test
		void shouldReturnSortedTermsBasedOnWhitelist() {

			List<TermDto> terms = generalDataService.getAllTerms();
			assertThat(terms).hasSize(14);
			assertEquals("EXW", terms.get(0).getDachserCode());
			assertEquals("FC1", terms.get(1).getDachserCode());
			assertEquals("FOA", terms.get(2).getDachserCode());
		}
	}

	@Nested
	class DeliveryProducts {

		@ParameterizedTest
		@EnumSource(Division.class)
		void shouldRequestDeliveryProducts(Division division){
			List<DeliveryProductDto> deliveryProducts = generalDataService.getDeliveryProductsForDivision(division, "en");
			assertNotNull(deliveryProducts);
			assertTrue(deliveryProducts.size() > 5);
		}
	}

	@Nested
	class MasterdataLanguages {
		@Test
		void shouldReturnMappedOnValidLangKey() {
			assertEquals(" ", generalDataService.getMasterdataLanguageForLocale(Locale.GERMANY));
			assertEquals("003", generalDataService.getMasterdataLanguageForLocale(Locale.forLanguageTag("es-ES")));
			assertEquals(" ", generalDataService.getMasterdataLanguageForLocale(Locale.forLanguageTag("de")));
			assertEquals("009", generalDataService.getMasterdataLanguageForLocale(Locale.forLanguageTag("cs")));
			assertEquals("002", generalDataService.getMasterdataLanguageForLocale(Locale.forLanguageTag("fr")));
		}

		@ParameterizedTest
		@ValueSource(strings = { "zh", "kr", "br", "tr", "jp" })
		void shouldUseFallback(String locale) {
			Locale locale1 = Locale.forLanguageTag(locale);
			assertEquals("001", generalDataService.getMasterdataLanguageForLocale(locale1));
		}
	}
}
