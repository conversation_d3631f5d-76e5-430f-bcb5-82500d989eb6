package com.dachser.dfe.book.service.label.shipment;

import com.dachser.bi.common.print.service.pri.api.label.bean.ShipmentLabelBean;
import com.dachser.bi.common.print.service.pri.api.label.bean.ShipmentV2Bean;
import com.dachser.bi.common.print.service.pri.api.label.service.ShipmentLabelV2Service;
import com.dachser.dfe.book.cache.CacheNames;
import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.exception.LabelsCouldNotBeCreatedException;
import com.dachser.dfe.book.mapper.order.ShipmentPrintBeanMapper;
import com.dachser.dfe.book.masterdata.road.RoadMasterDataApiWrapper;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.service.RelationLabelPrintingService;
import com.dachser.dfe.book.service.label.LabelAdapter;
import com.dachser.dfe.book.user.BusinessDomainProvider;
import com.dachser.dfe.road.masterdata.model.RMDBranchDataDTO;
import com.dachser.dfe.road.masterdata.model.RMDRelationLabelprintingDTO;
import com.dachser.framework.technical.ws.validation.DachValidationResult;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.dachser.bi.common.print.service.pri.api.label.LabelFormat.LABEL_SHIPMENT_PDF;
import static java.lang.Integer.valueOf;

@Service
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "false", matchIfMissing = true)
public class ShipmentLabelAdapterExt implements LabelAdapter.ShipmentLabelAdapter {

	private final BusinessDomainProvider businessDomainProvider;

	private final ShipmentPrintBeanMapper shipmentPrintBeanMapper;

	private final ShipmentLabelV2Service shipmentLabelService;

	private final RelationLabelPrintingService relationLabelPrintingService;

	private final CountryService countryService;

	private final RoadMasterDataApiWrapper roadMasterdataApiWrapper;

	@Value("${bi.common.print.pri.generateTrialLabel}")
	private boolean generateTrialLabel;

	@Override
	@Cacheable(value = CacheNames.PRINT_LABELS, key = "#order.getShipmentInformationHash()")
	public byte[] getPrintLabelsForForwardingOrder(@NonNull ForwardingOrder order, int businessDomain) {
		log.debug("No {} cache hit. Creating labels for order {}", CacheNames.PRINT_LABELS, order.getOrderId());
		ShipmentV2Bean shipmentPrintBean = shipmentPrintBeanMapper.mapForwardingOrder(order);

		// Retrieve relation from external service
		RMDRelationLabelprintingDTO relation = retrieveRelation(order, shipmentPrintBean);

		// Print labels
		return doPrint(order, businessDomain, shipmentPrintBean, relation);
	}

	private RMDRelationLabelprintingDTO retrieveRelation(ForwardingOrder order, ShipmentV2Bean shipmentPrintBean) {
		RMDRelationLabelprintingDTO request = fillRequest(order, shipmentPrintBean);
		RMDRelationLabelprintingDTO dispatchRelation = null;

		try {
			dispatchRelation = relationLabelPrintingService.retrieveRelation(request);
		} catch (Exception e) {
			// Print label without information from external service and just log the error
			log.warn("Could not retrieve dispatch relation for orderId {}: with message: {}", order.getOrderId(), e.getMessage());
		}

		return dispatchRelation;
	}

	byte[] doPrint(ForwardingOrder order, int businessDomain, ShipmentV2Bean shipmentPrintBean, @Nullable RMDRelationLabelprintingDTO relation) {
		final Locale locale = LocaleContextHolder.getLocale();

		try {

			final String longDistanceRelationCountry = relation != null && relation.getDispatchRelationCountryCode() != null ? relation.getDispatchRelationCountryCode() : " ";
			shipmentPrintBean.getConsignee().getAddressInformation().getCountryDachserCode();
			final String longDistanceRelationCode = relation != null && relation.getDispatchRelation() != null ? relation.getDispatchRelation() : " ";
			log.debug("Creating trialLabels={} for orderId {} with longDistanceRelationCountry {} and longDistanceRelationCode {} and locale {}", generateTrialLabel,
					order.getOrderId(), longDistanceRelationCountry, longDistanceRelationCode, locale);

			ShipmentLabelBean serviceResponse = generateTrialLabel ?
					shipmentLabelService.getDachserLabelTrial(businessDomain, LABEL_SHIPMENT_PDF, shipmentPrintBean, longDistanceRelationCountry, longDistanceRelationCode,
							locale) :
					shipmentLabelService.getDachserLabel(businessDomain, LABEL_SHIPMENT_PDF, shipmentPrintBean, longDistanceRelationCountry, longDistanceRelationCode, locale);

			if (serviceResponse != null) {
				if (isValidLabelResponse(serviceResponse)) {
					log.debug("Created labels for orderId {}: with size {}", order.getOrderId(), serviceResponse.getLabel().length);
					return serviceResponse.getLabel();
				}
				if (!serviceResponse.isResultSetEmpty()) {
					String validationResult = serviceResponse.getResultSet().stream().filter(Objects::nonNull).map(DachValidationResult::getMessage)
							.collect(Collectors.joining(", "));

					throw new LabelsCouldNotBeCreatedException("Service validation errors [%s]".formatted(validationResult));
				}
			}
			throw new LabelsCouldNotBeCreatedException("Service response is null");
		} catch (RuntimeException e) {
			log.error("Could not create labels for orderId {}: with message: {}", order.getOrderId(), e.getMessage());
			throw new LabelsCouldNotBeCreatedException(e.getMessage());
		}
	}

	private boolean isValidLabelResponse(ShipmentLabelBean serviceResponse) {
		return serviceResponse.isResultSetEmpty() && Optional.ofNullable(serviceResponse.getLabel()).isPresent();
	}

	private RMDRelationLabelprintingDTO fillRequest(ForwardingOrder order, ShipmentV2Bean printBean) {
		RMDRelationLabelprintingDTO request = new RMDRelationLabelprintingDTO();

		request.setRelationConsolidator(relationOfConsolidator(businessDomainProvider.getBusinessDomain(), order.getBranchId()));
		request.setAmountPackages(order.getTotalAmountPackages());
		request.setWeight(order.getTotalOrderWeight());
		// Consignee is always the receiver
		request.setConsigneeCountryCode(countryService.mapToDachserCountryCode(order.getConsigneeAddress().getCountryCode()));
		request.setConsigneePostCode(order.getConsigneeAddress().getPostcode().replaceAll("\\s", ""));
		// Consignor is always the shipper
		OrderAddress shipper = order.getShipperAddress();
		request.setConsignorPostCode(shipper.getPostcode().replaceAll("\\s", ""));
		request.setConsignorCountryCode(countryService.mapToDachserCountryCode(shipper.getCountryCode()));
		request.setConsignorCustomerNumber(valueOf(order.getCustomerNumber()));
		request.setAmountSSCC(printBean.getSsccs().size());
		// Must be set to "N" because we do not have dangerous goods information in the order
		request.setDangerousGoods("N");
		request.setDivision(order.getDivision().name());
		request.setProduct(order.getProduct());
		String returnCode = "string";
		request.setReturnCode(returnCode);
		request.setDispatchRelation(returnCode);
		request.setDispatchRelationCountryCode(returnCode);
		request.setDispatchRelationCity(returnCode);

		return request;
	}

	String relationOfConsolidator(Integer businessDomain, Integer branchId) {
		String relation = String.format("%04d", branchId);
		try {
			RMDBranchDataDTO data = roadMasterdataApiWrapper.getBranchData(businessDomain, branchId);
			if (data != null && StringUtils.isNotBlank(data.getRelation())) {
				relation = data.getRelation();
			} else {
				log.warn("Could not obtain a valid relation for consolidator branch {} then falling back to value {}", branchId, relation);
			}
		} catch (RestClientException restClientException) {
			log.error("Could not obtain a relation for consolidator branch {} due to following failure : {}", branchId, restClientException.getMessage());
		}
		return relation;
	}

}
