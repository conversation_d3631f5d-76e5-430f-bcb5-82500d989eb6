package com.dachser.dfe.book.dip.converter.road;

import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.road.RoadOrder;

import java.util.function.Function;

public interface ShipmentAddressBuilder<S, O extends RoadOrder, P> {

	default S buildShipmentAddressFromOrder(O order, String addressType, Function<O, P> mapPartnerInformation) {
		OrderAddress consigneeAddress = order.getConsigneeAddress();

		if (consigneeAddress == null) {
			return null;
		}

		S shipmentAddress = newShipmentAddress();
		setAddressType(shipmentAddress, addressType);

		if (order.getDeliveryOption() != null && order.getDeliveryOption() != DeliveryOptions.NO) {
			setServiceContactType(shipmentAddress, order.getDeliveryOption().getKey());
		} else {
			order.setDeliveryContact(null);//explicitly set to null to avoid mapping as empty fields
		}
		setPartnerInformation(shipmentAddress, mapPartnerInformation.apply(order));

		return shipmentAddress;
	}

	S newShipmentAddress();

	void setAddressType(S shipmentAddress, String addressType);

	void setServiceContactType(S shipmentAddress, String serviceContactType);

	void setPartnerInformation(S shipmentAddress, P partnerInformation);
}
