package com.dachser.dfe.book.config.term;

import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.generaldata.GeneralDataAdapterMock;
import com.dachser.dfe.book.generaldata.term.TermMapper;
import com.dachser.dfe.book.generaldata.term.TermMapperImpl;
import com.dachser.dfe.book.model.FreightTermDto;
import com.dachser.dfe.book.model.IncoTermDto;
import com.dachser.dfe.book.model.TermDto;
import com.dachser.dfe.generaldata.model.GDTermTypeDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TermSorterTest {

	@Mock
	private WhiteListFilterConfiguration whiteListFilterConfiguration;

	@InjectMocks
	private TermSorter termSorter;

	private final TermMapper termMapper = new TermMapperImpl();

	private final GeneralDataAdapterMock generalDataAdapterMock = new GeneralDataAdapterMock();

	@Test
	void shouldSortOverviewTerms() {
		WhiteListFilterConfiguration.TermFilter termFilter = new WhiteListFilterConfiguration.TermFilter();
		when(whiteListFilterConfiguration.getTerms()).thenReturn(termFilter);
		termFilter.setOverview(List.of("EXW", "FC1", "FOA"));
		List<TermDto> termDtos = termMapper.mapTerms(generalDataAdapterMock.getAllActiveTerms(null));
		List<TermDto> sorted = termSorter.sortOverviewTerms(termDtos);
		assertEquals(3, sorted.size());
		assertEquals("EXW", sorted.get(0).getDachserCode());
		assertEquals("FC1", sorted.get(1).getDachserCode());
		assertEquals("FOA", sorted.get(2).getDachserCode());
	}

	@Test
	void shouldSortFreightTerms() {
		WhiteListFilterConfiguration.TermFilter termFilter = new WhiteListFilterConfiguration.TermFilter();
		when(whiteListFilterConfiguration.getTerms()).thenReturn(termFilter);
		termFilter.setFreight(List.of("031", "011", "081"));
		List<FreightTermDto> termDtos = termMapper.mapFreightTerms(generalDataAdapterMock.getAllActiveTerms(GDTermTypeDto.FREIGHT));
		List<FreightTermDto> sorted = termSorter.sortFreightTerms(termDtos);
		assertEquals(3, sorted.size());
		assertEquals("031", sorted.get(0).getDachserTermKey());
		assertEquals("011", sorted.get(1).getDachserTermKey());
		assertEquals("081", sorted.get(2).getDachserTermKey());
	}

	@Test
	void shouldSortIncoTerms() {
		WhiteListFilterConfiguration.TermFilter termFilter = new WhiteListFilterConfiguration.TermFilter();
		when(whiteListFilterConfiguration.getTerms()).thenReturn(termFilter);
		termFilter.setInco(List.of("CFR", "CIF", "CIP"));
		List<IncoTermDto> termDtos = termMapper.mapIncoTerms(generalDataAdapterMock.getAllActiveTerms(GDTermTypeDto.INCO));
		List<IncoTermDto> sorted = termSorter.sortIncoTerms(termDtos);
		assertEquals(3, sorted.size());
		assertEquals("CFR", sorted.get(0).getDachserCode());
		assertEquals("CIF", sorted.get(1).getDachserCode());
		assertEquals("CIP", sorted.get(2).getDachserCode());
	}

	@Test
	void shouldOmitNotFoundObjects(){
		WhiteListFilterConfiguration.TermFilter termFilter = new WhiteListFilterConfiguration.TermFilter();
		when(whiteListFilterConfiguration.getTerms()).thenReturn(termFilter);
		termFilter.setInco(List.of("CFR", "CIF", "XXX"));
		List<IncoTermDto> termDtos = termMapper.mapIncoTerms(generalDataAdapterMock.getAllActiveTerms(GDTermTypeDto.INCO));
		List<IncoTermDto> sorted = termSorter.sortIncoTerms(termDtos);
		assertEquals(2, sorted.size());
	}
}