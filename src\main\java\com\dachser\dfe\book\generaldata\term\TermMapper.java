package com.dachser.dfe.book.generaldata.term;

import com.dachser.dfe.book.model.FreightTermDto;
import com.dachser.dfe.book.model.IncoTermDto;
import com.dachser.dfe.book.model.TermDto;
import com.dachser.dfe.generaldata.model.GDTermDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TermMapper {

	TermDto mapTerm(GDTermDto term);

	List<TermDto> mapTerms(List<GDTermDto> terms);

	@Mapping(target = "dachserTermKey", source = "freightTerm")
	@Mapping(target = "incoTermKey", source = "incoTerm")
	@Mapping(target = "headline", source = "label")
	FreightTermDto mapFreightTerm(GDTermDto term);

	List<FreightTermDto> mapFreightTerms(List<GDTermDto> terms);

	@Mapping(target = "code", source = "incoTerm")
	IncoTermDto mapIncoTerm(GDTermDto term);

	List<IncoTermDto> mapIncoTerms(List<GDTermDto> terms);
}