package com.dachser.dfe.book.order.validation;

import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.model.PostcodeValidationDto;
import com.dachser.dfe.book.model.ValidationResultDto;
import com.dachser.dfe.book.model.ValidationResultEntryDto;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.exception.OrderAddressValidationException;
import com.dachser.dfe.book.service.postalcode.PostalCodeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.dachser.dfe.book.order.OrderType.isAirOrderType;
import static com.dachser.dfe.book.order.OrderType.isRoadOrderType;
import static com.dachser.dfe.book.order.OrderType.isSeaOrderType;

@RequiredArgsConstructor
@Component
@Slf4j
public class OrderAddressValidator {

	private final Translator translator;

	private final PostalCodeService postalCodeService;

	private final CountryService countryService;

	public ValidationResultDto validateToResultAddressBasedOnOrderType(OrderAddress addressToValidate, OrderType orderType) {
		List<ValidationResultEntryDto> validationResultEntries = validateAddressBasedOnOrderType(addressToValidate, orderType);

		return new ValidationResultDto().results(validationResultEntries).valid(validationResultEntries.isEmpty());
	}

	public List<ValidationResultEntryDto> validateAddressBasedOnOrderType(OrderAddress addressToValidate, OrderType orderType) {
		log.debug("Validating address {} for order type {}", addressToValidate, orderType);

		List<ValidationResultEntryDto> results = new ArrayList<>();

		if (isAirOrderType(orderType) || isSeaOrderType(orderType)) {
			validateAddressWithContact(addressToValidate, results);
		} else if (isRoadOrderType(orderType)) {
			validateAddressWithoutContact(addressToValidate, results);
		} else {
			log.warn("Order type {} is not supported", orderType);
			throw new OrderAddressValidationException("Order type is not supported", new ValidationResultDto().results(results));
		}

		if (!results.isEmpty()) {
			log.debug("Address {} is not valid for order type {}, results {}", addressToValidate, orderType, results);
		}

		return results;
	}

	private void validateAddressWithContact(OrderAddress addressToValidate, List<ValidationResultEntryDto> results) {
		final List<ValidationResultEntryDto> validationErrors = new ArrayList<>();
		validateBaseAddress(addressToValidate, validationErrors);
		validateContact(addressToValidate, validationErrors);
		results.addAll(validationErrors);
	}

	private void validateAddressWithoutContact(OrderAddress addressToValidate, List<ValidationResultEntryDto> results) {
		final List<ValidationResultEntryDto> validationErrors = new ArrayList<>();
		validateBaseAddress(addressToValidate, validationErrors);
		results.addAll(validationErrors);
	}

	private void validateContact(OrderAddress addressToValidate, List<ValidationResultEntryDto> validationErrors) {
		if (!validationErrors.isEmpty()) {
			return;
		}

		if (Objects.isNull(addressToValidate.getOrderContact())) {
			addValidationResultEntry(validationErrors, "contact");
			return;
		}

		if (checkEmpty(addressToValidate.getOrderContact().getName())) {
			addValidationResultEntry(validationErrors, "name");
		} else if (checkEmpty(addressToValidate.getOrderContact().getEmail())) {
			addValidationResultEntry(validationErrors, "email");
		}
	}

	private void validateBaseAddress(OrderAddress addressToValidate, List<ValidationResultEntryDto> validationErrors) {
		if (Objects.isNull(addressToValidate)) {
			addValidationResultEntry(validationErrors, "address");
			return;
		}

		// When customerNumber is set we assume that this address is the address copied from principal data and skip validation for zip code and city in that case
		final boolean isIrelandPrincipalAddress = Objects.nonNull(addressToValidate.getCustomerNumber()) && "IE".equals(addressToValidate.getCountryCode());

		if (checkEmpty(addressToValidate.getName())) {
			addValidationResultEntry(validationErrors, "name");
		} else if (checkEmpty(addressToValidate.getStreet())) {
			addValidationResultEntry(validationErrors, "street");
		} else if (checkEmpty(addressToValidate.getCity()) && !isIrelandPrincipalAddress) {
			addValidationResultEntry(validationErrors, "city");
		} else if (checkEmpty(addressToValidate.getCountryCode())) {
			addValidationResultEntry(validationErrors, "countryCode");
		} else if (isPostCodeInvalid(addressToValidate)) {
			addValidationResultEntry(validationErrors, "postCode");
		}
	}

	private boolean isPostCodeInvalid(OrderAddress addressToValidate) {
		final boolean isIrelandPrincipalAddress = Objects.nonNull(addressToValidate.getCustomerNumber()) && "IE".equals(addressToValidate.getCountryCode());
		if (isIrelandPrincipalAddress) {
			return false;
		}

		if (checkEmpty(addressToValidate.getPostcode())) {
			// Input is empty so only relevant information is if postcode is mandatory
			return isPostcodeMandatory(addressToValidate);
		}
		// The input is surely not empty, so we check if the postcode is valid
		return isPostcodeStructureInvalid(addressToValidate);
	}

	private boolean isPostcodeStructureInvalid(OrderAddress addressToValidate) {
		PostcodeValidationDto validation = postalCodeService.validatePostCode(addressToValidate.getCountryCode(), addressToValidate.getPostcode());

		if (validation == null) {
			return false;
		}

		return !validation.getValid();
	}

	private boolean isPostcodeMandatory(final OrderAddress orderAddress) {
		return countryService.isPostCodeMandatory(orderAddress.getCountryCode());
	}

	private boolean checkEmpty(String stringToCheck) {
		return StringUtils.isBlank(stringToCheck);
	}

	private void addValidationResultEntry(final List<ValidationResultEntryDto> validationErrors, final String fieldName) {
		final ValidationResultEntryDto entry = new ValidationResultEntryDto();
		entry.setField(fieldName);
		entry.setErrorType(ValidationResultEntryDto.ErrorTypeEnum.REQUIRED_FIELD_MISSING);
		entry.setDescription(translator.toLocale(Messages.INVALID_INPUT));
		validationErrors.add(entry);
	}

}
