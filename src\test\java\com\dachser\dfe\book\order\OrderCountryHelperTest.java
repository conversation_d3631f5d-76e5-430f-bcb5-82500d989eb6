package com.dachser.dfe.book.order;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.country.CountryCode;
import com.dachser.dfe.book.model.FurtherAddressType;
import com.dachser.dfe.book.order.address.AddressTestHelper;
import com.dachser.dfe.book.order.address.OrderBaseAddressTestMapper;
import com.dachser.dfe.book.order.common.OrderBaseAddress;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.road.CollectionOrder;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mapstruct.factory.Mappers;

import java.util.Collections;
import java.util.stream.Stream;

@Slf4j
class OrderCountryHelperTest {

	private static final TestUtil UTILS = TestUtil.createInstanceNonSpring();

	private static final OrderBaseAddressTestMapper ADDRESS_MAPPER = Mappers.getMapper(OrderBaseAddressTestMapper.class);

	private final OrderCountryHelper helper = new OrderCountryHelper();

	@ParameterizedTest(name = "#{index} - does {0} has shipping for country {2} shall be {3}")
	@MethodSource("ordersAndCountries")
	void shippingForCountry(String scenario, Order order, String country, boolean evaluation) {
		// given an order and a country ISO code
		log.debug("scenario is {}", scenario);
		if (order != null) {
			log.debug("principal is {}", AddressTestHelper.stringify(order.getPrincipalAddress()));
			log.debug("shipper is {}", AddressTestHelper.stringify(order.getShipperAddress()));
			log.debug("consignee is {}", AddressTestHelper.stringify(order.getConsigneeAddress()));
			if (order instanceof CollectionOrder collectionOrder) {
				log.debug("different consignee is {}", AddressTestHelper.stringify(collectionOrder.getDifferentConsigneeAddress()));
			}
			log.debug("further addresses are {}", AddressTestHelper.stringify(order.getAddresses(), AddressTestHelper::stringify));
		}
		// when verifying if order is shipping for the country
		// then we obtain expected evaluation
		Assertions.assertEquals(evaluation, helper.hasShippingForCountry(order, country));
	}

	private static Stream<Arguments> ordersAndCountries() {
		// @formatter:off
		return Stream.of(
			Arguments.of("null order", null, CountryCode.ROMANIA.getIsoCode(), false),
			Arguments.of("order unrelated to Romania", orderUnrelatedToRomania(), CountryCode.ROMANIA.getIsoCode(), false),
			Arguments.of("order having principal in Romania", orderHavingPrincipalInRomania(), CountryCode.ROMANIA.getIsoCode(), true),
			Arguments.of("order having shipper in Romania", orderHavingShipperInRomania(), CountryCode.ROMANIA.getIsoCode(), true),
			Arguments.of("order having consignee in Romania", orderHavingConsigneeInRomania(), CountryCode.ROMANIA.getIsoCode(), true),
			Arguments.of("collection order having different consignee in Romania", orderHavingDifferentConsigneeInRomania(), CountryCode.ROMANIA.getIsoCode(), true),
			Arguments.of("order having final delivery in Romania", orderHavingFinalDeliveryAddressInRomania(), CountryCode.ROMANIA.getIsoCode(), true)
		);
		// @formatter:on
	}

	private static Order orderHavingFinalDeliveryAddressInRomania() {
		Order order = orderUnrelatedToRomania();
		order.setAddresses(Collections.singletonList(finalDeliveryAddress(AddressTestHelper.addressInBucarest())));
		return order;
	}

	private static Order orderHavingDifferentConsigneeInRomania() {
		CollectionOrder order = orderUnrelatedToRomania();
		order.setDifferentConsigneeAddress(ADDRESS_MAPPER.mapAsOrderAddress(AddressTestHelper.addressInBucarest()));
		return order;
	}

	private static Order orderHavingConsigneeInRomania() {
		Order order = orderUnrelatedToRomania();
		order.setConsigneeAddress(ADDRESS_MAPPER.mapAsOrderAddress(AddressTestHelper.addressInBucarest()));
		return order;
	}

	private static Order orderHavingPrincipalInRomania() {
		Order order = orderUnrelatedToRomania();
		order.setPrincipalAddress(ADDRESS_MAPPER.mapAsOrderAddress(AddressTestHelper.addressInBucarest()));
		return order;
	}

	private static Order orderHavingShipperInRomania() {
		Order order = orderUnrelatedToRomania();
		order.setShipperAddress(ADDRESS_MAPPER.mapAsOrderAddress(AddressTestHelper.addressInBucarest()));
		return order;
	}

	private static CollectionOrder orderUnrelatedToRomania() {
		CollectionOrder order = UTILS.generateCollectionOrder();
		order.setPrincipalAddress(ADDRESS_MAPPER.mapAsOrderAddress(AddressTestHelper.addressInKempten()));
		order.setShipperAddress(ADDRESS_MAPPER.mapAsOrderAddress(AddressTestHelper.addressInMunich()));
		order.setConsigneeAddress(ADDRESS_MAPPER.mapAsOrderAddress(AddressTestHelper.addressInChanverrie()));
		order.setDifferentConsigneeAddress(ADDRESS_MAPPER.mapAsOrderAddress(AddressTestHelper.addressInNantes()));
		order.setAddresses(Collections.singletonList(finalDeliveryAddress(AddressTestHelper.addressInVertou())));
		return order;
	}

	private static OrderFurtherAddress finalDeliveryAddress(OrderBaseAddress address) {
		OrderFurtherAddress furtherAddress = ADDRESS_MAPPER.mapAsOrderFurtherAddress(address);
		furtherAddress.setAddressType(FurtherAddressType.FINAL_DELIVERY_ADDRESS.getKey());
		return furtherAddress;
	}

}