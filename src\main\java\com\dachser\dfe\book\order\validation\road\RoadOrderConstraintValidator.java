package com.dachser.dfe.book.order.validation.road;

import com.dachser.dfe.book.document.Document;
import com.dachser.dfe.book.document.DocumentConst;
import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.common.orderline.OrderLine;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.validation.Messages;
import com.dachser.dfe.book.order.validation.PayloadProvidingValidator;
import com.dachser.dfe.book.product.road.ShipperMasterdataService;
import com.dachser.dfe.book.user.UserContextService;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.apache.commons.lang3.StringUtils.isEmpty;

@RequiredArgsConstructor
@Slf4j
class RoadOrderConstraintValidator implements ConstraintValidator<ValidRoadOrder, RoadOrder>, PayloadProvidingValidator {

	private static final String ORDER_TEXT_TYPE_GS = "GS";

	private final Translator translator;

	private final DocumentService documentService;

	private final ShipperMasterdataService shipperMasterdataService;

	private final UserContextService userContextService;

	@Override
	public boolean isValid(RoadOrder order, ConstraintValidatorContext context) {
		context.disableDefaultConstraintViolation();
		AtomicBoolean anyInvalid = new AtomicBoolean(false);

		if (!userContextService.isCustomerAllowedToViewPackingAidPosition(order.getCustomerNumber()) && order.getOrderLines().isEmpty()) {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode("orderLines").addConstraintViolation();
			anyInvalid.set(true);
		}

		if (!userContextService.isCustomerAllowedToViewDangerousGoods(order.getCustomerNumber()) && order.getOrderTexts().stream()
				.anyMatch(orderText -> ORDER_TEXT_TYPE_GS.equals(orderText.getTextType()))) {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode("orderTexts").addConstraintViolation();
			anyInvalid.set(true);
		}

		final List<Document> documentsOfOrder = documentService.getDocumentsOfOrder(order);

		hasDoubleEntryPerOrderReference(order).ifPresent(referenceType -> {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode("orderReferences")
					.addContainerElementNode("referenceType", RoadOrderReference.class, 0).addPropertyNode(referenceType.getReferenceType().getDfeValue()).addConstraintViolation();
			anyInvalid.set(true);
		});

		// delivery note number required if edn document has been added
		if (hasEDNDoc(documentsOfOrder) && hasNoDeliveryNoteNumber(order)) {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode("orderReferences")
					.addContainerElementNode("referenceType", RoadOrderReference.class, 0).addPropertyNode(ReferenceType.DELIVERY_NOTE_NUMBER.getDfeValue())
					.addConstraintViolation();
			anyInvalid.set(true);
		}

		if (hasCustomsDoc(documentsOfOrder) && hasNoCustomsSetting(order)) {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode("customsType").addConstraintViolation();
			anyInvalid.set(true);
		}

		if (!hasRoadOrderValidCollectionDates(order)) {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode("collectionDate").addConstraintViolation();
			anyInvalid.set(true);
		}

		if (!hasValidFixedDate(order)) {
			context.buildConstraintViolationWithTemplate(translator.toLocale("Fixdate is not valid")).addPropertyNode("fixDate").addConstraintViolation();
			anyInvalid.set(true);
		}

		List<Integer> invalidNatureOfGoods = findInvalidNatureOfGoods(order.getOrderLines());

		if (!invalidNatureOfGoods.isEmpty()) {
			ConstraintValidatorContext.ConstraintViolationBuilder violationWithTemplate = context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT));

			invalidNatureOfGoods.forEach(orderLineIndex -> violationWithTemplate.addPropertyNode(String.format("orderLines[%s]", orderLineIndex.toString()))
					.addContainerElementNode("content", OrderLine.class, 0).addConstraintViolation());
			anyInvalid.set(true);
		}

		if (checkOrderNumberMandatory(order)) {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode("orderNumber").addConstraintViolation();
			anyInvalid.set(true);
		}

		return !anyInvalid.get();
	}

	private boolean checkOrderNumberMandatory(RoadOrder order) {
		return shipperMasterdataService.isOrderNumberMandatoryForCustomerNumber(order.getCustomerNumber()) && isEmpty(order.getOrderNumber());
	}

	private List<Integer> findInvalidNatureOfGoods(List<RoadOrderLine> orderLines) {
		return orderLines.stream().filter(roadOrderLine -> StringUtils.isBlank(roadOrderLine.getContent())).map(orderLines::indexOf).toList();
	}

	private boolean hasValidFixedDate(RoadOrder order) {
		if (order.getFixDate() != null && order.getCollectionDate() != null) {
			return order.getFixDate().isAfter(order.getCollectionDate());
		}
		return true;
	}

	private boolean hasNoCustomsSetting(Order order) {
		return order.getCustomsType() == null;
	}

	private boolean hasCustomsDoc(List<Document> documentsOfOrder) {
		return documentsOfOrder.stream().filter(doc -> doc.getDocumentType() != null).anyMatch(doc -> DocumentConst.CUSTOMS_CATEGORY.equals(doc.getDocumentType().getCategory()));
	}

	private boolean hasNoDeliveryNoteNumber(RoadOrder order) {
		return order.getOrderReferences().stream().noneMatch(ref -> ref.getReferenceType().equals(ReferenceType.DELIVERY_NOTE_NUMBER));
	}

	private boolean hasEDNDoc(List<Document> documentsOfOrder) {
		return documentsOfOrder.stream().filter(doc -> doc.getDocumentType() != null).anyMatch(doc -> DocumentConst.TYPE_EDN.equals(doc.getDocumentType().getType()));
	}

	private boolean hasRoadOrderValidCollectionDates(RoadOrder roadOrder) {
		// both empty is a valid case
		if (roadOrder.getCollectionFrom() == null && roadOrder.getCollectionTo() == null) {
			return true;
		} else if (roadOrder.getCollectionFrom() != null && roadOrder.getCollectionTo() != null) {
			final OffsetDateTime plus2Hours = roadOrder.getCollectionFrom().plusHours(2);
			return plus2Hours.isBefore(roadOrder.getCollectionTo()) || plus2Hours.isEqual(roadOrder.getCollectionTo());
		} else {
			// Just one of the dates is filled, invalid
			return false;
		}
	}

	private Optional<RoadOrderReference> hasDoubleEntryPerOrderReference(RoadOrder order) {
		List<RoadOrderReference> orderReferences = order.getOrderReferences();

		if (orderReferences == null) {
			return Optional.empty();
		}

		return orderReferences.stream().filter(reference ->
						orderReferences.stream().filter(r -> r.getReferenceType().equals(reference.getReferenceType()) && r.getReference().equals(reference.getReference())).count() > 1)
				.findFirst();
	}

}