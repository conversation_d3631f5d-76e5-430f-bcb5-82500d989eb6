package com.dachser.dfe.book.order.common.dangerousgood;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Entity
@Table(name = "order_eq_dangerous_good")
@EqualsAndHashCode(callSuper = true)
@DiscriminatorValue("2")
public class EQDangerousGood extends DangerousGood {

	private int noOfPackages;

	private String packagingKey;

	@Override
	public DangerousGoodType getDangerousGoodType() {
		return DangerousGoodType.EQ_DANGEROUS_GOOD;
	}
}
