package com.dachser.dfe.book.service;

import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.model.IncoTermDto;
import com.dachser.dfe.book.term.IncoTermService;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class IncoTermServiceTest implements ResourceLoadingTest {

	@InjectMocks
	private IncoTermService incoTermService;

	@Mock
	private GeneralDataService generalDataService;

	@Test
	void getAllITermsTest() {
		when(generalDataService.getIncoTerms()).thenReturn(List.of(generateIncoTerm()));
		List<IncoTermDto> allIncoTerms = incoTermService.getAllActiveIncoTerms();
		assertNotNull(allIncoTerms);
		assertNotEquals(0, allIncoTerms.size());
		allIncoTerms.forEach(incoterm -> {
			assertTrue(StringUtils.isNotEmpty(incoterm.getCode()));
			assertTrue(StringUtils.isNotEmpty(incoterm.getLabel()));
		});
	}

	@Test
	void getIncoTermsByCode() {
		when(generalDataService.getIncoTerms()).thenReturn(List.of(generateIncoTerm()));
		IncoTermDto existingterm = incoTermService.getIncoTermByCode("FCA");
		assertNotNull(existingterm);
	}

	@Test
	void getAllIncoTermsAsJson() {
		when(generalDataService.getIncoTerms()).thenReturn(List.of(generateIncoTerm(), new IncoTermDto().code("DDP").label("Delivered Duty Paid").dachserCode("DDP")));
		List<IncoTermDto> incoTermList = incoTermService.getAllActiveIncoTerms();
		assertNotNull(incoTermList);
		assertEquals(2, incoTermList.size());
		assertEquals("DDP", incoTermList.get(0).getCode());
	}

	@Test
	void getJsonIncoTermFromRepository() {
		when(generalDataService.getIncoTerms()).thenReturn(List.of(generateIncoTerm()));
		IncoTermDto incoTermDto = incoTermService.getIncoTermByDachserCode("FCA");
		assertNotNull(incoTermDto);
		assertEquals("FCA", incoTermDto.getCode());
	}

	@Test
	void shouldReturnNullForNoIncoTermsFound() {
		when(generalDataService.getIncoTerms()).thenReturn(List.of());
		IncoTermDto incoTermDto = incoTermService.getIncoTermByDachserCode("FCA");
		assertNull(incoTermDto);
	}

	@Test
	void shouldReturnActiveIncoTerm() {
		when(generalDataService.getIncoTerms()).thenReturn(List.of(generateIncoTerm()));
		boolean active = incoTermService.isIncoTermActiveByDachserCode("FCA");
		assertTrue(active);
	}

	private IncoTermDto generateIncoTerm() {
		IncoTermDto incoTerm = new IncoTermDto();
		incoTerm.setCode("FCA");
		incoTerm.setDachserCode("FCA");
		incoTerm.setDescription(null);
		incoTerm.setLabel("Dachser Warehouse");
		return incoTerm;
	}
}
