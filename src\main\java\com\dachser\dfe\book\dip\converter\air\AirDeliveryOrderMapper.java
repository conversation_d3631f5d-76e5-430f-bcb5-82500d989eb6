package com.dachser.dfe.book.dip.converter.air;

import com.dachser.dfe.book.dip.converter.shared.AirSeaAddressMappingConfig;
import com.dachser.dfe.book.dip.converter.shared.AirSeaGoodsDescriptionMapper;
import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderContactCommunicationsMapper;
import com.dachser.dfe.book.dip.converter.shared.DateMapper;
import com.dachser.dfe.book.dip.converter.shared.StringNotEmptyConditionMapper;
import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.model.jaxb.order.asl.Address;
import com.dachser.dfe.book.model.jaxb.order.asl.Contact;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper(config = AirSeaAddressMappingConfig.class, componentModel = "spring", uses = { DateMapper.class, AirSeaOrderContactCommunicationsMapper.class, AirOrderLineMapper.class,
		AirOrderReferenceMapper.class, StringNotEmptyConditionMapper.class, AirSeaGoodsDescriptionMapper.class })
public interface AirDeliveryOrderMapper {

	default List<ForwardingOrder.AirFreightShipment.DeliveryOrder> mapDeliveryOrders(AirOrder order) {
		// We need to map an delivery order in that case
		if (!order.isDeliverToAirport()) {
			return List.of();
		}
		final ForwardingOrder.AirFreightShipment.DeliveryOrder deliveryOrder = mapDeliveryOrder(order);
		return List.of(deliveryOrder);
	}

	@Mapping(target = "orderPosition", source = "orderLines")
	@Mapping(target = "goodsDescription", source = ".", qualifiedByName = "mapGoodsDescription")
	@Mapping(target = "orderAddress", source = ".", qualifiedByName = "mapDelivererAddress")
	@Mapping(target = "orderReference", source = "orderReferences")
	@Mapping(target = "plannedDeliveryDate", source = "collectionDate", conditionExpression = "java(!order.isRequestArrangement())")
	@Mapping(target = "additionalOrderInformation", source = "orderLines")
	ForwardingOrder.AirFreightShipment.DeliveryOrder mapDeliveryOrder(AirOrder order);

	@Named("mapDelivererAddress")
	@Mapping(target = "address", source = "shipperAddress")
	@Mapping(target = "contact", source = "orderContact")
	@Mapping(target = "typeOfAddress", constant = Types.AddressTypes.DELIVERER)
	ForwardingOrder.AirFreightShipment.DeliveryOrder.OrderAddress mapDelivererAddress(AirOrder airOrder);

	@InheritConfiguration(name = "mapAddress")
	Address mapOrderAddress(OrderAddress orderAddress);

	@InheritConfiguration(name = "mapContact")
	Contact mapOrderContact(OrderContact orderContact);


}
