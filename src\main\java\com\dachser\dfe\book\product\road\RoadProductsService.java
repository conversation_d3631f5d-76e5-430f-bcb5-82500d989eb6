package com.dachser.dfe.book.product.road;

import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.exception.DachserCodeNotMappedException;
import com.dachser.dfe.book.exception.ExtDeliveryProductServiceNotAvailable;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.DeliveryProductsWithFavoritesDto;
import com.dachser.dfe.book.order.road.RoadOrder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class RoadProductsService {

	private final RoadProductsAdapter roadProductsAdapter;

	private final GeneralDataService generalDataService;

	private final WhiteListFilterConfiguration whiteListFilterConfiguration;

	private final FavoriteRoadProductsProvider favoriteRoadProductsProvider;

	public List<DeliveryProductDto> getDeliveryProductsForDivision(Division division) {
		final List<String> whiteList = determineWhitelistConfigurationBasedOnDivision(division);
		try {
			return generalDataService.getDeliveryProductsForDivision(division, LocaleContextHolder.getLocale().getLanguage()).stream()
					.filter(product -> whiteList.contains(product.getCode())).sorted(Comparator.comparing(DeliveryProductDto::getDescription)).toList();
		} catch (ExtDeliveryProductServiceNotAvailable e) {
			log.error("Error during getting delivery products for division {} and business domain {}", division, e);
			return new ArrayList<>();
		}
	}

	public Optional<DeliveryProductDto> getProductByKey(String key, Division division) {
		if (key == null) {
			return Optional.empty();
		}
		final List<DeliveryProductDto> deliveryProductsForDivision = getDeliveryProductsForDivision(division);
		return deliveryProductsForDivision.stream().filter(product -> key.equals(product.getCode())).findFirst();
	}

	/**
	 * product validation concerning transport constellation
	 *
	 * @param roadOrder - order to validate
	 * @return true if valid, any other case false
	 */
	public boolean validateProduct(RoadOrder roadOrder, int businessDomain) {
		if (roadOrder != null && roadOrder.getProduct() != null) {
			try {
				final boolean valid = roadProductsAdapter.validateProduct(roadOrder, businessDomain);
				log.debug("Product validation for product {} of order {}  - result {}", roadOrder.getProduct(), roadOrder.getOrderId(), valid);
				return valid;
			} catch (DachserCodeNotMappedException exception) {
				log.error("Error during mapping of country codes for order {}", roadOrder.getOrderId(), exception);
				return false;
			} catch (ExtDeliveryProductServiceNotAvailable exception) {
				log.error("Error during validation of product {} of order {} ", roadOrder.getProduct(), roadOrder.getOrderId(), exception);
				return false;
			}
		}
		return false;
	}

	public DeliveryProductsWithFavoritesDto getOnlyApplicableDeliveryProductsFavoritesForCustomer(String customerNumber, Division division, int businessDomain,
			String shipperCountry, String shipperPostCode, String consigneeCountry, String consigneePostcode) {
		final DeliveryProductsWithFavoritesDto productsWithFavorites = new DeliveryProductsWithFavoritesDto();
		final List<DeliveryProductDto> deliveryProducts = roadProductsAdapter.getOnlyValidDeliveryProducts(division, businessDomain,
				determineWhitelistConfigurationBasedOnDivision(division), shipperCountry, shipperPostCode, consigneeCountry, consigneePostcode);

		final List<DeliveryProductDto> favoriteProducts = favoriteFilterProductsList(customerNumber, deliveryProducts);

		productsWithFavorites.setFavorites(favoriteProducts);
		deliveryProducts.removeAll(favoriteProducts);
		productsWithFavorites.setDeliveryProducts(deliveryProducts);
		return productsWithFavorites;
	}

	private @NotNull List<DeliveryProductDto> favoriteFilterProductsList(String customerNumber, List<DeliveryProductDto> deliveryProducts) {
		List<String> favoriteProductCodes = favoriteRoadProductsProvider.determineFavoriteProductCodes(customerNumber);
		return favoriteProductCodes.stream().map(favorite -> deliveryProducts.stream().filter(product -> product.getCode().equals(favorite)).findFirst()).flatMap(Optional::stream)
				.toList();
	}

	private List<String> determineWhitelistConfigurationBasedOnDivision(Division division) {
		if (Division.F.equals(division)) {
			return whiteListFilterConfiguration.getDeliveryProducts().getRoad().getFoodLogistics();
		} else if (Division.T.equals(division)) {
			return whiteListFilterConfiguration.getDeliveryProducts().getRoad().getEuropeanLogistics();
		} else {
			throw new IllegalArgumentException("No whitelist filter configuration found for division " + division);
		}
	}

}
