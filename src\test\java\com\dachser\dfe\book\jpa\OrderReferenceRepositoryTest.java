package com.dachser.dfe.book.jpa;

import com.dachser.dfe.book.SpringBasedLocalMockTest;
import com.dachser.dfe.book.TestOrdersDirectory;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderRepository;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBasedLocalMockTest
@Transactional
@Rollback
@Slf4j
class OrderReferenceRepositoryTest {

	@Autowired
	private OrderRepository orderRepository;

	@Autowired
	private OrderReferenceRepository orderReferenceRepository;

	@ParameterizedTest(name = "#{index} - are references of type {0} well read ?")
	@MethodSource("orderIdsAndReferenceTypes")
	void readWithType(String scenario, Long orderId, ReferenceType referenceType) {
		// given an existing order
		// having references of a specific type
		Optional<Order> order = orderRepository.findById(orderId);
		assertTrue(order.isPresent());
		// when reading those references
		List<RoadOrderReference> references = orderReferenceRepository.findAllByOrder_OrderIdAndReferenceType(order.get().getOrderId(), referenceType);
		// then we obtain items
		assertReferences(references);
		log.debug("road references of type {} are {}", referenceType.name(), references.stream().map(RoadOrderReference::getReference).toArray());
		// and reference types are as expected
		assertTrue(references.stream().map(RoadOrderReference::getReferenceType).allMatch(referenceType::equals));
		// and reference values are present
		assertFalse(references.stream().map(RoadOrderReference::getReference).allMatch(StringUtils::isBlank));
	}

	private static Stream<Arguments> orderIdsAndReferenceTypes() {
		// @formatter:off
		return Stream.of(
			Arguments.of(ReferenceType.BOOKING_REFERENCE.name(), TestOrdersDirectory.ORDER_HAVING_REFERENCES_FOR_BOOKING, ReferenceType.BOOKING_REFERENCE),
			Arguments.of(ReferenceType.IDENTIFICATION_CODE_TRANSPORT.name(), TestOrdersDirectory.ORDER_HAVING_REFERENCES_FOR_IDENTIFICATION_CODE_TRANSPORT, ReferenceType.IDENTIFICATION_CODE_TRANSPORT)
		);
		// @formatter:on
	}

	@ParameterizedTest(name = "#{index} - are references of type {0} well read ?")
	@MethodSource("orderIdsAndReferenceTypesAndReferenceSubtypes")
	void readWithSubtype(String scenario, Long orderId, ReferenceType referenceType, RoadOrderReferenceSubtype referenceSubtype) {
		// given an existing order
		// having references of a specific type and subtype
		Optional<Order> order = orderRepository.findById(orderId);
		assertTrue(order.isPresent());
		// when reading those references
		List<RoadOrderReference> references = orderReferenceRepository.findAllByOrder_OrderIdAndReferenceTypeAndReferenceSubtype(order.get().getOrderId(), referenceType, referenceSubtype);
		// then we obtain items
		assertReferences(references);
		log.debug("road references of type {} and subtype {} are {}", referenceType.name(), referenceSubtype.name(), references.stream().map(RoadOrderReference::getReference).toArray());
		// and reference types are as expected
		assertTrue(references.stream().map(RoadOrderReference::getReferenceType).allMatch(referenceType::equals));
		// and reference subtypes are as expected
		assertTrue(references.stream().map(RoadOrderReference::getReferenceSubtype).allMatch(referenceSubtype::equals));
		// and reference values are present
		assertFalse(references.stream().map(RoadOrderReference::getReference).allMatch(StringUtils::isBlank));
	}

	private static Stream<Arguments> orderIdsAndReferenceTypesAndReferenceSubtypes() {
		// @formatter:off
		return Stream.of(
			Arguments.of(title(RoadOrderReferenceSubtype.UIT), TestOrdersDirectory.ORDER_HAVING_REFERENCES_FOR_IDENTIFICATION_CODE_TRANSPORT, ReferenceType.IDENTIFICATION_CODE_TRANSPORT, RoadOrderReferenceSubtype.UIT)
		);
		// @formatter:on
	}

	@Test
	void save() {
		// given an existing road order
		Optional<Order> order = orderRepository.findById(TestOrdersDirectory.FORWARDING_ROAD_ORDER);
		assertTrue(order.isPresent());
		// and a new reference for that order
		RoadOrderReference roadOrderReference = new RoadOrderReference();
		roadOrderReference.setOrder(order.get());
		roadOrderReference.setReferenceType(ReferenceType.PURCHASE_ORDER_NUMBER);
		roadOrderReference.setReference("my-purchase-order-number");
		assertNull(roadOrderReference.getOrderReferenceId());
		// when saving it
		roadOrderReference = orderReferenceRepository.saveAndFlush(roadOrderReference);
		assertNotNull(roadOrderReference);
		// then storage identifier is present
		log.debug("road reference storage id is {}", roadOrderReference.getOrderReferenceId());
		assertStorageId(roadOrderReference.getOrderReferenceId());
	}

	private static void assertStorageId(Long id) {
		assertNotNull(id);
		assertNotEquals(0L, id);
		assertTrue(id > 0L);
	}

	private static void assertReferences(List<RoadOrderReference> references) {
		assertNotNull(references);
		assertFalse(references.isEmpty());
		assertFalse(references.stream().anyMatch(Objects::isNull));
	}

	private static String title(RoadOrderReferenceSubtype subtype) {
		return subtype.getParentType().name().concat(".").concat(subtype.name());
	}

}