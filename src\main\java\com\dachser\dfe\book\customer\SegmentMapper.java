package com.dachser.dfe.book.customer;

import com.dachser.dfe.book.model.SegmentDto;
import com.dachser.dfe.book.model.Segment;
import org.mapstruct.Mapper;
import org.mapstruct.ValueMapping;

@Mapper(componentModel = "spring")
public interface SegmentMapper {

	Segment map(SegmentDto segmentDto);

	SegmentDto map(Segment segment);

	@ValueMapping(source = "AIR", target = "ASL")
	@ValueMapping(source = "SEA", target = "ASL")
	@ValueMapping(source = "ROAD", target = "ROAD")
	String mapToAsl(Segment segment);
}
