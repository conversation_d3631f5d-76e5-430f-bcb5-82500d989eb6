package com.dachser.dfe.book.product.road;

import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.exception.ExtDeliveryProductServiceNotAvailable;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.mapper.ProductNameMapper;
import com.dachser.dfe.book.mapper.ProductNameMapperImpl;
import com.dachser.dfe.book.masterdata.road.RoadMasterDataApiWrapper;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.road.masterdata.model.RMDProductPilotResponseDTO;
import com.dachser.dfe.road.masterdata.model.RMDProductPilotResponsePositionDTO;
import com.dachser.dfe.road.masterdata.model.RMDValidateProductDTO;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestClientException;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RoadProductsAdapterTest {

	@InjectMocks
	RoadProductsAdapterExt roadProductsAdapter;

	@Mock
	RoadMasterDataApiWrapper roadMasterDataApiWrapper;

	@Mock
	CountryService countryService;

	@Mock
	GeneralDataService generalDataService;

	@Spy
	ProductNameMapper productNameMapper = new ProductNameMapperImpl();

	@Nested
	class ValidateProduct {

		private RoadOrder roadOrder = new RoadOrder();

		private OrderAddress shipperAddress = new OrderAddress();

		private OrderAddress consigneeAddress = new OrderAddress();

		@Test
		void shouldReturnTrueOnValidProduct() {
			when(roadMasterDataApiWrapper.validateProduct(any())).thenReturn(new RMDValidateProductDTO().product("product").returnCode("L"));

			shipperAddress.setPostcode("12345");
			consigneeAddress.setPostcode("12345");

			roadOrder.setProduct("L");
			roadOrder.setDivision(Division.T);
			roadOrder.setShipperAddress(shipperAddress);
			roadOrder.setConsigneeAddress(consigneeAddress);
			boolean result = roadProductsAdapter.validateProduct(roadOrder, 1);
			assertTrue(result);
		}

		@Test
		void shouldReturnFalseOnInvalidProduct() {
			when(roadMasterDataApiWrapper.validateProduct(any())).thenReturn(new RMDValidateProductDTO().returnCode("FEHL"));

			shipperAddress.setPostcode("12345");
			consigneeAddress.setPostcode("12345");

			roadOrder.setProduct("L");
			roadOrder.setDivision(Division.T);
			roadOrder.setShipperAddress(shipperAddress);
			roadOrder.setConsigneeAddress(consigneeAddress);
			boolean result = roadProductsAdapter.validateProduct(roadOrder, 1);
			assertFalse(result);
		}

		@Test
		void shouldReturnFalseOnIncompleteOrder() {
			roadOrder.setProduct("L");
			roadOrder.setDivision(Division.T);
			boolean result = roadProductsAdapter.validateProduct(roadOrder, 1);
			assertFalse(result);
		}

		@Test
		void shouldThrowExceptionWhenApiIsNotAvailable() {
			when(roadMasterDataApiWrapper.validateProduct(any())).thenThrow(new RestClientException("API not available"));

			shipperAddress.setPostcode("12345");
			consigneeAddress.setPostcode("12345");

			roadOrder.setProduct("L");
			roadOrder.setDivision(Division.T);
			roadOrder.setShipperAddress(shipperAddress);
			roadOrder.setConsigneeAddress(consigneeAddress);
			assertThrows(ExtDeliveryProductServiceNotAvailable.class, () -> roadProductsAdapter.validateProduct(roadOrder, 0));
		}

	}

	@Nested
	class GetApplicableDeliveryProductsForDivision {

		@Test
		void shouldReturnDeliveryProduct() {
			when(countryService.mapToDachserCountryCode("DE")).thenReturn("D");
			when(roadMasterDataApiWrapper.retrieveProducts(any())).thenReturn(
					new RMDProductPilotResponseDTO().products(List.of(new RMDProductPilotResponsePositionDTO().productDescription("productName").product("L"))));

			List<DeliveryProductDto> result = roadProductsAdapter.getOnlyValidDeliveryProducts(Division.T, 1, List.of("L"), "DE", "12345", "DE", "12345");
			assertNotNull(result);
		}

		@Test
		void shouldReturnEmptyListWhenNoValidProductFound() {
			when(countryService.mapToDachserCountryCode("DE")).thenReturn("D");
			when(roadMasterDataApiWrapper.retrieveProducts(any())).thenReturn(
					new RMDProductPilotResponseDTO().products(List.of(new RMDProductPilotResponsePositionDTO().productDescription("productName").product("L"))));

			List<DeliveryProductDto> result = roadProductsAdapter.getOnlyValidDeliveryProducts(Division.T, 1, List.of("X"), "DE", "12345", "DE", "12345");
			assertTrue(result.isEmpty());
		}

		@Test
		void shouldReturnEmptyListWhenWhiteListIsEmpty() {
			when(roadMasterDataApiWrapper.retrieveProducts(any())).thenReturn(
					new RMDProductPilotResponseDTO().products(List.of(new RMDProductPilotResponsePositionDTO().productDescription("productName").product("L"))));

			List<DeliveryProductDto> result = roadProductsAdapter.getOnlyValidDeliveryProducts(Division.T, 1, List.of(), "DE", "12345", "DE", "12345");
			assertTrue(result.isEmpty());
		}

		@Test
		void shouldReturnEmptyListWhenProductMissing() {
			when(roadMasterDataApiWrapper.retrieveProducts(any())).thenReturn(
					new RMDProductPilotResponseDTO().products(List.of(new RMDProductPilotResponsePositionDTO().productDescription("productName"))));

			List<DeliveryProductDto> result = roadProductsAdapter.getOnlyValidDeliveryProducts(Division.T, 1, List.of("L"), "DE", "12345", "DE", "12345");
			assertTrue(result.isEmpty());
		}

		@Test
		void shouldReturnEmptyListWhenProductNameMissing() {
			when(roadMasterDataApiWrapper.retrieveProducts(any())).thenReturn(
					new RMDProductPilotResponseDTO().products(List.of(new RMDProductPilotResponsePositionDTO().product("L"))));

			List<DeliveryProductDto> result = roadProductsAdapter.getOnlyValidDeliveryProducts(Division.T, 1, List.of("L"), "DE", "12345", "DE", "12345");
			assertTrue(result.isEmpty());
		}

		@Test
		void shouldReturnEmptyListIfProductsIsMissingInResponse() {
			when(roadMasterDataApiWrapper.retrieveProducts(any())).thenReturn(new RMDProductPilotResponseDTO());
			List<DeliveryProductDto> result = roadProductsAdapter.getOnlyValidDeliveryProducts(Division.T, 1, List.of("L"), "DE", "12345", "DE", "12345");
			assertTrue(result.isEmpty());
		}

		@Test
		void shouldThrowExceptionWhenApiIsNotAvailable() {
			when(roadMasterDataApiWrapper.retrieveProducts(any())).thenThrow(new RestClientException("API not available"));

			List<String> whiteList = List.of();
			assertThrows(ExtDeliveryProductServiceNotAvailable.class,
					() -> roadProductsAdapter.getOnlyValidDeliveryProducts(Division.T, 0, whiteList, "DE", "12345", "DE", "12345"));
		}

	}

}