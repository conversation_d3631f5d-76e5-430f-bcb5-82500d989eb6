package com.dachser.dfe.book.jpa;

import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface OrderReferenceRepository extends JpaRepository<RoadOrderReference, Long> {

	@Query("select ore from RoadOrderReference ore where ore.reference in :references and ore.order.orderId = :orderId")
	List<RoadOrderReference> findOrderReferencesByReferenceAndOrderId(List<String> references, Long orderId);

	List<RoadOrderReference> findAllByOrder_OrderIdAndReferenceTypeAndReferenceSubtype(Long orderOrderId, @NotNull ReferenceType referenceType, RoadOrderReferenceSubtype referenceSubtype);

	List<RoadOrderReference> findAllByOrder_OrderIdAndReferenceType(Long orderOrderId, @NotNull ReferenceType referenceType);

}
