package com.dachser.dfe.book.generaldata;

import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ErrorIdExternalServiceNotAvailableException;
import com.dachser.dfe.book.generaldata.packaging.PackagingOptionMapper;
import com.dachser.dfe.book.generaldata.packaging.PackagingOptionMapperImpl;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.user.BusinessDomainProvider;
import com.dachser.dfe.book.user.DefaultBusinessDomain;
import com.dachser.dfe.generaldata.api.PackagingApi;
import com.dachser.dfe.generaldata.model.GDPackagingOptionDto;
import com.dachser.dfe.generaldata.model.GDSegmentDto;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestClientResponseException;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class GeneralDataAdapterPackagingTest {

	private static final Integer BUSINESS_DOMAIN = 1;

	@Mock
	PackagingApi packagingApi;

	@Spy
	BusinessDomainProvider businessDomainProvider = new DefaultBusinessDomain();

	@Spy
	PackagingOptionMapper packagingOptionMapper = new PackagingOptionMapperImpl();

	@InjectMocks
	GeneralDataAdapterExt sut;

	@Nested
	class PackagingOptionsForAir {

		@Test()
		void receiveNonEmptyList() {
			ArrayList<GDPackagingOptionDto> types = new ArrayList<>();
			types.addAll(constructValidDTOsASL());

			when(packagingApi.getPackagingOptionsV2(eq(BUSINESS_DOMAIN), anyString(), eq(GDSegmentDto.AIR))).thenReturn(types);

			final List<OptionDto> serviceResponse = sut.getPackagingOptionsAir("en");
			assertNotNull(serviceResponse);
			assertFalse(serviceResponse.isEmpty());
			assertEquals(3, serviceResponse.size());
			assertEquals("Box", serviceResponse.get(0).getDescription());
			assertEquals("Karton", serviceResponse.get(1).getDescription());
			assertEquals("Euroflachpalette", serviceResponse.get(2).getDescription());
		}

		@Test()
		void receiveEmptyListOnInvalidResponse() {
			when(packagingApi.getPackagingOptionsV2(eq(BUSINESS_DOMAIN), anyString(), eq(GDSegmentDto.AIR))).thenReturn(List.of());

			final List<OptionDto> serviceResponse = sut.getPackagingOptionsAir("en");
			assertNotNull(serviceResponse);
			assertTrue(serviceResponse.isEmpty());
		}

		@Test()
		void throwExceptionOnApiError() {
			when(packagingApi.getPackagingOptionsV2(eq(BUSINESS_DOMAIN), anyString(), eq(GDSegmentDto.AIR))).thenThrow(RestClientResponseException.class);

			ErrorIdExternalServiceNotAvailableException exception = assertThrows(ErrorIdExternalServiceNotAvailableException.class, () -> sut.getPackagingOptionsAir("en"));
			assertEquals(BookErrorId.ERR_PA_02, exception.getErrorId());
		}

	}

	private List<GDPackagingOptionDto> constructValidDTOsASL() {
		final GDPackagingOptionDto packagingNameDTO1 = new GDPackagingOptionDto();
		packagingNameDTO1.setCode("BX");
		packagingNameDTO1.setName("Box");

		final GDPackagingOptionDto packagingNameDTO2 = new GDPackagingOptionDto();
		packagingNameDTO2.setCode("CT");
		packagingNameDTO2.setName("Karton");

		final GDPackagingOptionDto packagingNameDTO3 = new GDPackagingOptionDto();
		packagingNameDTO3.setCode("PE");
		packagingNameDTO3.setName("Euroflachpalette");

		return List.of(packagingNameDTO1, packagingNameDTO2, packagingNameDTO3);
	}

}
