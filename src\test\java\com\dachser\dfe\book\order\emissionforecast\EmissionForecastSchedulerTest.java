package com.dachser.dfe.book.order.emissionforecast;

import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderRepository;
import com.dachser.dfe.book.order.OrderRepositoryFacade;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.sea.SeaImportOrder;
import com.dachser.dfe.book.order.sea.SeaOrder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EmissionForecastSchedulerTest {

    @Mock
    private OrderRepositoryFacade orderRepositoryFacade;

    @Mock
    private OrderRepository orderRepository;

    @Mock
    private EmissionForecastService emissionForecastService;

    @InjectMocks
    private EmissionForecastScheduler emissionForecastScheduler;

    private RoadOrder roadOrder;
    private AirOrder airOrder;
    private SeaOrder seaOrder;

    @BeforeEach
    void setUp() {
        roadOrder = new RoadOrder();
        roadOrder.setOrderId(1L);
        roadOrder.setEmissionForecast(null);
        roadOrder.setEmissionForecastRetryCount(0);

        airOrder = new AirImportOrder();
        airOrder.setOrderId(2L);
        airOrder.setEmissionForecast(null);
        airOrder.setEmissionForecastRetryCount(0);

        seaOrder = new SeaImportOrder();
        seaOrder.setOrderId(3L);
        seaOrder.setEmissionForecast(null);
        seaOrder.setEmissionForecastRetryCount(0);
    }

    @Test
    void shouldProcessOrdersWithMissingEmissionForecast() {
        when(orderRepositoryFacade.findAllWithEmissionForecastNullAndRetryCountBelow10Road())
            .thenReturn(List.of(roadOrder));
        when(orderRepositoryFacade.findAllWithEmissionForecastNullAndRetryCountBelow10Air())
            .thenReturn(List.of(airOrder));
        when(orderRepositoryFacade.findAllWithEmissionForecastNullAndRetryCountBelow10Sea())
            .thenReturn(List.of(seaOrder));

        when(emissionForecastService.getEmissionForecastForOrder(any(Order.class))).thenReturn(Optional.of(1.23));

        emissionForecastScheduler.processOrdersWithMissingEmissionForecast();

        verify(emissionForecastService, times(3)).getEmissionForecastForOrder(any(Order.class));
        verify(orderRepository, times(3)).save(any(Order.class));
    }

    @Test
    void shouldNotProcessWhenNoOrdersFound() {
        when(orderRepositoryFacade.findAllWithEmissionForecastNullAndRetryCountBelow10Road())
            .thenReturn(Collections.emptyList());
        when(orderRepositoryFacade.findAllWithEmissionForecastNullAndRetryCountBelow10Air())
            .thenReturn(Collections.emptyList());
        when(orderRepositoryFacade.findAllWithEmissionForecastNullAndRetryCountBelow10Sea())
            .thenReturn(Collections.emptyList());

        emissionForecastScheduler.processOrdersWithMissingEmissionForecast();

        verifyNoInteractions(emissionForecastService);
        verifyNoInteractions(orderRepository);
    }

    @Test
    void shouldHandleExceptionAndIncrementRetryCount() {
        when(orderRepositoryFacade.findAllWithEmissionForecastNullAndRetryCountBelow10Road())
            .thenReturn(List.of(roadOrder));
        when(orderRepositoryFacade.findAllWithEmissionForecastNullAndRetryCountBelow10Air())
            .thenReturn(Collections.emptyList());
        when(orderRepositoryFacade.findAllWithEmissionForecastNullAndRetryCountBelow10Sea())
            .thenReturn(Collections.emptyList());

        when(emissionForecastService.getEmissionForecastForOrder(any(Order.class)))
            .thenThrow(new RuntimeException("Simulated error"));

        emissionForecastScheduler.processOrdersWithMissingEmissionForecast();

        verify(orderRepository).save(roadOrder);
    }
}