package com.dachser.dfe.book.order.validation;

import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.model.PostcodeValidationDto;
import com.dachser.dfe.book.model.ValidationResultDto;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.service.postalcode.PostalCodeService;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderAddressValidatorTest implements ResourceLoadingTest {

	@Mock
	private Translator translator;

	@Mock
	private PostalCodeService postalCodeService;

	@Mock
	private CountryService countryService;

	@InjectMocks
	private OrderAddressValidator validator;

	@Nested
	class Valid {

		@EnumSource(OrderType.class)
		@ParameterizedTest
		void shouldValidateValidAddress(OrderType type) {
			OrderAddress address = constructValidAddressWithContact();

			assertTrue(validator.validateToResultAddressBasedOnOrderType(address, type).getValid());
		}

		@EnumSource(OrderType.class)
		@ParameterizedTest
		void shouldValidateMissingPostCodeWhenRequired(OrderType type) {
			OrderAddress address = constructValidAddressWithContact();
			address.setPostcode(null);

			ValidationResultDto validationResultDto = validator.validateToResultAddressBasedOnOrderType(address, type);

			assertTrue(validationResultDto.getValid());
		}

		@EnumSource(OrderType.class)
		@ParameterizedTest
		void shouldSkipCityAndPostCodeValidationForIrelandPrincipalAddress(OrderType type) {
			OrderAddress address = constructValidAddressWithContact();
			address.setCustomerNumber("123456789");
			address.setCountryCode("IE");
			address.setPostcode(null);
			address.setCity(null);

			ValidationResultDto validationResultDto = validator.validateToResultAddressBasedOnOrderType(address, type);

			assertTrue(validationResultDto.getValid());
		}

		@Test
		void shouldValidateHKAddressWithoutPostcode() {
			OrderAddress address = constructValidAddressWithoutContact();
			address.setPostcode(null);
			address.setCountryCode("HK");

			when(countryService.isPostCodeMandatory("HK")).thenReturn(false);

			ValidationResultDto validationResultDto = validator.validateToResultAddressBasedOnOrderType(address, OrderType.ROADFORWARDINGORDER);

			assertTrue(validationResultDto.getValid());
		}

	}

	@Nested
	class Invalid {

		@EnumSource(OrderType.class)
		@ParameterizedTest
		void shouldValidateInvalidStreet(OrderType type) {
			OrderAddress address = constructValidAddressWithoutContact();
			address.setStreet("");

			ValidationResultDto validationResultDto = validator.validateToResultAddressBasedOnOrderType(address, type);

			assertFalse(validationResultDto.getValid());
			assertEquals("street", validationResultDto.getResults().get(0).getField());
		}

		@EnumSource(OrderType.class)
		@ParameterizedTest
		void shouldValidateInvalidPostCodeEmpty(OrderType type) {
			OrderAddress address = constructValidAddressWithoutContact();
			address.setPostcode("");

			when(countryService.isPostCodeMandatory(eq("DE"))).thenReturn(true);
			ValidationResultDto validationResultDto = validator.validateToResultAddressBasedOnOrderType(address, type);

			assertFalse(validationResultDto.getValid());
			assertEquals("postCode", validationResultDto.getResults().get(0).getField());
		}

		@EnumSource(OrderType.class)
		@ParameterizedTest
		void shouldValidateInvalidPostCodeStructure(OrderType type) {
			OrderAddress address = constructValidAddressWithoutContact();
			address.setPostcode("DE34243");

			when(postalCodeService.validatePostCode(anyString(), anyString())).thenReturn(new PostcodeValidationDto().valid(false).examplePostcode("12345"));

			ValidationResultDto validationResultDto = validator.validateToResultAddressBasedOnOrderType(address, type);

			assertFalse(validationResultDto.getValid());
			assertEquals("postCode", validationResultDto.getResults().get(0).getField());
		}

		@EnumSource(OrderType.class)
		@ParameterizedTest
		void shouldValidateMissingPostCodeWhenRequired(OrderType type) {
			OrderAddress address = constructValidAddressWithoutContact();
			address.setPostcode(null);

			when(countryService.isPostCodeMandatory(eq("DE"))).thenReturn(true);

			ValidationResultDto validationResultDto = validator.validateToResultAddressBasedOnOrderType(address, type);

			assertFalse(validationResultDto.getValid());
			assertEquals("postCode", validationResultDto.getResults().get(0).getField());
		}

		@EnumSource(OrderType.class)
		@ParameterizedTest
		void shouldValidateInvalidCity(OrderType type) {
			OrderAddress address = constructValidAddressWithoutContact();
			address.setCity("");

			ValidationResultDto validationResultDto = validator.validateToResultAddressBasedOnOrderType(address, type);

			assertFalse(validationResultDto.getValid());
			assertEquals("city", validationResultDto.getResults().get(0).getField());
		}

		@EnumSource(OrderType.class)
		@ParameterizedTest
		void shouldValidateInvalidCountryCode(OrderType type) {
			OrderAddress address = constructValidAddressWithoutContact();
			address.setCountryCode("");

			ValidationResultDto validationResultDto = validator.validateToResultAddressBasedOnOrderType(address, type);

			assertFalse(validationResultDto.getValid());
			assertEquals("countryCode", validationResultDto.getResults().get(0).getField());
		}

		@EnumSource(OrderType.class)
		@ParameterizedTest
		void shouldValidateInvalidName(OrderType type) {
			OrderAddress address = constructValidAddressWithoutContact();
			address.setName("");

			ValidationResultDto validationResultDto = validator.validateToResultAddressBasedOnOrderType(address, type);

			assertFalse(validationResultDto.getValid());
			assertEquals("name", validationResultDto.getResults().get(0).getField());
		}

		@EnumSource(value = OrderType.class, names = { "AIREXPORTORDER", "AIRIMPORTORDER", "SEAEXPORTORDER", "SEAIMPORTORDER" })
		@ParameterizedTest
		void shouldValidateInvalidContact(OrderType type) {
			OrderAddress address = constructValidAddressWithContact();
			address.setOrderContact(null);

			ValidationResultDto validationResultDto = validator.validateToResultAddressBasedOnOrderType(address, type);

			assertFalse(validationResultDto.getValid());
			assertEquals("contact", validationResultDto.getResults().get(0).getField());
		}

		@EnumSource(value = OrderType.class, names = { "AIREXPORTORDER", "AIRIMPORTORDER", "SEAEXPORTORDER", "SEAIMPORTORDER" })
		@ParameterizedTest
		void shouldValidateInvalidContactName(OrderType type) {
			OrderAddress address = constructValidAddressWithContact();
			address.getOrderContact().setName("");

			ValidationResultDto validationResultDto = validator.validateToResultAddressBasedOnOrderType(address, type);

			assertFalse(validationResultDto.getValid());
			assertEquals("name", validationResultDto.getResults().get(0).getField());
		}

		@EnumSource(value = OrderType.class, names = { "AIREXPORTORDER", "AIRIMPORTORDER", "SEAEXPORTORDER", "SEAIMPORTORDER" })
		@ParameterizedTest
		void shouldValidateInvalidContactEmail(OrderType type) {
			OrderAddress address = constructValidAddressWithContact();
			address.getOrderContact().setEmail("");

			ValidationResultDto validationResultDto = validator.validateToResultAddressBasedOnOrderType(address, type);

			assertFalse(validationResultDto.getValid());
			assertEquals("email", validationResultDto.getResults().get(0).getField());
		}

		@EnumSource(OrderType.class)
		@ParameterizedTest
		void shouldNotSkipCityAndPostCodeValidationForNonPrincipalIrelandAddress(OrderType type) {
			OrderAddress address = constructValidAddressWithContact();
			address.setCustomerNumber(null);
			address.setCountryCode("IE");
			address.setPostcode(null);
			address.setCity(null);

			ValidationResultDto validationResultDto = validator.validateToResultAddressBasedOnOrderType(address, type);

			assertFalse(validationResultDto.getValid());
		}
	}

	private OrderAddress constructValidAddressWithoutContact() {
		OrderAddress address = new OrderAddress();
		address.setStreet("street");
		address.setPostcode("91757");
		address.setName("name");
		address.setCity("city");
		address.setCountryCode("DE");
		return address;
	}

	private OrderAddress constructValidAddressWithContact() {
		OrderAddress address = constructValidAddressWithoutContact();
		OrderContact contact = new OrderContact();
		contact.setName("name");
		contact.setEmail("email");
		address.setOrderContact(contact);
		return address;
	}

}
