package com.dachser.dfe.book.user.platform;

import com.dachser.dfe.platform.api.PlatformV4Api;
import com.dachser.dfe.platform.api.PlatformV5Api;
import com.dachser.dfe.platform.model.PlatformPreferencesV4;
import com.dachser.dfe.platform.model.PlatformUserV5;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PlatformApi {

	private final PlatformV5Api api;

	private final PlatformV4Api apiV4;

	PlatformApi(final PlatformV5Api api, final PlatformV4Api apiV4) {
		this.api = api;
		this.apiV4 = apiV4;
	}

	public PlatformUserV5 getPlatformUser() {
		return api.getUserProfileV5();
	}

	public PlatformPreferencesV4 getUserPreferencesV4() {
		return apiV4.getUserPreferencesV4();
	}
}
