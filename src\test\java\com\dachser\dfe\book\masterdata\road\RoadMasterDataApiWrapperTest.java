package com.dachser.dfe.book.masterdata.road;

import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.TestMockData;
import com.dachser.dfe.road.masterdata.api.AdressTypeDescriptionControllerApi;
import com.dachser.dfe.road.masterdata.api.BranchDataControllerApi;
import com.dachser.dfe.road.masterdata.api.CountryControllerApi;
import com.dachser.dfe.road.masterdata.api.CustomerOrderGroupControllerApi;
import com.dachser.dfe.road.masterdata.api.ForwardingDomainControllerApi;
import com.dachser.dfe.road.masterdata.api.PackagingNameServiceApi;
import com.dachser.dfe.road.masterdata.api.ProductGroupControllerApi;
import com.dachser.dfe.road.masterdata.api.ProductNameControllerApi;
import com.dachser.dfe.road.masterdata.api.ProductPilotControllerApi;
import com.dachser.dfe.road.masterdata.api.RelationLabelprintingControllerApi;
import com.dachser.dfe.road.masterdata.api.ShipperMasterdataControllerApi;
import com.dachser.dfe.road.masterdata.api.TrackablePackingAidControllerApi;
import com.dachser.dfe.road.masterdata.api.ValidateFixedDateControllerApi;
import com.dachser.dfe.road.masterdata.api.ValidateProductControllerApi;
import com.dachser.dfe.road.masterdata.api.ValidateTermControllerApi;
import com.dachser.dfe.road.masterdata.api.ValidateThirdCountryControllerApi;
import com.dachser.dfe.road.masterdata.model.RMDProductPilotRequestDTO;
import com.dachser.dfe.road.masterdata.model.RMDRelationLabelprintingDTO;
import com.dachser.dfe.road.masterdata.model.RMDValidateFixedDateDTO;
import com.dachser.dfe.road.masterdata.model.RMDValidateProductDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Locale;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RoadMasterDataApiWrapperTest {

	@Mock
	private PackagingNameServiceApi packagingNameServiceApi;

	@Mock
	private ProductNameControllerApi productNameControllerApi;

	@Mock
	private AdressTypeDescriptionControllerApi adressTypeDescriptionControllerApi;

	@Mock
	private CustomerOrderGroupControllerApi customerOrderGroupControllerApi;

	@Mock
	private ProductGroupControllerApi productGroupControllerApi;

	@Mock
	private ValidateProductControllerApi validateProductControllerApi;

	@Mock
	private ValidateTermControllerApi validateTermControllerApi;

	@Mock
	private ValidateFixedDateControllerApi validateFixedDateControllerApi;

	@Mock
	private ValidateThirdCountryControllerApi validateThirdCountryControllerApi;

	@Mock
	private BranchDataControllerApi branchDataControllerApi;

	@Mock
	private RelationLabelprintingControllerApi relationLabelprintingControllerApi;

	@Mock
	private TrackablePackingAidControllerApi trackablePackingAidControllerApi;

	@Mock
	private ProductPilotControllerApi productPilotControllerApi;

	@Mock
	private ShipperMasterdataControllerApi shipperMasterdataControllerApi;

	@Mock
	private ForwardingDomainControllerApi forwardingDomainControllerApi;

	@Mock
	private GeneralDataService generalDataService;

	@Mock
	private CountryControllerApi countryControllerApi;

	private RoadMasterDataApiWrapper roadMasterDataApiWrapper;

	@BeforeEach
	void setup() {
		roadMasterDataApiWrapper = new RoadMasterDataApiWrapper(productNameControllerApi, adressTypeDescriptionControllerApi,
				customerOrderGroupControllerApi, productGroupControllerApi, validateProductControllerApi, validateTermControllerApi, validateFixedDateControllerApi,
				validateThirdCountryControllerApi, branchDataControllerApi, relationLabelprintingControllerApi, trackablePackingAidControllerApi, productPilotControllerApi,
				shipperMasterdataControllerApi, forwardingDomainControllerApi, countryControllerApi, generalDataService);
	}

	@Test
	void shouldInvokeValidateFixDate() {
		// given
		// when
		final RMDValidateFixedDateDTO fixedDateDTO = new RMDValidateFixedDateDTO();
		roadMasterDataApiWrapper.validateFixedDate(fixedDateDTO);
		// then
		// verify that the method was called
		verify(validateFixedDateControllerApi).validateFixedDate1(fixedDateDTO);
	}

	@Test
	void shouldInvokeGetBranchData() {
		// given
		// when
		roadMasterDataApiWrapper.getBranchData(1, 2);
		// then
		// verify that the method was called
		verify(branchDataControllerApi).getBranchData(1, 2);
	}

	@Test
	void shouldInvokeGetProductName() {
		// given
		// when
		when(generalDataService.getMasterdataLanguageForLocale(Locale.GERMAN)).thenReturn(" ");
		roadMasterDataApiWrapper.getProductName(Locale.GERMAN, Division.T.name(), TestMockData.BUSINESS_DOMAIN);
		// then
		// verify that the method was called
		verify(productNameControllerApi).getProductName(" ", "T", TestMockData.BUSINESS_DOMAIN);
	}

	@Test
	void shouldInvokeGetAddressTypeDescription() {
		// given
		// when
		when(generalDataService.getMasterdataLanguageForLocale(Locale.GERMAN)).thenReturn(" ");
		roadMasterDataApiWrapper.getAddressTypeDescription(Locale.GERMAN, TestMockData.BUSINESS_DOMAIN);
		// then
		// verify that the method was called
		verify(adressTypeDescriptionControllerApi).getAdressTypeDescription(" ", TestMockData.BUSINESS_DOMAIN);
	}

	@Test
	void shouldInvokeValidateProduct() {
		// given
		// when
		final RMDValidateProductDTO validateProductDTO = new RMDValidateProductDTO();
		roadMasterDataApiWrapper.validateProduct(validateProductDTO);
		// then
		// verify that the method was called
		verify(validateProductControllerApi).validateFixedDate(validateProductDTO);
	}

	@Test
	void shouldInvokeGetProductGroup() {
		// given
		// when
		roadMasterDataApiWrapper.getProductGroup(1, 2L, TestMockData.BUSINESS_DOMAIN);
		// then
		// verify that the method was called
		verify(productGroupControllerApi).getProductGroup(1, 2L, TestMockData.BUSINESS_DOMAIN);
	}

	@Test
	void shouldInvokeGetCustomerGroup() {
		// given
		// when
		roadMasterDataApiWrapper.getCustomerGroup(TestMockData.BUSINESS_DOMAIN, 2L, 1, "9", "1", "1");
		// then
		// verify that the method was called
		verify(customerOrderGroupControllerApi).getCustomerGroup1(TestMockData.BUSINESS_DOMAIN, 2L, 1, "9", "1", "1");
	}

	@Test
	void shouldInvokeValidateFreightTerm() {
		// given
		// when
		roadMasterDataApiWrapper.validateFreightTerm(TestMockData.BUSINESS_DOMAIN, "12", "1");
		// then
		// verify that the method was called
		verify(validateTermControllerApi).validateFixedDate2(TestMockData.BUSINESS_DOMAIN, "12", "1");
	}

	@Test
	void shouldInvokeValidateThirdCountry() {
		// given
		// when
		final LocalDate now = LocalDate.now();
		roadMasterDataApiWrapper.isThirdCountry(TestMockData.BUSINESS_DOMAIN, "DE", "18956", "AT", "15123", now);
		// then
		// verify that the method was called
		verify(validateThirdCountryControllerApi).isThirdCountry(TestMockData.BUSINESS_DOMAIN, "DE", "18956", "AT", "15123", now);
	}

	@Test
	void shouldInvokeCustomerOrderGroupControllerApi() {
		// given
		// when
		roadMasterDataApiWrapper.getCustomerGroup(TestMockData.BUSINESS_DOMAIN, 2L, 1, "9", "1", "1");
		// then
		// verify that the method was called
		verify(customerOrderGroupControllerApi).getCustomerGroup1(TestMockData.BUSINESS_DOMAIN, 2L, 1, "9", "1", "1");
	}

	@Test
	void shouldInvokeValidateCustomerOrderGroup() {
		// given
		// when
		roadMasterDataApiWrapper.validateCustomerOrderGroup(TestMockData.BUSINESS_DOMAIN, 2L, 1, "9", "1", "key", "orderGroup");
		// then
		// verify that the method was called
		verify(customerOrderGroupControllerApi).validateCustomerOrderGroup(TestMockData.BUSINESS_DOMAIN, 2L, 1, "9", "1", "key", "orderGroup");
	}

	@Test
	void shouldInvokeRetrieveRelationForLabels() {
		// given
		// when
		final RMDRelationLabelprintingDTO relation = new RMDRelationLabelprintingDTO();
		roadMasterDataApiWrapper.retrieveRelation(relation);
		// then
		// verify that the method was called
		verify(relationLabelprintingControllerApi).retrieveRelation(relation);
	}

	@Test
	void shouldInvokeRetrieveProducts() {
		// given
		// when
		final RMDProductPilotRequestDTO relation = new RMDProductPilotRequestDTO();
		roadMasterDataApiWrapper.retrieveProducts(relation);
		// then
		// verify that the method was called
		verify(productPilotControllerApi).retrieveProducts(relation);
	}

	@Test
	void shouldInvokeGetShipperMasterdata() {
		// given
		// when
		roadMasterDataApiWrapper.getShipperMasterdata(1L, TestMockData.BUSINESS_DOMAIN);
		// then
		// verify that the method was called
		verify(shipperMasterdataControllerApi).getShipperMasterdata(1L, TestMockData.BUSINESS_DOMAIN);
	}

}