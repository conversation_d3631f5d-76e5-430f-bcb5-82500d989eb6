package com.dachser.dfe.book.transferlist.mapper;

import com.dachser.dfe.book.model.FreightTermDto;
import com.dachser.dfe.book.model.TransferListAddressDto;
import com.dachser.dfe.book.model.TransferListPdfItemDto;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.generator.OrderGenerator;
import com.dachser.dfe.book.order.mapper.FreightTermCodeMapper;
import com.dachser.dfe.book.transferlist.entity.VTransferList;
import com.dachser.dfe.book.transferlist.model.TransferListAddressType;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Locale;
import java.util.stream.Stream;

import static com.dachser.dfe.book.TestUtil.generateVTransferList;
import static com.dachser.dfe.book.transferlist.mapper.TransferListPdfMapper.TOUR_IDENTIFIER;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Fail.fail;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TransferListPdfMapperTest {
	private TransferListPdfMapper sut;

	@Mock
	private FreightTermCodeMapper freightTermMapper;

	@BeforeAll
	void setUp() {
		sut = new TransferListPdfMapperImpl(freightTermMapper);
	}

	@Test
	void shouldMapFreightTerm() {
		FreightTermDto freightTerm = generateJsonFreightTerm();

		FreightTermDto mappedFreightTerm = sut.mapFreightTerm(freightTerm);
		assertThat(mappedFreightTerm.getDachserTermKey()).isEqualTo(freightTerm.getDachserTermKey());
		assertThat(mappedFreightTerm.getIncoTermKey()).isEqualTo(freightTerm.getIncoTermKey());
		assertThat(mappedFreightTerm.getHeadline()).isEqualTo(freightTerm.getHeadline());
	}

	@ParameterizedTest
	@EnumSource(value = TransferListAddressType.class)
	void shouldMapFromAddress(TransferListAddressType addressType) {
		OrderFurtherAddress lpAddress = OrderGenerator.generateOrderFurtherAddress("_lp", "DE", "LP");
		OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", "DE", false, null);
		VTransferList vTransferList = generateVTransferList(addressType, null, principalAddress, lpAddress);
		TransferListAddressDto fromAddress = sut.mapFromAddress(vTransferList);
		switch (addressType) {
		case LOADING_POINT -> {
			assertThat(fromAddress.getName()).isEqualTo(lpAddress.getName());
			assertThat(fromAddress.getName2()).isEqualTo(lpAddress.getName2());
			assertThat(fromAddress.getName3()).isEqualTo(lpAddress.getName3());
			assertThat(fromAddress.getStreet()).isEqualTo(lpAddress.getStreet());
			assertThat(fromAddress.getStreet2()).isEqualTo(lpAddress.getStreet2());
			assertThat(fromAddress.getPostcode()).isEqualTo(lpAddress.getPostcode());
			assertThat(fromAddress.getCity()).isEqualTo(lpAddress.getCity());
			assertThat(fromAddress.getCountryCode()).isEqualTo(lpAddress.getCountryCode());
		}
		case PRINCIPAL -> {
			assertThat(fromAddress.getName()).isEqualTo(principalAddress.getName());
			assertThat(fromAddress.getName2()).isEqualTo(principalAddress.getName2());
			assertThat(fromAddress.getName3()).isEqualTo(principalAddress.getName3());
			assertThat(fromAddress.getStreet()).isEqualTo(principalAddress.getStreet());
			assertThat(fromAddress.getStreet2()).isEqualTo(principalAddress.getStreet2());
			assertThat(fromAddress.getPostcode()).isEqualTo(principalAddress.getPostcode());
			assertThat(fromAddress.getCity()).isEqualTo(principalAddress.getCity());
			assertThat(fromAddress.getCountryCode()).isEqualTo(principalAddress.getCountryCode());
		}
		}
	}

	@ParameterizedTest
	@EnumSource(value = TransferListAddressType.class)
	void shouldMapDocumentName(TransferListAddressType addressType) {
		OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", "DE", false, null);
		principalAddress.setOrderAddressId(12345L);
		OrderFurtherAddress lpAddress = OrderGenerator.generateOrderFurtherAddress("_lo", "DE", "LP");
		lpAddress.setOrderFurtherAddressId(67890L);
		VTransferList vTransferList = generateVTransferList(addressType, null, principalAddress, lpAddress);
		LocalDate documentDate = LocalDate.of(2024, 12, 31);
		String expectedDocumentDate = "20241231";
		String documentName = sut.mapDocumentName(vTransferList, documentDate);
		String expectedDocumentName = switch (addressType) {
			case LOADING_POINT -> "transfer-list-" + expectedDocumentDate + "-l" + lpAddress.getOrderFurtherAddressId() + "-" + TOUR_IDENTIFIER + ".pdf";
			case PRINCIPAL -> "transfer-list-" + expectedDocumentDate + "-p" + principalAddress.getOrderAddressId() + "-" + TOUR_IDENTIFIER + ".pdf";
		};
		assertThat(documentName).isEqualTo(expectedDocumentName);
	}

	@ParameterizedTest
	@MethodSource("provideDocumentMapperInvalidArguments")
	void shouldMapDocumentNameAsNullIfInputIsNull(VTransferList transferList, LocalDate date) {
		String documentName = sut.mapDocumentName(transferList, date);
		assertThat(documentName).isNull();
	}

	private static Stream<Arguments> provideDocumentMapperInvalidArguments() {
		return Stream.of(Arguments.of(null, null), Arguments.of(new VTransferList(), null), Arguments.of(null, LocalDate.of(2024, 7, 4)));
	}

	@Test
	void shouldMapTransferListPdfItem() {
		final String dateFormat = "yyyy-MM-dd";
		when(freightTermMapper.map(anyString())).thenReturn(generateJsonFreightTerm());
		sut = new TransferListPdfMapperImpl(freightTermMapper);
		VTransferList vTransferList = generateVTransferList(TransferListAddressType.PRINCIPAL, null, null, null);

		TransferListPdfItemDto mappedTransferList = sut.mapTransferListPdfItem(vTransferList, dateFormat, Locale.US);
		assertThat(mappedTransferList.getProduct().getCode()).isEqualTo(vTransferList.getProduct());
		assertThat(mappedTransferList.getProduct().getDescription()).isEqualTo(vTransferList.getProductName());
		DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(dateFormat);
		try {
			dateFormatter.parse(mappedTransferList.getDeliveryDate());
		} catch (DateTimeParseException e) {
			fail("Date is not in the correct format");
		}
	}

	@ParameterizedTest
	@ValueSource(strings = { "US", "GERMANY" })
	void testMapOffsetDateTimeDateToString(String localeString) {
		final String dateFormat = "yyyy-MM-dd";
		OffsetDateTime offsetDateTime = OffsetDateTime.now();
		Locale locale = Locale.forLanguageTag(localeString);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat, locale);
		String expected = offsetDateTime.format(formatter);

		String result = sut.formattedDateFromOffsetDateTime(offsetDateTime, dateFormat, locale);

		assertThat(result).isEqualTo(expected);
	}

	@ParameterizedTest
	@ValueSource(strings = { "US", "GERMANY" })
	void testMapLocalDateToDateString(String localeString) {
		final String dateFormat = "yyyy-MM-dd";
		LocalDate localDate = LocalDate.now();
		Locale locale = Locale.forLanguageTag(localeString);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat, locale);
		String expected = localDate.format(formatter);

		String result = sut.formattedDateFromLocalDate(localDate, dateFormat, locale);

		assertThat(result).isEqualTo(expected);
	}

	@ParameterizedTest
	@ValueSource(strings = { "US", "GERMANY", "ENGLISH" })
	void testMapOffsetDateTimeToDateTimeString(String localeString) {
		final String dateFormat = "yyyy-MM-dd, hh:mm a";
		OffsetDateTime offsetDateTime = OffsetDateTime.now();
		Locale locale = Locale.forLanguageTag(localeString);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat, locale);
		String expected = offsetDateTime.format(formatter);

		String result = sut.formattedDateTimeFromOffsetDateTime(offsetDateTime, dateFormat, locale);

		assertThat(result).isEqualTo(expected);
	}

	@Test
	void testMapDateTimeThrowsExceptionForInvalidDateFormat() {
		final String invalidDateFormat = "yyyy-MM-WWWoo";
		OffsetDateTime offsetDateTime = OffsetDateTime.now();
		Locale locale = Locale.forLanguageTag("US");

		assertThrows(IllegalArgumentException.class, () -> {
			sut.formattedDateTimeFromOffsetDateTime(offsetDateTime, invalidDateFormat, locale);
		});
	}

	@Test
	void testMapDateThrowsExceptionForInvalidDateFormat() {
		final String invalidDateFormat = "yyyy-MM-WWWoo";
		LocalDate date = LocalDate.now();
		Locale locale = Locale.forLanguageTag("US");

		assertThrows(IllegalArgumentException.class, () -> {
			sut.formattedDateFromLocalDate(date, invalidDateFormat, locale);
		});
	}

	@Test
	void testMapOffsetDateTimeDateIsNull() {
		final String dateFormat = "yyyy-MM-dd";

		String result = sut.formattedDateFromOffsetDateTime(null, dateFormat, Locale.ENGLISH);

		assertThat(result).isNull();
	}

	private FreightTermDto generateJsonFreightTerm() {
		FreightTermDto freightTerm = new FreightTermDto();
		freightTerm.setDachserTermKey("X1X");
		freightTerm.setIncoTermKey("S2S");
		freightTerm.setHeadline("headline");
		return freightTerm;
	}

	private String todayDate() {
		LocalDate date = LocalDate.now();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
		return date.format(formatter);
	}

}