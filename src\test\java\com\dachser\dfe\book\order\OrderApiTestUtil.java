package com.dachser.dfe.book.order;

import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.document.DocumentsTestUtil;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.model.AirExportOrderDto;
import com.dachser.dfe.book.model.AirImportOrderDto;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.FullContainerLoadDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderContactDataDto;
import com.dachser.dfe.book.model.OrderProcessResultDto;
import com.dachser.dfe.book.model.OrderSaveActionDto;
import com.dachser.dfe.book.model.OrderStatusDto;
import com.dachser.dfe.book.model.OrderValidationResultDto;
import com.dachser.dfe.book.model.RoadCollectionOrderDto;
import com.dachser.dfe.book.model.RoadForwardingOrderDto;
import com.dachser.dfe.book.model.SeaExportOrderDto;
import com.dachser.dfe.book.model.SeaImportOrderDto;
import com.dachser.dfe.book.user.UserServiceMock;
import com.fasterxml.jackson.core.type.TypeReference;
import io.restassured.http.Header;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import io.restassured.module.mockmvc.specification.MockMvcRequestSpecification;
import io.restassured.response.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import java.io.IOException;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

import static com.dachser.dfe.book.MockConstants.ACTIVE_COMPANY_ACCESS_TOKEN;
import static io.restassured.module.mockmvc.RestAssuredMockMvc.given;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;

public class OrderApiTestUtil implements ResourceLoadingTest {

	public static final String CUSTOMER_NUMBER_ROAD = "00000001";

	public static final String CUSTOMER_NUMBER_ASL = UserServiceMock.VALID_CUST_NO_ASL;

	public static final String CUSTOMER_NUMBER_ASL_AIR_PRODUCTS = "00000300";

	public static final int STATUS_CODE_VALIDATION_FAILS = HttpStatus.BAD_REQUEST.value();

	public static final int HTTP_STATUS_SUBMISSION_FAILED = HttpStatus.BAD_GATEWAY.value();

	public static final String MISSING_INPUT = "Missing or incorrect input";

	public static ResourceLoadingTest resourceLoader = new ResourceLoadingTest() {
	};

	public static void syncIds(RoadForwardingOrderDto order, RoadForwardingOrderDto forwardingOrder) {
		order.setOrderId(forwardingOrder.getOrderId());
		order.getShipperAddress().setId(forwardingOrder.getShipperAddress().getId());
		order.getShipperAddress().getContact().setId(forwardingOrder.getShipperAddress().getContact().getId());
		order.getConsigneeAddress().setId(forwardingOrder.getConsigneeAddress().getId());
		IntStream orderlineIndexStream = IntStream.range(0, forwardingOrder.getOrderLineItems().size());
		orderlineIndexStream.forEach(number -> order.getOrderLineItems().get(number).setId(forwardingOrder.getOrderLineItems().get(number).getId()));
		IntStream furtherAddressIndexStream = IntStream.range(0, forwardingOrder.getFurtherAddresses().size());
		furtherAddressIndexStream.forEach(number -> order.getFurtherAddresses().get(number).setId(forwardingOrder.getFurtherAddresses().get(number).getId()));
		IntStream textsIndexStream = IntStream.range(0, forwardingOrder.getTexts().size());
		textsIndexStream.forEach(number -> order.getTexts().get(number).setId(forwardingOrder.getTexts().get(number).getId()));
		IntStream referencesAddressIndexStream = IntStream.range(0, forwardingOrder.getReferences().size());
		referencesAddressIndexStream.forEach(number -> order.getReferences().get(number).setId(forwardingOrder.getReferences().get(number).getId()));

	}

	public static OrderContactDataDto createContactData(String nameSuffix) {
		return new OrderContactDataDto().email("email").mobile("mobile").telephone("telephone").name("name" + nameSuffix);
	}

	public static <T> T parseResponse(MockMvcResponse response, Class<T> returnType) {
		final T as = response.getBody().as(returnType);
		assertNotNull(as);
		return as;
	}

	public static <T> T parseOrderProcessResult(MockMvcResponse response, Class<T> returnType) {
		final OrderProcessResultDto as = response.getBody().as(OrderProcessResultDto.class);
		assertTrue(as.getOrder().getClass().isAssignableFrom(returnType));
		return (T) as.getOrder();
	}

	public static OrderValidationResultDto parseValidationResponse(MockMvcResponse response) {
		final OrderProcessResultDto as = response.getBody().as(OrderProcessResultDto.class);
		assertNotNull(as);
		return as.getValidationResult();
	}

	public static RoadForwardingOrderDto createValidatedForwardingOrder() throws IOException {
		return createNewForwardingOrder(OrderSaveActionDto.VALIDATE);
	}

	public static RoadForwardingOrderDto createNewForwardingOrder(OrderSaveActionDto saveAction) throws IOException {
		final RoadForwardingOrderDto order = resourceLoader.loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
		});

		order.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
		order.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
		order.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));

		order.setCustomerNumber(CUSTOMER_NUMBER_ROAD);
		return createNewForwardingOrder(order, saveAction);
	}

	public static RoadCollectionOrderDto createValidatedCollectionOrder() throws IOException {
		final RoadCollectionOrderDto order = resourceLoader.loadResourceAndConvert("orders/new-collection-order-complete-valid.json", new TypeReference<>() {
		});
		order.setCustomerNumber(CUSTOMER_NUMBER_ROAD);
		return createNewCollectionOrder(order, OrderSaveActionDto.VALIDATE);
	}

	public static RoadForwardingOrderDto createNewForwardingOrder(RoadForwardingOrderDto forwardingOrder, OrderSaveActionDto orderSaveAction) throws IOException {
		return createNewForwardingOrder(forwardingOrder, UserServiceMock.VALID_CUST_NO_ROAD, orderSaveAction);
	}

	public static RoadForwardingOrderDto createNewDraftForwardingOrder(RoadForwardingOrderDto forwardingOrder) throws IOException {
		return createNewForwardingOrder(forwardingOrder, UserServiceMock.VALID_CUST_NO_ROAD, null);
	}

	public static RoadForwardingOrderDto createNewForwardingOrder(RoadForwardingOrderDto forwardingOrder, String customerNumber, OrderSaveActionDto orderSaveAction)
			throws IOException {
		return createNewOrder(forwardingOrder, "orders/new-forwarding-order.json", customerNumber, orderSaveAction);
	}

	public static RoadCollectionOrderDto createNewCollectionOrder(RoadCollectionOrderDto order, OrderSaveActionDto orderSaveAction) throws IOException {
		return createNewOrder(order, "orders/new-collection-order.json", UserServiceMock.VALID_CUST_NO_ROAD, orderSaveAction);
	}

	public static AirExportOrderDto createValidDraftAirExportOrder(DocumentService documentService) throws IOException {
		return createAirExportOrder(documentService, null);
	}

	public static AirExportOrderDto createValidatedAirExportOrder(DocumentService documentService) throws IOException {
		return createAirExportOrder(documentService, OrderSaveActionDto.VALIDATE);
	}

	private static AirExportOrderDto createAirExportOrder(DocumentService documentService, OrderSaveActionDto action) throws IOException {
		final AirExportOrderDto order = resourceLoader.loadResourceAndConvert("orders/new-air-export-order.json", new TypeReference<>() {
		});
		order.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
		order.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
		order.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
		order.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));
		return createNewOrder(order, null, CUSTOMER_NUMBER_ASL, action);
	}

	public static AirImportOrderDto createValidDraftAirImportOrder(DocumentService documentService) throws IOException {
		return createAirImportOrder(documentService, null);
	}

	public static AirImportOrderDto createValidatedAirImportOrder(DocumentService documentService) throws IOException {
		return createAirImportOrder(documentService, OrderSaveActionDto.VALIDATE);
	}

	private static AirImportOrderDto createAirImportOrder(DocumentService documentService, OrderSaveActionDto action) throws IOException {
		final AirImportOrderDto order = resourceLoader.loadResourceAndConvert("orders/new-air-import-order.json", new TypeReference<>() {
		});
		order.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService), DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
		order.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
		order.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
		order.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));
		return createNewOrder(order, null, CUSTOMER_NUMBER_ASL, action);
	}

	public static SeaExportOrderDto createValidDraftSeaExportOrder() throws IOException {
		return createSeaExportOrder(null);
	}

	public static SeaExportOrderDto createValidDraftSeaExportOrderFcl() throws IOException {
		return createSeaExportOrderFcl(null);
	}

	public static SeaImportOrderDto createValidDraftSeaImportOrder() throws IOException {
		return createSeaImportOrder(null);
	}

	public static SeaExportOrderDto createValidatedSeaExportOrder() throws IOException {
		return createSeaExportOrder(OrderSaveActionDto.VALIDATE);
	}

	private static SeaExportOrderDto createSeaExportOrderFcl(OrderSaveActionDto action) throws IOException {
		final SeaExportOrderDto order = resourceLoader.loadResourceAndConvert("orders/new-sea-order.json", new TypeReference<>() {
		});
		order.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
		order.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
		order.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));

		order.setFullContainerLoad(true);
		FullContainerLoadDto fullContainerLoad = new FullContainerLoadDto().containerNumber("ABCU1234567").containerType(new OptionDto().code("BO").description("Box"))
				.sortingPosition(1);

		fullContainerLoad.setLines(order.getOrderLineItems());
		order.setOrderLineItems(new ArrayList<>());

		order.addFullContainerLoadsItem(fullContainerLoad);
		return createNewOrder(order, null, CUSTOMER_NUMBER_ASL, action);
	}

	private static SeaExportOrderDto createSeaExportOrder(OrderSaveActionDto action) throws IOException {
		final SeaExportOrderDto order = resourceLoader.loadResourceAndConvert("orders/new-sea-order.json", new TypeReference<>() {
		});
		order.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
		order.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
		order.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));
		return createNewOrder(order, null, CUSTOMER_NUMBER_ASL, action);
	}

	private static SeaImportOrderDto createSeaImportOrder(OrderSaveActionDto action) throws IOException {
		final SeaImportOrderDto order = resourceLoader.loadResourceAndConvert("orders/new-sea-import-order.json", new TypeReference<>() {
		});
		order.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
		order.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
		order.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));
		return createNewOrder(order, null, CUSTOMER_NUMBER_ASL, action);
	}

	public static <T extends BasicOrderDto> T createNewOrder(T order, String fallbackOrderResource, String customerNumber, OrderSaveActionDto orderSaveAction)
			throws IOException {
		if (order == null) {
			order = resourceLoader.loadResourceAndConvert(fallbackOrderResource, new TypeReference<>() {
			});
		}
		order.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
		order.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
		order.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));
		order.setCustomerNumber(customerNumber);
		if (StringUtils.isEmpty(order.getOrderNumber())) {
			order.setOrderNumber("123456");
		}
		MockMvcRequestSpecification request = prepareWriteRequest().body(order);

		String url = "/orders/";
		if (orderSaveAction != null) {
			url += orderSaveAction.getValue();
		}
		MockMvcResponse response = request.put(url);
		assertEquals(200, response.statusCode());
		return (T) parseOrderProcessResult(response, order.getClass());
	}

	public static <T extends BasicOrderDto> T updateOrder(BasicOrderDto order, OrderSaveActionDto orderSaveAction) {
		MockMvcRequestSpecification request = prepareWriteRequest().body(order);

		MockMvcResponse response = request.put("/orders/" + orderSaveAction.getValue());
		assertEquals(200, response.statusCode());
		return (T) parseOrderProcessResult(response, order.getClass());
	}

	public static OrderProcessResultDto updateOrderToError(BasicOrderDto order, OrderSaveActionDto orderSaveAction) {
		MockMvcRequestSpecification request = prepareWriteRequest().body(order);

		MockMvcResponse response = request.put("/orders/" + orderSaveAction.getValue());
		assertEquals(400, response.statusCode());
		return response.as(OrderProcessResultDto.class);
	}

	public static OrderStatusDto getOrderStatusFromOrderProcess(OrderProcessResultDto submitResult) {
		if (submitResult == null) {
			return null;
		}
		return ((BasicOrderDto) submitResult.getOrder()).getStatus();
	}

	public static BasicOrderDto getOrder(Long orderId) {
		MockMvcResponse response = prepareReadRequest().get("/orders/" + orderId);
		assertEquals(200, response.statusCode());
		return parseResponse(response, BasicOrderDto.class);
	}

	public static <T> T getOrder(Long orderId, Class<T> returnType) {
		MockMvcResponse response = prepareReadRequest().get("/orders/" + orderId);
		assertEquals(200, response.statusCode());
		return parseResponse(response, returnType);
	}

	public static void assertOrderStatus(Long orderId, OrderStatusDto expectedStatus) {
		final BasicOrderDto order = getOrder(orderId);
		assertEquals(expectedStatus, order.getStatus());
	}

	public static void assertErrorIdInProcessResult(ResponseBody responseBody, BookErrorId errorId) {
		OrderProcessResultDto orderProcessResult = responseBody.as(OrderProcessResultDto.class);
		assertEquals(errorId.getErrorId(), orderProcessResult.getError().getErrorId());
	}

	private static MockMvcRequestSpecification prepareReadRequest() {
		return given().header(new Header(HttpHeaders.AUTHORIZATION, "Bearer " + ACTIVE_COMPANY_ACCESS_TOKEN)).accept(MediaType.APPLICATION_JSON);
	}

	public static MockMvcRequestSpecification prepareWriteRequest() {
		return given().postProcessors(csrf()).header("Content-Type", "application/json").header(new Header(HttpHeaders.AUTHORIZATION, "Bearer " + ACTIVE_COMPANY_ACCESS_TOKEN));
	}

}
