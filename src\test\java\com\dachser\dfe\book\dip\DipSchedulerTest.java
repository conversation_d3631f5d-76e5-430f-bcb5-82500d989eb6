package com.dachser.dfe.book.dip;

import com.dachser.dfe.book.dip.repository.OrderXml;
import com.dachser.dfe.book.dip.repository.OrderXmlRepository;
import com.dachser.dfe.book.dip.xml.XmlFileStorageProvider;
import com.dachser.dfe.book.dip.xml.XmlPublishStatus;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;
import java.util.logging.LogManager;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DipSchedulerTest {

	@Mock
	private XmlFileStorageProvider xmlFileStorage;

	@Mock
	private OrderXmlRepository orderXmlRepository;

	@Mock
	private DipService dipService;

	@InjectMocks
	private DipScheduler dipScheduler;

	private OrderXml orderXml;

	@BeforeEach
	void setUp() {
		orderXml = new OrderXml();
		orderXml.setCustomerNumber("12345678");
		orderXml.setShipmentNumber(67890L);
		orderXml.setOrderType(DipOrderType.FORWARDING);
		orderXml.setStatus(XmlPublishStatus.READY);
		orderXml.setPublishAt(Instant.now());
		orderXml.setRetryCount(0);
	}

	@AfterEach
	void reset() throws Exception {
		LogManager.getLogManager().readConfiguration();
	}

	@Nested
	class PublishForwardingOrderXmls {

		@Test
		void shouldHandleNullXmlContent() {
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml));
			when(xmlFileStorage.getXml(anyLong())).thenReturn(null);

			dipScheduler.publishForwardingOrderXmls();

			verify(dipService, never()).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldHandleEmptyXmlContent() {
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml));
			when(xmlFileStorage.getXml(anyLong())).thenReturn("");

			dipScheduler.publishForwardingOrderXmls();

			verify(dipService, never()).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldHandleMultipleOrders() {
			OrderXml anotherOrderXml = new OrderXml();
			anotherOrderXml.setCustomerNumber("87654321");
			anotherOrderXml.setShipmentNumber(12345L);
			anotherOrderXml.setOrderType(DipOrderType.FORWARDING);
			anotherOrderXml.setStatus(XmlPublishStatus.READY);
			anotherOrderXml.setPublishAt(Instant.now());
			anotherOrderXml.setRetryCount(0);

			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml, anotherOrderXml));
			when(xmlFileStorage.getXml(anyLong())).thenReturn("<order>content</order>");
			when(dipService.tryToPublish(anyString(), any(OrderXml.class))).thenReturn(true);

			dipScheduler.publishForwardingOrderXmls();

			verify(dipService, times(2)).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldIgnoreOrdersWithDifferentStatus() {
			orderXml.setStatus(XmlPublishStatus.DONE);
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml));

			dipScheduler.publishForwardingOrderXmls();

			verify(dipService, never()).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldIgnoreOrdersWithDifferentOrderType() {
			orderXml.setOrderType(DipOrderType.AIRSEA);
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml));

			dipScheduler.publishForwardingOrderXmls();

			verify(dipService, never()).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldPublishForwardingOrderXmlsSuccessfully() {
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml));
			when(xmlFileStorage.getXml(anyLong())).thenReturn("<order>content</order>");
			when(dipService.tryToPublish(anyString(), any(OrderXml.class))).thenReturn(true);

			dipScheduler.publishForwardingOrderXmls();

			verify(dipService, times(1)).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldNotPublishForwardingOrderXmlsIfPublishAtIsInFuture() {
			orderXml.setPublishAt(Instant.now().plusSeconds(60 * 60 * 24)); // 1 day in the future
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml));

			dipScheduler.publishForwardingOrderXmls();

			verify(dipService, never()).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldPublishForwardingOrderXmlsIfPublishAtIsExactlyEndOfDay() {
			orderXml.setPublishAt(LocalDate.now().atStartOfDay().toInstant(ZoneOffset.UTC).plusSeconds(23 * 60 * 60 + 59 * 60 + 59L));
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml));
			when(xmlFileStorage.getXml(anyLong())).thenReturn("<order>content</order>");
			when(dipService.tryToPublish(anyString(), any(OrderXml.class))).thenReturn(true);

			dipScheduler.publishForwardingOrderXmls();

			verify(dipService, times(1)).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldHandleExceptionDuringPublishing() {
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml));
			when(xmlFileStorage.getXml(anyLong())).thenReturn("<order>content</order>");
			doThrow(new RuntimeException("Test Exception")).when(dipService).tryToPublish(anyString(), any(OrderXml.class));

			dipScheduler.publishForwardingOrderXmls();

			verify(dipService, times(1)).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldDoNothingWhenNoOrdersFound() {
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(Collections.emptyList());

			dipScheduler.publishForwardingOrderXmls();

			verify(dipService, never()).tryToPublish(anyString(), any(OrderXml.class));
		}

	}

	@Nested
	class PublishOrderXmls {

		@Test
		void shouldHandleNullXmlContent() {
			orderXml.setOrderType(DipOrderType.COLLECTION);
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml));
			when(xmlFileStorage.getXml(anyLong())).thenReturn(null);

			dipScheduler.publishOrderXmls();

			verify(dipService, never()).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldHandleEmptyXmlContent() {
			orderXml.setOrderType(DipOrderType.COLLECTION);
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml));
			when(xmlFileStorage.getXml(anyLong())).thenReturn("");

			dipScheduler.publishOrderXmls();

			verify(dipService, never()).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldHandleMultipleOrders() {
			orderXml.setOrderType(DipOrderType.COLLECTION);
			OrderXml anotherOrderXml = new OrderXml();
			anotherOrderXml.setCustomerNumber("87654321");
			anotherOrderXml.setShipmentNumber(12345L);
			anotherOrderXml.setOrderType(DipOrderType.COLLECTION);
			anotherOrderXml.setStatus(XmlPublishStatus.READY);
			anotherOrderXml.setPublishAt(Instant.now());
			anotherOrderXml.setRetryCount(0);

			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml, anotherOrderXml));
			when(xmlFileStorage.getXml(anyLong())).thenReturn("<order>content</order>");
			when(dipService.tryToPublish(anyString(), any(OrderXml.class))).thenReturn(true);

			dipScheduler.publishOrderXmls();

			verify(dipService, times(2)).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldPublishOrderXmlsSuccessfully() {
			orderXml.setOrderType(DipOrderType.COLLECTION);
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml));
			when(xmlFileStorage.getXml(anyLong())).thenReturn("<order>content</order>");
			when(dipService.tryToPublish(anyString(), any(OrderXml.class))).thenReturn(true);

			dipScheduler.publishOrderXmls();

			verify(dipService, times(1)).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldHandleExceptionDuringPublishing() {
			orderXml.setOrderType(DipOrderType.COLLECTION);
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml));
			when(xmlFileStorage.getXml(anyLong())).thenReturn("<order>content</order>");
			doThrow(new RuntimeException("Test Exception")).when(dipService).tryToPublish(anyString(), any(OrderXml.class));

			dipScheduler.publishOrderXmls();

			verify(dipService, times(1)).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldDoNothingWhenNoOrdersFound() {
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(Collections.emptyList());

			dipScheduler.publishOrderXmls();

			verify(dipService, never()).tryToPublish(anyString(), any(OrderXml.class));
		}

		@Test
		void shouldIgnoreOrdersWithDifferentStatus() {
			orderXml.setStatus(XmlPublishStatus.DONE);
			when(orderXmlRepository.findByStatus(XmlPublishStatus.READY)).thenReturn(List.of(orderXml));

			dipScheduler.publishForwardingOrderXmls();

			verify(dipService, never()).tryToPublish(anyString(), any(OrderXml.class));
		}

	}

	@Nested
	class CleanUpPublishedOrderXmls {

		@Test
		void shouldCleanUpPublishedOrderXmlsSuccessfully() {
			doNothing().when(xmlFileStorage).cleanUpXmlFiles(anyInt());

			dipScheduler.cleanUpPublishedOrderXmls();

			verify(xmlFileStorage, times(1)).cleanUpXmlFiles(DipScheduler.PUBLISHED_ORDERS_CLEANUP_THRESHOLD_DAYS);
		}

		@Test
		void shouldHandleExceptionDuringCleanup() {
			doThrow(new RuntimeException("Cleanup Exception")).when(xmlFileStorage).cleanUpXmlFiles(anyInt());

			dipScheduler.cleanUpPublishedOrderXmls();

			verify(xmlFileStorage, times(1)).cleanUpXmlFiles(DipScheduler.PUBLISHED_ORDERS_CLEANUP_THRESHOLD_DAYS);
		}
	}
}