package com.dachser.dfe.book.document;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DocumentConst {

	public static final String TYPE_COMMERCIAL_INVOICE = "CDINV";

	public static final String TYPE_PROFORMA_INVOICE = "CDPIN";

	public static final String TYPE_CUSTOMS_OTHERS = "CDETC";

	public static final String TYPE_DG_DOCUMENTS_IMDG = "GGDGA";

	public static final String TYPE_DG_DOCUMENTS = "GGETA";

	public static final String TYPE_MISC = "XXXXX";

	public static final String TYPE_EDN = "edn";

	public static final String CUSTOMS_CATEGORY = "label.text.customs_documents_label";
}
