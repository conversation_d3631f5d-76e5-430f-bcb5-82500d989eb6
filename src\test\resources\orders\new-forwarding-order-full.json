{"customerNumber": "00000001", "shipperAddress": {"name": "<PERSON><PERSON><PERSON>", "name2": null, "name3": null, "street": "<PERSON><PERSON><PERSON><PERSON><PERSON>-Str. 2", "postcode": "87439", "city": "<PERSON><PERSON>", "countryCode": "HU", "supplement": "Bayern", "gln": "9306139417676", "addressType": null, "neutralizeAddress": true, "contact": {"id": 0, "name": "Test Contact", "email": "Test email", "telephone": "telephone test", "mobile": "mobile number"}}, "consigneeAddress": {"name": "<PERSON><PERSON><PERSON>", "name2": null, "name3": null, "street": "<PERSON><PERSON><PERSON><PERSON><PERSON>-Str. 2", "postcode": "87439", "city": "<PERSON><PERSON>", "countryCode": "HU", "supplement": null, "gln": "9306139417676", "addressType": null, "neutralizeAddress": false}, "packingPositions": [], "orderLineItems": [{"number": 0, "quantity": 1, "packaging": {"code": "CON", "description": "Testing"}, "content": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weight": 3, "length": 122, "width": 122, "height": 12, "volume": 0.179, "loadingMeter": 1, "goodsGroup": {"code": "VI", "quantity": 1}}], "collectionTime": {"collectionDate": "2022-11-15", "from": "2022-12-06T11:00:00Z", "to": "2022-12-06T15:00:00Z"}, "product": "G", "deliveryOption": "AP", "orderNumber": "testOrder", "references": [{"referenceType": "PURCHASE_ORDER_NUMBER", "referenceValue": "4711"}], "texts": [{"textType": "ZU", "value": "Lieferanweisungen"}, {"textType": "WA", "value": "Warenbeschreibung"}, {"textType": "RE", "value": "Rechnungstext"}, {"textType": "SI", "value": "Sonstige Informationen"}, {"textType": "CI", "value": ""}], "furtherAddresses": [{"name": "DA Adress Name", "name2": "Da Addres Name 2", "name3": "DA Address Name 3", "street": "Street DA", "postcode": "2314", "city": "Leipzig", "countryCode": "DE", "supplement": "none", "gln": "9306139417676", "addressType": "DA"}, {"name": "", "name2": "", "name3": "", "street": "", "postcode": "", "city": "", "countryCode": "", "supplement": "", "gln": null, "addressType": "RE"}, {"name": "", "name2": "", "name3": "", "street": "", "postcode": "", "city": "", "countryCode": "", "supplement": "", "gln": null, "addressType": "CB"}, {"name": "", "name2": "", "name3": "", "street": "", "postcode": "", "city": "", "countryCode": "", "supplement": "", "gln": null, "addressType": "DP"}, {"name": "", "name2": "", "name3": "", "street": "", "postcode": "", "city": "", "countryCode": "", "supplement": "", "gln": null, "addressType": "AE"}, {"name": "", "name2": "", "name3": "", "street": "", "postcode": "", "city": "", "countryCode": "", "supplement": "", "gln": null, "addressType": "DC"}, {"name": "", "name2": "", "name3": "", "street": "", "postcode": "", "city": "", "countryCode": "", "supplement": "", "gln": null, "addressType": ""}], "orderType": "RoadForwardingOrder", "fixDate": "", "tailLiftDelivery": true, "freightTerm": {"dachserTermKey": "011", "incoTermKey": "EXW", "headline": "Ex Works"}, "createdBy": "bb1b5613-c916-4bc2-a54a-8064c2a5583c", "orderGroup": "7", "labelPrinted": true, "transferListPrinted": true, "selfCollection": true, "transportName": "Testing", "generatedSsccs": [], "customsType": "DACHSER", "palletLocations": 10.0, "frostProtection": true, "cashOnDelivery": true, "cashOnDeliveryAmount": 100.0}