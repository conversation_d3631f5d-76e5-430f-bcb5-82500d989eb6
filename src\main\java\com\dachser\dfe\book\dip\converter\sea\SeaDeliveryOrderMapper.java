package com.dachser.dfe.book.dip.converter.sea;

import com.dachser.dfe.book.dip.converter.shared.AirSeaAddressMappingConfig;
import com.dachser.dfe.book.dip.converter.shared.AirSeaGoodsDescriptionMapper;
import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderContactCommunicationsMapper;
import com.dachser.dfe.book.dip.converter.shared.DateMapper;
import com.dachser.dfe.book.dip.converter.shared.StringNotEmptyConditionMapper;
import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.model.jaxb.order.asl.Address;
import com.dachser.dfe.book.model.jaxb.order.asl.Contact;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.sea.SeaOrder;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper(config = AirSeaAddressMappingConfig.class, componentModel = "spring", uses = { DateMapper.class, AirSeaOrderContactCommunicationsMapper.class, SeaOrderLineMapper.class,
		SeaOrderReferenceMapper.class, StringNotEmptyConditionMapper.class, AirSeaGoodsDescriptionMapper.class })
public interface SeaDeliveryOrderMapper {

	default List<ForwardingOrder.SeaFreightShipment.DeliveryOrder> mapDeliveryOrders(SeaOrder order) {
		// We need to map an delivery order in that case
		if (!order.isDeliverToPort()) {
			return List.of();
		}
		final ForwardingOrder.SeaFreightShipment.DeliveryOrder deliveryOrder = mapDeliveryOrder(order);
		return List.of(deliveryOrder);
	}

	@Mapping(target = "orderPosition", source = ".")
	@Mapping(target = "goodsDescription", source = ".", qualifiedByName = "mapGoodsDescriptionSea")
	@Mapping(target = "orderAddress", source = ".", qualifiedByName = "mapDelivererAddress")
	@Mapping(target = "orderReference", source = "orderReferences")
	@Mapping(target = "plannedDeliveryDate", source = "collectionDate", conditionExpression = "java(!order.isRequestArrangement())")
	@Mapping(target = "additionalOrderInformation", source = "allOrderLinesOrFCLOrderLines")
	ForwardingOrder.SeaFreightShipment.DeliveryOrder mapDeliveryOrder(SeaOrder order);

	@Named("mapDelivererAddress")
	@Mapping(target = "address", source = "shipperAddress")
	@Mapping(target = "contact", source = "orderContact")
	@Mapping(target = "typeOfAddress", constant = Types.AddressTypes.DELIVERER)
	ForwardingOrder.SeaFreightShipment.DeliveryOrder.OrderAddress mapDelivererAddress(SeaOrder airOrder);

	@InheritConfiguration(name = "mapAddress")
	Address mapOrderAddress(OrderAddress orderAddress);

	@InheritConfiguration(name = "mapContact")
	Contact mapOrderContact(OrderContact orderContact);

}
