package com.dachser.dfe.book.quote.validation;

import com.dachser.dfe.book.order.air.AirOrderLine;
import com.dachser.dfe.book.order.common.orderline.OrderLine;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.sea.SeaOrderLine;
import com.dachser.dfe.book.quote.QuoteOrderLine;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
class PriceRelevantOrderLineFieldsValidator<T extends OrderLine> implements PriceRelevantFieldsValidator<List<QuoteOrderLine>, List<T>> {

	private static final String ORDER_LINE_ITEM_COUNT = "orderLineItem_count";

	private static final String ORDER_LINE_ITEM_NON_MATCHING_UPDATE_LINE = "orderLineItem_non matching update line";

	private static final String ORDER_LINE_ITEM_NEW_INSERT = "orderLineItem_newInsert";

	private static final String ORDER_LINE_ITEM_WEIGHT = "orderLineItem_weight";

	@Override
	public List<PriceRelevantChange> getPriceRelevantChanges(List<QuoteOrderLine> quoteOrderLines, List<T> orderLines) {
		List<PriceRelevantChange> priceRelevantChanges = new ArrayList<>();

		if (orderLines == null || quoteOrderLines.isEmpty() || orderLines.isEmpty() || quoteOrderLines.size() != orderLines.size()) { // same number of orderlines
			priceRelevantChanges.add(new PriceRelevantChange(ORDER_LINE_ITEM_COUNT));
		} else if (orderLines.stream().anyMatch(orderLine -> orderLine.getOrderLineId() == null)) { // no new items without id
			priceRelevantChanges.add(new PriceRelevantChange(ORDER_LINE_ITEM_NEW_INSERT));
		} else if (orderLinesNotMatching(quoteOrderLines, orderLines)) {
			priceRelevantChanges.add(new PriceRelevantChange(ORDER_LINE_ITEM_NON_MATCHING_UPDATE_LINE));
		} else if (hasWeightChanged(quoteOrderLines, orderLines)) {
			priceRelevantChanges.add(new PriceRelevantChange(ORDER_LINE_ITEM_WEIGHT));
		}

		return priceRelevantChanges;
	}

	private static <T extends OrderLine> boolean orderLinesNotMatching(@NonNull List<QuoteOrderLine> quoteOrderLines, List<T> orderLines) {
		return orderLines.stream().anyMatch(orderLine -> quoteOrderLines.stream().noneMatch(quoteOrderLine -> compareOrderLine(quoteOrderLine, orderLine)));
	}

	private static <T extends OrderLine> boolean compareOrderLine(@NonNull QuoteOrderLine quoteOrderLine, T orderLine) {

		//@formatter:off
		boolean basicMatching =
				Objects.equals(quoteOrderLine.getNumber(), orderLine.getNumber()) &&
						Objects.equals(quoteOrderLine.getQuantity(), orderLine.getQuantity()) &&
						Objects.equals(quoteOrderLine.getLength(), orderLine.getLength()) &&
						Objects.equals(quoteOrderLine.getHeight(), orderLine.getHeight()) &&
						Objects.equals(quoteOrderLine.getWidth(), orderLine.getWidth()) &&
						Objects.equals(quoteOrderLine.getVolume(), orderLine.getVolume()) &&
						Objects.equals(quoteOrderLine.getPackagingType(), orderLine.getPackagingType());
		//@formatter:on

		if (!basicMatching) {
			return false;
		}

		if (orderLine instanceof RoadOrderLine roadOrderLine) {
			return quoteOrderLine.getLoadingMeter() == null || Objects.equals(BigDecimal.valueOf(quoteOrderLine.getLoadingMeter()), roadOrderLine.getLoadingMeter());
		}

		return orderLine instanceof AirOrderLine || orderLine instanceof SeaOrderLine;
	}

	private static <T extends OrderLine> boolean hasWeightChanged(List<QuoteOrderLine> quoteOrderLines, List<T> orderLines) {
		double totalQuoteWeight = quoteOrderLines.stream().mapToDouble(q -> q.getWeight() != null ? q.getWeight().doubleValue() : 0.0).sum();
		double totalOrderWeight = orderLines.stream().mapToDouble(q -> q.getWeight() != null ? q.getWeight().doubleValue() : 0.0).sum();

		return totalQuoteWeight != totalOrderWeight;
	}
}
