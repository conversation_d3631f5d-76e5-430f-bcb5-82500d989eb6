package com.dachser.dfe.book.resthandler;

import com.dachser.dfe.book.BaseOpenApiTest;
import com.dachser.dfe.book.exception.ExtDeliveryProductServiceNotAvailable;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.DivisionDto;
import com.dachser.dfe.book.model.TermDto;
import io.restassured.common.mapper.TypeRef;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import io.restassured.module.mockmvc.specification.MockMvcRequestSpecification;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static com.dachser.dfe.book.MockConstants.ACTIVE_COMPANY_TT_ACCESS_TOKEN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

class OrderOverviewApiHandlerTest extends BaseOpenApiTest {

	@Nested
	class OrderTerms {
		@Nested
		class WithAccessToken {

			@Test
			void shouldReturn200() {
				MockMvcRequestSpecification request = givenRequest();
				request.header("Accept-Language", "de");

				final MockMvcResponse response = request.get(buildUrl("/overview/terms"));

				assertThat(response.statusCode()).isEqualTo(200);
				final List<TermDto> content = response.getBody().as(new TypeRef<>() {
				});
				assertThat(content).isNotEmpty();
				content.stream().findFirst().ifPresent(option -> {
					assertThat(option.getDachserCode()).isNotEmpty();
					assertThat(option.getIncoTerm()).isNotEmpty();
					assertThat(option.getLabel()).isNotEmpty();
				});
			}
		}
	}

	@Nested
	class GetDeliveryProducts {
		@Test
		void shouldReturn200() {
			when(roadProductsService.getDeliveryProductsForDivision(Division.T)).thenReturn(
					List.of(new DeliveryProductDto().code("code").description("description")));

			MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
			request.queryParam("division", DivisionDto.EUROPEAN_LOGISTICS.name());
			final MockMvcResponse response = request.get(buildUrl("/overview/delivery-products-road"));
			assertEquals(200, response.statusCode());
			final List<DeliveryProductDto> content = response.getBody().as(new TypeRef<>() {
			});
			assertFalse(content.isEmpty());
			final DeliveryProductDto countryDto = content.getFirst();
			assertNotNull(countryDto.getCode());
			assertNotNull(countryDto.getDescription());
		}

		@Test
		void shouldReturn200ForFood() {
			when(roadProductsService.getDeliveryProductsForDivision(Division.F)).thenReturn(
					List.of(new DeliveryProductDto().code("code").description("description")));

			MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
			request.queryParam("division", DivisionDto.FOOD_LOGISTICS.name());
			final MockMvcResponse response = request.get(buildUrl("/overview/delivery-products-road"));
			assertEquals(200, response.statusCode());
			final List<DeliveryProductDto> content = response.getBody().as(new TypeRef<>() {
			});
			assertFalse(content.isEmpty());
			final DeliveryProductDto countryDto = content.getFirst();
			assertNotNull(countryDto.getCode());
			assertNotNull(countryDto.getDescription());
		}

		@Test
		void shouldReturn500() {
			when(roadProductsService.getDeliveryProductsForDivision(Division.T)).thenThrow(ExtDeliveryProductServiceNotAvailable.class);

			MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");
			request.queryParam("division", DivisionDto.EUROPEAN_LOGISTICS.name());
			final MockMvcResponse response = request.get(buildUrl("/overview/delivery-products-road"));

			assertEquals(500, response.statusCode());
		}

		@Test
		void shouldReturn400() {
			MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json").body("{}");

			final MockMvcResponse response = request.get(buildUrl("/overview/delivery-products-road"));
			assertEquals(400, response.statusCode());
		}
	}
	@Nested
	class AirProducts {
		@Test
		void shouldReturn200() {
			final MockMvcRequestSpecification request = givenRequest().header("Content-Type", "application/json");
			final MockMvcResponse response = request.get(buildUrl("/v1/air-products"));
			assertEquals(HttpStatus.SC_OK, response.statusCode());
			List<AirProductDto> airProducts = response.as(new TypeRef<>() {
			});
			Optional<AirProductDto> first = airProducts.stream().findFirst();
			assertTrue(first.isPresent());
			assertEquals("1", first.get().getCode());
		}

		@Test
		void shouldReturn200TT() {
			final MockMvcRequestSpecification request = prepareNewRequestWithToken(ACTIVE_COMPANY_TT_ACCESS_TOKEN);
			final MockMvcResponse response = request.get(buildUrl("/v1/air-products"));
			assertEquals(HttpStatus.SC_OK, response.statusCode());
			List<AirProductDto> airProducts = response.as(new TypeRef<>() {
			});
			Optional<AirProductDto> first = airProducts.stream().findFirst();
			assertTrue(first.isPresent());
			assertEquals("1", first.get().getCode());
		}
	}
}