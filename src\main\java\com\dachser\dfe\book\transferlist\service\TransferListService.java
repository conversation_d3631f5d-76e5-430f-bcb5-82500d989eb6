package com.dachser.dfe.book.transferlist.service;

import com.dachser.dfe.book.model.TransferListFilterDto;
import com.dachser.dfe.book.model.TransferListQueryObjectDto;
import com.dachser.dfe.book.model.TransferListResponseDto;

import java.util.List;

public interface TransferListService {
	TransferListResponseDto getTransferList(TransferListQueryObjectDto queryObject);

	List<TransferListFilterDto> getTransferListFilter();
}
