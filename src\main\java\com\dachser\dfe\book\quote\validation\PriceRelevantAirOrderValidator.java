package com.dachser.dfe.book.quote.validation;

import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.air.AirOrderLine;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.quote.AirQuoteInformation;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
@Getter
abstract class PriceRelevantAirOrderValidator<O extends AirOrder> implements PriceRelevantFieldsValidator<AirQuoteInformation, O> {

	protected static final String SHIPPER_ADDRESS_PREFIX = "shipperAddress_";

	protected static final String CONSIGNEE_ADDRESS_PREFIX = "consigneeAddress_";

	protected static final String DELIVERY_ADDRESS_PREFIX = "deliveryAddress_";

	protected static final String PICKUP_ADDRESS_PREFIX = "pickupAddress_";

	private static final String INCO_TERM = "incoTerm";

	private static final String DELIVER_TO_AIRPORT = "deliverToAirport";

	private static final String COLLECT_FROM_AIRPORT = "collectFromAirport";

	private static final String FROM_IATA = "fromIATA";

	private static final String TO_IATA = "toIATA";

	private static final String PRODUCT = "product";

	private static final String ORDER_REFERENCE = "orderReference";

	private final PriceRelevantOrderLineFieldsValidator<AirOrderLine> orderLineValidator;

	private final PriceRelevantAddressFieldsValidator addressValidator;

	abstract List<PriceRelevantChange> getOrderSpecificChanges(AirQuoteInformation quoteInformation, O order);

	@Override
	public List<PriceRelevantChange> getPriceRelevantChanges(AirQuoteInformation quoteInformation, AirOrder order) {
		List<PriceRelevantChange> priceRelevantChanges = new ArrayList<>();

		//@formatter:off
		List<Pair<String, Boolean>> aslFields = List.of(
				Pair.of(DELIVER_TO_AIRPORT, !order.isDeliverToAirport() || Objects.equals(order.getFromIATA(), quoteInformation.getFromIATA())),
				Pair.of(COLLECT_FROM_AIRPORT, !order.isCollectFromAirport() || Objects.equals(order.getToIATA(), quoteInformation.getToIATA())),
				Pair.of(FROM_IATA, Objects.equals(quoteInformation.getFromIATA(), order.getFromIATA())),
				Pair.of(TO_IATA, Objects.equals(quoteInformation.getToIATA(), order.getToIATA())),
				Pair.of(PRODUCT, quoteInformation.getProduct() == null || Objects.equals(Integer.parseInt(quoteInformation.getProduct()), order.getProductCode())),
				Pair.of(INCO_TERM, Objects.equals(quoteInformation.getTermCode(), order.getIncoTerm())),
				Pair.of(ORDER_REFERENCE, hasQuotationReference(order) && isMatchingQuotationReference(quoteInformation, order))
		);
		//@formatter:on

		priceRelevantChanges.addAll(buildPriceRelevantChangesFromFields(aslFields));

		priceRelevantChanges.addAll(orderLineValidator.getPriceRelevantChanges(quoteInformation.getOrderLineItems(), order.getOrderLines()));

		return priceRelevantChanges;
	}

	private boolean hasQuotationReference(AirOrder order) {
		return order.getOrderReferences().stream().filter(ref -> Objects.equals(ref.getReferenceType(), AirSeaOrderReferenceType.QUOTATION_REFERENCE)).count() == 1;
	}

	private boolean isMatchingQuotationReference(AirQuoteInformation quoteInformation, AirOrder order) {
		return order.getOrderReferences().stream().anyMatch(
				ref -> Objects.equals(ref.getReferenceType(), AirSeaOrderReferenceType.QUOTATION_REFERENCE) && Objects.equals(ref.getReferenceValue(),
						quoteInformation.getQuoteReference()));
	}
}
