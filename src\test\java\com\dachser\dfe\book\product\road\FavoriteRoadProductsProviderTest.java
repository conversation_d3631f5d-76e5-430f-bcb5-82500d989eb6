package com.dachser.dfe.book.product.road;

import com.dachser.dfe.book.SpringBasedLocalMockTest;
import com.dachser.dfe.book.model.RoadForwardingOrderDto;
import com.dachser.dfe.book.order.OrderService;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.user.UserServiceMock;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Tag("IntegrationTest")
@SpringBasedLocalMockTest
@Transactional
@Rollback
@Slf4j
class FavoriteRoadProductsProviderTest {

	@Autowired
	private OrderService orderService;

	@Autowired
	private OrderMapper orderMapper;

	@Autowired
	private FavoriteRoadProductsProvider favoriteRoadProductsProvider;

	@Autowired
	private ObjectMapper objectMapper;

	@Value("classpath:/orders/new-forwarding-order-complete-valid.json")
	private Resource forwardingOrderCompletePayload;

	@ParameterizedTest(name = "#{index} - find {1} favorite product(s) when existing product(s) are {0}")
	@MethodSource("favoriteRoadProductsScenarios")
	void favoriteRoadProducts(List<String> existingProducts, int expectedAmountOfFavoriteProducts, String expectedFavoriteProduct) {
		// given existing products
		existingProducts.forEach(this::createOrderHavingProduct);
		// when looking for favorite products
		List<String> favoriteProducts = favoriteRoadProductsProvider.determineFavoriteProductCodes(UserServiceMock.VALID_CUST_NO_ROAD);
		// then we obtain items
		assertFavoriteProducts(favoriteProducts);
		log.debug("favorite products are {}", favoriteProducts);
		// and expected amount of those items
		assertEquals(expectedAmountOfFavoriteProducts, favoriteProducts.size());
		// and any expected favorite product in particular is present
		if (expectedFavoriteProduct != null) {
			assertTrue(favoriteProducts.contains(expectedFavoriteProduct));
		}
	}

	private void createOrderHavingProduct(String product) {
		ForwardingOrder forwardingOrder = loadForwardingOrder();
		forwardingOrder.setProduct(product);
		orderService.createNewTestOrder(forwardingOrder);
	}

	private ForwardingOrder loadForwardingOrder() {
		RoadForwardingOrderDto forwardingOrderDto = null;
		try {
			forwardingOrderDto = objectMapper.readValue(forwardingOrderCompletePayload.getInputStream(), RoadForwardingOrderDto.class);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		final ForwardingOrder order = (ForwardingOrder) orderMapper.map(forwardingOrderDto);
		order.setCustomerNumber(UserServiceMock.VALID_CUST_NO_ROAD);
		return order;
	}

	private static Stream<Arguments> favoriteRoadProductsScenarios() {
		// @formatter:off
		return Stream.of(
			Arguments.of(Collections.singletonList("E"), 1, "E"),
			Arguments.of(Arrays.asList("E", "F", "X"), 3, null),
			Arguments.of(Arrays.asList("E", "F", "X", "Y"), 3, null),
			Arguments.of(Arrays.asList("E", "F", "X", "Y"), 3, "E")
		);
		// @formatter:on
	}

	private static void assertFavoriteProducts(List<String> favoriteProducts) {
		assertNotNull(favoriteProducts);
		assertFalse(favoriteProducts.isEmpty());
		assertFalse(favoriteProducts.stream().anyMatch(StringUtils::isBlank));
	}

}