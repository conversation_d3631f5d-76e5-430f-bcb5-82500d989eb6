package com.dachser.dfe.book.customer;

import org.mapstruct.Mapper;

/* only purpose of this mapper at the moment is to easily find all locations where conversion is necessary
 */
@Mapper(componentModel = "spring")
public interface CustomerNumberMapper {

	default Long map(String customerNumber) {
		if (customerNumber == null) {
			return null;
		}
		return Long.valueOf(customerNumber);
	}

	default Integer mapToInteger(String customerNumber) {
		if (customerNumber == null) {
			return null;
		}
		return Integer.valueOf(customerNumber);
	}

	default String map(Long principalId) {
		if (principalId == null) {
			return null;
		}
		return "%08d".formatted(principalId);
	}
}
