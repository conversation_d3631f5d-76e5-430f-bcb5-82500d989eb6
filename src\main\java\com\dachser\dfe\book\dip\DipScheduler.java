package com.dachser.dfe.book.dip;

import com.dachser.dfe.book.dip.repository.OrderXml;
import com.dachser.dfe.book.dip.repository.OrderXmlRepository;
import com.dachser.dfe.book.dip.xml.XmlFileStorageProvider;
import com.dachser.dfe.book.dip.xml.XmlPublishStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class DipScheduler {

	public static final int PUBLISHED_ORDERS_CLEANUP_THRESHOLD_DAYS = 14;

	private final XmlFileStorageProvider xmlFileStorage;

	private final OrderXmlRepository orderXmlRepository;

	private final DipService dipService;

	@Scheduled(cron = "${orderXml.scheduled.publishForwarding}")
	@SchedulerLock(name = "scheduledForwardingOrderXmlPublisherLock", lockAtLeastFor = "PT1M", lockAtMostFor = "PT10M")
	public void publishForwardingOrderXmls() {
		List<OrderXml> orderXmls = orderXmlRepository.findByStatus(XmlPublishStatus.READY).stream().filter(isForwardingOrder()).filter(isPublishDayReached()).toList();

		collectAndPublishXmlFiles(orderXmls);
	}

	@Scheduled(cron = "${orderXml.scheduled.publish}")
	@SchedulerLock(name = "scheduledOrderXmlPublisherLock", lockAtLeastFor = "PT1M", lockAtMostFor = "PT10M")
	public void publishOrderXmls() {
		List<OrderXml> orderXmls = orderXmlRepository.findByStatus(XmlPublishStatus.READY).stream().filter(isForwardingOrder().negate()).toList();

		collectAndPublishXmlFiles(orderXmls);
	}

	private void collectAndPublishXmlFiles(List<OrderXml> orderXmls) {
		if (orderXmls == null || orderXmls.isEmpty()) {
			log.info("No orders to publish");
		} else {
			final Map<OrderXml, String> orderXmlsMap = collectXmlFilesForOrders(orderXmls);

			orderXmlsMap.entrySet().parallelStream().forEach((entry -> {
				final OrderXml orderXml = entry.getKey();
				final String xmlFile = entry.getValue();
				if (StringUtils.isBlank(xmlFile)) {
					log.error("XML file for order {} is empty", orderXml.getShipmentNumber());
				} else {
					try {
						dipService.tryToPublish(xmlFile, orderXml);
					} catch (Exception e) {
						logPublishErrorWithOrderType(orderXml, e);
					}
				}
			}));
		}
	}

	@Scheduled(cron = "${orderXml.scheduled.reorg}")
	@SchedulerLock(name = "scheduledOrderXmlCleanUpLock", lockAtLeastFor = "PT10M", lockAtMostFor = "PT20M")
	public void cleanUpPublishedOrderXmls() {
		final StopWatch stopWatch = new StopWatch();
		try {
			stopWatch.start("XML-Cleanup-" + LocalDate.now());
			xmlFileStorage.cleanUpXmlFiles(PUBLISHED_ORDERS_CLEANUP_THRESHOLD_DAYS);
			stopWatch.stop();
			log.info("Cleaned up old XML files in {} ms", stopWatch.getTotalTimeMillis());
		} catch (Exception e) {
			log.error("Error during XML files cleanup: ", e);
		}
	}

	private Map<OrderXml, String> collectXmlFilesForOrders(List<OrderXml> orderXmls) {
		return orderXmls.stream().collect(Collectors.toMap(orderXml -> orderXml, orderXml -> Objects.requireNonNullElse(xmlFileStorage.getXml(orderXml.getShipmentNumber()), "")));
	}

	private Predicate<OrderXml> isPublishDayReached() {
		final LocalDate currentDate = LocalDate.now();
		return orderXml -> LocalDate.ofInstant(orderXml.getPublishAt(), ZoneOffset.UTC).isBefore(currentDate) || currentDate.isEqual(
				LocalDate.ofInstant(orderXml.getPublishAt(), ZoneOffset.UTC));
	}

	private Predicate<OrderXml> isForwardingOrder() {
		return orderXml -> DipOrderType.FORWARDING.equals(orderXml.getOrderType());
	}

	private void logPublishErrorWithOrderType(OrderXml orderXml, Exception e) {
		log.error("Scheduled publishing of {} Order {} failed. Number of tries: {} Error: ", orderXml.getOrderType(), orderXml.getShipmentNumber(), orderXml.getRetryCount(), e);
	}
}