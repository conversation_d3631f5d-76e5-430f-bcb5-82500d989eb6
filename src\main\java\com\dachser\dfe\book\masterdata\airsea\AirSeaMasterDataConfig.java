package com.dachser.dfe.book.masterdata.airsea;

import com.dachser.dfe.airsea.masterdata.ApiClient;
import com.dachser.dfe.airsea.masterdata.api.AirportApi;
import com.dachser.dfe.airsea.masterdata.api.AirportRoutingApi;
import com.dachser.dfe.airsea.masterdata.api.EmbargoApi;
import com.dachser.dfe.airsea.masterdata.api.SeaportApi;
import com.dachser.dfe.airsea.masterdata.api.SeaportRoutingApi;
import com.dachser.dfe.book.cache.Cache2KConfiguration;
import com.dachser.dfe.book.masterdata.airsea.cache.CachingAirportApi;
import com.dachser.dfe.book.masterdata.airsea.cache.CachingAirportRoutingApi;
import com.dachser.dfe.book.masterdata.airsea.cache.CachingEmbargoApi;
import com.dachser.dfe.book.masterdata.airsea.cache.CachingSeaportApi;
import com.dachser.dfe.book.masterdata.airsea.cache.CachingSeaportRoutingApi;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.client.RestTemplate;

@Configuration
@ConfigurationProperties(prefix = "dfe.airsea.masterdata")
@Getter
@Setter
@Import(Cache2KConfiguration.class)
class AirSeaMasterDataConfig {

	private String baseURL;

	private String username;

	private String password;

	private boolean debugging = false;

	private boolean useCache = true;

	private final RestTemplate bookRestTemplateNoAuth;

	@Autowired
	public AirSeaMasterDataConfig(@Qualifier("bookRestTemplateNoAuth") RestTemplate bookRestTemplateNoAuth) {
		this.bookRestTemplateNoAuth = bookRestTemplateNoAuth;
	}

	@Bean
	ApiClient airseaMasterdataApiClient() {
		final ApiClient apiClient = new ApiClient(bookRestTemplateNoAuth);
		apiClient.setBasePath(baseURL);
		apiClient.setPassword(password);
		apiClient.setUsername(username);
		apiClient.setDebugging(debugging);
		return apiClient;
	}

	@Bean
	EmbargoApi embargoApi() {
		if (useCache) {
			return new CachingEmbargoApi(airseaMasterdataApiClient());
		} else {
			return new EmbargoApi(airseaMasterdataApiClient());
		}
	}

	@Bean
	AirportApi airportApi() {
		if (useCache) {
			return new CachingAirportApi(airseaMasterdataApiClient());
		} else {
			return new AirportApi(airseaMasterdataApiClient());
		}
	}

	@Bean
	AirportRoutingApi airportRoutingApi() {
		if (useCache) {
			return new CachingAirportRoutingApi(airseaMasterdataApiClient());
		} else {
			return new AirportRoutingApi(airseaMasterdataApiClient());
		}
	}

	@Bean
	SeaportApi seaportApi() {
		if (useCache) {
			return new CachingSeaportApi(airseaMasterdataApiClient());
		} else {
			return new SeaportApi(airseaMasterdataApiClient());
		}
	}

	@Bean
	SeaportRoutingApi seaportRoutingApi() {
		if (useCache) {
			return new CachingSeaportRoutingApi(airseaMasterdataApiClient());
		} else {
			return new SeaportRoutingApi(airseaMasterdataApiClient());
		}

	}

}
