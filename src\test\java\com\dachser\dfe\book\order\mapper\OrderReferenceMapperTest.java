package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.model.AirSeaOrderReferenceDto;
import com.dachser.dfe.book.model.OrderReferenceTypeDto;
import com.dachser.dfe.book.model.RoadOrderReferenceDto;
import com.dachser.dfe.book.model.TestMockData;
import com.dachser.dfe.book.order.OrderReferenceHelper;
import com.dachser.dfe.book.order.air.AirOrderReference;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import com.dachser.dfe.book.order.sea.SeaOrderReference;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
@Slf4j
class OrderReferenceMapperTest {

	@InjectMocks
	private OrderReferenceMapperImpl orderReferenceMapper;

	@Test
	void shouldReturnNullWhenOrderReferenceIsNull() {
		assertNull(orderReferenceMapper.mapOrderReference(null));
	}

	@ParameterizedTest
	@MethodSource("provideNewRoadTypeParameters")
	void shouldMapRoadReferenceTypes(OrderReferenceTypeDto inputType, ReferenceType outputType) {
		final RoadOrderReferenceDto airOrderReferenceDto = new RoadOrderReferenceDto();
		airOrderReferenceDto.setReferenceType(inputType);
		airOrderReferenceDto.setReferenceValue(inputType + " Value");
		final RoadOrderReference roadOrderReference = orderReferenceMapper.mapRoadReference(airOrderReferenceDto);
		assertEquals(outputType, roadOrderReference.getReferenceType());
	}

	@ParameterizedTest
	@MethodSource("provideAirTypeParameters")
	void shouldMapAirReferenceTypes(OrderReferenceTypeDto inputType, AirSeaOrderReferenceType outputType) {
		final AirSeaOrderReferenceDto airOrderReferenceDto = new AirSeaOrderReferenceDto();
		airOrderReferenceDto.setReferenceType(inputType);
		airOrderReferenceDto.setReferenceValue(inputType + " Value");
		final AirOrderReference airOrderReference = orderReferenceMapper.mapAirReference(airOrderReferenceDto);
		assertEquals(outputType, airOrderReference.getReferenceType());
	}

	@ParameterizedTest
	@MethodSource("provideSeaTypeParameters")
	void shouldMapSeaReferenceTypes(OrderReferenceTypeDto inputType, AirSeaOrderReferenceType outputType) {
		final AirSeaOrderReferenceDto aslOrderReferenceDto = new AirSeaOrderReferenceDto();
		aslOrderReferenceDto.setReferenceType(inputType);
		aslOrderReferenceDto.setReferenceValue(inputType + " Value");
		final SeaOrderReference seaOrderReference = orderReferenceMapper.mapSeaReference(aslOrderReferenceDto);
		assertEquals(outputType, seaOrderReference.getReferenceType());
	}

	@Test
	void shouldMapIDFieldOfAirReference() {
		final AirOrderReference airOrderReference = new AirOrderReference();
		airOrderReference.setOrderReferenceId(1L);
		final AirSeaOrderReferenceDto airOrderReferenceDto = orderReferenceMapper.mapAirReference(airOrderReference);
		assertEquals(1L, airOrderReferenceDto.getId());
	}

	@Test
	void roadOrderReferenceSubtypeMappingFromOas() {
		// given an OAS order reference for ROAD
		RoadOrderReferenceDto oasRoadOrderReference = new RoadOrderReferenceDto();
		// having a ROAD reference type that accepts subtypes
		OrderReferenceTypeDto oasRoadOrderReferenceType = OrderReferenceTypeDto.IDENTIFICATION_CODE_TRANSPORT;
		ReferenceType roadOrderReferenceType = orderReferenceMapper.mapReferenceType(oasRoadOrderReferenceType);
		assertNotNull(roadOrderReferenceType);
		List<RoadOrderReferenceSubtype> roadOrderReferenceSubtypes = OrderReferenceHelper.roadSubtypes(roadOrderReferenceType);
		assertNotNull(roadOrderReferenceSubtypes);
		assertFalse(roadOrderReferenceSubtypes.isEmpty());
		log.debug("JPA road reference subtypes for {} = {}", roadOrderReferenceType.name(), roadOrderReferenceSubtypes.stream().map(Enum::name).toArray());
		oasRoadOrderReference.setReferenceType(oasRoadOrderReferenceType);
		// and having a reference value prefix matching any of those ROAD reference subtype
		RoadOrderReferenceSubtype roadOrderReferenceSubtype = RoadOrderReferenceSubtype.UIT;
		assertTrue(roadOrderReferenceSubtypes.contains(roadOrderReferenceSubtype));
		String referenceRawValue = TestMockData.UIT_REFERENCE_VALUE;
		String oasRoadOrderReferenceValue = OrderReferenceHelper.prefix(roadOrderReferenceSubtype).concat(referenceRawValue);
		oasRoadOrderReference.setReferenceValue(oasRoadOrderReferenceValue);
		log.debug("OAS road reference = {}", oasRoadOrderReference);
		// when mapping to JPA entity
		RoadOrderReference roadOrderReference = orderReferenceMapper.mapRoadReference(oasRoadOrderReference);
		// then we obtain a JPA entity
		assertNotNull(roadOrderReference);
		log.debug("JPA road reference = {}", roadOrderReference);
		// and it has expected ROAD reference type
		assertEquals(roadOrderReferenceType, roadOrderReference.getReferenceType());
		// and it has expected ROAD reference subtype
		assertEquals(roadOrderReferenceSubtype, roadOrderReference.getReferenceSubtype());
		// and its reference value is without ROAD reference subtype prefix
		assertEquals(referenceRawValue, roadOrderReference.getReference());
	}

	@Test
	void roadOrderReferenceSubtypeMappingFromJpa() {
		// given a JPA order reference for ROAD
		RoadOrderReference roadOrderReference = new RoadOrderReference();
		// having a ROAD reference type that accepts subtypes
		ReferenceType roadOrderReferenceType = ReferenceType.IDENTIFICATION_CODE_TRANSPORT;
		List<RoadOrderReferenceSubtype> roadOrderReferenceSubtypes = OrderReferenceHelper.roadSubtypes(roadOrderReferenceType);
		assertNotNull(roadOrderReferenceSubtypes);
		assertFalse(roadOrderReferenceSubtypes.isEmpty());
		log.debug("JPA road reference subtypes for {} = {}", roadOrderReferenceType.name(), roadOrderReferenceSubtypes.stream().map(Enum::name).toArray());
		roadOrderReference.setReferenceType(roadOrderReferenceType);
		// and having a ROAD reference sub type
		RoadOrderReferenceSubtype roadOrderReferenceSubtype = RoadOrderReferenceSubtype.UIT;
		roadOrderReference.setReferenceSubtype(roadOrderReferenceSubtype);
		// and having a raw reference value
		String referenceRawValue = TestMockData.UIT_REFERENCE_VALUE;
		roadOrderReference.setReference(referenceRawValue);
		log.debug("JPA road reference = {}", roadOrderReference);
		// when mapping to OAS order reference for ROAD
		RoadOrderReferenceDto oasRoadOrderReference = orderReferenceMapper.mapOrderReference(roadOrderReference);
		// then we obtain an OAS object
		assertNotNull(oasRoadOrderReference);
		log.debug("OAS road reference = {}", oasRoadOrderReference);
		// and it has expected ROAD reference type
		assertEquals(orderReferenceMapper.mapReferenceType(roadOrderReferenceType), oasRoadOrderReference.getReferenceType());
		// and its reference value is prefixed with expected ROAD reference subtype
		String oasRoadOrderReferenceValue = OrderReferenceHelper.prefix(roadOrderReferenceSubtype).concat(referenceRawValue);
		assertEquals(oasRoadOrderReferenceValue, oasRoadOrderReference.getReferenceValue());
	}

	private static Stream<Arguments> provideAirTypeParameters() {
		// @formatter:off
		return Stream.of(Arguments.of(OrderReferenceTypeDto.QUOTATION_REFERENCE, AirSeaOrderReferenceType.QUOTATION_REFERENCE),
			Arguments.of(OrderReferenceTypeDto.SHIPPING_ORDER_NUMBER, AirSeaOrderReferenceType.SHIPPING_ORDER_NUMBER),
			Arguments.of(OrderReferenceTypeDto.CONSIGNEE_REFERENCE_NUMBER, AirSeaOrderReferenceType.CONSIGNEE_REFERENCE_NUMBER),
			Arguments.of(OrderReferenceTypeDto.INVOICE_NUMBER, AirSeaOrderReferenceType.INVOICE_NUMBER),
			Arguments.of(OrderReferenceTypeDto.PURCHASE_ORDER_NUMBER, AirSeaOrderReferenceType.PURCHASE_ORDER_NUMBER),
			Arguments.of(OrderReferenceTypeDto.DELIVERY_NOTE_NUMBER, AirSeaOrderReferenceType.DELIVERY_NOTE_NUMBER),
			Arguments.of(OrderReferenceTypeDto.MARKS_AND_NUMBERS, AirSeaOrderReferenceType.MARKS_AND_NUMBERS),
			Arguments.of(OrderReferenceTypeDto.PROVIDER_SHIPMENT_NUMBER, AirSeaOrderReferenceType.PROVIDER_SHIPMENT_NUMBER),
			Arguments.of(OrderReferenceTypeDto.PACKAGING_LIST_NUMBER, AirSeaOrderReferenceType.PACKAGING_LIST_NUMBER),
			Arguments.of(OrderReferenceTypeDto.SUPPLIER_SHIPMENT_NUMBER, AirSeaOrderReferenceType.SUPPLIER_SHIPMENT_NUMBER),
			Arguments.of(OrderReferenceTypeDto.COMMERCIAL_INVOICE_NUMBER, AirSeaOrderReferenceType.COMMERCIAL_INVOICE_NUMBER),
			Arguments.of(OrderReferenceTypeDto.SHIPPERS_REFERENCE, AirSeaOrderReferenceType.SHIPPERS_REFERENCE),
			Arguments.of(OrderReferenceTypeDto.OTHERS, AirSeaOrderReferenceType.OTHERS), Arguments.of(OrderReferenceTypeDto.EKAER_NUMBER, null),
			Arguments.of(OrderReferenceTypeDto.IDENTIFICATION_CODE_TRANSPORT, null),
			Arguments.of(OrderReferenceTypeDto.BOOKING_REFERENCE, null),
			Arguments.of(null, null));
		// @formatter:on
	}

	private static Stream<Arguments> provideSeaTypeParameters() {
		// @formatter:off
		return Stream.of(
			Arguments.of(OrderReferenceTypeDto.QUOTATION_REFERENCE, AirSeaOrderReferenceType.QUOTATION_REFERENCE),
			Arguments.of(OrderReferenceTypeDto.SHIPPING_ORDER_NUMBER, AirSeaOrderReferenceType.SHIPPING_ORDER_NUMBER),
			Arguments.of(OrderReferenceTypeDto.CONSIGNEE_REFERENCE_NUMBER, AirSeaOrderReferenceType.CONSIGNEE_REFERENCE_NUMBER),
			Arguments.of(OrderReferenceTypeDto.INVOICE_NUMBER, AirSeaOrderReferenceType.INVOICE_NUMBER),
			Arguments.of(OrderReferenceTypeDto.PURCHASE_ORDER_NUMBER, AirSeaOrderReferenceType.PURCHASE_ORDER_NUMBER),
			Arguments.of(OrderReferenceTypeDto.DELIVERY_NOTE_NUMBER, AirSeaOrderReferenceType.DELIVERY_NOTE_NUMBER),
			Arguments.of(OrderReferenceTypeDto.MARKS_AND_NUMBERS, AirSeaOrderReferenceType.MARKS_AND_NUMBERS),
			Arguments.of(OrderReferenceTypeDto.PROVIDER_SHIPMENT_NUMBER, AirSeaOrderReferenceType.PROVIDER_SHIPMENT_NUMBER),
			Arguments.of(OrderReferenceTypeDto.PACKAGING_LIST_NUMBER, AirSeaOrderReferenceType.PACKAGING_LIST_NUMBER),
			Arguments.of(OrderReferenceTypeDto.SUPPLIER_SHIPMENT_NUMBER, AirSeaOrderReferenceType.SUPPLIER_SHIPMENT_NUMBER),
			Arguments.of(OrderReferenceTypeDto.COMMERCIAL_INVOICE_NUMBER, AirSeaOrderReferenceType.COMMERCIAL_INVOICE_NUMBER),
			Arguments.of(OrderReferenceTypeDto.SHIPPERS_REFERENCE, AirSeaOrderReferenceType.SHIPPERS_REFERENCE),
			Arguments.of(OrderReferenceTypeDto.OTHERS, AirSeaOrderReferenceType.OTHERS), Arguments.of(OrderReferenceTypeDto.EKAER_NUMBER, null),
			Arguments.of(OrderReferenceTypeDto.IDENTIFICATION_CODE_TRANSPORT, null),
			Arguments.of(OrderReferenceTypeDto.BOOKING_REFERENCE, null),
			Arguments.of(null, null));
		// @formatter:on
	}

	private static Stream<Arguments> provideNewRoadTypeParameters() {
		// @formatter:off
		return Stream.of(
			Arguments.of(OrderReferenceTypeDto.EKAER_NUMBER, ReferenceType.EKAER_NUMBER),
			Arguments.of(OrderReferenceTypeDto.PURCHASE_ORDER_NUMBER, ReferenceType.PURCHASE_ORDER_NUMBER),
			Arguments.of(OrderReferenceTypeDto.DELIVERY_NOTE_NUMBER, ReferenceType.DELIVERY_NOTE_NUMBER),
			Arguments.of(OrderReferenceTypeDto.IDENTIFICATION_CODE_TRANSPORT, ReferenceType.IDENTIFICATION_CODE_TRANSPORT),
			Arguments.of(OrderReferenceTypeDto.BOOKING_REFERENCE, ReferenceType.BOOKING_REFERENCE),
			Arguments.of(null, null));
		// @formatter:on
	}

}