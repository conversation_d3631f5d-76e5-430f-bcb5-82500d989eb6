package com.dachser.dfe.book.advice;

import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.common.OrderSscc;
import com.dachser.dfe.book.order.common.OrderText;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.common.dangerousgood.ADRDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGoodDataItem;
import com.dachser.dfe.book.order.common.dangerousgood.EQDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.LQDangerousGood;
import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.legacy.advice.bean.Address;
import com.dachser.dfe.legacy.advice.bean.AdvisedOrder;
import com.dachser.dfe.legacy.advice.bean.Contact;
import com.dachser.dfe.legacy.advice.bean.GoodsGroup;
import com.dachser.dfe.legacy.advice.bean.MonetaryAmount;
import com.dachser.dfe.legacy.advice.bean.Reference;
import com.dachser.dfe.legacy.advice.bean.Text;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;

public class AdvisedOrderTestUtil {

	static final long SHIPMENT_NUMBER = 99999999999L;
	static final String CUSTOMER_NUMBER = "99999999";
	static final int BRANCH_ID = 918;
	static final String CUSTOM_REFERENCE = "Custom Reference 01";
	static final String PRODUCT = "Y";
	static final String FREIGHT_TERM = "031";
	static final LocalDate DATE = LocalDate.now();
	static final LocalDate FIX_DATE = DATE.plusDays(5);
	static final LocalDateTime COLLECTION_DATE_TIME_TO = FIX_DATE.atTime(8, 0);
	static final LocalDateTime COLLECTION_DATE_TIME_FROM = FIX_DATE.atTime(6, 0);
	static final String CONSIGNEE_NAME_1 = "Dachser SE";
	static final String CONSIGNEE_NAME_2 = "Head Office";
	static final String CONSIGNEE_NAME_3 = "Germany";
	static final String CONSIGNEE_STREET = "Thomas-Dachser-Str. 2";
	static final String CONSIGNEE_CITY = "Kempten";
	static final String CONSIGNEE_POSTCODE = "87439";
	static final String CONSIGNEE_SUPPLEMENT = "Amtsgericht Kempten HRB 12407 - Zu langer Text";
	static final String CONSIGNEE_GLN = "4022769000004";
	static final String CURRENCY = "EUR";
	static final double GOODS_VALUE = 10000.99;
	static final String ORDER_CONTACT = "Thomas Dachser";
	static final String ORDER_CONTACT_MAIL = "<EMAIL>";
	static final String ORDER_CONTACT_PHONE = "0177 5916 043";
	static final String REFERENCE_VALUE = "Testreference";
	static final String ORDER_TEXT_VALUE = "Testtext";
	static final String ORDER_TEXT_CODE = "TE";
	static final String FURTHER_ADDRESS_TYPE = "DA";
	static final String FURTHER_ADDRESS_NAME = "Deckadresse";
	static final String ORDERLINE_TESTCONTENT = "Testcontent";
	static final String ORDERLINE_PACKAGING = "EU";
	static final String GOODS_GROUP_CODE = "01";
	static final Long PACKING_POSITION_ID = 1001L;

	public static ForwardingOrder createValidForwardingOrderForAdvice() {
		ForwardingOrder forwardingOrder = new ForwardingOrder();
		forwardingOrder.setOrderTexts(new ArrayList<>());
		forwardingOrder.setOrderLines(new ArrayList<>());
		forwardingOrder.setOrderReferences(new ArrayList<>());
		forwardingOrder.setAddresses(new ArrayList<>());
		forwardingOrder.setCustomerNumber(CUSTOMER_NUMBER);
		RoadOrderReference rr1 = new RoadOrderReference();
		rr1.setOrder(forwardingOrder);
		rr1.setReference(CUSTOM_REFERENCE);
		rr1.setOrderReferenceId(5L);
		rr1.setReferenceType(ReferenceType.ORDER_NUMBER);
		forwardingOrder.setBranchId(BRANCH_ID);
		forwardingOrder.setProduct(PRODUCT);
		forwardingOrder.setFreightTerm(FREIGHT_TERM);
		forwardingOrder.setDivision(Division.T);
		forwardingOrder.setShipmentNumber(SHIPMENT_NUMBER);
		forwardingOrder.setFixDate(FIX_DATE);
		forwardingOrder.setCollectionFrom(COLLECTION_DATE_TIME_FROM.atOffset(ZoneOffset.of("+1")));
		forwardingOrder.setCollectionTo(COLLECTION_DATE_TIME_TO.atOffset(ZoneOffset.of("+1")));
		forwardingOrder.setCurrency(CURRENCY);
		forwardingOrder.setGoodsValue(BigDecimal.valueOf(GOODS_VALUE));
		forwardingOrder.setDeliveryOption(DeliveryOptions.AC);
		forwardingOrder.setStatus(OrderStatus.COMPLETE);

		OrderSscc sscc1 = new OrderSscc();
		OrderSscc sscc2 = new OrderSscc();
		sscc1.setSscc("123456789012345678");
		sscc2.setSscc("123456789012345679");
		sscc1.setOrderSsccId(1L);
		sscc2.setOrderSsccId(2L);
		forwardingOrder.setSsccs(List.of(sscc1, sscc2));

		OrderAddress consignee = new OrderAddress();
		consignee.setName(CONSIGNEE_NAME_1);
		consignee.setName2(CONSIGNEE_NAME_2);
		consignee.setName3(CONSIGNEE_NAME_3);
		consignee.setStreet(CONSIGNEE_STREET);
		consignee.setCity(CONSIGNEE_CITY);
		consignee.setCountryCode("DE");
		consignee.setPostcode(CONSIGNEE_POSTCODE);
		consignee.setSupplement(CONSIGNEE_SUPPLEMENT);
		consignee.setGln(CONSIGNEE_GLN);

		OrderContact orderContact = new OrderContact();
		orderContact.setName(ORDER_CONTACT);
		orderContact.setEmail(ORDER_CONTACT_MAIL);
		orderContact.setMobile(ORDER_CONTACT_PHONE);
		orderContact.setTelephone(ORDER_CONTACT_PHONE);
		consignee.setOrderContact(orderContact);
		forwardingOrder.setDeliveryContact(orderContact);
		forwardingOrder.setConsigneeAddress(consignee);

		OrderFurtherAddress orderFurtherAddress = new OrderFurtherAddress();
		orderFurtherAddress.setAddressType(FURTHER_ADDRESS_TYPE);
		orderFurtherAddress.setName(CONSIGNEE_NAME_1);
		orderFurtherAddress.setName2(FURTHER_ADDRESS_NAME);
		orderFurtherAddress.setName3(CONSIGNEE_NAME_3);
		orderFurtherAddress.setStreet(CONSIGNEE_STREET);
		orderFurtherAddress.setCity(CONSIGNEE_CITY);
		orderFurtherAddress.setPostcode(CONSIGNEE_POSTCODE);
		orderFurtherAddress.setCountryCode("DE");
		orderFurtherAddress.setSupplement(CONSIGNEE_SUPPLEMENT);
		forwardingOrder.addAddress(orderFurtherAddress);

		forwardingOrder.addOrderLine(orderLine(1L));

		RoadOrderLine orderLine2 = orderLine(2L);
		orderLine2.setWeight(BigDecimal.valueOf(50L));
		forwardingOrder.addOrderLine(orderLine2);

		RoadOrderLine orderLine3 = orderLine(3L);
		orderLine3.setWeight(BigDecimal.valueOf(50L));
		orderLine3.setPackingPositionId(PACKING_POSITION_ID);
		forwardingOrder.addOrderLine(orderLine3);

		PackingPosition packingPosition = new PackingPosition();
		packingPosition.setId(PACKING_POSITION_ID);
		packingPosition.setQuantity(3);
		packingPosition.setPackagingType(ORDERLINE_PACKAGING);
		packingPosition.setOrder(forwardingOrder);
		packingPosition.setOrderLines(List.of(orderLine3));
		forwardingOrder.setPackingPositions(List.of(packingPosition));

		RoadOrderReference roadOrderReference = new RoadOrderReference();
		roadOrderReference.setReference(REFERENCE_VALUE);
		roadOrderReference.setReferenceType(ReferenceType.DELIVERY_NOTE_NUMBER);
		forwardingOrder.addOrderReference(roadOrderReference);

		OrderText orderText = new OrderText();
		orderText.setText(ORDER_TEXT_VALUE);
		orderText.setTextType(ORDER_TEXT_CODE);
		orderText.setOrderTextId(1L);
		forwardingOrder.addOrderText(orderText);

		return forwardingOrder;
	}

	public static RoadOrderLine orderLine(Long id) {
		RoadOrderLine orderLine = new RoadOrderLine();
		orderLine.setOrderLineId(id);
		orderLine.setContent(ORDERLINE_TESTCONTENT);
		orderLine.setQuantity(1);
		orderLine.setPackagingType(ORDERLINE_PACKAGING);
		orderLine.setLength(120);
		orderLine.setWidth(80);
		orderLine.setHeight(100);
		orderLine.setWeight(BigDecimal.valueOf(100L));
		orderLine.setVolume(BigDecimal.valueOf(10.3));
		orderLine.setLoadingMeter(BigDecimal.valueOf(1.2));
		orderLine.setGoodsGroup(GOODS_GROUP_CODE);
		orderLine.setGoodsGroupQuantity(1);
		return orderLine;
	}

	public static ADRDangerousGood dangerousGoodAsADR(Long id, Integer positionId, String unNumber) {
		ADRDangerousGood dangerousGood = new ADRDangerousGood();
		dangerousGood.setId(id);
		dangerousGood.setSortingPosition(positionId);
		dangerousGood.setNos("ISOPROPANOL");
		dangerousGood.setGrossMass(BigDecimal.valueOf(4L));
		dangerousGood.setNoOfPackages(8);
		dangerousGood.setPackagingKey("KI");
		dangerousGood.setEnvironmentallyHazardous(Boolean.TRUE);
		dangerousGood.setDangerousGoodDataItem(dangerousGoodData(unNumber));
		return dangerousGood;
	}

	public static LQDangerousGood dangerousGoodAsLQ(Long id, Integer positionId) {
		LQDangerousGood dangerousGood = new LQDangerousGood();
		dangerousGood.setId(id);
		dangerousGood.setSortingPosition(positionId);
		dangerousGood.setNos("ISOPROPANOL");
		dangerousGood.setGrossMass(BigDecimal.valueOf(4L));
		return dangerousGood;
	}

	public static EQDangerousGood dangerousGoodAsEQ(Long id, Integer positionId) {
		EQDangerousGood dangerousGood = new EQDangerousGood();
		dangerousGood.setId(id);
		dangerousGood.setSortingPosition(positionId);
		dangerousGood.setNoOfPackages(8);
		dangerousGood.setPackagingKey("KI");
		return dangerousGood;
	}

	public static DangerousGoodDataItem dangerousGoodData(String unNumber) {
		DangerousGoodDataItem data = new DangerousGoodDataItem();
		data.setExternalDgmId("my-".concat(unNumber));
		data.setDescription("ALCOHOLS");
		data.setUnNumber(unNumber);
		data.setPackingGroup("III");
		data.setClassificationCode("F1");
		data.setMainDanger("3");
		data.setSubsidiaryHazardOne("6.1");
		data.setSubsidiaryHazardTwo("8");
		return data;
	}

	public static AdvisedOrder createValidAdvisedOrder() {
		AdvisedOrder advisedOrder = new AdvisedOrder();
		advisedOrder.setConsignmentNumber(SHIPMENT_NUMBER);
		advisedOrder.setConsignmentReference(CUSTOM_REFERENCE);
		advisedOrder.setPrincipalNumber(Integer.valueOf(CUSTOMER_NUMBER));
		advisedOrder.setConsolidatorNumber(BRANCH_ID);
		advisedOrder.setConsignorNumber(Integer.valueOf(CUSTOMER_NUMBER));
		advisedOrder.setDivisionCode("T");
		advisedOrder.setProductCode(PRODUCT);
		advisedOrder.setTermsCode(FREIGHT_TERM);
		advisedOrder.setWeight(200);
		advisedOrder.setSsccCount(2);
		advisedOrder.setStatusCode("J");
		advisedOrder.setDate(DATE);
		advisedOrder.setFixDate(FIX_DATE);
		advisedOrder.setCollectionDateTimeFrom(COLLECTION_DATE_TIME_FROM);
		advisedOrder.setCollectionDateTimeTo(COLLECTION_DATE_TIME_TO);

		Address consignee = new Address();
		consignee.setName1(CONSIGNEE_NAME_1);
		consignee.setName2(CONSIGNEE_NAME_2);
		consignee.setName3(CONSIGNEE_NAME_3);
		consignee.setStreet(CONSIGNEE_STREET);
		consignee.setCity(CONSIGNEE_CITY);
		consignee.setPostcode(CONSIGNEE_POSTCODE);
		consignee.setCountryCode("D");
		consignee.setSupplement(CONSIGNEE_SUPPLEMENT);
		consignee.setGlobalLocationNumber(CONSIGNEE_GLN);
		advisedOrder.setConsignee(consignee);

		com.dachser.dfe.legacy.advice.bean.OrderLine orderLine1 = advisedOrderLine(1);
		advisedOrder.getOrderLines().add(advisedOrderLine(1));

		com.dachser.dfe.legacy.advice.bean.OrderLine orderLine2 = advisedOrderLine(2);
		orderLine2.setWeight(50);
		advisedOrder.getOrderLines().add(orderLine2);

		com.dachser.dfe.legacy.advice.bean.OrderLine orderLine3 = advisedOrderLine(3);
		orderLine3.setWeight(50);
		orderLine3.setPackingPositionId(1);
		advisedOrder.getOrderLines().add(orderLine3);

		com.dachser.dfe.legacy.advice.bean.PackingPosition packingPosition = new com.dachser.dfe.legacy.advice.bean.PackingPosition();
		packingPosition.setId(1);
		packingPosition.setQuantity(3);
		packingPosition.setPackaging(ORDERLINE_PACKAGING);
		advisedOrder.getPackingPositions().add(packingPosition);

		GoodsGroup goodsGroup = new GoodsGroup();
		goodsGroup.setQuantityOfPackages(1);
		goodsGroup.setCode(GOODS_GROUP_CODE);
		orderLine2.setGoodsGroup(goodsGroup);
		orderLine1.setGoodsGroup(goodsGroup);
		orderLine3.setGoodsGroup(goodsGroup);

		MonetaryAmount monetaryAmount = new MonetaryAmount();
		monetaryAmount.setCurrencyCode(CURRENCY);
		monetaryAmount.setValue(BigDecimal.valueOf(GOODS_VALUE));
		advisedOrder.setInsuranceGoodsValue(monetaryAmount);

		Text text = new Text();
		text.setId(1);
		text.setCode(ORDER_TEXT_CODE);
		text.setValue(ORDER_TEXT_VALUE);
		advisedOrder.getTexts().add(text);

		Address furtherAddress = new Address();
		furtherAddress.setName1(CONSIGNEE_NAME_1);
		furtherAddress.setName2(FURTHER_ADDRESS_NAME);
		furtherAddress.setName3(CONSIGNEE_NAME_3);
		furtherAddress.setStreet(CONSIGNEE_STREET);
		furtherAddress.setCity(CONSIGNEE_CITY);
		furtherAddress.setPostcode(CONSIGNEE_POSTCODE);
		furtherAddress.setCountryCode("D");
		furtherAddress.setSupplement(CONSIGNEE_SUPPLEMENT);
		advisedOrder.getFurtherAddresses().put(FURTHER_ADDRESS_TYPE, furtherAddress);

		Reference reference = new Reference();
		reference.setCode("003");
		reference.setValue(REFERENCE_VALUE);
		advisedOrder.getReferences().add(reference);

		Contact contact = new Contact();
		contact.setCode("AC");
		contact.setName(ORDER_CONTACT);
		contact.setMail(ORDER_CONTACT_MAIL);
		contact.setPhone(ORDER_CONTACT_PHONE);
		contact.setMobilePhone(ORDER_CONTACT_PHONE);
		advisedOrder.setDeliveryNotification(contact);

		return advisedOrder;
	}

	public static com.dachser.dfe.legacy.advice.bean.OrderLine advisedOrderLine(Integer position) {
		com.dachser.dfe.legacy.advice.bean.OrderLine orderLine = new com.dachser.dfe.legacy.advice.bean.OrderLine();
		orderLine.setId(position);
		orderLine.setContent(ORDERLINE_TESTCONTENT);
		orderLine.setQuantityOfPackages(1);
		orderLine.setPackaging(ORDERLINE_PACKAGING);
		orderLine.setLength(new BigDecimal("1.200"));
		orderLine.setWidth(new BigDecimal("0.800"));
		orderLine.setHeight(new BigDecimal("1.000"));
		orderLine.setLoadingMeter(new BigDecimal("1.2"));
		orderLine.setWeight(100);
		orderLine.setVolume(new BigDecimal("10.300"));
		return orderLine;
	}

}
