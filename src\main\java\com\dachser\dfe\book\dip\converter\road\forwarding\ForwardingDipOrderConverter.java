package com.dachser.dfe.book.dip.converter.road.forwarding;

import com.dachser.dfe.book.dip.converter.DipOrderConverter;
import com.dachser.dfe.book.dip.xml.XmlConverter;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ForwardingOrderInformation;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import org.springframework.stereotype.Component;

@Component
public class ForwardingDipOrderConverter extends DipOrderConverter<ForwardingOrder> {

	private final ForwardingOrderInformationMapper mapper;

	public ForwardingDipOrderConverter(XmlConverter xmlConverter, ForwardingOrderInformationMapper mapper) {
		super(xmlConverter);
		this.mapper = mapper;
	}

	@Override
	protected ForwardingOrderInformation map(ForwardingOrder order) {
		return mapper.map(order);
	}

}
