package com.dachser.dfe.book.document;

import com.dachser.dfe.book.api.DocumentsApiDelegate;
import com.dachser.dfe.book.customer.CustomerNumberValid;
import com.dachser.dfe.book.model.BasicDocumentDto;
import com.dachser.dfe.book.model.BooleanResultDto;
import com.dachser.dfe.book.model.DocumentDownloadDto;
import com.dachser.dfe.book.model.DocumentGroupDto;
import com.dachser.dfe.book.model.DocumentInfoDto;
import com.dachser.dfe.book.model.DocumentResponseDto;
import com.dachser.dfe.book.model.DocumentTypeDto;
import com.dachser.dfe.book.model.ExtensionDto;
import com.dachser.dfe.book.model.OrderTypeDto;
import com.dachser.dfe.book.model.SegmentDto;
import com.dachser.dfe.book.user.UserContextService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class DocumentApiHandler implements DocumentsApiDelegate {

	private final DocumentService documentService;

	private final UserContextService userContextService;

	@Override
	@DocumentAccessValid
	public ResponseEntity<DocumentResponseDto> uploadSingleDocument(String customerNumber, SegmentDto customerSegment, Long documentId, Long orderId, Integer documentTypeId,
			String documentName, String mediaType, String mimeType, Boolean startProcessing, MultipartFile file) {
		DocumentResponseDto response = documentService.uploadDocument(customerNumber, orderId, userContextService.getCurrentUserId(), documentTypeId, documentName, MediaType.parseMediaType(mimeType),
				file);

		return ResponseEntity.ok(response);
	}

	/**
	 * Documents are now getting deleted when saving the order
	 */
	@Override
	@DocumentAccessValid
	@Deprecated(forRemoval = true)
	public ResponseEntity<BooleanResultDto> deleteSingleDocument(Long documentId, String customerNumber, SegmentDto customerSegment) {
		documentService.deleteDocument(documentId);
		return ResponseEntity.ok(new BooleanResultDto().result(true));
	}

	@Override
	@DocumentAccessValid
	public ResponseEntity<BooleanResultDto> updateSingleDocument(Long documentId, String customerNumber, SegmentDto customerSegment, BasicDocumentDto basicDocumentDto) {
		documentService.updateDocument(documentId, basicDocumentDto);
		return ResponseEntity.ok(new BooleanResultDto().result(true));
	}

	@Override
	@CustomerNumberValid
	public ResponseEntity<List<DocumentTypeDto>> getCustomerRecentlyUsedDocumentTypes(String customerNumber, SegmentDto customerSegment) {
		List<DocumentTypeDto> list = documentService.getRecentlyUsedDocumentTypes(customerNumber, customerSegment);
		return ResponseEntity.ok(list);
	}

	@Override
	public ResponseEntity<List<ExtensionDto>> getExtensions() {
		List<ExtensionDto> list = new ArrayList<>();

		for (FileType fileType : Arrays.stream(FileType.values()).filter(documentService::isFileTypeSupported).toArray(FileType[]::new)) {
			list.add(new ExtensionDto().label(fileType.getLabel()).description(fileType.getContentType().toString()));
		}

		return ResponseEntity.ok(list);
	}

	@Override
	public ResponseEntity<List<DocumentTypeDto>> getOrderTypeDocumentTypes(OrderTypeDto orderType) {
		List<DocumentTypeDto> list = documentService.getDocumentTypes(orderType);
		return ResponseEntity.ok(list);
	}

	@Override
	@DocumentAccessValid
	public ResponseEntity<List<DocumentInfoDto>> getDocuments(Long orderId) {
		List<DocumentInfoDto> list = documentService.getDocuments(orderId);
		return ResponseEntity.ok(list);
	}

	@Override
	@DocumentAccessValid
	public ResponseEntity<List<DocumentGroupDto>> getDocumentsGrouped(Long orderId) {
		List<DocumentGroupDto> list = documentService.getDocumentsGrouped(orderId);
		return ResponseEntity.ok(list);
	}

	@Override
	@DocumentAccessValid
	public ResponseEntity<DocumentDownloadDto> downloadDocument(Long documentId) {
		return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(documentService.downloadDocument(documentId));
	}
}
