package com.dachser.dfe.book.order.sea;

import com.dachser.dfe.book.order.common.orderline.OrderLine;
import com.dachser.dfe.book.order.common.orderline.OrderLineHsCode;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;

public interface OrderLineCombiner<T extends OrderLine, K extends OrderLineHsCode> {

	default String combineGoods(List<T> orderLines, Function<T, List<K>> resolveHsCodes) {
		if (orderLines != null) {
			return orderLines.stream().flatMap(orderLine -> resolveHsCodes.apply(orderLine).stream()).map(K::getGoods).filter(Objects::nonNull)
					.collect(java.util.stream.Collectors.joining(" | "));
		} else {
			return "";
		}
	}

}
