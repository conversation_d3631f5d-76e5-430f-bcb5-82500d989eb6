package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.model.OrderLineDetailDto;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.math.BigDecimal;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class RoadOrderLineMapperTest {

	@Spy
	private DangerousGoodsMapper dangerousGoodsMapper;

	@InjectMocks
	private final OrderLineMapperImpl sut = new OrderLineMapperImpl();

	private final List<RoadOrderLine> orderLines = List.of(createRoadOrderLine(1L), createRoadOrderLine(2L));

	@Test
	void shouldMapSingleEntry() {
		OrderLineDetailDto mappedOrderLine = sut.mapOrderLineDetailDto(orderLines.get(0));
		assertThat(mappedOrderLine).isNotNull();
		assertThat(mappedOrderLine.getId()).isEqualTo(orderLines.get(0).getOrderLineId());
		assertThat(mappedOrderLine.getNumber()).isEqualTo(orderLines.get(0).getNumber());
		assertThat(mappedOrderLine.getQuantity()).isEqualTo(orderLines.get(0).getQuantity());
		assertThat(mappedOrderLine.getPackaging().getCode()).isEqualTo(orderLines.get(0).getPackagingType());
		assertThat(mappedOrderLine.getPackaging().getDescription()).isEqualTo(orderLines.get(0).getPackagingTypeDescription());
		assertThat(mappedOrderLine.getContent()).isEqualTo(orderLines.get(0).getContent());
		assertThat(mappedOrderLine.getLength()).isEqualTo(orderLines.get(0).getLength());
		assertThat(mappedOrderLine.getWidth()).isEqualTo(orderLines.get(0).getWidth());
		assertThat(mappedOrderLine.getHeight()).isEqualTo(orderLines.get(0).getHeight());
		assertThat(mappedOrderLine.getVolume()).isEqualTo(orderLines.get(0).getVolume().doubleValue());
		assertThat(mappedOrderLine.getWeight()).isEqualTo(orderLines.get(0).getWeight().intValue());
		assertThat(mappedOrderLine.getLoadingMeter()).isEqualTo(orderLines.get(0).getLoadingMeter().doubleValue());
		assertThat(mappedOrderLine.getGoodsGroup().getCode()).isEqualTo(orderLines.get(0).getGoodsGroup());
		assertThat(mappedOrderLine.getGoodsGroup().getQuantity()).isEqualTo(orderLines.get(0).getGoodsGroupQuantity());
	}

	@Test
	void shouldMapList() {
		List<OrderLineDetailDto> mappedOrderLines = sut.mapOrderLineDetailDtos(orderLines);
		assertThat(mappedOrderLines).isNotNull().hasSameSizeAs(orderLines);
	}

	private RoadOrderLine createRoadOrderLine(Long orderLineId) {
		RoadOrderLine roadOrderLine = new RoadOrderLine();
		roadOrderLine.setOrderLineId(orderLineId);
		roadOrderLine.setNumber(2);
		roadOrderLine.setQuantity(3);
		roadOrderLine.setPackagingType("packagingType");
		roadOrderLine.setPackagingTypeDescription("packagingTypeDescription");
		roadOrderLine.setContent("content");
		roadOrderLine.setLength(4);
		roadOrderLine.setWidth(5);
		roadOrderLine.setHeight(6);
		roadOrderLine.setVolume(BigDecimal.valueOf(7.0));
		roadOrderLine.setWeight(BigDecimal.valueOf(8L));
		roadOrderLine.setLoadingMeter(BigDecimal.valueOf(9.0));
		roadOrderLine.setGoodsGroup("goodsGroup");
		roadOrderLine.setGoodsGroupQuantity(10);
		return roadOrderLine;
	}
}