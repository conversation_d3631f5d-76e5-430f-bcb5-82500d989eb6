package com.dachser.dfe.book.order.address;

import com.dachser.dfe.book.country.CountryCode;
import com.dachser.dfe.book.model.jaxb.order.road.collection.AddressInformation;
import com.dachser.dfe.book.model.jaxb.order.road.collection.ContactInformation;
import com.dachser.dfe.book.model.jaxb.order.road.collection.PartnerInformation;
import com.dachser.dfe.book.model.jaxb.order.road.collection.ShipmentAddress;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderBaseAddress;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;
import java.util.function.Function;

public abstract class AddressTestHelper {

	public static boolean is(OrderAddress address, OrderAddress anotherAddress) {
		// @formatter:off
		return address != null
		&& anotherAddress != null
		&& (
			(address.getOrderAddressId() != null && address.getOrderAddressId().equals(anotherAddress.getOrderAddressId()))
			|| stringify(address).equals(stringify(anotherAddress))
		);
		// @formatter:on
	}

	public static String stringify(OrderFurtherAddress address) {
		ReflectionToStringBuilder builder = new ReflectionToStringBuilder(address, ToStringStyle.SHORT_PREFIX_STYLE);
		builder.setExcludeNullValues(true);
		builder.setExcludeFieldNames("order");
		return builder.toString();
	}

	public static String stringify(OrderAddress address) {
		return ReflectionToStringBuilder.toString(address, ToStringStyle.SHORT_PREFIX_STYLE);
	}

	public static String stringify(ShipmentAddress address) {
		ReflectionToStringBuilder builder = new ReflectionToStringBuilder(address, ToStringStyle.SHORT_PREFIX_STYLE);
		builder.setExcludeNullValues(true);
		builder.setExcludeFieldNames("partnerInformation");
		if (address.getPartnerInformation() != null) {
			builder.append("partnerInformation", stringify(address.getPartnerInformation()));
		}
		return builder.toString();
	}

	public static String stringify(PartnerInformation partner) {
		ReflectionToStringBuilder builder = new ReflectionToStringBuilder(partner, ToStringStyle.SHORT_PREFIX_STYLE);
		builder.setExcludeNullValues(true);
		builder.setExcludeFieldNames("addressInformation", "contactInformation");
		if (partner.getAddressInformation() != null) {
			builder.append("addressInformation", stringify(partner.getAddressInformation()));
		}
		if (partner.getContactInformation() != null && !partner.getContactInformation().isEmpty()) {
			builder.append("contactInformation", stringify(partner.getContactInformation(), AddressTestHelper::stringify));
		}
		return builder.toString();
	}

	public static String stringify(AddressInformation address) {
		return ReflectionToStringBuilder.toString(address, ToStringStyle.SHORT_PREFIX_STYLE);
	}

	public static String stringify(ContactInformation contact) {
		return ReflectionToStringBuilder.toString(contact, ToStringStyle.SHORT_PREFIX_STYLE);
	}

	public static <T> String stringify(List<T> items, Function<T, String> stringifier) {
		return StringUtils.joinWith(",", items.stream().map(stringifier).toArray());
	}

	public static OrderBaseAddress addressInKempten() {
		OrderBaseAddress address = new OrderBaseAddress();
		address.setCountryCode(CountryCode.GERMANY.getIsoCode());
		address.setCity("Kempten");
		address.setPostcode("87435");
		address.setStreet("Thomas-Dachser-Straße 2");
		address.setName("DACHSER SE");
		address.setName2("Head Office");
		return address;
	}

	public static OrderBaseAddress addressInMunich() {
		OrderBaseAddress address = new OrderBaseAddress();
		address.setCountryCode(CountryCode.GERMANY.getIsoCode());
		address.setCity("Munich");
		address.setPostcode("80336");
		return address;
	}

	public static OrderBaseAddress addressInChanverrie() {
		OrderBaseAddress address = new OrderBaseAddress();
		address.setCountryCode(CountryCode.FRANCE.getIsoCode());
		address.setCity("Chanverrie");
		address.setPostcode("85130");
		address.setStreet("1, avenue de l'Europe");
		address.setName("DACHSER SE");
		address.setName2("Corporate IT");
		address.setName3("Hub France");
		return address;
	}

	public static OrderBaseAddress addressInNantes() {
		OrderBaseAddress address = new OrderBaseAddress();
		address.setCountryCode(CountryCode.FRANCE.getIsoCode());
		address.setCity("Nantes");
		address.setPostcode("44100");
		return address;
	}

	public static OrderBaseAddress addressInVertou() {
		OrderBaseAddress address = new OrderBaseAddress();
		address.setCountryCode(CountryCode.FRANCE.getIsoCode());
		address.setCity("Vertou");
		address.setPostcode("44120");
		return address;
	}

	public static OrderBaseAddress addressInBucarest() {
		OrderBaseAddress address = new OrderBaseAddress();
		address.setCountryCode(CountryCode.ROMANIA.getIsoCode());
		address.setCity("Bucarest");
		address.setPostcode("061129");
		address.setStreet("Bd. Iuliu Maniu nr. 600A");
		address.setName("DACHSER");
		address.setName2("Romania");
		address.setName3("SRL");
		return address;
	}

}