package com.dachser.dfe.book.masterdata.airsea;

import com.dachser.dfe.airsea.masterdata.api.AirportApi;
import com.dachser.dfe.airsea.masterdata.api.AirportRoutingApi;
import com.dachser.dfe.airsea.masterdata.api.EmbargoApi;
import com.dachser.dfe.airsea.masterdata.api.SeaportApi;
import com.dachser.dfe.book.masterdata.airsea.cache.CachingAirportApi;
import com.dachser.dfe.book.masterdata.airsea.cache.CachingAirportRoutingApi;
import com.dachser.dfe.book.masterdata.airsea.cache.CachingEmbargoApi;
import com.dachser.dfe.book.masterdata.airsea.cache.CachingSeaportApi;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
class AirSeaMasterDataConfigTest {

	private AirSeaMasterDataConfig airSeaMasterDataConfig;

	@Mock
	private RestTemplate restTemplate;

	@BeforeEach
	void setUp() {
		airSeaMasterDataConfig = new AirSeaMasterDataConfig(restTemplate);
	}

	@Nested
	class EmbargoApiTest {

		@Test
		void shouldDeliverUncachedEmbargoApi() {
			// given
			airSeaMasterDataConfig.setUseCache(false);
			// when
			EmbargoApi embargoApi = airSeaMasterDataConfig.embargoApi();
			// then
			assertNotNull(embargoApi);
			assertFalse(embargoApi instanceof CachingEmbargoApi);
		}

		@Test
		void shouldDeliverCachedEmbargoApi() {
			// given
			airSeaMasterDataConfig.setUseCache(true);
			// when
			EmbargoApi embargoApi = airSeaMasterDataConfig.embargoApi();
			// then
			assertNotNull(embargoApi);
			assertInstanceOf(CachingEmbargoApi.class, embargoApi);
		}
	}

	@Nested
	class AirportApiTest {

		@Test
		void shouldDeliverUncachedAirportApi() {
			// given
			airSeaMasterDataConfig.setUseCache(false);
			// when
			AirportApi airportApi = airSeaMasterDataConfig.airportApi();
			// then
			assertNotNull(airportApi);
			assertFalse(airportApi instanceof CachingAirportApi);
		}

		@Test
		void shouldDeliverCachedAirportApi() {
			// given
			airSeaMasterDataConfig.setUseCache(true);
			// when
			AirportApi airportApi = airSeaMasterDataConfig.airportApi();
			// then
			assertNotNull(airportApi);
			assertInstanceOf(CachingAirportApi.class, airportApi);
		}
	}

	@Nested
	class AirportRoutingApiTest {

		@Test
		void shouldDeliverUncachedAirportRoutingApi() {
			// given
			airSeaMasterDataConfig.setUseCache(false);
			// when
			AirportRoutingApi airportRoutingApi = airSeaMasterDataConfig.airportRoutingApi();
			// then
			assertNotNull(airportRoutingApi);
			assertFalse(airportRoutingApi instanceof CachingAirportRoutingApi);
		}

		@Test
		void shouldDeliverCachedAirportRoutingApi() {
			// given
			airSeaMasterDataConfig.setUseCache(true);
			// when
			AirportRoutingApi airportRoutingApi = airSeaMasterDataConfig.airportRoutingApi();
			// then
			assertNotNull(airportRoutingApi);
			assertInstanceOf(CachingAirportRoutingApi.class, airportRoutingApi);
		}
	}

	@Nested
	class SeaportApiTest {

		@Test
		void shouldDeliverUncachedSeaportApi() {
			// given
			airSeaMasterDataConfig.setUseCache(false);
			// when
			SeaportApi seaportApi = airSeaMasterDataConfig.seaportApi();
			// then
			assertNotNull(seaportApi);
			assertFalse(seaportApi instanceof CachingSeaportApi);
		}

		@Test
		void shouldDeliverCachedSeaportApi() {
			// given
			airSeaMasterDataConfig.setUseCache(true);
			// when
			SeaportApi seaportApi = airSeaMasterDataConfig.seaportApi();
			// then
			assertNotNull(seaportApi);
			assertInstanceOf(CachingSeaportApi.class, seaportApi);
		}
	}


}