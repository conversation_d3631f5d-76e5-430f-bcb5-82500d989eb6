package com.dachser.dfe.book.product.road;

import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.mapper.ProductNameMapper;
import com.dachser.dfe.book.masterdata.road.RoadMasterDataApiWrapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@TestConfiguration
public class RoadProductsAdapterTestConfig {

	@Bean
	@ConditionalOnProperty(name = "dfe.book.mock.product.road", havingValue = "false")
	public RoadProductsAdapter roadProductsAdapter(ProductNameMapper productNameMapper, RoadMasterDataApiWrapper roadMasterDataApiWrapper, CountryService countryService,
			GeneralDataService generalDataService) {
		return new RoadProductsAdapterExt(productNameMapper, roadMasterDataApiWrapper, countryService, generalDataService);
	}

	@Bean
	@ConditionalOnProperty(name = "dfe.book.mock.product.road", havingValue = "true", matchIfMissing = true)
	@ConditionalOnMissingBean(RoadProductsAdapterMock.class)
	public RoadProductsAdapter roadProductsAdapterMock() {
		return new RoadProductsAdapterMock();
	}

}
