package com.dachser.dfe.book.packaging;

import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.jpa.query.QueryService;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.Segment;
import lombok.RequiredArgsConstructor;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class PackagingOptionsService {

	private final GeneralDataService generalDataService;

	private final WhiteListFilterConfiguration whiteListFilterConfiguration;

	private final QueryService queryService;

	public List<OptionDto> getPackagingOptions(Segment segment) {
		return switch (segment) {
			case ROAD -> getPackagingOptionsRoad();
			case AIR, SEA -> getPackagingOptionsAir();
		};
	}

	public List<OptionDto> getPackagingOptionsFavoritesForCustomer(String customerNumber, Segment segment) {
		List<OptionDto> packagingOptions = getPackagingOptions(segment);

		String segmentTable = null;

		if (Segment.ROAD.equals(segment)) {
			segmentTable = "order_line_road";
		} else if (Segment.AIR.equals(segment)) {
			segmentTable = "order_line_air";
		} else {
			segmentTable = "order_line_sea";
		}

		// @formatter:off
		final String query = (
				"""
				SELECT packaging_type FROM \
				(SELECT TOP 250 ol.packaging_type AS packaging_type \
				FROM order_base ob INNER JOIN %s ol \
				ON ob.order_id = ol.order_id \
				WHERE ob.customer_number = '%s' \
				AND ob.last_modified > '%s' AND packaging_type IS NOT NULL) AS T \
				GROUP BY packaging_type \
				ORDER BY COUNT(*) DESC\
				""").formatted(segmentTable, customerNumber, OffsetDateTime.now().minusDays(30));

		// @formatter:on
		final List<Object> queryResult = queryService.performNativeQuery(query);

		final List<String> favoritesCodes = queryResult.stream().map(String.class::cast).limit(3).toList();

		return favoritesCodes.stream().map(code -> packagingOptions.stream().filter(option -> option.getCode().equals(code)).findFirst()).flatMap(Optional::stream).toList();
	}

	private List<OptionDto> getPackagingOptionsAir() {
		List<OptionDto> packagingOptionsAir = generalDataService.getPackagingOptionsAir(LocaleContextHolder.getLocale().getLanguage());

		return doFilteringAir(packagingOptionsAir);

	}

	private List<OptionDto> getPackagingOptionsRoad() {
		List<OptionDto> packagingOptionsRoad = generalDataService.getPackagingOptionsRoad(LocaleContextHolder.getLocale().getLanguage());
		return doFilteringRoad(packagingOptionsRoad);
	}

	private List<OptionDto> doFilteringRoad(List<OptionDto> response) {

		return response.stream().filter(dto -> whiteListFilterConfiguration.getPackagingOptionsFilter().getRoad().contains(dto.getCode()))
				.sorted(Comparator.comparing(OptionDto::getDescription)).toList();

	}

	private List<OptionDto> doFilteringAir(List<OptionDto> options) {
		if (Objects.isNull(options)) {
			return new ArrayList<>();
		}

		return options.stream().filter(dto -> whiteListFilterConfiguration.getPackagingOptionsFilter().getAir().contains(dto.getCode()))
				.sorted(Comparator.comparing(OptionDto::getDescription)).toList();

	}

}
