package com.dachser.dfe.book.dangerousgoods;

import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.masterdata.thirdparty.model.MDTCalculationPoint;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Locale;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "true")
public class DangerousGoodsThirdPartyAdapterMock implements DangerousGoodsThirdPartyAdapter {

	private Map<String, List<DangerousGoodDataItemDto>> unMap = prepareUnNumberDataSetDtoList();

	@NotNull
	@Override
	public List<DangerousGoodDataItemDto> searchUnNumbersRoad(@NotNull String unNumber, @NotNull String countryCode) {
		if (unMap.containsKey(unNumber)) {
			return unMap.get(unNumber);
		} else {
			return List.of();
		}
	}

	@Nonnull
	@Override
	public List<DangerousGoodDataItemDto> fetchLocalizedInformationForDgmIdsRoad(@Nonnull List<String> dgmIds, @Nonnull String countryCode) {
		if (Locale.ENGLISH.getLanguage().equals(countryCode)) {
			return dgmIds.stream().map(dgmId -> new DangerousGoodDataItemDto().dgmId(dgmId).description("Description")).toList();
		} else {
			return dgmIds.stream().map(dgmId -> new DangerousGoodDataItemDto().dgmId(dgmId).description("Beschreibung")).toList();
		}
	}

	@NotNull
	@Override
	public List<DangerousGoodsTransferListService.DgmIdAndQuantity> calculatePointsForSingleValues(@NotNull String countryCode, @NotNull List<MDTCalculationPoint> points) {
		return List.of();
	}

	private Map<String, List<DangerousGoodDataItemDto>> prepareUnNumberDataSetDtoList() {
		return Map.of("1791",  List.of(
				new DangerousGoodDataItemDto().unNumber("1791").classificationCode("C9").description("HYPOCHLORITE SOLUTION").mainDanger("8").packingGroup("II"),
				new DangerousGoodDataItemDto().unNumber("1791").classificationCode("C9").description("HYPOCHLORITE SOLUTION").mainDanger("8").packingGroup("III"))
		);
	}
}
