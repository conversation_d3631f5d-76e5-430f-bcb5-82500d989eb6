package com.dachser.dfe.book.user.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderOptions {

	private Boolean finalDeliveryAddress;

	private Boolean differingInvoiceAddress;

	private Boolean deconsolidatorAddress;

	private Boolean coverAddress;

	private Boolean consigneeExportCertification;

	private Boolean pickupAddress;

	private Boolean importer;

	private Boolean notifyAddress;

	private Boolean customsAgent;

	private Boolean deliveryInstructions;

	private Boolean invoiceText;

	private Boolean otherInformations;

	private Boolean goodsDescriptions;

	private Boolean commonInstructions;

	private Boolean frostProtection;

	private Boolean palletLocation;

	private Boolean manualNumberSscc;

	private Boolean selfCollection;

	private Boolean neutralization;

	private Boolean orderNumberMandatory;

	private Boolean packagingAidPosition;

	private Boolean atILO;

	private Boolean dangerousGoods;

}
