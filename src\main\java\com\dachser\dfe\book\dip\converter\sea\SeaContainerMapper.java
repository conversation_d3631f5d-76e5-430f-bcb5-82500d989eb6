package com.dachser.dfe.book.dip.converter.sea;

import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.model.ContainerTypeDto;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigInteger;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public abstract class SeaContainerMapper {

	@Autowired
	GeneralDataService generalDataService;

	@Named("mapContainerType")
	String mapContainerType(String containerType) {
		ContainerTypeDto containerTypeForKey = generalDataService.getContainerTypeForKey(containerType);
		if (containerTypeForKey != null) {
			return containerTypeForKey.getSize().toString() + containerTypeForKey.getType();
		}
		return null;
	}

	@Named("mapSortingPosition")
	public BigInteger mapSortingPosition(Integer sortingPosition) {
		return (sortingPosition == null) ? null : BigInteger.valueOf(sortingPosition + 1L);

	}

}
