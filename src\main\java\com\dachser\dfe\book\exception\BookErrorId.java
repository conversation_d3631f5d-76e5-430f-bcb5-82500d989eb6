package com.dachser.dfe.book.exception;

import com.dachser.dfe.book.model.GeneralProblemDto;
import lombok.Getter;
import lombok.NonNull;

import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_DETAIL_AL_28;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_DETAIL_PA_01;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_GET_A_NEW_QUOTE;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_SERVICE_NOT_AVAILABLE;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_AL_01;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_AL_10;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_AL_26;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_DL_02;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_DL_03;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_DO_07;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_DO_11_DO_10;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_DO_12;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_DU_02;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_GG_01;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_KC_02;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_LB_01;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_LB_04;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_OV_02;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_PA_01;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_PF_01;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_RT_01;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_SE_01;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_SE_04;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_SV_01;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_SV_04;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_TL_01;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_UN_04;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_UN_06;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_UN_07;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_UN_09;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TITLE_VA_01;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TRY_AGAIN;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TRY_AGAIN_CREATE_ORDER;
import static com.dachser.dfe.book.exception.ProblemUtils.LABEL_TRY_AGAIN_LATER;
import static com.dachser.dfe.book.model.GeneralProblemDto.SeverityEnum.CRITICAL;
import static com.dachser.dfe.book.model.GeneralProblemDto.SeverityEnum.HIGH;
import static com.dachser.dfe.book.model.GeneralProblemDto.SeverityEnum.LOW;
import static com.dachser.dfe.book.model.GeneralProblemDto.SeverityEnum.MODERATE;

/*
 * This enum is used to define all error IDs for the book service.
 */
@Getter
public enum BookErrorId {
	// @formatter:off
	// General
	ERR_KC_02("errKC-02", LABEL_TITLE_KC_02, MODERATE, LABEL_TRY_AGAIN),
	ERR_KC_03("errKC-03", LABEL_TITLE_KC_02, MODERATE, LABEL_TRY_AGAIN),
	ERR_UN_04("errUN-04", LABEL_TITLE_UN_04, LOW, LABEL_TRY_AGAIN_LATER),
	ERR_UN_06("errUN-06", LABEL_TITLE_UN_06, MODERATE, LABEL_TRY_AGAIN),
	ERR_UN_07("errUN-07", LABEL_TITLE_UN_07, LOW, LABEL_TRY_AGAIN),
	ERR_UN_09("errUN-09", LABEL_TITLE_UN_09, CRITICAL, LABEL_TRY_AGAIN),

	// Documents
	ERR_DO_02("errDO-02", LABEL_TITLE_DO_07, HIGH, LABEL_TRY_AGAIN),
	ERR_DO_07("errDO-07", LABEL_TITLE_DO_07, HIGH, LABEL_TRY_AGAIN),
	ERR_DO_10("errDo-10", LABEL_TITLE_DO_11_DO_10, HIGH, LABEL_TRY_AGAIN ),
	ERR_DO_11("errDo-11", LABEL_TITLE_DO_11_DO_10, HIGH, LABEL_TRY_AGAIN ),
	ERR_DO_12("errDo-12", LABEL_TITLE_DO_12, MODERATE, LABEL_TRY_AGAIN ),

	// Address
	ERR_AL_01("errAL-01", LABEL_TITLE_AL_01, MODERATE, null),
	ERR_AL_10("errAL-10", LABEL_TITLE_AL_10, HIGH, LABEL_TRY_AGAIN),
	ERR_AL_21("errAL-21", LABEL_SERVICE_NOT_AVAILABLE, LOW, LABEL_TRY_AGAIN),
	ERR_AL_26("errAL-26", LABEL_TITLE_AL_26, HIGH, null),
	ERR_AL_27("errAL-27", LABEL_SERVICE_NOT_AVAILABLE, LOW, null),
	ERR_AL_28("errAL-28", LABEL_TRY_AGAIN_LATER, HIGH, LABEL_DETAIL_AL_28),

	// Order
	ERR_PA_01("errPA-01", LABEL_TITLE_PA_01, HIGH, LABEL_DETAIL_PA_01),
	ERR_PA_02("errPA-02", LABEL_TITLE_PA_01, HIGH, LABEL_DETAIL_PA_01),
	ERR_PA_03("errPA-03", LABEL_SERVICE_NOT_AVAILABLE, MODERATE, LABEL_TRY_AGAIN_LATER),
	ERR_PD_01("errPD-01", LABEL_SERVICE_NOT_AVAILABLE, HIGH, LABEL_TRY_AGAIN_LATER),
	ERR_PD_03("errPD-03", LABEL_SERVICE_NOT_AVAILABLE, MODERATE, LABEL_SERVICE_NOT_AVAILABLE),
	ERR_DL_02("errDL-02", LABEL_TITLE_DL_02, HIGH, LABEL_TRY_AGAIN),
	ERR_DL_03("errDL-03", LABEL_TITLE_DL_03, HIGH, null),

	ERR_SV_01("errSV-01", LABEL_TITLE_SV_01, HIGH, LABEL_TRY_AGAIN),
	ERR_SV_04("errSV-04", LABEL_TITLE_SV_04, HIGH, LABEL_GET_A_NEW_QUOTE),

	ERR_SE_01("errSE-01", LABEL_TITLE_SE_01, HIGH, LABEL_TRY_AGAIN),
	ERR_SE_04("errSE-04", LABEL_TITLE_SE_04, HIGH, LABEL_GET_A_NEW_QUOTE),
	// A Placeholder for later use - DFE-2557 - Save Error,

	ERR_LB_01("errLB-01", LABEL_TITLE_LB_01, HIGH, LABEL_TRY_AGAIN_LATER),
	ERR_LB_02("errLB-02", LABEL_SERVICE_NOT_AVAILABLE, MODERATE, null),
	ERR_LB_04("errLB-04", LABEL_TITLE_LB_04, HIGH, LABEL_GET_A_NEW_QUOTE),

	ERR_RT_01("errRT-01", LABEL_TITLE_RT_01, HIGH, LABEL_TRY_AGAIN),

	// PDF Export
	ERR_PF_01("errPF-01", LABEL_TITLE_PF_01, HIGH, LABEL_TRY_AGAIN),

	// Order Overview
	ERR_OV_02("errOV-02", LABEL_TITLE_OV_02, HIGH, LABEL_TRY_AGAIN),

	// Duplication
	ERR_DU_02("errDU-02", LABEL_TITLE_DU_02, MODERATE, LABEL_TRY_AGAIN_CREATE_ORDER),
	
	// Delivery
	ERR_DV_01("errDV-01", LABEL_SERVICE_NOT_AVAILABLE, HIGH, LABEL_TRY_AGAIN_LATER),

	// Goods group
	ERR_GG_01("errGG-01", LABEL_TITLE_GG_01, HIGH, null),

	// Order groups
	ERR_OG_01("errOG-01", LABEL_TITLE_GG_01, MODERATE, null),

	// References
	ERR_RF_01("errRF-01", LABEL_SERVICE_NOT_AVAILABLE, LOW, null),

	// Freight Term
	ERR_TE_05("errTE-05", LABEL_SERVICE_NOT_AVAILABLE, HIGH, LABEL_TRY_AGAIN_LATER),

	// Validation e.g. Forwarding Domain Service
	ERR_VA_01("errVA-01", LABEL_TITLE_VA_01, HIGH, LABEL_TRY_AGAIN),

	// Transfer List
	ERR_TL_01("errTL-01", LABEL_TITLE_TL_01, HIGH, LABEL_TRY_AGAIN);
	// @formatter:on

	/**
	 * @see <a href="https://dil-itd.atlassian.net/wiki/spaces/10172/pages/38864545/Error+handling+cross-team+analysis">Error Id Documentation</a>
	 */
	@NonNull
	private final String errorId;

	/**
	 * Some not localized error title.
	 */
	@NonNull
	private final String errorTitle;

	@NonNull
	private final GeneralProblemDto.SeverityEnum severity;

	private final String i18nDetailsLabel;

	BookErrorId(@NonNull String errorId, @NonNull String errorTitle, @NonNull GeneralProblemDto.SeverityEnum severity, String i18nDetailsLabel) {
		this.errorId = errorId;
		this.errorTitle = errorTitle;
		this.severity = severity;
		this.i18nDetailsLabel = i18nDetailsLabel;
	}
}
