package com.dachser.dfe.book.order.emissionforecast;

import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.orderline.OrderLine;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.emissionforecast.model.EFAirLocationDto;
import com.dachser.dfe.emissionforecast.model.EFCarriageTypeDto;
import com.dachser.dfe.emissionforecast.model.EFDivisionDto;
import com.dachser.dfe.emissionforecast.model.EFForecastRequestDto;
import com.dachser.dfe.emissionforecast.model.EFRoadLocationDto;
import com.dachser.dfe.emissionforecast.model.EFSeaLocationDto;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Context;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, imports = { StringUtils.class, OrderLine.class, EFDivisionDto.class })
public interface EmissionForecastMapper {

	@Mapping(target = "mainCarriage", expression = "java(mapToEFCarriageTypeDto(segment))")
	@Mapping(target = "weight", source = "order.totalOrderWeight", qualifiedByName = "mapToDouble")
	@Mapping(target = "division", source = "division")
	void commonMappings(Order order, String division, @Context Segment segment, @MappingTarget EFForecastRequestDto dto);

	@InheritConfiguration(name = "commonMappings")
	@Mapping(target = "departure.roadLocation", source = "order.shipperAddress")
	@Mapping(target = "destination.roadLocation", source = "order.consigneeAddress")
	EFForecastRequestDto mapRoad(final RoadOrder order, String division, @Context Segment segment);

	@InheritConfiguration(name = "commonMappings")
	@Mapping(target = "departure.airLocation", source = "order.fromIATA")
	@Mapping(target = "destination.airLocation", source = "order.toIATA")
	EFForecastRequestDto mapAir(final AirOrder order, String division, @Context Segment segment);

	@InheritConfiguration(name = "commonMappings")
	@Mapping(target = "departure.seaLocation", source = "order.fromPort")
	@Mapping(target = "destination.seaLocation", source = "order.toPort")
	EFForecastRequestDto mapSea(final SeaOrder order, String division, @Context Segment segment);

	@Mapping(target = "iataCode", source = "airport")
	EFAirLocationDto mapToAirLocation(final String airport);

	@Mapping(target = "locode", source = "seaport")
	EFSeaLocationDto mapToSeaLocation(final String seaport);

	@Mapping(target = "postCode", source = "postcode")
	EFRoadLocationDto mapToRoadLocation(final OrderAddress address);

	default EFForecastRequestDto mapToEFForecastRequestDto(Order order, String division, @Context Segment segment) {
		return switch (segment) {
			case AIR -> mapAir((AirOrder) order, division, segment);
			case SEA -> mapSea((SeaOrder) order, division, segment);
			default -> mapRoad((RoadOrder) order, division, segment);
		};
	}

	@Named("mapToDouble")
	default Double mapToDouble(Number value) {
		return value == null ? null : value.doubleValue();
	}

	@Named("mapToEFCarriageTypeDto")
	EFCarriageTypeDto mapToEFCarriageTypeDto(final Segment segment);

}
