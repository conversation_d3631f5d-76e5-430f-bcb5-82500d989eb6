package com.dachser.dfe.book.order.common.fcl;

import com.dachser.dfe.book.order.validation.sea.CompleteOrderValidationSea;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@MappedSuperclass
public class BaseFullContainerLoad {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@NotNull(groups = CompleteOrderValidationSea.class, message = "{label.text.invalid_input}")
	private String containerType;

	@Pattern(regexp = "^$|^(?:[A-Z]{3}[UJZ])[0-9]{6}[0-9]$", groups = CompleteOrderValidationSea.class, message = "{label.text.invalid_input}")
	private String containerNumber;

	private Integer verifiedGrossMass;

	@NotNull
	private Integer sortingPosition;

}
