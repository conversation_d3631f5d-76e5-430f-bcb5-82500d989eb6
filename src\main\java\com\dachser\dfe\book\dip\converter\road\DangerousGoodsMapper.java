package com.dachser.dfe.book.dip.converter.road;

import com.dachser.dfe.book.order.common.dangerousgood.ADRDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.EQDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.LQDangerousGood;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.math.BigDecimal;
import java.util.List;

public interface DangerousGoodsMapper<D, W> {

	String UNIT_KILOGRAM = "KGM";

	default D map(DangerousGood dangerousGood) {
		if (dangerousGood instanceof ADRDangerousGood adrDangerousGood) {
			return mapADR(adrDangerousGood);
		} else if (dangerousGood instanceof LQDangerousGood lqDangerousGood) {
			return mapLQ(lqDangerousGood);
		} else if (dangerousGood instanceof EQDangerousGood eqDangerousGood) {
			return mapEQ(eqDangerousGood);
		}
		throw new IllegalArgumentException("Unsupported Dangerous Good type: " + dangerousGood.getClass().getName());
	}

	@Mapping(target = "clazz", source = "dangerousGoodDataItem.mainDanger")
	@Mapping(target = "technicalName", source = "dangerousGoodDataItem.description")
	@Mapping(target = "UNDGnumber", source = "dangerousGoodDataItem.unNumber")
	@Mapping(target = "NOS", source = "dangerousGoodDataItem.nosRequired", qualifiedByName = "mapBoolean")
	@Mapping(target = "mainDangerNOS", source = "nos")
	@Mapping(target = "ADRWeight", source = "grossMass")
	@Mapping(target = "packagesQuantity", source = "noOfPackages")
	@Mapping(target = "packingType", source = "packagingKey")
	@Mapping(target = "packingGroup", source = "dangerousGoodDataItem.packingGroup")
	@Mapping(target = "classificationCode", source = "dangerousGoodDataItem.classificationCode")
	@Mapping(target = "secondaryDanger", source = "dangerousGoodDataItem.subsidiaryHazardOne")
	@Mapping(target = "environmentalIndicator", source = "environmentallyHazardous", qualifiedByName = "mapBoolean")
	D mapADR(ADRDangerousGood adrDangerousGood);

	@Mapping(target = "type", source = "dangerousGoodType.ediType")
	@Mapping(target = "ADRWeight", source = "grossMass")
	@Mapping(target = "UNDGnumber", source = "dangerousGoodDataItem.unNumber")
	D mapLQ(LQDangerousGood adrDangerousGood);

	@Mapping(target = "type", source = "dangerousGoodType.ediType")
	@Mapping(target = "packagesQuantity", source = "noOfPackages")
	@Mapping(target = "packingType", source = "packagingKey")
	D mapEQ(EQDangerousGood adrDangerousGood);

	List<D> map(List<DangerousGood> dangerousGoods);

	@Mapping(target = "measurement.unit", constant = UNIT_KILOGRAM)
	@Mapping(target = "measurement.value", source = "weight")
	W mapWeight(BigDecimal weight);

	@Named("mapBoolean")
	default String mapBoolean(Boolean bool) {
		if (bool != null && bool) {
			return "Y";
		} else {
			return "N";
		}
	}
}
