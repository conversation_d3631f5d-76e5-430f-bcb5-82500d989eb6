package com.dachser.dfe.book.quote;

import com.dachser.dfe.book.model.AirExportQuoteInformationDto;
import com.dachser.dfe.book.model.AirImportQuoteInformationDto;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.BasicQuoteInformationDto;
import com.dachser.dfe.book.model.OrderTypeDto;
import com.dachser.dfe.book.model.PackingPositionDto;
import com.dachser.dfe.book.model.RoadCollectionQuoteInformationDto;
import com.dachser.dfe.book.model.RoadForwardingQuoteInformationDto;
import com.dachser.dfe.book.model.SeaExportQuoteInformationDto;
import com.dachser.dfe.book.model.SeaImportQuoteInformationDto;
import com.dachser.dfe.book.model.SourceOfOrder;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.air.AirOrderLine;
import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.order.mapper.DeliveryOptionMapper;
import com.dachser.dfe.book.order.mapper.OrderAddressMapper;
import com.dachser.dfe.book.order.mapper.OrderLineMapper;
import com.dachser.dfe.book.order.mapper.OrderReferenceMapper;
import com.dachser.dfe.book.order.mapper.OrderTypeMapper;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaImportOrder;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.book.order.sea.SeaOrderLine;
import com.dachser.dfe.book.port.PortMapper;
import com.dachser.dfe.book.product.air.AirProductService;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.AfterMapping;
import org.mapstruct.BeforeMapping;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.SubclassExhaustiveStrategy;
import org.mapstruct.SubclassMapping;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Mapper(componentModel = "spring", subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION, uses = { OrderTypeMapper.class, OrderLineMapper.class,
		OrderReferenceMapper.class,
		OrderAddressMapper.class, PortMapper.class, DeliveryOptionMapper.class, QuotePackingPositionMapper.class }, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
@Slf4j
public abstract class QuoteMapper {

	@Autowired
	private AirProductService airProductService;

	@SubclassMapping(source = RoadForwardingQuoteInformationDto.class, target = RoadQuoteInformation.class)
	@SubclassMapping(source = RoadCollectionQuoteInformationDto.class, target = RoadQuoteInformation.class)
	@SubclassMapping(source = AirExportQuoteInformationDto.class, target = AirQuoteInformation.class)
	@SubclassMapping(source = AirImportQuoteInformationDto.class, target = AirQuoteInformation.class)
	@SubclassMapping(source = SeaExportQuoteInformationDto.class, target = SeaQuoteInformation.class)
	@SubclassMapping(source = SeaImportQuoteInformationDto.class, target = SeaQuoteInformation.class)
	public abstract QuoteInformation mapQuoteInfo(BasicQuoteInformationDto quoteInformation);

	@InheritInverseConfiguration(name = "mapQuoteInfo")
	public BasicQuoteInformationDto mapQuoteInfo(QuoteInformation quoteInformation) {
		if (quoteInformation == null) {
			return null;
		}
		return switch (quoteInformation.getOrderType()) {
			case ROADCOLLECTIONORDER -> mapCollectionQuoteInfo((RoadQuoteInformation) quoteInformation);
			case ROADFORWARDINGORDER -> mapForwardingQuoteInfo((RoadQuoteInformation) quoteInformation);
			case AIREXPORTORDER -> mapAirExportQuoteInfo((AirQuoteInformation) quoteInformation);
			case AIRIMPORTORDER -> mapAirImportQuoteInfo((AirQuoteInformation) quoteInformation);
			case SEAEXPORTORDER -> mapSeaExportQuoteInfo((SeaQuoteInformation) quoteInformation);
			case SEAIMPORTORDER -> mapSeaImportQuoteInfo((SeaQuoteInformation) quoteInformation);
		};
	}

	@Mapping(target = "collectionFrom", source = ".", qualifiedByName = "mapCollectionFrom")
	@Mapping(target = "collectionTo", source = ".", qualifiedByName = "mapCollectionTo")
	@Mapping(target = "deliveryOption", source = "deliveryOption", defaultValue = "NO")
	@Mapping(target = "cashOnDelivery", expression = "java(quoteInformation.getCashOnDeliveryAmount() != null)")
	public abstract RoadQuoteInformation mapRoadQuoteInfo(RoadForwardingQuoteInformationDto quoteInformation);

	@Mapping(target = "collectionFrom", source = ".", qualifiedByName = "mapCollectionFrom")
	@Mapping(target = "collectionTo", source = ".", qualifiedByName = "mapCollectionTo")
	@Mapping(target = "deliveryOption", source = "deliveryOption", defaultValue = "NO")
	@Mapping(target = "cashOnDeliveryAmount", ignore = true)
	public abstract RoadQuoteInformation mapRoadQuoteInfo(RoadCollectionQuoteInformationDto quoteInformation);

	@InheritInverseConfiguration(name = "mapRoadQuoteInfo")
	abstract RoadForwardingQuoteInformationDto mapForwardingQuoteInfo(RoadQuoteInformation roadQuoteInformation);

	@InheritInverseConfiguration(name = "mapRoadQuoteInfo")
	abstract RoadCollectionQuoteInformationDto mapCollectionQuoteInfo(RoadQuoteInformation roadQuoteInformation);

	@Mapping(target = "fromIATA", source = "fromIATA.code", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "toIATA", source = "toIATA.code", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "collectionFrom", source = ".", qualifiedByName = "mapCollectionFrom")
	@Mapping(target = "collectionTo", source = ".", qualifiedByName = "mapCollectionTo")
	public abstract AirQuoteInformation mapAirQuoteInfo(AirExportQuoteInformationDto quoteInformation);

	@Mapping(target = "fromIATA", source = "fromIATA.code", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "toIATA", source = "toIATA.code", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "collectionFrom", source = ".", qualifiedByName = "mapCollectionFrom")
	@Mapping(target = "collectionTo", source = ".", qualifiedByName = "mapCollectionTo")
	public abstract AirQuoteInformation mapAirQuoteInfo(AirImportQuoteInformationDto quoteInformation);

	@InheritInverseConfiguration(name = "mapAirQuoteInfo")
	abstract AirExportQuoteInformationDto mapAirExportQuoteInfo(AirQuoteInformation airQuoteInformation);

	@InheritInverseConfiguration(name = "mapAirQuoteInfo")
	abstract AirImportQuoteInformationDto mapAirImportQuoteInfo(AirQuoteInformation airQuoteInformation);

	@Mapping(target = "fromPort", source = "fromPort.code", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "toPort", source = "toPort.code", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "collectionFrom", source = ".", qualifiedByName = "mapCollectionFrom")
	@Mapping(target = "collectionTo", source = ".", qualifiedByName = "mapCollectionTo")
	public abstract SeaQuoteInformation mapSeaQuoteInfo(SeaExportQuoteInformationDto quoteInformation);

	@Mapping(target = "fromPort", source = "fromPort.code", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "toPort", source = "toPort.code", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "collectionFrom", source = ".", qualifiedByName = "mapCollectionFrom")
	@Mapping(target = "collectionTo", source = ".", qualifiedByName = "mapCollectionTo")
	public abstract SeaQuoteInformation mapSeaQuoteInfo(SeaImportQuoteInformationDto quoteInformation);

	@InheritInverseConfiguration(name = "mapSeaQuoteInfo")
	abstract SeaExportQuoteInformationDto mapSeaExportQuoteInfo(SeaQuoteInformation seaQuoteInformation);

	@InheritInverseConfiguration(name = "mapSeaQuoteInfo")
	abstract SeaImportQuoteInformationDto mapSeaImportQuoteInfo(SeaQuoteInformation seaQuoteInformation);

	public Order mapToOrder(BasicQuoteInformationDto quoteInformation) {
		return switch (quoteInformation.getOrderType()) {
			case ROAD_FORWARDING_ORDER, ROAD_COLLECTION_ORDER -> mapRoad(quoteInformation);
			case AIR_EXPORT_ORDER, AIR_IMPORT_ORDER -> mapAir(quoteInformation);
			case SEA_EXPORT_ORDER, SEA_IMPORT_ORDER -> mapSea(quoteInformation);
		};
	}

	public RoadOrder mapRoad(BasicQuoteInformationDto roadQuoteInformation) {
		return OrderTypeDto.ROAD_FORWARDING_ORDER.equals(roadQuoteInformation.getOrderType()) ?
				mapForwardingOrder((RoadForwardingQuoteInformationDto) roadQuoteInformation) :
				mapCollectionOrder((RoadCollectionQuoteInformationDto) roadQuoteInformation);
	}

	public AirOrder mapAir(BasicQuoteInformationDto airQuoteInformation) {
		return OrderTypeDto.AIR_EXPORT_ORDER.equals(airQuoteInformation.getOrderType()) ?
				mapAirExportOrder((AirExportQuoteInformationDto) airQuoteInformation) :
				mapAirImportOrder((AirImportQuoteInformationDto) airQuoteInformation);
	}

	public SeaOrder mapSea(BasicQuoteInformationDto seaQuoteInformation) {
		return OrderTypeDto.SEA_EXPORT_ORDER.equals(seaQuoteInformation.getOrderType()) ?
				mapSeaExportOrder((SeaExportQuoteInformationDto) seaQuoteInformation) :
				mapSeaImportOrder((SeaImportQuoteInformationDto) seaQuoteInformation);
	}

	@Mapping(target = "quoteInformation", source = ".")
	@Mapping(target = "orderExpiryDate", source = "quoteExpiryDate")
	@Mapping(target = "orderLines", source = "orderLineItems")
	@Mapping(target = "incoTerm", source = "termCode")
	@Mapping(target = "productCode", source = "product")
	@Mapping(target = "productName", source = ".", qualifiedByName = "mapExportProductName")
	@Mapping(target = "currency", source = "goodsCurrency", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "fromIATA", expression = "java(airQuoteInformation.getFromIATA() == null ? null : airQuoteInformation.getFromIATA().getCode())")
	@Mapping(target = "toIATA", expression = "java(airQuoteInformation.getToIATA() == null ? null : airQuoteInformation.getToIATA().getCode())")
	@Mapping(target = "deliverToAirport", constant = "false")
	@Mapping(target = "collectFromAirport", source = ".", qualifiedByName = "mapCollectFromAirport")
	@Mapping(target = "collectionFrom", source = ".", qualifiedByName = "mapCollectionFrom")
	@Mapping(target = "collectionTo", source = ".", qualifiedByName = "mapCollectionTo")
	public abstract AirExportOrder mapAirExportOrder(AirExportQuoteInformationDto airQuoteInformation);

	@Mapping(target = "quoteInformation", source = ".")
	@Mapping(target = "orderExpiryDate", source = "quoteExpiryDate")
	@Mapping(target = "orderLines", source = "orderLineItems")
	@Mapping(target = "incoTerm", source = "termCode")
	@Mapping(target = "productCode", source = "product")
	@Mapping(target = "productName", source = ".", qualifiedByName = "mapImportProductName")
	@Mapping(target = "currency", source = "goodsCurrency", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "fromIATA", expression = "java(airQuoteInformation.getFromIATA() == null ? null : airQuoteInformation.getFromIATA().getCode())")
	@Mapping(target = "toIATA", expression = "java(airQuoteInformation.getToIATA() == null ? null : airQuoteInformation.getToIATA().getCode())")
	@Mapping(target = "deliverToAirport", source = ".", qualifiedByName = "mapDeliverToAirport")
	@Mapping(target = "collectFromAirport", constant = "false")
	@Mapping(target = "collectionFrom", source = ".", qualifiedByName = "mapCollectionFrom")
	@Mapping(target = "collectionTo", source = ".", qualifiedByName = "mapCollectionTo")
	public abstract AirImportOrder mapAirImportOrder(AirImportQuoteInformationDto airQuoteInformation);

	@Mapping(target = "quoteInformation", source = ".")
	@Mapping(target = "orderExpiryDate", source = "quoteExpiryDate")
	@Mapping(target = "orderLines", source = "orderLineItems")
	@Mapping(target = "product", source = "product")
	@Mapping(target = "freightTerm", source = "termCode")
	@Mapping(target = "currency", source = "goodsCurrency", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "collectionFrom", source = ".", qualifiedByName = "mapCollectionFrom")
	@Mapping(target = "collectionTo", source = ".", qualifiedByName = "mapCollectionTo")
	@Mapping(target = "tailLiftDelivery", source = "tailLift", defaultValue = "false")
	@Mapping(target = "frostProtection", source = "frostProtected", defaultValue = "false")
	@Mapping(target = "selfCollection", source = "selfCollector", defaultValue = "false")
	@Mapping(target = "deliveryOption", source = "deliveryOption", defaultValue = "NO")
	public abstract ForwardingOrder mapForwardingOrder(RoadForwardingQuoteInformationDto roadQuoteInformation);

	@Mapping(target = "quoteInformation", source = ".")
	@Mapping(target = "orderExpiryDate", source = "quoteExpiryDate")
	@Mapping(target = "orderLines", source = "orderLineItems")
	@Mapping(target = "product", source = "product")
	@Mapping(target = "freightTerm", source = "termCode")
	@Mapping(target = "currency", source = "goodsCurrency", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "collectionFrom", source = ".", qualifiedByName = "mapCollectionFrom")
	@Mapping(target = "collectionTo", source = ".", qualifiedByName = "mapCollectionTo")
	@Mapping(target = "tailLiftDelivery", source = "tailLift", defaultValue = "false")
	@Mapping(target = "frostProtection", source = "frostProtected", defaultValue = "false")
	@Mapping(target = "deliveryOption", source = "deliveryOption", defaultValue = "NO")
	public abstract CollectionOrder mapCollectionOrder(RoadCollectionQuoteInformationDto roadQuoteInformation);

	@Mapping(target = "quoteInformation", source = ".")
	@Mapping(target = "orderExpiryDate", source = "quoteExpiryDate")
	@Mapping(target = "orderLines", source = "orderLineItems")
	@Mapping(target = "incoTerm", source = "termCode")
	@Mapping(target = "currency", source = "goodsCurrency", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "fromPort", expression = "java(seaQuoteInformation.getFromPort() == null ? null : seaQuoteInformation.getFromPort().getCode())")
	@Mapping(target = "toPort", expression = "java(seaQuoteInformation.getToPort() == null ? null : seaQuoteInformation.getToPort().getCode())")
	@Mapping(target = "collectionFrom", source = ".", qualifiedByName = "mapCollectionFrom")
	@Mapping(target = "collectionTo", source = ".", qualifiedByName = "mapCollectionTo")
	public abstract SeaExportOrder mapSeaExportOrder(SeaExportQuoteInformationDto seaQuoteInformation);

	@Mapping(target = "quoteInformation", source = ".")
	@Mapping(target = "orderExpiryDate", source = "quoteExpiryDate")
	@Mapping(target = "orderLines", source = "orderLineItems")
	@Mapping(target = "incoTerm", source = "termCode")
	@Mapping(target = "currency", source = "goodsCurrency", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "fromPort", expression = "java(seaQuoteInformation.getFromPort() == null ? null : seaQuoteInformation.getFromPort().getCode())")
	@Mapping(target = "toPort", expression = "java(seaQuoteInformation.getToPort() == null ? null : seaQuoteInformation.getToPort().getCode())")
	@Mapping(target = "collectionFrom", source = ".", qualifiedByName = "mapCollectionFrom")
	@Mapping(target = "collectionTo", source = ".", qualifiedByName = "mapCollectionTo")
	public abstract SeaImportOrder mapSeaImportOrder(SeaImportQuoteInformationDto seaQuoteInformation);

	@Mapping(target = "orderLineItems", source = "orderLines")
	@Mapping(target = "quoteExpiryDate", source = "orderExpiryDate")
	@Mapping(target = "product", source = "productCode")
	@Mapping(target = "termCode", source = "incoTerm")
	public abstract AirQuoteInformation mapAirQuoteInfoFromOrder(AirOrder airOrder);

	@Mapping(target = "orderLineItems", source = "orderLines")
	@Mapping(target = "quoteExpiryDate", source = "orderExpiryDate")
	@Mapping(target = "termCode", source = "freightTerm")
	@Mapping(target = "tailLift", source = "tailLiftDelivery", defaultValue = "false")
	@Mapping(target = "selfCollector", source = ".", qualifiedByName = "mapSelfCollection")
	@Mapping(target = "frostProtected", source = "frostProtection", defaultValue = "false")
	@Mapping(target = "deliveryOption", source = "deliveryOption", defaultValue = "NO")
	public abstract RoadQuoteInformation mapRoadQuoteInfoFromOrder(RoadOrder roadOrder);

	public RoadQuoteInformation mapForwardingRoadQuoteInfoFromOrder(ForwardingOrder roadOrder) {
		RoadQuoteInformation roadQuoteInformation = mapRoadQuoteInfoFromOrder(roadOrder);
		if (roadOrder.getPalletLocations() != null) {
			roadQuoteInformation.setPalletLocations(roadOrder.getPalletLocations());
		}
		return roadQuoteInformation;
	}

	@Mapping(target = "orderLineItems", source = "orderLines")
	@Mapping(target = "quoteExpiryDate", source = "orderExpiryDate")
	@Mapping(target = "termCode", source = "incoTerm")
	public abstract SeaQuoteInformation mapSeaQuoteInfoFromOrder(SeaOrder seaOrder);

	@Mapping(source = "packagingType.code", target = "packagingType")
	@Mapping(source = "packagingType.description", target = "packagingTypeDescription")
	@Mapping(source = "lines", target = "orderLines")
	abstract PackingPosition mapPackingPosition(PackingPositionDto packagingPosition);

	@BeforeMapping
	public void fillNullCollections(@MappingTarget final Order basicOrder, BasicQuoteInformationDto quoteInformation) {
		if (basicOrder == null) {
			return;
		}
		if (basicOrder.getAddresses() == null) {
			basicOrder.setAddresses(new ArrayList<>());
		}
		if (basicOrder.getOrderTexts() == null) {
			basicOrder.setOrderTexts(new ArrayList<>());
		}
	}

	@AfterMapping
	public void setQuoteDataSource(@MappingTarget Order order, BasicQuoteInformationDto quoteInformation) {
		order.setDatasource(SourceOfOrder.QUOTE);
	}

	@AfterMapping
	public void updateQuoteOrderLines(@MappingTarget final AirQuoteInformation quoteInformation) {
		final List<QuoteOrderLine> orderLines = quoteInformation.getOrderLineItems();
		if (orderLines != null) {
			orderLines.forEach(line -> line.setQuoteInformation(quoteInformation));
		}
	}

	@AfterMapping
	public void updateQuoteOrderLines(@MappingTarget final RoadQuoteInformation quoteInformation) {
		final List<QuoteOrderLine> orderLines = quoteInformation.getOrderLineItems();
		if (orderLines != null) {
			orderLines.forEach(line -> line.setQuoteInformation(quoteInformation));
		}

		final List<QuotePackingPosition> packingPositions = quoteInformation.getPackingPositions();
		if (packingPositions != null) {
			packingPositions.forEach(position -> position.setQuoteInformation(quoteInformation));
		}
	}

	@AfterMapping
	public void updateQuoteOrderLines(@MappingTarget final SeaQuoteInformation quoteInformation) {
		final List<QuoteOrderLine> orderLines = quoteInformation.getOrderLineItems();
		if (orderLines != null) {
			orderLines.forEach(line -> line.setQuoteInformation(quoteInformation));
		}
	}

	@AfterMapping
	public void updateOrderLinesAir(@MappingTarget final AirOrder order, BasicQuoteInformationDto quoteInformation) {
		final List<AirOrderLine> orderLines = order.getOrderLines();
		if (orderLines != null) {
			orderLines.forEach(line -> {
				line.setOrder(order);

				if (line.getHsCodes() != null) {
					line.getHsCodes().forEach(hsCode -> hsCode.setOrderLine(line));
				}
			});
		}
	}

	@AfterMapping
	public void updateOrderLinesSea(@MappingTarget final SeaOrder order, BasicQuoteInformationDto quoteInformation) {
		final List<SeaOrderLine> orderLines = order.getOrderLines();
		if (orderLines != null) {
			orderLines.forEach(line -> {
				line.setOrder(order);

				if (line.getHsCodes() != null) {
					line.getHsCodes().forEach(hsCode -> hsCode.setOrderLine(line));
				}
			});
		}
	}

	@AfterMapping
	public void updateOrderLinesRoad(@MappingTarget final RoadOrder order, BasicQuoteInformationDto quoteInformation) {
		final List<RoadOrderLine> orderLines = order.getOrderLines();
		if (orderLines != null) {
			orderLines.forEach(line -> line.setOrder(order));
		}

		final List<PackingPosition> packingPositions = order.getPackingPositions();
		if (packingPositions != null) {
			packingPositions.forEach(position -> position.setOrder(order));
		}
	}

	@Named("mapCollectionFrom")
	public OffsetDateTime mapCollectionFrom(BasicQuoteInformationDto quoteInformation) {
		if (quoteInformation.getFrom() == null) {
			return OffsetDateTime.of(quoteInformation.getCollectionDate(), LocalTime.of(8, 0), ZoneOffset.UTC);
		}
		return quoteInformation.getFrom();
	}

	@Named("mapCollectionTo")
	public OffsetDateTime mapCollectionTo(BasicQuoteInformationDto quoteInformation) {
		if (quoteInformation.getTo() == null) {
			return OffsetDateTime.of(quoteInformation.getCollectionDate(), LocalTime.of(16, 0), ZoneOffset.UTC);
		}
		return quoteInformation.getTo();
	}

	@Named("mapSelfCollection")
	public boolean mapSelfCollection(RoadOrder roadOrder) {
		return roadOrder instanceof ForwardingOrder forwardingOrder && forwardingOrder.getSelfCollection();
	}

	@Named("mapCollectFromAirport")
	public boolean mapCollectFromAirport(AirExportQuoteInformationDto quoteInformation) {
		final boolean hasToIATA = quoteInformation.getToIATA() != null;
		final boolean hasConsigneeAddress = quoteInformation.getConsigneeAddress() != null;
		return hasToIATA && !hasConsigneeAddress;
	}

	@Named("mapDeliverToAirport")
	public boolean mapDeliverToAirport(AirImportQuoteInformationDto quoteInformation) {
		final boolean hasFromIATA = quoteInformation.getFromIATA() != null;
		final boolean hasShipperAddress = quoteInformation.getShipperAddress() != null;
		return hasFromIATA && !hasShipperAddress;
	}

	@Named("mapExportProductName")
	public String mapExportProductName(AirExportQuoteInformationDto quoteInformation) {
		return mapAirProductNameFromRepository(quoteInformation);
	}

	@Named("mapImportProductName")
	public String mapImportProductName(AirImportQuoteInformationDto quoteInformation) {
		return mapAirProductNameFromRepository(quoteInformation);
	}

	private String mapAirProductNameFromRepository(BasicQuoteInformationDto quoteInformation) {
		String productCode;
		if (quoteInformation instanceof AirImportQuoteInformationDto importQuoteInformation) {
			productCode = importQuoteInformation.getProduct();
		} else if (quoteInformation instanceof AirExportQuoteInformationDto exportQuoteInformation) {
			productCode = exportQuoteInformation.getProduct();
		} else {
			log.warn("Invalid quote information type {} for AirProduct", quoteInformation.getClass());
			return null;
		}

		if (productCode == null) {
			return null;
		}

		final Optional<AirProductDto> optionalAirProduct = airProductService.getAirProductByProductCode(productCode);
		if (optionalAirProduct.isEmpty()) {
			log.warn("Product with code {} not found in repository", productCode);
			return null;
		}

		return optionalAirProduct.get().getDescription();
	}

	public void buildQuoteInformationForOrder(final Order order) {
		if (order instanceof AirOrder airOrder) {
			AirQuoteInformation mapped = mapAirQuoteInfoFromOrder(airOrder);
			airOrder.setQuoteInformation(mapped);
		} else if (order instanceof CollectionOrder roadOrder) {
			RoadQuoteInformation mapped = mapRoadQuoteInfoFromOrder(roadOrder);
			roadOrder.setQuoteInformation(mapped);
		}
		else if (order instanceof ForwardingOrder roadOrder) {
				RoadQuoteInformation mapped = mapForwardingRoadQuoteInfoFromOrder(roadOrder);
				roadOrder.setQuoteInformation(mapped);
		} else if (order instanceof SeaOrder seaOrder) {
			SeaQuoteInformation mapped = mapSeaQuoteInfoFromOrder(seaOrder);
			seaOrder.setQuoteInformation(mapped);
		}
	}

}