package com.dachser.dfe.book.user;

import com.dachser.dfe.book.customer.SegmentMapper;
import com.dachser.dfe.book.exception.BranchUnresolvableException;
import com.dachser.dfe.book.exception.DivisionUnresolvableException;
import com.dachser.dfe.book.exception.generic.NotAuthenticatedException;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.CustomerDto;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.product.road.ShipperMasterdataService;
import com.dachser.dfe.book.user.model.Address;
import com.dachser.dfe.book.user.model.AslCustomer;
import com.dachser.dfe.book.user.model.Customer;
import com.dachser.dfe.book.user.model.OrderOptions;
import com.dachser.dfe.book.user.model.RoadCustomer;
import com.dachser.dfe.book.user.model.User;
import com.dachser.dfe.book.user.model.UserPreferences;
import com.dachser.dfe.book.user.platform.PlatformApi;
import com.dachser.dfe.platform.model.PlatformUserV5;
import com.dachser.dfe.platform.security.PermissionExpressions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Predicate;
import java.util.stream.Stream;

@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = { "dfe.book.mock" }, havingValue = "false", matchIfMissing = true)
public class UserService implements UserContextService, PermissionService {

	private final UserMapper userMapper;

	private final PlatformApi platformApi;

	private final PermissionExpressions permissionExpressions;

	private final SegmentMapper segmentMapper;

	private final ShipperMasterdataService shipperMasterdataService;

	private final BusinessDomainProvider businessDomainProvider;

	public User getCurrentUser() throws NotAuthenticatedException {
		PlatformUserV5 user = Optional.ofNullable(platformApi.getPlatformUser()).orElseThrow(NotAuthenticatedException::new);
		return userMapper.map(user);
	}

	public UserPreferences getUserPreferences() {
		return userMapper.map(platformApi.getUserPreferencesV4());
	}

	protected boolean customerNumberIsNotNullOrEmpty(String customerNumber, UUID customerId) {
		if ((customerNumber == null) || (StringUtils.isEmpty(customerNumber))) {
			log.error("Customer with id {}  has no customer number.", customerId);
			return false;
		}
		return true;
	}

	@Override
	public List<String> getAllBookPermittedCustomerIds() {
		// AIR also matches sea, no separate filter needs to be applied
		return Stream.concat(
						getRoadCustomers().stream().map(customer -> customerNumberIsNotNullOrEmpty(customer.getCustomerNumber(), customer.getId()) ? customer.getCustomerNumber() : null),
						getAslCustomers().stream().map(customer -> customerNumberIsNotNullOrEmpty(customer.getCustomerNumber(), customer.getId()) ? customer.getCustomerNumber() : null))
				.distinct().filter(Objects::nonNull).toList();
	}

	@Override
	public List<String> getAllBookPermittedCustomerIdsWithSegment() {
		return Stream.concat(getRoadCustomers().stream()
						.map(customer -> customerNumberIsNotNullOrEmpty(customer.getCustomerNumber(), customer.getId()) ? customer.getCustomerNumber() + Segment.ROAD.getGroup() : null),
				getAslCustomers().stream().map(customer -> customerNumberIsNotNullOrEmpty(customer.getCustomerNumber(), customer.getId()) ?
						customer.getCustomerNumber() + Segment.AIR.getGroup() :
						null)).filter(Objects::nonNull).distinct().toList();
	}

	private boolean isBookPermitted(Customer cust, Segment segment) {
		return isCustomerNumberValid(cust.getCustomerNumber(), segment);
	}

	@Override
	public String getCurrentUserId() throws NotAuthenticatedException {
		return getCurrentUser().getId().toString();
	}

	@Override
	public String getCurrentUserIdIfAvailable() {
		try {
			if (isContextAuthenticated()) {
				return getCurrentUserId();
			}
		} catch (Exception anyException) {
			log.debug("Catch also nullpointer because a nullpointer in plattform");
		}
		return null;
	}

	private boolean isContextAuthenticated() {
		return SecurityContextHolder.getContext().getAuthentication() != null && SecurityContextHolder.getContext().getAuthentication().isAuthenticated();
	}

	@Override
	public List<CustomerDto> getCustomers(Segment segment) {
		List<CustomerDto> customers;
		if (Segment.ROAD == segment) {
			customers = getRoadCustomers().stream()
					// Filter out ROAD customers without a branch
					.filter(roadCustomerHasBranch()).map(customer -> new CustomerDto().customerNumber(customer.getCustomerNumber()).label(getCustomerTitle(customer.getAddress()))
					.cashOnDelivery(customer.isCashOnDelivery())).toList();
		} else {
			customers = getAslCustomers().stream().map(customer -> new CustomerDto().customerNumber(customer.getCustomerNumber()).label(getCustomerTitle(customer.getAddress())))
					.toList();
		}
		log.debug("Found customers: {} for segment {} and user {}", customers, segment, getCurrentUserId());
		return customers;
	}

	public Optional<RoadCustomer> getCustomerInformationRoad(String customerNumber) {
		if (customerNumber == null) {
			return Optional.empty();
		}

		Optional<RoadCustomer> roadCustomer = getRoadCustomers().stream().filter(customer -> customer.getCustomerNumber().equals(customerNumber)).findFirst();

		if (roadCustomer.isEmpty()) {
			log.warn("Could not find customer information for customer number {}", customerNumber);
		} else {
			RoadCustomer rc = roadCustomer.get();
			rc.getOrderOptions().setOrderNumberMandatory(shipperMasterdataService.isOrderNumberMandatoryForCustomerNumber(customerNumber));
		}

		return roadCustomer;
	}

	public Optional<AslCustomer> getCustomerInformationAsl(String customerNumber) {
		if (customerNumber == null) {
			return Optional.empty();
		}

		Optional<AslCustomer> aslCustomer = getAslCustomers().stream().filter(customer -> customer.getCustomerNumber().equals(customerNumber)).findFirst();

		if (aslCustomer.isEmpty()) {
			log.warn("Could not find customer information for customer number {}", customerNumber);
		}
		return aslCustomer;
	}

	@NonNull
	@Override
	public int getBranchId(String customerNumber, OrderType orderType) {
		LoggerFactory.getLogger(UserContextService.class).debug("getBranchId for customerNumber: {}, orderType: {}", customerNumber, orderType);
		switch (orderType.getSegment()) {
		case ROAD -> {
			return getBranchIdRoad(customerNumber);
		}
		case AIR, SEA -> {
			return getBranchIdAsl(customerNumber, orderType);
		}
		default -> {
			log.warn("Branch not resolvable for customer {} - Unsupported segment: {}", customerNumber, orderType.getSegment());
			throw new BranchUnresolvableException(customerNumber);
		}
		}
	}

	private int getBranchIdRoad(String customerNumber) {
		final Optional<RoadCustomer> roadCustomerOptional = getCustomerInformationRoad(customerNumber);

		if (roadCustomerOptional.isEmpty()) {
			log.warn("Branch not resolvable - Missing ROAD customer for customer number {}", customerNumber);
			throw new BranchUnresolvableException(customerNumber);
		}

		final RoadCustomer roadCustomer = roadCustomerOptional.get();
		final Integer branchId = resolveBranchForRoadCustomer(roadCustomer);

		if (branchId == null) {
			log.warn("Missing branch for customer {}", customerNumber);
			throw new BranchUnresolvableException(customerNumber);
		}

		return branchId;
	}

	private int getBranchIdAsl(String customerNumber, OrderType orderType) {
		final Optional<AslCustomer> aslCustomerOptional = getCustomerInformationAsl(customerNumber);

		if (aslCustomerOptional.isEmpty()) {
			log.warn("Branch not resolvable - Missing ASL customer for customer number {}", customerNumber);
			throw new BranchUnresolvableException(customerNumber);
		}

		final AslCustomer aslCustomer = aslCustomerOptional.get();
		final Integer branchId = resolveBranchForAslCustomer(aslCustomer, orderType);

		if (branchId == null) {
			log.warn("Missing branch for customer {} and order type {}", customerNumber, orderType);
			throw new BranchUnresolvableException(customerNumber);
		}

		return branchId;
	}

	@Nullable
	private Integer resolveBranchForRoadCustomer(RoadCustomer roadCustomer) {
		final Division division = getFirstFoundDivision(roadCustomer.getCustomerNumber(), Segment.ROAD);

		switch (division) {
		case T -> {
			if (Objects.nonNull(roadCustomer.getBookForwardingElBranchNumber())) {
				return roadCustomer.getBookForwardingElBranchNumber();
			} else if (Objects.nonNull(roadCustomer.getBookCollectingElBranchNumber())) {
				return roadCustomer.getBookCollectingElBranchNumber();
			}
		}
		case F -> {
			if (Objects.nonNull(roadCustomer.getBookForwardingFlBranchNumber())) {
				return roadCustomer.getBookForwardingFlBranchNumber();
			} else if (Objects.nonNull(roadCustomer.getBookCollectingFlBranchNumber())) {
				return roadCustomer.getBookCollectingFlBranchNumber();
			}
		}
		default -> log.warn("Branch not resolvable for customer {} - Unsupported division: {}", roadCustomer.getCustomerNumber(), division);
		}
		return null;
	}

	@Nullable
	private Integer resolveBranchForAslCustomer(AslCustomer aslCustomer, OrderType orderType) {
		switch (orderType) {
		case AIREXPORTORDER -> {
			return aslCustomer.getBookExportAirBranchNumber();
		}
		case AIRIMPORTORDER -> {
			return aslCustomer.getBookImportAirBranchNumber();
		}
		case SEAEXPORTORDER -> {
			return aslCustomer.getBookExportLclBranchNumber();
		}
		case SEAIMPORTORDER -> {
			return aslCustomer.getBookImportLclBranchNumber();
		}
		default -> log.warn("Branch not resolvable for customer {} - Unsupported order type: {}", aslCustomer.getCustomerNumber(), orderType);
		}
		return null;
	}

	@Override
	public boolean isCustomerNumberValid(String customerNumber, Segment segment) {
		return permissionExpressions.canBook(getCurrentUser().getCompany().getName(), customerNumber, segmentMapper.mapToAsl(segment));
	}

	@Override
	public Optional<OrderOptions> getOrderOptions(String customerNumber, Segment segment) {
		if (Segment.ROAD.equals(segment)) {
			return getCustomerInformationRoad(customerNumber).map(RoadCustomer::getOrderOptions);
		} else {
			return getCustomerInformationAsl(customerNumber).map(AslCustomer::getOrderOptions);
		}
	}

	@Override
	public Optional<Address> getCustomerAddress(String customerNumber, Segment segment) {
		if (Segment.ROAD.equals(segment)) {
			return getCustomerInformationRoad(customerNumber).map(RoadCustomer::getAddress);
		} else {
			return getCustomerInformationAsl(customerNumber).map(AslCustomer::getAddress);
		}
	}

	@Override
	public Integer getBusinessDomain() {
		return businessDomainProvider.getBusinessDomain();
	}

	@NonNull
	@Override
	public Division getFirstFoundDivision(String customerNumber, Segment segment) {
		Division division;
		if (Segment.ROAD.equals(segment)) {
			division = getCustomerInformationRoad(customerNumber).map(RoadCustomer::getDivision).orElse(null);
		} else {
			division = getCustomerInformationAsl(customerNumber).map(customer -> Division.T).orElse(null);
		}

		if (division == null) {
			throw new DivisionUnresolvableException();
		}

		return division;
	}

	@Override
	public boolean isCustomerAllowedToSetManualSscc(String customerNumber) {
		return getCustomerInformationRoad(customerNumber).map(RoadCustomer::getOrderOptions).map(OrderOptions::getManualNumberSscc).orElse(false);
	}

	@Override
	public boolean isCustomerAllowedToViewAirProducts(String customerNumber, Segment segment) {
		if (segment.equals(Segment.AIR)) {
			return getCustomerInformationAsl(customerNumber).map(AslCustomer::getAirProducts).orElse(false);
		} else {
			return false;
		}
	}

	@Override
	public boolean isCustomerAllowedToViewPackingAidPosition(String customerNumber) {
		return getCustomerInformationRoad(customerNumber).map(RoadCustomer::getOrderOptions).map(OrderOptions::getPackagingAidPosition).orElse(false);
	}

	@Override
	public boolean isCustomerAllowedToViewDangerousGoods(String customerNumber) {
		return getCustomerInformationRoad(customerNumber).map(RoadCustomer::getOrderOptions).map(OrderOptions::getDangerousGoods).orElse(false);
	}

	@Override
	public boolean isCustomerAllowedToPrintConsignmentLabels(String customerNumber) {
		return getCustomerInformationRoad(customerNumber).map(RoadCustomer::getOrderOptions).map(OrderOptions::getAtILO).orElse(false);
	}

	private List<RoadCustomer> getRoadCustomers() {
		return getCurrentUser().getCompany().getCustomersROAD().stream().filter(customer -> isBookPermitted(customer, Segment.ROAD)).toList();
	}

	private List<AslCustomer> getAslCustomers() {
		return getCurrentUser().getCompany().getCustomersASL().stream().filter(customer -> isBookPermitted(customer, Segment.AIR)).toList();
	}

	private String getCustomerTitle(Address address) {
		List<String> names = Stream.of(address.getName1(), address.getName2(), address.getName3()).filter(name -> name != null && !name.isBlank()).toList();

		final String customerTitle = names.size() > 1 ? String.join(", ", names) : address.getName1();

		return customerTitle != null && customerTitle.length() > 30 ? customerTitle.substring(0, 27) + "..." : customerTitle;
	}

	private Predicate<RoadCustomer> roadCustomerHasBranch() {
		return customer -> Objects.nonNull(resolveBranchForRoadCustomer(customer));
	}


}
