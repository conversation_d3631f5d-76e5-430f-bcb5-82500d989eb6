package com.dachser.dfe.book.order.validation.road.delivery;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({ ElementType.TYPE, ElementType.ANNOTATION_TYPE })
@Retention(RUNTIME)
@Constraint(validatedBy = { DeliveryDateValidator.class })
@Documented
public @interface DeliveryDateValid {

	String message() default """
			{com.dachser.dfe.book.validation.\
			DeliveryDateValid.message}\
			""";

	Class<?>[] groups() default { };

	Class<? extends Payload>[] payload() default { };
}