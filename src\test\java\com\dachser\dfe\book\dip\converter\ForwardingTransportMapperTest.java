package com.dachser.dfe.book.dip.converter;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.dip.converter.road.GeneralTransportDataMapper;
import com.dachser.dfe.book.dip.converter.road.GeneralTransportDataMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingDangerousGoodsMapper;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingDangerousGoodsMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingPackingPositionMapper;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingPackingPositionMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingShipmentAddressMapper;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingShipmentAddressMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingShipmentLineMapper;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingShipmentLineMapperImpl;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingTransportMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.DateMapper;
import com.dachser.dfe.book.dip.converter.shared.DateMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.SharedEdiReference;
import com.dachser.dfe.book.jpa.entity.CustomsType;
import com.dachser.dfe.book.model.SourceOfOrder;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.AdditionalReference;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.COD;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.DangerousGoods;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentHeader;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentLine;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentText;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.Transport;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class ForwardingTransportMapperTest {

	@Spy
	DateMapper dateMapper = new DateMapperImpl();

	@Spy
	GeneralTransportDataMapper generalTransportDataMapper = new GeneralTransportDataMapperImpl();

	@Spy
	ForwardingDangerousGoodsMapper forwardingDangerousGoodsMapper = new ForwardingDangerousGoodsMapperImpl();

	@Spy
	ForwardingShipmentLineMapper forwardingShipmentLineMapper = new ForwardingShipmentLineMapperImpl(forwardingDangerousGoodsMapper);

	@Spy
	ForwardingShipmentAddressMapper forwardingShipmentAddressMapper = new ForwardingShipmentAddressMapperImpl(generalTransportDataMapper);

	@Spy
	ForwardingPackingPositionMapper forwardingPackingPositionMapper = new ForwardingPackingPositionMapperImpl();

	@InjectMocks
	ForwardingTransportMapperImpl transportMapper;

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();

	@BeforeEach
	void setIDs() {
		for (PackingPosition packingPosition : forwardingOrder.getPackingPositions()) {
			packingPosition.setId(new Random().nextLong(3));
			packingPosition.getOrderLines().forEach(orderLines -> orderLines.setPackingPositionId(packingPosition.getId()));
		}
	}

	@ParameterizedTest
	@MethodSource("provideCustomsArguments")
	void shouldMapCustomsFields(boolean isCustoms, CustomsType customsType) {
		forwardingOrder.setCustomsGoods(isCustoms);
		forwardingOrder.setCustomsType(customsType);
		Transport mapped = transportMapper.map(forwardingOrder);
		assertThat(mapped).isNotNull();
		ShipmentHeader shipmentHeader = mapped.getShipmentHeader().get(0);
		assertThat(shipmentHeader).isNotNull();
		if (isCustoms) {
			assertThat(shipmentHeader.getCustomsIndicator()).isEqualTo("Y");
			Optional<AdditionalReference> customsReference = shipmentHeader.getAdditionalReference().stream().filter(ref -> ref.getCode().equals("EXD")).findFirst();
			assertTrue(customsReference.isPresent());
			assertThat(customsReference.get().getReference()).isNotNull();
		} else {
			assertThat(shipmentHeader.getCustomsIndicator()).isEqualTo("N");
			Optional<AdditionalReference> customsReference = shipmentHeader.getAdditionalReference().stream().filter(ref -> ref.getCode().equals("EXD")).findFirst();
			assertTrue(customsReference.isEmpty());
		}
	}

	@ParameterizedTest
	@MethodSource("provideFurtherAddresses")
	void shouldMapLoadingPoints(List<OrderFurtherAddress> furtherAddresses, Integer amountExpected, String addressTypeExpected) {
		forwardingOrder.setAddresses(furtherAddresses);
		Transport mapped = transportMapper.map(forwardingOrder);
		assertThat(mapped).isNotNull();
		ShipmentHeader shipmentHeader = mapped.getShipmentHeader().get(0);
		assertThat(shipmentHeader).isNotNull();
		assertThat(shipmentHeader.getShipmentAddress()).isNotNull();
		assertThat(shipmentHeader.getShipmentAddress()).hasSize(3 + amountExpected);
	}

	@Test
	void shouldOnlyMapServiceContactTypeForConsignee() {
		Transport mapped = transportMapper.map(forwardingOrder);
		assertThat(mapped).isNotNull();

		mapped.getShipmentHeader().forEach(shipmentHeader -> {
			shipmentHeader.getShipmentAddress().forEach(shipmentAddress -> {
				if (shipmentAddress.getAddressType().equals("CN")) {
					assertThat(shipmentAddress.getServiceContactType()).isNotNull();
				} else {
					assertThat(shipmentAddress.getServiceContactType()).isNull();
				}
			});
		});
	}

	@Test
	void shouldOnlyMapFilledAddresses() {
		forwardingOrder.setConsigneeAddress(null);
		Transport mapped = transportMapper.map(forwardingOrder);
		assertThat(mapped).isNotNull();

		mapped.getShipmentHeader().forEach(shipmentHeader -> {
			assertThat(shipmentHeader.getShipmentAddress()).hasSize(3);
		});
	}

	@Test
	void shouldMapDropOfLocationOrderText() {
		Transport mapped = transportMapper.map(forwardingOrder);
		assertThat(mapped).isNotNull();

		mapped.getShipmentHeader().forEach(shipmentHeader -> {
			assertThat(shipmentHeader.getShipmentAddress()).hasSize(4);
			assertThat(shipmentHeader.getShipmentText()).hasSize(3);
			assertThat(shipmentHeader.getShipmentText().stream().filter(shipmentText -> shipmentText.getTextType().equals("AG")).findFirst().get().getText().get(0)).isEqualTo(
					forwardingOrder.getConsigneeAddress().getDropOfLocation());
		});
	}

	@Test
	void shouldOnlyMapContactInformationForConsignee() {
		final OrderContact deliveryContact = new OrderContact();
		deliveryContact.setEmail("<EMAIL>");
		deliveryContact.setName("Delivery Contact");
		deliveryContact.setMobile("0000777777778");
		deliveryContact.setTelephone("0000777777777");
		forwardingOrder.setDeliveryContact(deliveryContact);

		Transport mapped = transportMapper.map(forwardingOrder);
		assertThat(mapped).isNotNull();
		assertThat(mapped.getShipmentHeader()).isNotNull();

		mapped.getShipmentHeader().forEach(shipmentHeader -> {
			shipmentHeader.getShipmentAddress().forEach(shipmentAddress -> {
				if (shipmentAddress.getAddressType().equals("CN")) {
					assertFalse(shipmentAddress.getPartnerInformation().getContactInformation().isEmpty());
				} else {
					assertTrue(shipmentAddress.getPartnerInformation().getContactInformation().isEmpty());
				}
			});
		});

	}

	@ParameterizedTest
	@ValueSource(doubles = { -1.0, 0, 1.0, 10.0, 78.0 })
	void shouldMapPalletLocations(Double palletLocation) {
		forwardingOrder.setPalletLocations(BigDecimal.valueOf(palletLocation));
		Transport mapped = transportMapper.map(forwardingOrder);
		ShipmentHeader shipmentHeader = mapped.getShipmentHeader().get(0);
		assertThat(shipmentHeader).isNotNull();
		if (palletLocation <= 0) {
			assertTrue(shipmentHeader.getStoringPositions().isEmpty());
		} else {
			assertThat(shipmentHeader.getStoringPositions().get(0).getQuantity()).isEqualTo(palletLocation.floatValue());
		}
	}

	@Test
	void shouldMapNullPalletLocations() {
		forwardingOrder.setPalletLocations(null);
		Transport mapped = transportMapper.map(forwardingOrder);
		ShipmentHeader shipmentHeader = mapped.getShipmentHeader().get(0);
		assertThat(shipmentHeader).isNotNull();
		assertTrue(shipmentHeader.getStoringPositions().isEmpty());
	}

	@ParameterizedTest
	@ValueSource(booleans = { true, false })
	void shouldMapFrostProtection(boolean frostProtection) {
		forwardingOrder.setFrostProtection(frostProtection);
		Transport mapped = transportMapper.map(forwardingOrder);
		assertThat(mapped).isNotNull();
		ShipmentHeader shipmentHeader = mapped.getShipmentHeader().get(0);
		assertThat(shipmentHeader).isNotNull();
		Optional<ShipmentText> shipmentText = shipmentHeader.getShipmentText().stream().filter(text -> text.getTextType().equals("TH")).findFirst();
		if (frostProtection) {
			assertTrue(shipmentText.isPresent());
			assertThat(shipmentText.get().getText().get(0)).isEqualTo("Frost Protected");
		} else {
			assertTrue(shipmentText.isEmpty());
		}
	}

	@Test
	void shouldMapGoodsGroups() {
		String goodsGroup = forwardingOrder.getOrderLines().get(0).getGoodsGroup();
		String goodsDescription = forwardingOrder.getOrderLines().get(0).getContent();
		Transport mapped = transportMapper.map(forwardingOrder);
		assertThat(mapped).isNotNull();
		ShipmentHeader shipmentHeader = mapped.getShipmentHeader().get(0);
		assertThat(shipmentHeader).isNotNull();
		assertThat(shipmentHeader.getShipmentLine().get(0).getGoodsGroup()).isEqualTo(goodsGroup);
		assertThat(shipmentHeader.getShipmentLine().get(0).getGoodsDescription()).isEqualTo(goodsDescription);

	}

	@Test
	void shouldMapOrderOriginReference() {
		SourceOfOrder referenceValue = forwardingOrder.getDatasource();
		Transport mapped = transportMapper.map(forwardingOrder);
		assertThat(mapped).isNotNull();
		ShipmentHeader shipmentHeader = mapped.getShipmentHeader().get(0);
		assertThat(shipmentHeader).isNotNull();
		Optional<AdditionalReference> orderOriginReference = shipmentHeader.getAdditionalReference().stream()
				.filter(ref -> ref.getCode().equals(SharedEdiReference.ORDER_ORIGIN_REFERENCE.getReferenceCode())).findFirst();
		assertTrue(orderOriginReference.isPresent());
		assertEquals(referenceValue.getEdiReferenceValue(), orderOriginReference.get().getReference());
	}

	@Test
	void shouldMapCashOnDelivery(){
		forwardingOrder.setCashOnDelivery(true);
		forwardingOrder.setCashOnDeliveryAmount(BigDecimal.valueOf(100));
		Transport mapped = transportMapper.map(forwardingOrder);
		assertThat(mapped).isNotNull();
		ShipmentHeader shipmentHeader = mapped.getShipmentHeader().get(0);
		assertThat(shipmentHeader).isNotNull();
		COD cod = shipmentHeader.getCOD();
		assertThat(cod).isNotNull();
		assertThat(cod.getCurrency()).isEqualTo("EUR");
		assertThat(cod.getCode()).isEqualTo("02");
		assertThat(cod.getAmount()).isEqualTo(100.0f);
	}

	@Test
	void shouldMapEQDangerousGoods() {
		//given
		RoadOrderLine first = forwardingOrder.getOrderLines().getFirst();
		first.getDangerousGoods().add(testUtil.eqDangerousGoods(first));

		// when
		Transport mapped = transportMapper.map(forwardingOrder);

		// then
		ShipmentLine shipmentLine = mapped.getShipmentHeader().getFirst().getShipmentLine().getFirst();
		assertThat(shipmentLine.getDangerousGoods()).isNotEmpty();
		assertThat(shipmentLine.getDangerousGoods().getFirst().getType()).isEqualTo("EQ");
		assertThat(shipmentLine.getDangerousGoods().getFirst().getPackagesQuantity()).isEqualTo(BigInteger.TEN);
		assertThat(shipmentLine.getDangerousGoods().getFirst().getPackingType()).isEqualTo("BA");
	}

	@Test
	void shouldMapLQDangerousGoods() {
		//given
		RoadOrderLine first = forwardingOrder.getOrderLines().getFirst();
		first.getDangerousGoods().add(testUtil.lqDangerousGoods(first));

		// when
		Transport mapped = transportMapper.map(forwardingOrder);

		// then
		ShipmentLine shipmentLine = mapped.getShipmentHeader().getFirst().getShipmentLine().getFirst();
		assertThat(shipmentLine.getDangerousGoods()).isNotEmpty();
		DangerousGoods firstEntry = shipmentLine.getDangerousGoods().getFirst();
		assertThat(firstEntry.getType()).isEqualTo("LQ");
		assertThat(firstEntry.getADRWeight().getMeasurement().getValue()).isEqualTo(10.50f);
		assertThat(firstEntry.getADRWeight().getMeasurement().getUnit()).isEqualTo("KGM");
		assertThat(firstEntry.getUNDGnumber()).isEqualTo("1791");
	}

	@Test
	void shouldMapADRDangerousGoods() {
		//given
		RoadOrderLine first = forwardingOrder.getOrderLines().getFirst();
		first.getDangerousGoods().add(testUtil.adrDangerousGoods(first));

		// when
		Transport mapped = transportMapper.map(forwardingOrder);

		// then
		ShipmentLine shipmentLine = mapped.getShipmentHeader().getFirst().getShipmentLine().getFirst();
		assertThat(shipmentLine.getDangerousGoods()).isNotEmpty();
		DangerousGoods firstEntry = shipmentLine.getDangerousGoods().getFirst();
		assertThat(firstEntry.getType()).isNull();
		assertThat(firstEntry.getADRWeight().getMeasurement().getValue()).isEqualTo(10.75f);
		assertThat(firstEntry.getADRWeight().getMeasurement().getUnit()).isEqualTo("KGM");
		assertThat(firstEntry.getUNDGnumber()).isEqualTo("1791");
		assertThat(firstEntry.getClazz()).isEqualTo("8");
		assertThat(firstEntry.getTechnicalName()).isEqualTo("Test Description");
		assertThat(firstEntry.getNOS()).isEqualTo("Y");
		assertThat(firstEntry.getMainDangerNOS()).isEqualTo("Nos text");
		assertThat(firstEntry.getPackingGroup()).isEqualTo("II");
		assertThat(firstEntry.getClassificationCode()).isEqualTo("C9");
		assertThat(firstEntry.getSecondaryDanger()).isEqualTo("2");
		assertThat(firstEntry.getEnvironmentalIndicator()).isEqualTo("Y");
		assertThat(firstEntry.getPackagesQuantity()).isEqualTo(BigInteger.valueOf(20));
		assertThat(firstEntry.getPackingType()).isEqualTo("XD");
	}

	private static Stream<Arguments> provideCustomsArguments() {
		return Stream.of(Arguments.of(false, CustomsType.CUSTOMER), Arguments.of(false, CustomsType.DACHSER), Arguments.of(true, CustomsType.CUSTOMER),
				Arguments.of(true, CustomsType.DACHSER));
	}

	private static Stream<Arguments> provideFurtherAddresses() {
		OrderFurtherAddress loadingPointAddress = new OrderFurtherAddress();
		loadingPointAddress.setAddressType("LP");
		loadingPointAddress.setName("Test GmbH");
		loadingPointAddress.setStreet("Teststraße 1");
		loadingPointAddress.setCity("Teststadt");
		loadingPointAddress.setPostcode("12345");
		loadingPointAddress.setCountryCode("DE");

		OrderFurtherAddress ignoredLoadingPointAddress = new OrderFurtherAddress();
		ignoredLoadingPointAddress.setAddressType("LP");
		//empty LP address should be ignored

		OrderFurtherAddress otherFurtherAddress = new OrderFurtherAddress();
		otherFurtherAddress.setAddressType("XX");
		otherFurtherAddress.setName("Test GmbH");
		otherFurtherAddress.setStreet("Teststraße 2");
		otherFurtherAddress.setCity("Teststadt");
		otherFurtherAddress.setPostcode("12345");
		otherFurtherAddress.setCountryCode("DE");

		return Stream.of(Arguments.of(List.of(loadingPointAddress), 1, "LP"), Arguments.of(List.of(otherFurtherAddress), 1, "XX"),
				Arguments.of(List.of(ignoredLoadingPointAddress, otherFurtherAddress), 1, "XX"), Arguments.of(List.of(loadingPointAddress, otherFurtherAddress), 2, "LP"));
	}
}
