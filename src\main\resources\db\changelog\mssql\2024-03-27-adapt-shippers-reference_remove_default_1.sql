-- liquibase formatted sql

-- changeset joergw:2024-03-27-shippers-reference_mssql
-- validCheckSum: 9:7c254f00c25a3f7847ceb3678534b1f3

BEGIN
declare @constraint_name nvarchar(128)
select @constraint_name = name FROM SYS.default_constraints WHERE parent_object_id = (SELECT object_id FROM SYS.COLUMNS WHERE object_id = OBJECT_ID('[${default-schema}].[order_sea]') and name = 'loading') AND parent_column_id = (SELECT column_id FROM SYS.COLUMNS WHERE object_id = OBJECT_ID('[${default-schema}].[order_sea]') and name = 'loading')
exec('ALTER TABLE order_sea DROP CONSTRAINT IF EXISTS ' + @constraint_name)
END



