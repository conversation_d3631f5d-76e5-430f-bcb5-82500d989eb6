package com.dachser.dfe.book.service.postalcode;

import com.dachser.dfe.book.DFEBookApplication;
import com.dachser.dfe.book.model.IrelandPostalCodeDto;
import com.dachser.dfe.book.model.PostcodeValidationDto;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = { DFEBookApplication.class })
@ActiveProfiles("integrationtest")
@Tag("IntegrationTest")
public class PostalCodeAdapterExtIntegrationTest {

	@Autowired
	private PostalCodeAdapterExt dachserPostalCodeAdapterExt;

	@Nested
	class FindDachserPostalcodes {

		@Test
		void returnsPostalcodesWhenApiCallSucceeds() {
			List<IrelandPostalCodeDto> h12 = dachserPostalCodeAdapterExt.findDachserPostalcodes("H12");
			assertNotNull(h12);
			assertEquals(39, h12.size());
			List<IrelandPostalCodeDto> expectedTowns = List.of(new IrelandPostalCodeDto().county("LEITRIM").eircode("H12").dachserPLZ("12005").town("AGHAVAS"),
					new IrelandPostalCodeDto().county("CAVAN").eircode("H12").dachserPLZ("02025").town("BRUCE HALL"));
			assertTrue(h12.containsAll(expectedTowns));
		}

		@Test
		void returnsNoPostalcodesWhenApiCallSucceedsForNoMatch() {
			List<IrelandPostalCodeDto> h12 = dachserPostalCodeAdapterExt.findDachserPostalcodes("X02");
			assertNotNull(h12);
			assertTrue(h12.isEmpty());
		}

	}

	@Nested
	class ValidatePostCode {

		@Test
		void returnsValidPostCodeWhenApiCallSucceeds() {
			String countryCode = "DE";
			String postalCode = "81245";
			PostcodeValidationDto expected = new PostcodeValidationDto().valid(true);

			PostcodeValidationDto result = dachserPostalCodeAdapterExt.validatePostCode(countryCode, postalCode);

			assertEquals(expected, result);
		}

		@Test
		void returnsInvalidPostCodeWhenApiCallSucceedsWithInvalidRegex() {
			String countryCode = "DE";
			String postalCode = "999999";
			String exampleZipCode = "10115";
			String regex = "\\d{5}";
			PostcodeValidationDto expected = new PostcodeValidationDto().valid(false).examplePostcode(exampleZipCode).validatedPattern(regex);

			PostcodeValidationDto result = dachserPostalCodeAdapterExt.validatePostCode(countryCode, postalCode);

			assertEquals(expected, result);
		}

		@Test
		void returnsValidPostCodeWhenApiCallFails() {
			String countryCode = "HK";
			PostcodeValidationDto expected = new PostcodeValidationDto().valid(false);

			PostcodeValidationDto result = dachserPostalCodeAdapterExt.validatePostCode(countryCode, null);

			assertEquals(expected, result);
		}

	}

}