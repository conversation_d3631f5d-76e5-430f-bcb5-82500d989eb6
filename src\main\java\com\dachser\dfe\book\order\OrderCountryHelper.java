package com.dachser.dfe.book.order;

import com.dachser.dfe.book.model.FurtherAddressType;
import com.dachser.dfe.book.order.common.OrderBaseAddress;
import com.dachser.dfe.book.order.road.CollectionOrder;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class OrderCountryHelper {

	public boolean hasShippingForCountry(Order order, String countryIsoCode) {
		return hasShippingForCountryAsConsignor(order, countryIsoCode) || hasShippingForCountryAsConsignee(order, countryIsoCode) || hasShippingForCountryAsEdgeCase(order,
				countryIsoCode);
	}

	private boolean hasShippingForCountryAsConsignor(Order order, String countryIsoCode) {
		return order != null && (hasCountry(order.getShipperAddress(), countryIsoCode) || hasCountry(order.getPrincipalAddress(), countryIsoCode));
	}

	private boolean hasShippingForCountryAsConsignee(Order order, String countryIsoCode) {
		return (order instanceof CollectionOrder collectionOrder && hasCountry(collectionOrder.getDifferentConsigneeAddress(), countryIsoCode)) || (order != null && hasCountry(
				order.getConsigneeAddress(), countryIsoCode));
	}

	private boolean hasShippingForCountryAsEdgeCase(Order order, String countryIsoCode) {
		return order != null && order.getAddresses() != null && order.getAddresses().stream()
				.filter(address -> FurtherAddressType.FINAL_DELIVERY_ADDRESS.getKey().equalsIgnoreCase(address.getAddressType()))
				.anyMatch(address -> hasCountry(address, countryIsoCode));
	}

	private boolean hasCountry(OrderBaseAddress address, String countryIsoCode) {
		return address != null && StringUtils.isNotBlank(countryIsoCode) && countryIsoCode.equalsIgnoreCase(address.getCountryCode());
	}

}