package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.model.AirExportOrderDto;
import com.dachser.dfe.book.model.AirImportOrderDto;
import com.dachser.dfe.book.model.AirSeaOrderReferenceDto;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.OrderReferenceTypeDto;
import com.dachser.dfe.book.model.OrderResponseBodyDto;
import com.dachser.dfe.book.model.RoadCollectionOrderDto;
import com.dachser.dfe.book.model.RoadForwardingOrderDto;
import com.dachser.dfe.book.model.RoadOrderReferenceDto;
import com.dachser.dfe.book.model.SeaExportOrderDto;
import com.dachser.dfe.book.model.SeaImportOrderDto;
import com.dachser.dfe.book.model.SourceOfOrder;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.air.AirOrderLine;
import com.dachser.dfe.book.order.air.AirOrderReference;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.common.OrderSscc;
import com.dachser.dfe.book.order.common.OrderText;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaImportOrder;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.book.order.sea.SeaOrderLine;
import com.dachser.dfe.book.order.sea.SeaOrderReference;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.BeforeMapping;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.SubclassExhaustiveStrategy;
import org.mapstruct.SubclassMapping;
import org.mapstruct.ValueMapping;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Mapper(config = OrderMapperConfig.class, componentModel = "spring", subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION)
@Named("OrderMapper")
public interface OrderMapper {

	@SubclassMapping(source = RoadCollectionOrderDto.class, target = CollectionOrder.class)
	@SubclassMapping(source = RoadForwardingOrderDto.class, target = ForwardingOrder.class)
	@SubclassMapping(source = AirImportOrderDto.class, target = AirImportOrder.class)
	@SubclassMapping(source = AirExportOrderDto.class, target = AirExportOrder.class)
	@SubclassMapping(source = SeaImportOrderDto.class, target = SeaImportOrder.class)
	@SubclassMapping(source = SeaExportOrderDto.class, target = SeaExportOrder.class)
	Order map(BasicOrderDto order);

	@InheritInverseConfiguration
	@SubclassMapping(target = RoadCollectionOrderDto.class, source = CollectionOrder.class)
	@SubclassMapping(target = RoadForwardingOrderDto.class, source = ForwardingOrder.class)
	@SubclassMapping(target = AirImportOrderDto.class, source = AirImportOrder.class)
	@SubclassMapping(target = AirExportOrderDto.class, source = AirExportOrder.class)
	@SubclassMapping(target = SeaImportOrderDto.class, source = SeaImportOrder.class)
	@SubclassMapping(target = SeaExportOrderDto.class, source = SeaExportOrder.class)
	OrderResponseBodyDto map(Order order);

	@Mapping(source = "texts", target = "orderTexts")
	@Mapping(source = "furtherAddresses", target = "addresses")
	@Mapping(source = "goodsCurrency", target = "currency")
	@Mapping(source = "references", target = "orderReferences")
	@Mapping(source = "orderLineItems", target = "orderLines")
	@Mapping(source = "incoTerm.dachserCode", target = "incoTerm")
	@Mapping(source = "customerContactData", target = "orderContact")
	@Mapping(source = "fromIATA.code", target = "fromIATA")
	@Mapping(source = "toIATA.code", target = "toIATA")
	@Mapping(source = "product.code", target = "productCode")
	@Mapping(source = "product.description", target = "productName")
	AirExportOrder mapAir(AirExportOrderDto airOrder);

	@Mapping(source = "texts", target = "orderTexts")
	@Mapping(source = "furtherAddresses", target = "addresses")
	@Mapping(source = "goodsCurrency", target = "currency")
	@Mapping(source = "references", target = "orderReferences")
	@Mapping(source = "orderLineItems", target = "orderLines")
	@Mapping(source = "incoTerm.dachserCode", target = "incoTerm")
	@Mapping(source = "customerContactData", target = "orderContact")
	@Mapping(source = "fromIATA.code", target = "fromIATA")
	@Mapping(source = "toIATA.code", target = "toIATA")
	@Mapping(source = "product.code", target = "productCode")
	@Mapping(source = "product.description", target = "productName")
	AirImportOrder mapAir(AirImportOrderDto airOrder);

	@Mapping(source = "texts", target = "orderTexts")
	@Mapping(source = "furtherAddresses", target = "addresses")
	@Mapping(source = "goodsCurrency", target = "currency")
	@Mapping(source = "references", target = "orderReferences")
	@Mapping(source = "orderLineItems", target = "orderLines")
	@Mapping(source = "incoTerm.dachserCode", target = "incoTerm")
	@Mapping(source = "customerContactData", target = "orderContact")
	@Mapping(source = "fromPort.code", target = "fromPort")
	@Mapping(source = "toPort.code", target = "toPort")
	SeaExportOrder mapSea(SeaExportOrderDto seaOrder);

	@Mapping(source = "texts", target = "orderTexts")
	@Mapping(source = "furtherAddresses", target = "addresses")
	@Mapping(source = "goodsCurrency", target = "currency")
	@Mapping(source = "references", target = "orderReferences")
	@Mapping(source = "orderLineItems", target = "orderLines")
	@Mapping(source = "incoTerm.dachserCode", target = "incoTerm")
	@Mapping(source = "customerContactData", target = "orderContact")
	@Mapping(source = "fromPort.code", target = "fromPort")
	@Mapping(source = "toPort.code", target = "toPort")
	SeaImportOrder mapSea(SeaImportOrderDto seaOrder);

	@Mapping(target = "transferlistPrinted", source = "transferListPrinted")
	@Mapping(target = "freightTerm", source = "freightTerm.dachserTermKey")
	@Mapping(source = "references", target = "orderReferences")
	@Mapping(target = "orderLines", source = "orderLineItems")
	@Mapping(source = "labelsPrintedAt", target = "labelsPrintedAt", ignore = true)
	@Mapping(source = "adviceSentAt", target = "adviceSent", ignore = true)
	ForwardingOrder mapForwardingOrderToEntity(RoadForwardingOrderDto roadOrder);

	@Mapping(target = "collectionFrom", source = "collectionTime.from")
	@Mapping(target = "collectionTo", source = "collectionTime.to")
	@Mapping(target = "transferlistPrinted", source = "transferListPrinted")
	@Mapping(target = "freightTerm", source = "freightTerm.dachserTermKey", ignore = true)
	@Mapping(source = "references", target = "orderReferences")
	@Mapping(target = "orderLines", source = "orderLineItems")
	CollectionOrder mapCollectionOrderToEntity(RoadCollectionOrderDto roadOrder);

	@InheritInverseConfiguration(name = "mapCollectionOrderToEntity")
	@Mapping(target = "freightTerm", source = "freightTerm", ignore = true)
	@Mapping(source = "principalAddress", target = "principalAddress")
	@Mapping(target = "lastModified", source = "lastModified", qualifiedByName = "mapInstantToDateTime")
	@Mapping(source = "quoteInformation", target = "quoteInformation")
	@Mapping(target = "sendAt", source = "sendAt", qualifiedByName = "mapInstantToDateTime")
	@Mapping(target = "sendBy", source = "sendUser")
	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "mapInstantToDateTime")
	@Mapping(target = "createdBy", source = "creator")
	RoadCollectionOrderDto mapCollectionOrderToDto(CollectionOrder order);

	@InheritInverseConfiguration(name = "mapForwardingOrderToEntity")
	@Mapping(target = "generatedSsccs", source = "ssccs", qualifiedByName = "extractSSCC")
	@Mapping(target = "freightTerm", source = "freightTerm")
	@Mapping(target = "customerNumber", source = "customerNumber")
	@Mapping(source = "principalAddress", target = "principalAddress")
	@Mapping(target = "lastModified", source = "lastModified", qualifiedByName = "mapInstantToDateTime")
	@Mapping(source = "quoteInformation", target = "quoteInformation")
	@Mapping(target = "sendAt", source = "sendAt", qualifiedByName = "mapInstantToDateTime")
	@Mapping(target = "sendBy", source = "sendUser")
	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "mapInstantToDateTime")
	@Mapping(target = "createdBy", source = "creator")
	@Mapping(target = "adviceSentAt", source = "adviceSent", qualifiedByName = "mapInstantToDateTime")
	@Mapping(target = "labelsPrintedAt", source = "labelsPrintedAt", qualifiedByName = "mapInstantToDateTime")
	RoadForwardingOrderDto mapForwardingOrderToDto(ForwardingOrder order);

	@InheritInverseConfiguration(name = "mapAir")
	@Mapping(source = "incoTerm", target = "incoTerm")
	@Mapping(source = "fromIATA", target = "fromIATA")
	@Mapping(source = "toIATA", target = "toIATA")
	@Mapping(source = "principalAddress", target = "principalAddress")
	@Mapping(target = "lastModified", source = "lastModified", qualifiedByName = "mapInstantToDateTime")
	@Mapping(source = "quoteInformation", target = "quoteInformation")
	@Mapping(target = "sendAt", source = "sendAt", qualifiedByName = "mapInstantToDateTime")
	@Mapping(target = "sendBy", source = "sendUser")
	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "mapInstantToDateTime")
	@Mapping(target = "createdBy", source = "creator")
	AirImportOrderDto mapAirOrderToDto(AirImportOrder order);

	@InheritInverseConfiguration(name = "mapAir")
	@Mapping(source = "incoTerm", target = "incoTerm")
	@Mapping(source = "fromIATA", target = "fromIATA")
	@Mapping(source = "toIATA", target = "toIATA")
	@Mapping(source = "principalAddress", target = "principalAddress")
	@Mapping(target = "lastModified", source = "lastModified", qualifiedByName = "mapInstantToDateTime")
	@Mapping(source = "quoteInformation", target = "quoteInformation")
	@Mapping(target = "sendAt", source = "sendAt", qualifiedByName = "mapInstantToDateTime")
	@Mapping(target = "sendBy", source = "sendUser")
	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "mapInstantToDateTime")
	@Mapping(target = "createdBy", source = "creator")
	AirExportOrderDto mapAirOrderToDto(AirExportOrder order);

	@InheritInverseConfiguration(name = "mapSea")
	@Mapping(source = "incoTerm", target = "incoTerm")
	@Mapping(source = "fromPort", target = "fromPort")
	@Mapping(source = "toPort", target = "toPort")
	@Mapping(source = "principalAddress", target = "principalAddress")
	@Mapping(target = "lastModified", source = "lastModified", qualifiedByName = "mapInstantToDateTime")
	@Mapping(source = "quoteInformation", target = "quoteInformation")
	@Mapping(target = "sendAt", source = "sendAt", qualifiedByName = "mapInstantToDateTime")
	@Mapping(target = "sendBy", source = "sendUser")
	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "mapInstantToDateTime")
	@Mapping(target = "createdBy", source = "creator")
	SeaImportOrderDto mapSeaOrderToDto(SeaImportOrder order);

	@InheritInverseConfiguration(name = "mapSea")
	@Mapping(source = "incoTerm", target = "incoTerm")
	@Mapping(source = "fromPort", target = "fromPort")
	@Mapping(source = "toPort", target = "toPort")
	@Mapping(source = "principalAddress", target = "principalAddress")
	@Mapping(target = "lastModified", source = "lastModified", qualifiedByName = "mapInstantToDateTime")
	@Mapping(source = "quoteInformation", target = "quoteInformation")
	@Mapping(target = "sendAt", source = "sendAt", qualifiedByName = "mapInstantToDateTime")
	@Mapping(target = "sendBy", source = "sendUser")
	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "mapInstantToDateTime")
	@Mapping(target = "createdBy", source = "creator")
	SeaExportOrderDto mapSeaOrderToDto(SeaExportOrder order);

	@ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL)
	OrderReferenceTypeDto mapReference(AirSeaOrderReferenceType reference);

	default void update(final BasicOrderDto basicOrderDto, final Order order) {
		if (basicOrderDto instanceof final RoadCollectionOrderDto collectionOrderDto && order instanceof final CollectionOrder collectionOrder) {
			updateCollectionOrder(collectionOrderDto, collectionOrder);
		} else if (basicOrderDto instanceof final RoadForwardingOrderDto forwardingOrderDto && order instanceof final ForwardingOrder forwardingOrder) {
			updateForwardingOrder(forwardingOrderDto, forwardingOrder);
		} else if (basicOrderDto instanceof final AirImportOrderDto airImportOrderDto && order instanceof final AirImportOrder airImportOrder) {
			updateAirOrder(airImportOrderDto, airImportOrder);
		} else if (basicOrderDto instanceof final AirExportOrderDto airExportOrderDto && order instanceof final AirExportOrder airExportOrder) {
			updateAirOrder(airExportOrderDto, airExportOrder);
		} else if (basicOrderDto instanceof final SeaImportOrderDto seaImportOrderDto && order instanceof final SeaImportOrder seaImportOrder) {
			updateSeaOrder(seaImportOrderDto, seaImportOrder);
		} else if (basicOrderDto instanceof final SeaExportOrderDto seaExportOrderDto && order instanceof final SeaExportOrder seaExportOrder) {
			updateSeaOrder(seaExportOrderDto, seaExportOrder);
		} else {
			throw new IllegalArgumentException("Not all subclasses are supported for this mapping. Missing for " + order.getClass());
		}
	}

	@InheritConfiguration(name = "mapCollectionOrderToEntity")
	@Mapping(source = "shipmentNumber", target = "shipmentNumber", ignore = true)
	@Mapping(source = "status", target = "status", ignore = true)
	@Mapping(source = "division", target = "division", ignore = true)
	@Mapping(source = "quoteInformation", target = "quoteInformation", ignore = true)
	void updateCollectionOrder(RoadCollectionOrderDto order, @MappingTarget CollectionOrder targetOrder);

	@InheritConfiguration(name = "mapForwardingOrderToEntity")
	@Mapping(source = "shipmentNumber", target = "shipmentNumber", ignore = true)
	@Mapping(source = "status", target = "status", ignore = true)
	@Mapping(source = "division", target = "division", ignore = true)
	@Mapping(source = "quoteInformation", target = "quoteInformation", ignore = true)
	@Mapping(source = "generatedSsccs", target = "ssccs", qualifiedByName = "updateSsccs", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(source = ".", target = "labelsPrintedAt", ignore = true)
	void updateForwardingOrder(RoadForwardingOrderDto order, @MappingTarget ForwardingOrder targetOrder);

	@InheritConfiguration(name = "mapAir")
	@Mapping(source = "shipmentNumber", target = "shipmentNumber", ignore = true)
	@Mapping(source = "status", target = "status", ignore = true)
	@Mapping(source = "quoteInformation", target = "quoteInformation", ignore = true)
	void updateAirOrder(AirImportOrderDto order, @MappingTarget AirImportOrder targetOrder);

	@BeforeMapping
	default void switchAirAddressesConditionally(AirExportOrderDto source, @MappingTarget AirExportOrder target) {

		if (target.getQuoteInformation() != null) {
			// Q2B handling

			if (source.getConsigneeAddress().getId() == null && source.getDeliveryAddress() != null && Objects.equals(source.getDeliveryAddress().getId(),
					target.getConsigneeAddress().getOrderAddressId())) {
				// switch consignee to delivery
				OrderAddress consigneeAddress = target.getConsigneeAddress();
				target.setDeliveryAddress(consigneeAddress);
				target.setConsigneeAddress(null);
			} else if (source.getDeliveryAddress() == null && target.getDeliveryAddress() != null) {
				// delivery address deleted, switch back
				OrderAddress deliveryAddress = target.getDeliveryAddress();
				target.setConsigneeAddress(deliveryAddress);
				target.setDeliveryAddress(null);
			}
		}
	}

	@BeforeMapping
	default void switchAirAddressesConditionally(AirImportOrderDto source, @MappingTarget AirImportOrder target) {

		if (target.getQuoteInformation() != null) {
			// Q2B handling

			if (source.getShipperAddress().getId() == null && source.getPickupAddress() != null && Objects.equals(source.getPickupAddress().getId(),
					target.getShipperAddress().getOrderAddressId())) {
				// switch shipper to pickup
				OrderAddress shipperAddress = target.getShipperAddress();
				target.setPickupAddress(shipperAddress);
				target.setShipperAddress(null);
			} else if (source.getPickupAddress() == null && target.getPickupAddress() != null) {
				// pickup address deleted, switch back
				OrderAddress pickupAddress = target.getPickupAddress();
				target.setShipperAddress(pickupAddress);
				target.setPickupAddress(null);
			}
		}
	}

	@BeforeMapping
	default void switchSeaAddressesConditionally(SeaExportOrderDto source, @MappingTarget SeaExportOrder target) {

		if (target.getQuoteInformation() != null) {
			// Q2B handling

			if (source.getConsigneeAddress().getId() == null && source.getDeliveryAddress() != null && Objects.equals(source.getDeliveryAddress().getId(),
					target.getConsigneeAddress().getOrderAddressId())) {
				// switch consignee to delivery
				OrderAddress consigneeAddress = target.getConsigneeAddress();
				target.setDeliveryAddress(consigneeAddress);
				target.setConsigneeAddress(null);
			} else if (source.getDeliveryAddress() == null && target.getDeliveryAddress() != null) {
				// delivery address deleted, switch back
				OrderAddress deliveryAddress = target.getDeliveryAddress();
				target.setConsigneeAddress(deliveryAddress);
				target.setDeliveryAddress(null);
			}
		}
	}

	@BeforeMapping
	default void switchSeaAddressesConditionally(SeaImportOrderDto source, @MappingTarget SeaImportOrder target) {

		if (target.getQuoteInformation() != null) {
			// Q2B handling

			if (source.getShipperAddress().getId() == null && source.getPickupAddress() != null && Objects.equals(source.getPickupAddress().getId(),
					target.getShipperAddress().getOrderAddressId())) {
				// switch shipper to pickup
				OrderAddress shipperAddress = target.getShipperAddress();
				target.setPickupAddress(shipperAddress);
				target.setShipperAddress(null);
			} else if (source.getPickupAddress() == null && target.getPickupAddress() != null) {
				// pickup address deleted, switch back
				OrderAddress pickupAddress = target.getPickupAddress();
				target.setShipperAddress(pickupAddress);
				target.setPickupAddress(null);
			}
		}
	}

	@InheritConfiguration(name = "mapAir")
	@Mapping(source = "shipmentNumber", target = "shipmentNumber", ignore = true)
	@Mapping(source = "status", target = "status", ignore = true)
	@Mapping(source = "quoteInformation", target = "quoteInformation", ignore = true)
	void updateAirOrder(AirExportOrderDto airExportOrderDto, @MappingTarget AirExportOrder targetOrder);

	@InheritConfiguration(name = "mapSea")
	@Mapping(source = "shipmentNumber", target = "shipmentNumber", ignore = true)
	@Mapping(source = "status", target = "status", ignore = true)
	@Mapping(source = "quoteInformation", target = "quoteInformation", ignore = true)
	void updateSeaOrder(SeaImportOrderDto order, @MappingTarget SeaImportOrder targetOrder);

	@InheritConfiguration(name = "mapSea")
	@Mapping(source = "shipmentNumber", target = "shipmentNumber", ignore = true)
	@Mapping(source = "status", target = "status", ignore = true)
	@Mapping(source = "quoteInformation", target = "quoteInformation", ignore = true)
	void updateSeaOrder(SeaExportOrderDto order, @MappingTarget SeaExportOrder targetOrder);

	@AfterMapping
	default void updateReferences(@MappingTarget final Order order) {
		final List<OrderFurtherAddress> addresses = order.getAddresses();
		if (addresses != null) {
			addresses.forEach(address -> address.setOrder(order));
		}
		final List<OrderText> texts = order.getOrderTexts();
		if (texts != null) {
			texts.forEach(line -> line.setOrder(order));
		}
	}

	// region AIR

	@AfterMapping
	default void updateReferencesAir(@MappingTarget final AirOrder order) {
		final List<AirOrderLine> orderLines = order.getOrderLines();
		if (orderLines != null) {
			orderLines.forEach(line -> {
				line.setOrder(order);

				if (line.getHsCodes() != null) {
					line.getHsCodes().forEach(hsCode -> hsCode.setOrderLine(line));
				}
			});
		}

		final List<AirOrderReference> orderReferences = order.getOrderReferences();
		if (orderReferences != null) {
			orderReferences.forEach(reference -> reference.setOrder(order));
		}
	}

	@AfterMapping
	default void handleShippersReference(@MappingTarget final AirOrder order, final AirExportOrderDto inputOrder) {
		handleShippersReference(order, inputOrder.getLoading(), inputOrder.getUnloading());
	}

	@AfterMapping
	default void handleShippersReference(@MappingTarget final AirOrder order, final AirImportOrderDto inputOrder) {
		handleShippersReference(order, inputOrder.getLoading(), inputOrder.getUnloading());
	}

	@AfterMapping
	default void handleShippersReference(@MappingTarget final AirExportOrderDto order, final AirOrder inputOrder) {
		List<AirOrderReference> orderReferences = inputOrder.getOrderReferences();
		order.setLoading(isAirShippersReferenceLoadingUnloading(orderReferences, AirOrderReference::isLoading));
		order.setUnloading(isAirShippersReferenceLoadingUnloading(orderReferences, AirOrderReference::isUnloading));
	}

	@AfterMapping
	default void handleShippersReference(@MappingTarget final AirImportOrderDto order, final AirOrder inputOrder) {
		List<AirOrderReference> orderReferences = inputOrder.getOrderReferences();
		order.setLoading(isAirShippersReferenceLoadingUnloading(orderReferences, AirOrderReference::isLoading));
		order.setUnloading(isAirShippersReferenceLoadingUnloading(orderReferences, AirOrderReference::isUnloading));
	}

	// endregion

	// region ROAD

	@AfterMapping
	default void removeAdditionalAddresses(@MappingTarget final CollectionOrder order, RoadCollectionOrderDto inputOrder) {
		order.getAddresses().clear();
	}

	// endregion

	default Boolean isAirShippersReferenceLoadingUnloading(List<AirOrderReference> orderReferences, Predicate<AirOrderReference> unloadingFunction) {
		return Optional.ofNullable(orderReferences)
				.map(refs -> refs.stream().filter(r -> r.getReferenceType().equals(AirSeaOrderReferenceType.SHIPPERS_REFERENCE)).anyMatch(unloadingFunction)).orElse(null);
	}

	default void handleShippersReference(final AirOrder order, Boolean inputLoading, Boolean inputUnloading) {
		final List<AirOrderReference> orderReferences = order.getOrderReferences();
		if (orderReferences != null) {
			Optional<AirOrderReference> existingShippersReference = orderReferences.stream().filter(s -> s.getReferenceType().equals(AirSeaOrderReferenceType.SHIPPERS_REFERENCE))
					.findFirst();

			if (existingShippersReference.isPresent()) {
				// Update shippers reference
				AirOrderReference ref = existingShippersReference.get();
				ref.setReferenceValue(order.getShipperReference());
				if (inputUnloading != null) {
					ref.setUnloading(inputUnloading);
				}
				if (inputLoading != null) {
					ref.setLoading(inputLoading);
				}

			} else {
				// Create shippers reference
				AirOrderReference ref = new AirOrderReference();
				ref.setReferenceValue(order.getShipperReference());
				ref.setLoading(MappingUtil.nullSafe(inputLoading));
				ref.setUnloading(MappingUtil.nullSafe(inputUnloading));
				ref.setReferenceType(AirSeaOrderReferenceType.SHIPPERS_REFERENCE);
				ref.setOrder(order);
				orderReferences.add(ref);
			}
		}
	}

	@AfterMapping
	default void updateOrderLinesSea(@MappingTarget final SeaOrder order) {
		final List<SeaOrderLine> orderLines = order.getOrderLines();
		if (orderLines != null) {
			orderLines.forEach(line -> {
				line.setOrder(order);

				if (line.getHsCodes() != null) {
					line.getHsCodes().forEach(hsCode -> hsCode.setOrderLine(line));
				}
			});
		}

		if (order.getFullContainerLoads() != null) {
			order.getFullContainerLoads().forEach(containerLoad -> {
				containerLoad.setOrder(order);

				containerLoad.getOrderLines().forEach(orderLine -> {
					orderLine.setFullContainerLoadId(containerLoad.getId());
					orderLine.setOrder(order);

					if (orderLine.getHsCodes() != null) {
						orderLine.getHsCodes().forEach(hsCode -> hsCode.setOrderLine(orderLine));
					}
				});
			});
		}
	}

	@AfterMapping
	default void updateReferencesSea(@MappingTarget final SeaOrder order) {
		final List<SeaOrderReference> orderReferences = order.getOrderReferences();
		if (orderReferences != null) {
			orderReferences.forEach(reference -> reference.setOrder(order));
		}
	}

	@AfterMapping
	default void setDefaultsForFullContainerLoadOrder(@MappingTarget final SeaOrder order) {
		if (order.isFullContainerLoad()) {
			order.setStackable(Boolean.FALSE);
			order.setShockSensitive(Boolean.FALSE);
		}
	}

	@AfterMapping
	default void handleShippersReference(@MappingTarget final SeaImportOrderDto order, final SeaOrder inputOrder) {
		List<SeaOrderReference> orderReferences = inputOrder.getOrderReferences();
		order.setLoading(isShippersReferenceLoadingUnloading(orderReferences, SeaOrderReference::isLoading));
		order.setUnloading(isShippersReferenceLoadingUnloading(orderReferences, SeaOrderReference::isUnloading));
	}

	@AfterMapping
	default void handleShippersReference(@MappingTarget final SeaExportOrderDto order, final SeaOrder inputOrder) {
		List<SeaOrderReference> orderReferences = inputOrder.getOrderReferences();
		order.setLoading(isShippersReferenceLoadingUnloading(orderReferences, SeaOrderReference::isLoading));
		order.setUnloading(isShippersReferenceLoadingUnloading(orderReferences, SeaOrderReference::isUnloading));
	}

	default Boolean isShippersReferenceLoadingUnloading(List<SeaOrderReference> orderReferences, Predicate<SeaOrderReference> unloadingFunction) {
		return Optional.ofNullable(orderReferences)
				.map(refs -> refs.stream().filter(r -> r.getReferenceType().equals(AirSeaOrderReferenceType.SHIPPERS_REFERENCE)).anyMatch(unloadingFunction)).orElse(null);
	}

	@AfterMapping
	default void handleShippersReference(@MappingTarget final SeaOrder order, final SeaExportOrderDto inputOrder) {
		handleShippersReference(order, inputOrder.getLoading(), inputOrder.getUnloading());
	}

	@AfterMapping
	default void handleShippersReference(@MappingTarget final SeaOrder order, final SeaImportOrderDto inputOrder) {
		handleShippersReference(order, inputOrder.getLoading(), inputOrder.getUnloading());
	}

	default void handleShippersReference(@MappingTarget SeaOrder order, Boolean inputLoading, Boolean inputUnloading) {
		List<SeaOrderReference> orderReferences = order.getOrderReferences();

		if (order.getShipperReference() != null && StringUtils.isNotBlank(order.getShipperReference()) && orderReferences != null) {
			Optional<SeaOrderReference> existingShippersReference = orderReferences.stream().filter(s -> s.getReferenceType().equals(AirSeaOrderReferenceType.SHIPPERS_REFERENCE))
					.findFirst();

			if (existingShippersReference.isPresent()) {
				// Update shippers reference
				SeaOrderReference ref = existingShippersReference.get();
				ref.setReferenceValue(order.getShipperReference());
				if (inputUnloading != null) {
					ref.setUnloading(inputUnloading);
				}
				if (inputLoading != null) {
					ref.setLoading(inputLoading);
				}

			} else {
				// Create shippers reference
				SeaOrderReference ref = new SeaOrderReference();
				ref.setReferenceValue(order.getShipperReference());
				ref.setUnloading(MappingUtil.nullSafe(inputUnloading));
				ref.setLoading(MappingUtil.nullSafe(inputLoading));
				ref.setReferenceType(AirSeaOrderReferenceType.SHIPPERS_REFERENCE);
				ref.setOrder(order);
				orderReferences.add(ref);
			}
		}
	}

	@AfterMapping
	default void updateReferencesRoad(@MappingTarget final RoadOrder order) {
		final List<RoadOrderLine> orderLines = order.getOrderLines();
		if (orderLines != null) {
			orderLines.forEach(line -> line.setOrder(order));
		}
		final List<RoadOrderReference> orderReferences = order.getOrderReferences();
		if (orderReferences != null) {
			orderReferences.forEach(reference -> reference.setOrder(order));
		}

		if (order.getOrderNumber() != null && StringUtils.isNotBlank(order.getOrderNumber()) && orderReferences != null) {
			Optional<RoadOrderReference> existingOrderNumber = orderReferences.stream().filter(r -> r.getReferenceType().equals(ReferenceType.ORDER_NUMBER)).findFirst();

			if (existingOrderNumber.isPresent()) {
				// Update order number reference
				RoadOrderReference ref = existingOrderNumber.get();
				ref.setReference(order.getOrderNumber());
			} else {
				// Create order number reference
				RoadOrderReference refToCreate = new RoadOrderReference();
				refToCreate.setOrder(order);
				refToCreate.setReferenceType(ReferenceType.ORDER_NUMBER);
				refToCreate.setReference(order.getOrderNumber());
				orderReferences.add(refToCreate);
			}
		}

		if (order.getPackingPositions() != null) {
			order.getPackingPositions().forEach(packingPosition -> {
				packingPosition.setOrder(order);

				packingPosition.getOrderLines().forEach(orderLine -> {
					orderLine.setPackingPositionId(packingPosition.getId());
					orderLine.setOrder(order);
				});
			});
		}

	}

	@AfterMapping
	default void afterMappingAirOrder(@MappingTarget AirExportOrderDto airExportOrder) {
		final DeliveryProductDto product = airExportOrder.getProduct();
		// Remove the complete sub object if code is not available
		if (product != null && product.getCode() == null) {
			airExportOrder.setProduct(null);
		}
	}

	@AfterMapping
	default void afterMappingAirOrder(@MappingTarget AirImportOrderDto airImportOrder) {
		final DeliveryProductDto product = airImportOrder.getProduct();
		// Remove the complete sub object if code is not available
		if (product != null && product.getCode() == null) {
			airImportOrder.setProduct(null);
		}
	}

	// Avoid whitespace for GLN: https://dil-itd.atlassian.net/browse/DFE-2549
	@BeforeMapping
	default void avoidWhiteSpaceForGlns(final BasicOrderDto basicOrder) {
		if (basicOrder == null) {
			return;
		}
		if (basicOrder.getConsigneeAddress() != null && StringUtils.isAllBlank(basicOrder.getConsigneeAddress().getGln())) {
			basicOrder.getConsigneeAddress().setGln(null);
		}
		if (basicOrder.getShipperAddress() != null && StringUtils.isAllBlank(basicOrder.getShipperAddress().getGln())) {
			basicOrder.getShipperAddress().setGln(null);
		}
		if (basicOrder.getFurtherAddresses() != null) {
			basicOrder.getFurtherAddresses().forEach(address -> {
				if (StringUtils.isAllBlank(address.getGln())) {
					address.setGln(null);
				}
			});
		}
	}

	// We need to add empty collections to avoid the setter of null overriding an empty persistent bag during update
	@BeforeMapping
	default void fillNullCollections(final BasicOrderDto basicOrder) {
		if (basicOrder == null) {
			return;
		}
		if (basicOrder.getFurtherAddresses() == null) {
			basicOrder.setFurtherAddresses(new ArrayList<>());
		}
		if (basicOrder.getTexts() == null) {
			basicOrder.setTexts(new ArrayList<>());
		}
	}

	// We need to add empty collections to avoid the setter of null overriding an empty persistent bag during update
	@BeforeMapping
	default void fillNullCollectionsAirExport(final AirExportOrderDto airOrder) {
		if (airOrder == null) {
			return;
		}
		if (airOrder.getReferences() == null) {
			airOrder.setReferences(new ArrayList<>());
		} else {
			List<AirSeaOrderReferenceDto> references = airOrder.getReferences().stream().filter(Objects::nonNull).collect(Collectors.toCollection(ArrayList::new));
			airOrder.setReferences(references);
		}
		if (airOrder.getOrderLineItems() == null) {
			airOrder.setOrderLineItems(new ArrayList<>());
		}
	}

	@BeforeMapping
	default void fillNullCollectionsAirImport(final AirImportOrderDto airOrder) {
		if (airOrder == null) {
			return;
		}
		if (airOrder.getReferences() == null) {
			airOrder.setReferences(new ArrayList<>());
		} else {
			List<AirSeaOrderReferenceDto> references = airOrder.getReferences().stream().filter(Objects::nonNull).collect(Collectors.toCollection(ArrayList::new));
			airOrder.setReferences(references);
		}
		if (airOrder.getOrderLineItems() == null) {
			airOrder.setOrderLineItems(new ArrayList<>());
		}
	}

	@BeforeMapping
	default void fillNullCollectionsSea(final SeaExportOrderDto seaOrder) {
		if (seaOrder == null) {
			return;
		}
		if (seaOrder.getReferences() == null) {
			seaOrder.setReferences(new ArrayList<>());
		} else {
			List<AirSeaOrderReferenceDto> references = seaOrder.getReferences().stream().filter(Objects::nonNull).collect(Collectors.toCollection(ArrayList::new));
			seaOrder.setReferences(references);
		}
		if (seaOrder.getOrderLineItems() == null) {
			seaOrder.setOrderLineItems(new ArrayList<>());
		}

		if (seaOrder.getFullContainerLoads() == null) {
			seaOrder.setFullContainerLoads(new ArrayList<>());
		}
	}

	@BeforeMapping
	default void fillNullCollectionsSea(final SeaImportOrderDto seaOrder) {
		if (seaOrder == null) {
			return;
		}
		if (seaOrder.getReferences() == null) {
			seaOrder.setReferences(new ArrayList<>());
		} else {
			List<AirSeaOrderReferenceDto> references = seaOrder.getReferences().stream().filter(Objects::nonNull).collect(Collectors.toCollection(ArrayList::new));
			seaOrder.setReferences(references);
		}
		if (seaOrder.getOrderLineItems() == null) {
			seaOrder.setOrderLineItems(new ArrayList<>());
		}

		if (seaOrder.getFullContainerLoads() == null) {
			seaOrder.setFullContainerLoads(new ArrayList<>());
		}
	}

	// We need to add empty collections to avoid the setter of null overriding an empty persistent bag during update
	@BeforeMapping
	default void fillNullCollectionsRoadForwarding(final RoadForwardingOrderDto roadOrder) {
		if (roadOrder == null) {
			return;
		}
		if (roadOrder.getReferences() == null) {
			roadOrder.setReferences(new ArrayList<>());
		} else {
			List<RoadOrderReferenceDto> references = roadOrder.getReferences().stream().filter(Objects::nonNull).collect(Collectors.toCollection(ArrayList::new));
			roadOrder.setReferences(references);
		}
		if (roadOrder.getOrderLineItems() == null) {
			roadOrder.setOrderLineItems(new ArrayList<>());
		}

		if (roadOrder.getPackingPositions() == null) {
			roadOrder.setPackingPositions(new ArrayList<>());
		}
	}

	@BeforeMapping
	default void fillNullCollectionsRoadCollection(final RoadCollectionOrderDto roadOrder) {
		if (roadOrder == null) {
			return;
		}
		if (roadOrder.getReferences() == null) {
			roadOrder.setReferences(new ArrayList<>());
		} else {
			List<RoadOrderReferenceDto> references = roadOrder.getReferences().stream().filter(Objects::nonNull).collect(Collectors.toCollection(ArrayList::new));
			roadOrder.setReferences(references);
		}
		if (roadOrder.getOrderLineItems() == null) {
			roadOrder.setOrderLineItems(new ArrayList<>());
		}

		if (roadOrder.getPackingPositions() == null) {
			roadOrder.setPackingPositions(new ArrayList<>());
		}
	}

	@Named("extractSSCC")
	default List<String> extractSSCC(final List<OrderSscc> sscc) {
		if (sscc == null) {
			return new ArrayList<>();
		}
		return sscc.stream().map(OrderSscc::getSscc).toList();
	}

	@Named("mapInstantToDateTime")
	default OffsetDateTime mapInstantToDateTime(final Instant instant) {
		return instant != null ? OffsetDateTime.ofInstant(instant, ZoneOffset.UTC) : null;
	}

	@Named("mapOrderCloned")
	default Boolean isClonedOrder(SourceOfOrder sourceOfOrder) {
		return SourceOfOrder.CLONE.equals(sourceOfOrder);
	}

	@Named("updateSsccs")
	default void updateSsccs(final List<String> orderSsccs, @MappingTarget final List<OrderSscc> ssccs) {
		if (!orderSsccs.isEmpty()) {
			int previousSize = ssccs.size();
			ssccs.removeIf(sscc -> !orderSsccs.contains(sscc.getSscc()));
			if (previousSize != ssccs.size()) {
				LoggerFactory.getLogger(OrderMapper.class).info("{} SSCCs removed from order only keeping {}", previousSize - ssccs.size(), ssccs);
			}
		}
	}

}
