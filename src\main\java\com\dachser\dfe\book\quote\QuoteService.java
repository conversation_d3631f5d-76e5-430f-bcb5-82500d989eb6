package com.dachser.dfe.book.quote;

import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.BasicQuoteInformationDto;
import com.dachser.dfe.book.model.OrderResponseBodyDto;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderDefaults;
import com.dachser.dfe.book.order.OrderService;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.exception.OrderCreationFromQuoteException;
import com.dachser.dfe.book.order.road.RoadOrder;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class QuoteService {

	private final QuoteMapper quoteMapper;

	private final OrderService orderService;

	private final OrderDefaults orderDefaults;

	public @Nullable OrderResponseBodyDto createDraftOrderFromQuote(@Nullable BasicQuoteInformationDto quoteInformation) {
		if (quoteInformation == null) {
			throw new OrderCreationFromQuoteException("Quote information is missing", new Throwable("Missing argument 'quoteInformation'"), null);
		}
		log.info("Recording new order from quote: {}", quoteInformation.getQuoteReference());
		final Order mappedFromQuote = quoteMapper.mapToOrder(quoteInformation);

		if (mappedFromQuote instanceof AirOrder airOrder) {
			orderDefaults.fillPrincipalDataToAirQ2BOrder(airOrder);
			orderDefaults.createShipperReferenceDefault(airOrder);
		}

		if (mappedFromQuote instanceof RoadOrder roadOrder && !roadOrder.hasValidExpiryDateFromQuote()) {
			throw new OrderCreationFromQuoteException("ePricing quotes must have expiration time, regular quotes must not have expiration time",
					new Throwable("Invalid expiry date"), quoteInformation);
		}

		try {
			OrderResponseBodyDto createdOrder = orderService.createNewDraftOrder(mappedFromQuote);
			((BasicOrderDto) createdOrder).setQuoteInformation(quoteInformation);
			return createdOrder;
		} catch (ConstraintViolationException e) {
			log.error("ConstraintViolations while creating order from quote: {}", e.getConstraintViolations());
			throw new OrderCreationFromQuoteException("ConstraintViolations while creating order from quote", e, quoteInformation);
		} catch (Exception e) {
			log.error("Error while creating order from quote {}", quoteInformation.getQuoteReference(), e);
			throw new OrderCreationFromQuoteException("Error while creating order from quote", e, quoteInformation);
		}
	}

}
