package com.dachser.dfe.book.dangerousgoods;

import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.dangerousgood.ADRDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGoodDataItem;
import com.dachser.dfe.book.order.common.dangerousgood.LQDangerousGood;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.masterdata.thirdparty.model.MDTCalculationPoint;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DangerousGoodsTransferListServiceTest {

	private static final String VALID_DGM_ID = "123";

	private static final String INVALID_DGM_ID = "abc";

	private static final String NULL_DGM_ID = null;

	private static final String COUNTRY_CODE = "DE";

	private static final BigDecimal GROSS_MASS = BigDecimal.valueOf(5);

	private static final int CALCULATED_POINTS = 99;

	private static final int EXISTING_POINTS = 50;

	@Mock
	private DangerousGoodsThirdPartyAdapter adapter;

	@InjectMocks
	private DangerousGoodsTransferListService service;

	@Nested
	class CalculatePoints {

		@Test
		void shouldReturnEarlyWhenNoValidCalculationPoints() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of());
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			service.calculateDangerousGoodsPoints(order);

			verifyNoInteractions(adapter);
		}

		@Test
		void shouldReturnEarlyWhenNoDangerousGoods() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(false);

			service.calculateDangerousGoodsPoints(order);

			verifyNoInteractions(adapter);
			verify(order, never()).getOrderLines();
		}

		@Test
		void shouldReturnEarlyWhenNoOrderLines() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);
			when(order.getOrderLines()).thenReturn(List.of());

			service.calculateDangerousGoodsPoints(order);

			verifyNoInteractions(adapter);
		}

		@Test
		void shouldSkipNullDangerousGoods() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			List<ADRDangerousGood> dangerousGoodsWithNull = new ArrayList<>();
			dangerousGoodsWithNull.add(null);
			when(orderLine.getAdrDangerousGoods()).thenReturn(dangerousGoodsWithNull);
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			service.calculateDangerousGoodsPoints(order);

			verifyNoInteractions(adapter);
		}

		@Test
		void shouldSkipADRDangerousGoodWithNullDataItem() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			ADRDangerousGood dangerousGood = new ADRDangerousGood();
			dangerousGood.setDangerousGoodDataItem(null);

			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			service.calculateDangerousGoodsPoints(order);

			verifyNoInteractions(adapter);
		}

		@Test
		void shouldSkipDangerousGoodWithExistingCalculatedPoints() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			DangerousGoodDataItem dataItem = new DangerousGoodDataItem();
			dataItem.setExternalDgmId(VALID_DGM_ID);

			ADRDangerousGood dangerousGood = new ADRDangerousGood();
			dangerousGood.setDangerousGoodDataItem(dataItem);
			dangerousGood.setCalculatedPoints(EXISTING_POINTS);
			dangerousGood.setGrossMass(GROSS_MASS);

			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			service.calculateDangerousGoodsPoints(order);

			verifyNoInteractions(adapter);
			assertThat(dangerousGood.getCalculatedPoints()).isEqualTo(EXISTING_POINTS);
		}

		@Test
		void shouldSkipDangerousGoodWithNullDgmId() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			DangerousGoodDataItem dataItem = new DangerousGoodDataItem();
			dataItem.setExternalDgmId(NULL_DGM_ID);

			ADRDangerousGood dangerousGood = new ADRDangerousGood();
			dangerousGood.setDangerousGoodDataItem(dataItem);
			dangerousGood.setGrossMass(GROSS_MASS);

			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			service.calculateDangerousGoodsPoints(order);

			verifyNoInteractions(adapter);
		}

		@Test
		void shouldSkipDangerousGoodWithInvalidDgmId() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			DangerousGoodDataItem dataItem = new DangerousGoodDataItem();
			dataItem.setExternalDgmId(INVALID_DGM_ID);

			ADRDangerousGood dangerousGood = new ADRDangerousGood();
			dangerousGood.setDangerousGoodDataItem(dataItem);
			dangerousGood.setGrossMass(GROSS_MASS);

			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			service.calculateDangerousGoodsPoints(order);

			verifyNoInteractions(adapter);
		}

		@Test
		void shouldSkipDangerousGoodWithNullGrossMass() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			DangerousGoodDataItem dataItem = new DangerousGoodDataItem();
			dataItem.setExternalDgmId(VALID_DGM_ID);

			ADRDangerousGood dangerousGood = new ADRDangerousGood();
			dangerousGood.setDangerousGoodDataItem(dataItem);
			dangerousGood.setGrossMass(null);

			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			service.calculateDangerousGoodsPoints(order);

			verifyNoInteractions(adapter);
		}

		@Test
		void shouldCalculatePointsForValidDangerousGood() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			DangerousGoodDataItem dataItem = new DangerousGoodDataItem();
			dataItem.setExternalDgmId(VALID_DGM_ID);

			ADRDangerousGood dangerousGood = new ADRDangerousGood();
			dangerousGood.setDangerousGoodDataItem(dataItem);
			dangerousGood.setGrossMass(GROSS_MASS);
			dangerousGood.setCalculatedPoints(null);

			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			OrderAddress shipperAddress = new OrderAddress();
			shipperAddress.setCountryCode(COUNTRY_CODE);
			when(order.getShipperAddress()).thenReturn(shipperAddress);

			DangerousGoodsTransferListService.DgmIdAndQuantity result = new DangerousGoodsTransferListService.DgmIdAndQuantity(123, 5.0, CALCULATED_POINTS);
			when(adapter.calculatePointsForSingleValues(eq(COUNTRY_CODE), anyList())).thenReturn(List.of(result));

			service.calculateDangerousGoodsPoints(order);

			assertThat(dangerousGood.getCalculatedPoints()).isEqualTo(495);

			ArgumentCaptor<List<MDTCalculationPoint>> captor = ArgumentCaptor.forClass(List.class);
			verify(adapter).calculatePointsForSingleValues(eq(COUNTRY_CODE), captor.capture());
			MDTCalculationPoint capturedPoint = captor.getValue().get(0);
			assertThat(capturedPoint.getDgmId()).isEqualTo(123);
			assertThat(capturedPoint.getQuantity()).isEqualTo(1.0);
		}

		@Test
		void shouldHandleNullCalculatedResults() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			DangerousGoodDataItem dataItem = new DangerousGoodDataItem();
			dataItem.setExternalDgmId(VALID_DGM_ID);

			ADRDangerousGood dangerousGood = new ADRDangerousGood();
			dangerousGood.setDangerousGoodDataItem(dataItem);
			dangerousGood.setGrossMass(GROSS_MASS);
			dangerousGood.setCalculatedPoints(null);

			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			OrderAddress shipperAddress = new OrderAddress();
			shipperAddress.setCountryCode(COUNTRY_CODE);
			when(order.getShipperAddress()).thenReturn(shipperAddress);

			when(adapter.calculatePointsForSingleValues(eq(COUNTRY_CODE), anyList())).thenReturn(null);

			service.calculateDangerousGoodsPoints(order);

			assertThat(dangerousGood.getCalculatedPoints()).isNull();
		}

		@Test
		void shouldHandleEmptyCalculatedResults() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			DangerousGoodDataItem dataItem = new DangerousGoodDataItem();
			dataItem.setExternalDgmId(VALID_DGM_ID);

			ADRDangerousGood dangerousGood = new ADRDangerousGood();
			dangerousGood.setDangerousGoodDataItem(dataItem);
			dangerousGood.setGrossMass(GROSS_MASS);
			dangerousGood.setCalculatedPoints(null);

			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			OrderAddress shipperAddress = new OrderAddress();
			shipperAddress.setCountryCode(COUNTRY_CODE);
			when(order.getShipperAddress()).thenReturn(shipperAddress);

			when(adapter.calculatePointsForSingleValues(eq(COUNTRY_CODE), anyList())).thenReturn(List.of());

			service.calculateDangerousGoodsPoints(order);

			assertThat(dangerousGood.getCalculatedPoints()).isNull();
		}

		@Test
		void shouldNotUpdateDangerousGoodWhenNoMatchingResult() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			DangerousGoodDataItem dataItem = new DangerousGoodDataItem();
			dataItem.setExternalDgmId(VALID_DGM_ID);

			ADRDangerousGood dangerousGood = new ADRDangerousGood();
			dangerousGood.setDangerousGoodDataItem(dataItem);
			dangerousGood.setGrossMass(GROSS_MASS);
			dangerousGood.setCalculatedPoints(null);

			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			OrderAddress shipperAddress = new OrderAddress();
			shipperAddress.setCountryCode(COUNTRY_CODE);
			when(order.getShipperAddress()).thenReturn(shipperAddress);

			DangerousGoodsTransferListService.DgmIdAndQuantity result = new DangerousGoodsTransferListService.DgmIdAndQuantity(999, 5.0, CALCULATED_POINTS);
			when(adapter.calculatePointsForSingleValues(eq(COUNTRY_CODE), anyList())).thenReturn(List.of(result));

			service.calculateDangerousGoodsPoints(order);

			assertThat(dangerousGood.getCalculatedPoints()).isNull();
		}

		@Test
		void shouldHandleMultipleDangerousGoodsInMultipleOrderLines() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			DangerousGoodDataItem dataItem1 = new DangerousGoodDataItem();
			dataItem1.setExternalDgmId("123");
			ADRDangerousGood dangerousGood1 = new ADRDangerousGood();
			dangerousGood1.setDangerousGoodDataItem(dataItem1);
			dangerousGood1.setGrossMass(BigDecimal.valueOf(3));
			dangerousGood1.setCalculatedPoints(null);

			DangerousGoodDataItem dataItem2 = new DangerousGoodDataItem();
			dataItem2.setExternalDgmId("456");
			ADRDangerousGood dangerousGood2 = new ADRDangerousGood();
			dangerousGood2.setDangerousGoodDataItem(dataItem2);
			dangerousGood2.setGrossMass(BigDecimal.valueOf(7));
			dangerousGood2.setCalculatedPoints(null);

			RoadOrderLine orderLine1 = mock(RoadOrderLine.class);
			when(orderLine1.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood1));

			RoadOrderLine orderLine2 = mock(RoadOrderLine.class);
			when(orderLine2.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood2));

			when(order.getOrderLines()).thenReturn(List.of(orderLine1, orderLine2));

			OrderAddress shipperAddress = new OrderAddress();
			shipperAddress.setCountryCode(COUNTRY_CODE);
			when(order.getShipperAddress()).thenReturn(shipperAddress);

			List<DangerousGoodsTransferListService.DgmIdAndQuantity> results = List.of(new DangerousGoodsTransferListService.DgmIdAndQuantity(123, 3.0, 55),
					new DangerousGoodsTransferListService.DgmIdAndQuantity(456, 7.0, 77));
			when(adapter.calculatePointsForSingleValues(eq(COUNTRY_CODE), anyList())).thenReturn(results);

			service.calculateDangerousGoodsPoints(order);

			assertThat(dangerousGood1.getCalculatedPoints()).isEqualTo(165);
			assertThat(dangerousGood2.getCalculatedPoints()).isEqualTo(539);
		}
	}

	@Nested
	class FetchLocalizedDescription {

		@Test
		void shouldNotCallApiWhenNoDangerousGoods() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(false);
			service.fetchLocalizedDescriptionForDangerousGoods(order, Locale.US.getLanguage());

			verifyNoInteractions(adapter);
		}

		@Test
		void shouldNotCallApiWhenDgmIdsAreEmpty() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of());
			when(orderLine.getLqDangerousGoods()).thenReturn(List.of());
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			service.fetchLocalizedDescriptionForDangerousGoods(order, Locale.US.getLanguage());

			verifyNoInteractions(adapter);
		}

		@Test
		void shouldNotCallApiWhenAllDescriptionsAreSetAndLangIsDefault() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			RoadOrderLine orderLine = mock(RoadOrderLine.class);

			when(order.isDangerousGoodsOrder()).thenReturn(true);
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			ADRDangerousGood dangerousGood = new ADRDangerousGood();
			DangerousGoodDataItem dataItem = new DangerousGoodDataItem();
			dataItem.setExternalDgmId(VALID_DGM_ID);
			dataItem.setDescription("Localized Description");
			dataItem.setDefaultDescription("Default Description");
			dangerousGood.setDangerousGoodDataItem(dataItem);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(orderLine.getLqDangerousGoods()).thenReturn(List.of());

			service.fetchLocalizedDescriptionForDangerousGoods(order, Locale.US.getLanguage());

			verifyNoInteractions(adapter);
		}

		@Test
		void shouldCallApiWhenAllDescriptionsAreSetAndLangIsNotDefault() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			RoadOrderLine orderLine = mock(RoadOrderLine.class);

			when(order.isDangerousGoodsOrder()).thenReturn(true);
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			ADRDangerousGood dangerousGood = new ADRDangerousGood();
			DangerousGoodDataItem dataItem = new DangerousGoodDataItem();
			dataItem.setExternalDgmId(VALID_DGM_ID);
			dataItem.setDescription("Localized Description");
			dataItem.setDefaultDescription("Default Description");
			dangerousGood.setDangerousGoodDataItem(dataItem);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(orderLine.getLqDangerousGoods()).thenReturn(List.of());

			service.fetchLocalizedDescriptionForDangerousGoods(order, Locale.GERMANY.getLanguage());

			verify(adapter, times(1)).fetchLocalizedInformationForDgmIdsRoad(List.of(VALID_DGM_ID), "DE");
		}

		@Test
		void shouldCallApiWhenLangDiffersFromDefaultAndDefaultDescriptionIsNotSet() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			ADRDangerousGood dangerousGood = new ADRDangerousGood();
			DangerousGoodDataItem dataItem = new DangerousGoodDataItem();
			dataItem.setExternalDgmId(VALID_DGM_ID);
			dataItem.setDescription("Localized Description");
			dangerousGood.setDangerousGoodDataItem(dataItem);
			when(order.getOrderLines()).thenReturn(List.of(orderLine));
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(orderLine.getLqDangerousGoods()).thenReturn(List.of());

			service.fetchLocalizedDescriptionForDangerousGoods(order, Locale.GERMAN.getLanguage());

			verify(adapter, times(1)).fetchLocalizedInformationForDgmIdsRoad(List.of(VALID_DGM_ID), "DE");
			verify(adapter, times(1)).fetchLocalizedInformationForDgmIdsRoad(List.of(VALID_DGM_ID), "US");
		}

		@Test
		void shouldCallApiWhenLangDiffersFromDefaultAndLocalizedDescriptionIsNotSet() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			ADRDangerousGood dangerousGood = new ADRDangerousGood();
			DangerousGoodDataItem dataItem = new DangerousGoodDataItem();
			dataItem.setExternalDgmId(VALID_DGM_ID);
			dataItem.setDefaultDescription("Default Description");
			dangerousGood.setDangerousGoodDataItem(dataItem);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(orderLine.getLqDangerousGoods()).thenReturn(List.of());
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			service.fetchLocalizedDescriptionForDangerousGoods(order, Locale.GERMAN.getLanguage());

			verify(adapter, times(1)).fetchLocalizedInformationForDgmIdsRoad(List.of(VALID_DGM_ID), "DE");
			verify(adapter, times(1)).fetchLocalizedInformationForDgmIdsRoad(List.of(VALID_DGM_ID), "US");
		}

		@Test
		void shouldUpdateAdrGoodsWithDescriptions() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			DangerousGoodDataItem dataItem = new DangerousGoodDataItem();
			dataItem.setExternalDgmId(VALID_DGM_ID);
			dataItem.setDescription("Beschreibung Localized");
			dataItem.setDefaultDescription(null);

			ADRDangerousGood dangerousGood = new ADRDangerousGood();
			dangerousGood.setDangerousGoodDataItem(dataItem);
			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			when(orderLine.hasDangerousGoods()).thenReturn(true);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(orderLine.getLqDangerousGoods()).thenReturn(List.of());
			when(orderLine.getDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			DangerousGoodDataItemDto responseDtoLocalized = new DangerousGoodDataItemDto();
			responseDtoLocalized.setDescription("Beschreibung Update");
			responseDtoLocalized.setDgmId(VALID_DGM_ID);
			when(adapter.fetchLocalizedInformationForDgmIdsRoad(List.of(VALID_DGM_ID), "DE")).thenReturn(List.of(responseDtoLocalized));

			DangerousGoodDataItemDto responseDtoDefault = new DangerousGoodDataItemDto();
			responseDtoDefault.setDescription("Description Default");
			responseDtoDefault.setDgmId(VALID_DGM_ID);
			when(adapter.fetchLocalizedInformationForDgmIdsRoad(List.of(VALID_DGM_ID), "US")).thenReturn(List.of(responseDtoDefault));

			service.fetchLocalizedDescriptionForDangerousGoods(order, Locale.GERMAN.getLanguage());

			assertThat(dataItem.getDescription()).isEqualTo("Beschreibung Update");
			assertThat(dataItem.getDefaultDescription()).isEqualTo("Description Default");
			assertThat(dataItem.getExternalDgmId()).isEqualTo(VALID_DGM_ID);
		}

		@Test
		void shouldUpdateLqGoodsWithDescriptions() {
			ForwardingOrder order = mock(ForwardingOrder.class);
			when(order.isDangerousGoodsOrder()).thenReturn(true);

			DangerousGoodDataItem dataItem = new DangerousGoodDataItem();
			dataItem.setExternalDgmId(VALID_DGM_ID);
			dataItem.setDescription("Beschreibung Localized");
			dataItem.setDefaultDescription(null);

			LQDangerousGood dangerousGood = new LQDangerousGood();
			dangerousGood.setDangerousGoodDataItem(dataItem);
			RoadOrderLine orderLine = mock(RoadOrderLine.class);
			when(orderLine.hasDangerousGoods()).thenReturn(true);
			when(orderLine.getAdrDangerousGoods()).thenReturn(List.of());
			when(orderLine.getLqDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(orderLine.getDangerousGoods()).thenReturn(List.of(dangerousGood));
			when(order.getOrderLines()).thenReturn(List.of(orderLine));

			DangerousGoodDataItemDto responseDtoLocalized = new DangerousGoodDataItemDto();
			responseDtoLocalized.setDescription("Beschreibung Update");
			responseDtoLocalized.setDgmId(VALID_DGM_ID);
			when(adapter.fetchLocalizedInformationForDgmIdsRoad(List.of(VALID_DGM_ID), ("DE"))).thenReturn(List.of(responseDtoLocalized));

			DangerousGoodDataItemDto responseDtoDefault = new DangerousGoodDataItemDto();
			responseDtoDefault.setDescription("Description Default");
			responseDtoDefault.setDgmId(VALID_DGM_ID);
			when(adapter.fetchLocalizedInformationForDgmIdsRoad(List.of(VALID_DGM_ID), "US")).thenReturn(List.of(responseDtoDefault));

			service.fetchLocalizedDescriptionForDangerousGoods(order, Locale.GERMAN.getLanguage());

			assertThat(dataItem.getDescription()).isEqualTo("Beschreibung Update");
			assertThat(dataItem.getDefaultDescription()).isEqualTo("Description Default");
			assertThat(dataItem.getExternalDgmId()).isEqualTo(VALID_DGM_ID);
		}

	}
}