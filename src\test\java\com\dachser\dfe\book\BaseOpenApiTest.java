package com.dachser.dfe.book;

import com.dachser.bi.common.security.service.pri.api.scan.bean.FileWrapper;
import com.dachser.bi.common.security.service.pri.api.scan.service.FileScanService;
import com.dachser.bi.common.validation.service.pri.api.file.bean.FileWrapperBean;
import com.dachser.bi.common.validation.service.pri.api.file.service.FileCheckService;
import com.dachser.dfe.book.dataset.DatasetController;
import com.dachser.dfe.book.dip.DipService;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.IncoTermDto;
import com.dachser.dfe.book.model.IrelandPostalCodeDto;
import com.dachser.dfe.book.model.PostcodeValidationDto;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.sea.BaseFullContainerLoadRepository;
import com.dachser.dfe.book.port.PortRepository;
import com.dachser.dfe.book.port.entity.Port;
import com.dachser.dfe.book.port.entity.Type;
import com.dachser.dfe.book.product.road.RoadProductsService;
import com.dachser.dfe.book.product.road.ShipperMasterdataService;
import com.dachser.dfe.book.service.FixedDateValidationService;
import com.dachser.dfe.book.service.RelationLabelPrintingService;
import com.dachser.dfe.book.service.ThirdCountryService;
import com.dachser.dfe.book.service.freightterm.FreightTermAdapter;
import com.dachser.dfe.book.service.goodsgroup.GoodsGroupService;
import com.dachser.dfe.book.service.loadingpoint.LoadingPointService;
import com.dachser.dfe.book.service.ordergroup.OrderGroupService;
import com.dachser.dfe.book.service.postalcode.PostalCodeService;
import com.dachser.dfe.book.term.IncoTermService;
import com.dachser.dfe.book.user.model.RoadCustomer;
import com.dachser.dfe.road.masterdata.model.RMDRelationLabelprintingDTO;
import com.dachser.road.ohs.pub.api.openinghours.service.OpeningHoursService;
import io.restassured.http.Header;
import io.restassured.module.mockmvc.specification.MockMvcRequestSpecification;
import jakarta.annotation.Nullable;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;

import java.time.Instant;
import java.util.List;

import static com.dachser.dfe.book.MockConstants.ACTIVE_COMPANY_ACCESS_TOKEN;
import static com.dachser.dfe.book.MockConstants.INACTIVE_USER_ACCESS_TOKEN;
import static com.dachser.dfe.book.MockConstants.buildMockedInstant;
import static io.restassured.module.mockmvc.RestAssuredMockMvc.given;
import static io.restassured.module.mockmvc.RestAssuredMockMvc.mockMvc;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;

/**
 * Base class for all OpenApi tests.
 * Do not use for other especially unit tests!
 */
@SpringBootTest(classes = DFEBookApplication.class)
@AutoConfigureMockMvc
@ActiveProfiles(value = { "apitest", "localmock" })
@TestPropertySource(properties = { "spring.main.allow-bean-definition-overriding=true" })
@AutoConfigureWireMock(port = 0, stubs = "classpath:/wiremock-stubs")
public abstract class BaseOpenApiTest {

	protected static final String DEFAULT_CORS_TOKEN_VALUE = "default-cors-token-value-mock";

	protected static final String INVALID_CUST_NO = "09000001";

	protected static final String VALID_CUST_NO_1 = "00000001";

	protected static final String VALID_CUST_NO_AIR = "00000100";

	protected static final String CUSTOMER_NUMBER_PARAM = "customerNumber";

	protected static final String CUSTOMER_SEGMENT_PARAM = "customerSegment";

	protected static final String ORDER_TYPE_PARAM = "orderType";

	protected static final String TRACK_AND_TRACE_VERSION = "/v1";

	protected static final String DIVISION_PARAM = "division";

	protected static final String SHIPPER_COUNTRY_CODE_PARAM = "shipperCountryCode";

	protected static final String CONSIGNEE_COUNTRY_CODE_PARAM = "consigneeCountryCode";

	@MockBean
	protected OpeningHoursService hoursService;

	@MockBean
	protected BaseFullContainerLoadRepository baseFullContainerLoadRepository;

	@MockBean
	protected RoadProductsService roadProductsService;

	@MockBean
	protected FreightTermAdapter freightTermAdapter;

	@MockBean
	protected IncoTermService incoTermService;

	@MockBean
	protected FixedDateValidationService fixedDateValidationService;

	@MockBean
	protected OrderGroupService orderGroupService;

	@MockBean
	protected GoodsGroupService goodsGroupService;

	@MockBean
	protected ThirdCountryService thirdCountryService;

	@MockBean
	protected FileScanService fileScanService;

	@MockBean
	protected FileCheckService fileCheckService;

	@MockBean
	protected DipService dipService;

	@MockBean
	protected RelationLabelPrintingService relationLabelPrintingService;

	@MockBean
	protected LoadingPointService loadingPointService;

	@MockBean
	protected ShipperMasterdataService shipperMasterdataService;

	@MockBean
	protected DatasetController datasetController;

	@MockBean
	protected PostalCodeService postalCodeService;

	@Value("${openapi.dFEBook.base-path:}")
	private String basePath;

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private PortRepository portRepository;

	private IncoTermDto mockIncoTerm;

	private IncoTermDto cptInocTerm;

	protected MockedStatic<Instant> instantMockedStatic;

	private MockMvcRequestSpecification requestSpec;

	private MockMvcRequestSpecification invalidRequestSpec;

	private MockMvcRequestSpecification requestWriteSpec;


	@BeforeEach
	public void baseOpenApiTestSetUp() {

		final IrelandPostalCodeDto irelandPostalCodeDto = new IrelandPostalCodeDto();
		irelandPostalCodeDto.setTown("Dublin");
		irelandPostalCodeDto.setCounty("Testing");
		irelandPostalCodeDto.setEircode("H12");
		irelandPostalCodeDto.setDachserPLZ("12322");

		mockMvc(mockMvc);
		when(freightTermAdapter.validateFreightTerm(anyInt(), anyString(), anyString())).thenReturn(Boolean.TRUE);
		mockIncoTerm = new IncoTermDto().code("MOC").dachserCode("MOC").label("Mocklabel").description("Mockdescription");
		cptInocTerm = new IncoTermDto().code("CPT").dachserCode("CPT").description("CPT").label("CPT");
		when(incoTermService.getAllActiveIncoTerms()).thenReturn(List.of(mockIncoTerm, cptInocTerm));
		when(incoTermService.getIncoTermByDachserCode(mockIncoTerm.getDachserCode())).thenReturn(mockIncoTerm);
		when(incoTermService.getIncoTermByDachserCode(cptInocTerm.getDachserCode())).thenReturn(cptInocTerm);
		when(incoTermService.isIncoTermActiveByDachserCode(cptInocTerm.getDachserCode())).thenReturn(true);
		when(incoTermService.isIncoTermActiveByDachserCode(mockIncoTerm.getDachserCode())).thenReturn(true);

		when(roadProductsService.validateProduct(any(RoadOrder.class), anyInt())).thenReturn(Boolean.TRUE);
		when(fixedDateValidationService.validateFixedDate(any(RoadOrder.class), anyInt())).thenReturn(Boolean.TRUE);
		when(orderGroupService.validateOrderGroup(any(RoadOrder.class), anyInt())).thenReturn(Boolean.TRUE);
		when(orderGroupService.getOrderGroupsForCustomer(any(RoadCustomer.class), any(Division.class), anyInt(), anyString(), anyString(), any(OrderType.class))).thenReturn(
				List.of());
		when(goodsGroupService.hasCustomerGoodsGroups(anyString(), anyInt(), anyInt())).thenReturn(Boolean.TRUE);
		when(goodsGroupService.findInvalidGoodsGroupInOrderLines(anyList(), anyBoolean())).thenReturn(List.of());
		when(fileScanService.isThisFileHarmless(any(FileWrapper.class), anyString(), anyString())).thenReturn(Boolean.TRUE);
		when(fileCheckService.isFileExtensionCorrect(any(FileWrapperBean.class), anyString())).thenReturn(Boolean.TRUE);
		when(dipService.createAndPublishXmlForOrder(any(Order.class))).thenReturn(Boolean.TRUE);
		when(relationLabelPrintingService.retrieveRelation(any())).thenReturn(new RMDRelationLabelprintingDTO());
		when(shipperMasterdataService.isOrderNumberMandatoryForCustomerNumber(anyString())).thenReturn(Boolean.TRUE);
		when(postalCodeService.findDachserPostalcodes(anyString())).thenReturn(List.of(irelandPostalCodeDto));
		when(postalCodeService.validatePostCode(anyString(), anyString())).thenReturn(new PostcodeValidationDto().valid(true));

		final Port muc = new Port();
		muc.setCode("MUC");
		muc.setName("München");
		muc.setCountryCode("DE");
		muc.setType(Type.AIRPORT);

		final Port fra = new Port();
		fra.setCode("FRA");
		fra.setName("Frankfurt");
		fra.setCountryCode("DE");
		fra.setType(Type.AIRPORT);

		final Port deHam = new Port();
		deHam.setCode("DEHAM");
		deHam.setName("Hamburg");
		deHam.setCountryCode("DE");
		deHam.setType(Type.SEAPORT);

		final Port deMuc = new Port();
		deMuc.setCode("DEMUC");
		deMuc.setName("Muenchen");
		deMuc.setCountryCode("DE");
		deMuc.setType(Type.SEAPORT);

		portRepository.save(muc);
		portRepository.save(fra);
		portRepository.save(deMuc);
		portRepository.save(deHam);

		instantMockedStatic = buildMockedInstant();
		requestSpec = prepareNewRequestWithToken(null);
		invalidRequestSpec = prepareNewRequestWithToken(INACTIVE_USER_ACCESS_TOKEN);
		requestWriteSpec = prepareNewRequestWithToken(null).postProcessors(csrf());
	}

	@AfterEach
	protected void tearDownOpenApi() {
		instantMockedStatic.close();
	}

	public String buildUrl(final String urlPath) {
		return basePath + urlPath;
	}

	private MockMvcRequestSpecification prepareRequest() {
		return given().contentType(MediaType.APPLICATION_JSON);
	}

	protected MockMvcRequestSpecification prepareNewRequestWithToken(@Nullable final String token) {
		return withValidAccessToken(prepareRequest(), token);
	}

	protected MockMvcRequestSpecification withValidAccessToken(final MockMvcRequestSpecification requestSpec, @Nullable final String token) {
		if (token == null) {
			return requestSpec.header(getValidAccessTokenHeader());
		} else {
			return requestSpec.header(getValidAccessTokenHeader(token));
		}
	}

	protected Header getValidAccessTokenHeader() {
		return getValidAccessTokenHeader(ACTIVE_COMPANY_ACCESS_TOKEN);
	}

	private Header getValidAccessTokenHeader(final String token) {
		return new Header(HttpHeaders.AUTHORIZATION, "Bearer " + token);
	}

	public MockMvcRequestSpecification givenRequest() {
		return requestSpec;
	}

	public MockMvcRequestSpecification givenWriteRequest() {
		return requestWriteSpec;
	}

	public MockMvcRequestSpecification givenRequestWithInvalidToken() {
		return invalidRequestSpec;
	}
}
