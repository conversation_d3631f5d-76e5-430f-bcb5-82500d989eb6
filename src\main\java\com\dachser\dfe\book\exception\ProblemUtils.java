package com.dachser.dfe.book.exception;

import com.dachser.dfe.book.model.GeneralProblemDto;

import java.net.URI;
import java.time.OffsetDateTime;
import java.time.ZoneId;

/*
 * This class is a copy of the class with the same name in the dfe-platform-backend project.
 */
public final class ProblemUtils {

	// @formatter:off
	public static final String PROBLEM_URI_PREFIX = "book:";
	public static final String LABEL_TITLE_KC_02 = "label.text.title_kc_02"; // Translation label for "Access denied" for status 401
	public static final String LABEL_TITLE_UN_04 = "label.text.title_un_04"; // Translation label for "Ooops, an unexpected error occurred" for status 400
	public static final String LABEL_TITLE_UN_06 = "label.text.title_un_06"; // Translation label for "Page doesn't exist" for status 403
	public static final String LABEL_TITLE_UN_07 = "label.text.title_un_07"; // Translation label for "Page doesn't exist" for status 404
	public static final String LABEL_TITLE_UN_09 = "label.text.title_un_09"; // Translation label for "An unexpected error occurred" for status 500
	public static final String LABEL_TRY_AGAIN = "message.text.6608"; // Translation label for "try again later."
	public static final String LABEL_TRY_AGAIN_CREATE_ORDER = "label.text.try_again_create_order"; // Translation label for "Try again later or create a new order manually."
	public static final String LABEL_TRY_AGAIN_LATER = "message.text.6607"; // Translation label for "Try again later or contact your branch."
	public static final String LABEL_SERVICE_NOT_AVAILABLE = "label.text.service_not_available"; // Translation label for "try again later."

	public static final String LABEL_GET_A_NEW_QUOTE = "label.text.get_a_new_quote"; // Translation label for "Get a new quote or contact your branch."
	public static final String LABEL_TITLE_DO_07 = "label.text.title_do_07"; // Translation label for "Document upload not possible, "
	public static final String LABEL_TITLE_AL_01 = "message.text.6987"; // Translation label for "Further addresses can't be edited, "
	public static final String LABEL_TITLE_AL_10 = "label.text.title_al_10"; // Translation label for "Country list not available, "
	public static final String LABEL_TITLE_AL_26 = "message.text.6986"; // Translation label for "Loading points not available due to a technical error, "
	public static final String LABEL_TITLE_PA_01 = "label.text.title_pa_01"; // Translation label for "List of packaging options not available."
	public static final String LABEL_DETAIL_PA_01 = "message.text.6947"; // Translation label for "Save as draft and try again later."
	public static final String LABEL_DETAIL_AL_28 = "message.text.6996"; // Translation label for "Town/County list not available."
	public static final String LABEL_TITLE_DO_12 = "label.text.title_do_12"; // Translation label for "Try again later or download the documents one by one."
	public static final String LABEL_TITLE_DO_11_DO_10 = "label.text.title_do_11"; // Translation label for "Try again later or contact your branch."

	public static final String LABEL_TITLE_DL_02 = "label.text.title_dl_02"; // Translation label for "Deletion not possible."
	public static final String LABEL_TITLE_DL_03 = "label.text.title_dl_03"; // Translation label for "Deletion not possible in current order status."

	public static final String LABEL_TITLE_SV_01 = "label.text.title_sv_01"; // Translation label for "Order could not be saved"
	public static final String LABEL_TITLE_SV_04 = "label.text.title_sv_04"; // Translation label for "Saving not possible, order and related quote option expired"
	public static final String LABEL_TITLE_LB_01 = "label.text.title_lb_01"; // Translation label for "Label print not available"
	public static final String LABEL_TITLE_LB_04 = "label.text.title_lb_04"; // Translation label for "Label print not possible, order and related quote option expired"
	public static final String LABEL_TITLE_SE_01 = "label.text.sending_not_possible"; // Translation label for "submission not possible."
	public static final String LABEL_TITLE_SE_04 = "label.text.sent_not_possible_order_expired"; // Translation label for "Booking not possible, order and related quote option expired"
	public static final String LABEL_TITLE_RT_01 = "label.text.title_rs_01"; // Routing services not available
	public static final String LABEL_TITLE_PF_01 = "label.text.title_tl_02"; // Translation label for: (Transfer List) PDF Service not available
	public static final String LABEL_TITLE_TL_01 = "label.text.title_tl_01"; // Translation label for: Transfer List query error, database connection lost

	public static final String LABEL_TITLE_OV_02 = "label.text.title_ov_02"; // Translation label for: Order Overview query error, database connection lost
	public static final String LABEL_TITLE_DU_02 = "label.text.duplication_not_possible"; // Translation label for: Duplication not possible
	public static final String LABEL_TITLE_GG_01 = "message.text.6989"; // Translation label for: Order Overview query error, database connection lost
	public static final String LABEL_TITLE_VA_01 = "message.text.7058"; // Translation label for "The action could not be processed further"

	public static final String LABEL_TITLE_NF_ORDER = "label.text.order_nf"; // Translation label for "Order not found"
	public static final String LABEL_TITLE_NF_DOCUMENT = "label.text.document_not_found"; // Translation label for "Document not found"
	public static final String LABEL_TITLE_NF_PORT = "label.text.port_not_found"; // Translation label for "Port not found"
	public static final String LABEL_TITLE_INVALID_FILTER = "label.text.invalid_filter"; // Translation label for "Invalid filter values"
	public static final String LABEL_TITLE_NF_DATA = "label.text.transferlist_data_not_found"; // Translation label for "No data found for the given filters"
	// @formatter:on

	private ProblemUtils() {
		// do nothing
	}

	/**
	 * Prefills some problem fields in a common way.
	 *
	 * @param instance   the concrete problem instance
	 * @param severity   problem severity as described <a href="https://confluence.dach041.dachser.com/pages/viewpage.action?pageId=315371085">Error handling // cross-team analysis</a>
	 * @param errorId    problem id as described <a href="https://confluence.dach041.dachser.com/pages/viewpage.action?pageId=315371085">Error handling // cross-team analysis</a>
	 * @param typePrefix will be prepended on the default problem type
	 * @param <T>        type of the problem
	 * @return A problem of type T with prefilled fields.
	 */
	public static <T extends GeneralProblemDto> T populateProblemData(T instance, GeneralProblemDto.SeverityEnum severity, BookErrorId errorId, String typePrefix) {
		instance.type(URI.create(typePrefix + instance.getType().toString())).severity(severity).timestamp(OffsetDateTime.now(ZoneId.of("UTC"))).errorId(errorId.getErrorId())
				.title(errorId.getErrorTitle());
		return instance;
	}

	/**
	 * Prefills some problem fields in a common way.
	 *
	 * @param instance   the concrete problem instance
	 * @param errorId    problem id as described <a href="https://confluence.dach041.dachser.com/pages/viewpage.action?pageId=315371085">Error handling // cross-team analysis</a>
	 * @param typePrefix will be prepended on the default problem type
	 * @param <T>        type of the problem
	 * @return A problem of type T with prefilled fields.
	 */
	public static <T extends GeneralProblemDto> T populateProblemData(T instance, BookErrorId errorId, String typePrefix) {
		return populateProblemData(instance, errorId.getSeverity(), errorId, typePrefix);
	}
}
