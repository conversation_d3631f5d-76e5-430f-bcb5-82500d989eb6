package com.dachser.dfe.book.order;

import com.dachser.dfe.book.document.DocumentRepositoryFacade;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.common.orderline.RoadOrderLineRepository;
import com.dachser.dfe.book.order.common.orderline.SeaOrderLineRepository;
import com.dachser.dfe.book.order.emissionforecast.EmissionForecastConfig;
import com.dachser.dfe.book.order.exception.OrderNotFoundException;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.book.service.BaseEntityService;
import com.dachser.dfe.book.user.UserContextService;
import com.dachser.dfe.book.user.UserMapper;
import com.dachser.dfe.book.user.model.Address;
import com.dachser.dfe.book.user.model.User;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Hibernate;
import org.hibernate.collection.spi.PersistentBag;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class OrderRepositoryFacade {

	private final OrderRepository orderRepository;

	private final DocumentRepositoryFacade documentRepository;

	private final RoadOrderLineRepository roadOrderLineRepository;

	private final SeaOrderLineRepository seaOrderLineRepository;

	private final UserContextService userContextService;

	private final UserMapper userMapper;

	private final EmissionForecastConfig emissionForecastConfig;

	public <T extends Order> T save(T order) {
		final List<Long> documentIds = order.getDocumentIds();
		BaseEntityService.modified(order, userContextService.getCurrentUserIdIfAvailable());

		boolean withContact = !(order instanceof RoadOrder);
		fillPrincipalAddress(order, withContact);
		final T saved = orderRepository.save(order);
		setReferenceToPackingPosition(saved);
		setReferenceToFullContainerLoad(saved);
		if (documentIds != null && !documentIds.isEmpty()) {
			documentRepository.updateOrder(saved.getOrderId(), documentIds);
		}
		return saved;
	}

	private <T extends Order> void setReferenceToFullContainerLoad(T order) {
		// This approach is used intentionally because we decided to reference the full container load solely by their ID.
		if (order instanceof SeaOrder seaOrder && Objects.nonNull(seaOrder.getFullContainerLoads())) {
			seaOrder.getFullContainerLoads().forEach(containerLoad -> containerLoad.getOrderLines().forEach(orderLine -> {
				orderLine.setFullContainerLoadId(containerLoad.getId());
				seaOrderLineRepository.save(orderLine);
			}));
		}
	}

	private <T extends Order> void setReferenceToPackingPosition(T order) {
		// This approach is used intentionally because we decided to reference the packing positions solely by their ID.
		if (order instanceof RoadOrder roadOrder && Objects.nonNull(roadOrder.getPackingPositions())) {
			roadOrder.getPackingPositions().forEach(packingPosition -> packingPosition.getOrderLines().forEach(orderLine -> {
				orderLine.setPackingPositionId(packingPosition.getId());
				roadOrderLineRepository.save(orderLine);
			}));
		}
	}

	private void fillPrincipalAddress(Order order, boolean withContact) {
		Optional<Address> customerAddress = userContextService.getCustomerAddress(order.getCustomerNumber(), order.getOrderType().getSegment());
		User currentUser = userContextService.getCurrentUser();
		customerAddress.ifPresent(address -> {
			if (order.getPrincipalAddress() != null) {
				userMapper.updateOrderAddress(address, currentUser, order.getPrincipalAddress());
			} else {
				order.setPrincipalAddress(userMapper.mapOrderAddress(address, currentUser));
			}

			if (withContact) {
				order.getPrincipalAddress().setOrderContact(userMapper.mapOrderContact(currentUser));
			}
		});
	}

	public Order loadOrderByIdWithoutSoftDeleteCheck(Long orderId) {
		if (orderId == null) {
			throw new OrderNotFoundException();
		}
		return orderRepository.findById(orderId).orElseThrow(OrderNotFoundException::new);
	}

	public Order loadOrderById(Long orderId) {
		if (orderId == null) {
			throw new OrderNotFoundException();
		}
		Order order = orderRepository.findById(orderId).orElseThrow(OrderNotFoundException::new);
		checkIfOrderDeleted(order.getOrderId(), order);
		return order;
	}

	public OrderTypeStatusAndOwner findOrderTypeAndStatus(Long orderId) {
		if (orderId == null) {
			throw new OrderNotFoundException();
		}
		return orderRepository.findTypeOwnerAndStatus(orderId);
	}

	public Optional<OrderTypeStatusAndOwner> getOrderTypeStatusAndOwner(Long orderId) {
		if (orderId == null) {
			return Optional.empty();
		}
		Optional<OrderTypeStatusAndOwner> ownerAndStatus = Optional.ofNullable(orderRepository.findTypeOwnerAndStatus(orderId));
		ownerAndStatus.ifPresent(order -> checkIfOrderDeleted(orderId, order));
		return ownerAndStatus;
	}

	public List<Order> findByOrderStatusListAndAfterExpirationDate(List<OrderStatus> statusList, OffsetDateTime now) {
		return orderRepository.findByStatusInAndExpired(statusList, now);
	}

	public List<SeaOrder> findAllExpiredSeaOrders(List<OrderStatus> statusList, OffsetDateTime currentDateTime) {
		return orderRepository.findAllExpiredSeaOrders(statusList, currentDateTime);
	}

	public List<AirOrder> findAllExpiredAirOrders(List<OrderStatus> statusList, OffsetDateTime currentDateTime) {
		return orderRepository.findAllExpiredAirOrders(statusList, currentDateTime);
	}

	public List<CollectionOrder> findAllExpiredCollectionOrders(List<OrderStatus> statusList, OffsetDateTime currentDateTime) {
		return orderRepository.findAllExpiredCollectionOrders(statusList, currentDateTime);
	}

	public List<ForwardingOrder> findAllExpiredForwardingOrders(List<OrderStatus> statusList, OffsetDateTime defaultOffsetDateTime, OffsetDateTime adviceOffsetDateTime) {
		return orderRepository.findAllExpiredForwardingOrders(statusList, defaultOffsetDateTime, adviceOffsetDateTime);
	}

	public List<Order> findAllSentOrdersOlderThan(int days) {
		final Instant daysDifference = Instant.now().minus(days, ChronoUnit.DAYS);

		return orderRepository.findAllSentOrdersOlderThan(daysDifference);
	}

	public List<Order> findAllDeletedOrdersOlderThan(int days) {
		final Instant daysDifference = Instant.now().minus(days, ChronoUnit.DAYS);

		return orderRepository.findAllDeletedOrdersOlderThan(daysDifference);
	}

	@Transactional
	public List<RoadOrder> findAllWithEmissionForecastNullAndRetryCountBelow10Road() {
		List<RoadOrder> list = orderRepository.findAllWithEmissionForecastNullAndRetryCountBelowRetryCountRoad(
				List.of(OrderStatus.LABEL_PENDING, OrderStatus.COMPLETE, OrderStatus.SENT), emissionForecastConfig.getRetryCount());
		list.forEach(roadOrder -> {
			Hibernate.initialize(roadOrder.getOrderLines());
			Hibernate.initialize(roadOrder.getPackingPositions());
		});

		return list;
	}

	@Transactional
	public List<AirOrder> findAllWithEmissionForecastNullAndRetryCountBelow10Air() {
		List<AirOrder> list = orderRepository.findAllWithEmissionForecastNullAndRetryCountBelowRetryCountAir(List.of(OrderStatus.COMPLETE, OrderStatus.SENT),
				emissionForecastConfig.getRetryCount());
		list.forEach(airOrder -> Hibernate.initialize(airOrder.getOrderLines()));

		return list;
	}

	@Transactional
	public List<SeaOrder> findAllWithEmissionForecastNullAndRetryCountBelow10Sea() {
		List<SeaOrder> list = orderRepository.findAllWithEmissionForecastNullAndRetryCountBelowRetryCountSea(List.of(OrderStatus.COMPLETE, OrderStatus.SENT),
				emissionForecastConfig.getRetryCount());
		list.forEach(seaOrder -> {
			Hibernate.initialize(seaOrder.getOrderLines());
			Hibernate.initialize(seaOrder.getFullContainerLoads());
		});
		return list;
	}

	void preLoadLazyOrderCollections(Order order) {
		if (order instanceof RoadOrder roadOrder) {
			preLoadLazyCollection(roadOrder.getOrderReferences());
			preLoadLazyCollection(roadOrder.getPackingPositions());
		}
		preLoadLazyCollection(order.getAddresses());
		preLoadLazyCollection(order.getOrderTexts());

		if (order instanceof RoadOrder roadOrder) {
			preLoadLazyCollection(roadOrder.getOrderLines());
		}

		if (order instanceof AirOrder airOrder) {
			preLoadLazyCollection(airOrder.getOrderLines());
			preLoadLazyCollection(airOrder.getOrderReferences());
		}

		if (order instanceof SeaOrder seaOrder) {
			preLoadLazyCollection(seaOrder.getOrderLines());
			preLoadLazyCollection(seaOrder.getFullContainerLoads());
			preLoadLazyCollection(seaOrder.getOrderReferences());
		}
	}

	protected void preLoadLazyCollection(List<?> collection) {
		if (collection == null) {
			return;
		}

		if (collection instanceof PersistentBag persistentBag) {
			persistentBag.forceInitialization();
			log.debug("Forced initialization of {}", collection);
		} else {
			final Optional<?> first = collection.stream().findFirst();
			log.debug("Preloaded via stream {} - first {}", collection.size(), first);
		}
	}

	private void checkIfOrderDeleted(Long orderId, OrderTypeStatusAndOwner order) {
		if (OrderStatus.DELETED.equals(order.getStatus())) {
			log.warn("User with id #{} tried to access deleted order with id #{}", userContextService.getCurrentUserIdIfAvailable(), orderId);
			throw new OrderNotFoundException();
		}
	}
}
