package com.dachser.dfe.book.service.postalcode;

import com.dachser.dfe.book.country.PostalCodeMapper;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ErrorIdExternalServiceNotAvailableException;
import com.dachser.dfe.book.model.IrelandPostalCodeDto;
import com.dachser.dfe.book.model.PostcodeValidationDto;
import com.dachser.dfe.book.service.Interruptible;
import com.dachser.dfe.masterdata.geo.api.LocationsApi;
import com.dachser.dfe.masterdata.geo.model.GMDDachserPostalCode;
import com.dachser.dfe.masterdata.geo.model.GMDPostalCodeValidation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "false", matchIfMissing = true)
@Interruptible
class PostalCodeAdapterExt implements PostalCodeAdapter {

	private final LocationsApi locationsApi;

	private final PostalCodeMapper mapper;

	@Override
	public List<IrelandPostalCodeDto> findDachserPostalcodes(String eircode) {
		try {
			List<GMDDachserPostalCode> irelandDachserPostalCodes = locationsApi.findDachserPostalcodes(eircode);
			if (irelandDachserPostalCodes == null || irelandDachserPostalCodes.isEmpty()) {
				log.debug("No town/county combination found for: {}", eircode);
				return List.of();
			}
			return mapper.map(irelandDachserPostalCodes);
		} catch (RestClientException e) {
			log.error("Exception when calling the Geo API: ", e);
			throw new ErrorIdExternalServiceNotAvailableException(BookErrorId.ERR_AL_28, "Exception when calling external Geo service");
		}
	}

	@Override
	public PostcodeValidationDto validatePostCode(String countryCode, String postalCode) {
		try {
			GMDPostalCodeValidation mdgZipCodeValidation = locationsApi.validatePostalCode(countryCode.toUpperCase(), postalCode);
			boolean isValid = mdgZipCodeValidation != null;
			boolean isValidRegex = isValid && Boolean.TRUE.equals(mdgZipCodeValidation.getValidRegex());
			boolean isStateUndefined = mdgZipCodeValidation.getCountryPostalCodeState() == GMDPostalCodeValidation.CountryPostalCodeStateEnum.UNDEFINED;
			boolean isStateUndefinedAndValid = isStateUndefined && isValidRegex;
			boolean isStateDefinedAndValid =
					isValid && mdgZipCodeValidation.getCountryPostalCodeState() != GMDPostalCodeValidation.CountryPostalCodeStateEnum.UNDEFINED && Boolean.TRUE.equals(
							mdgZipCodeValidation.getValidRegex());
			boolean isStateDefinedAndInvalid =
					isValid && mdgZipCodeValidation.getCountryPostalCodeState() != GMDPostalCodeValidation.CountryPostalCodeStateEnum.UNDEFINED && !Boolean.TRUE.equals(
							mdgZipCodeValidation.getValidRegex());

			if (isStateUndefinedAndValid || isStateDefinedAndValid) {
				return new PostcodeValidationDto().valid(true);
			} else if (isStateUndefined) {
				return new PostcodeValidationDto().valid(false);
			} else if (isStateDefinedAndInvalid) {
				return new PostcodeValidationDto().valid(false).examplePostcode(mdgZipCodeValidation.getExampleZipCode())
						.validatedPattern(mdgZipCodeValidation.getRegularExpression());
			}
		} catch (Exception e) {
			log.info("Exception when calling external Geo service with countryCode: {} and postalCode: {}", countryCode, postalCode, e);
		}
		return new PostcodeValidationDto().valid(true);
	}
}
