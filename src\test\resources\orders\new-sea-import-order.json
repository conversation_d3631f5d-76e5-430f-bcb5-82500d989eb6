{"orderType": "SeaImportOrder", "shipmentNumber": null, "shipperAddress": {"name": "Shipper <PERSON>", "name2": null, "name3": null, "street": "Shipper street", "street2": "Shipper street2", "postcode": "1231", "city": "City Shipper", "countryCode": "AT", "supplement": null, "gln": null, "neutralizeAddress": null, "individualId": "555", "contact": {"name": "Test Contact", "email": "Test email", "telephone": "telephone test", "mobile": "mobile number"}, "taxID": "12313124234"}, "collectionTime": {"collectionDate": "2022-11-15", "from": "2022-11-15T10:43:37.0972671+01:00", "to": "2022-11-15T12:43:37.0972671+01:00"}, "shipperReference": "Reference Shipper", "loading": true, "unloading": false, "incoTerm": {"code": "CPT", "description": "CPT", "dachserCode": "CPT", "label": "CPT"}, "consigneeAddress": {"name": "Consignee <PERSON>", "name2": null, "name3": null, "street": "Consignee street", "postcode": "1231", "city": "City Consignee", "countryCode": "AT", "supplement": null, "gln": null, "neutralizeAddress": null, "individualId": "666", "contact": {"name": "Test Contact", "email": "Test email", "telephone": "telephone test", "mobile": "mobile number"}, "taxID": "12313124234"}, "pickupAddress": {"name": "<PERSON>up <PERSON>", "name2": null, "name3": null, "street": "Pickup street", "postcode": "1231", "city": "City pickuper", "countryCode": "AT", "supplement": null, "gln": null, "neutralizeAddress": null, "contact": {"name": "Test Contact", "email": "Test email", "telephone": "telephone test", "mobile": "mobile number"}, "taxID": "12313124234"}, "deliveryAddress": {"name": "<PERSON>up <PERSON>", "name2": null, "name3": null, "street": "Pickup street", "postcode": "1231", "city": "City pickuper", "countryCode": "AT", "supplement": null, "gln": null, "neutralizeAddress": null, "contact": {"name": "Test Contact", "email": "Test email", "telephone": "telephone test", "mobile": "mobile number"}, "taxID": "12313124234"}, "customerContactData": {"name": "Testuser", "email": "<EMAIL>", "telephone": "0123456789", "mobile": "0123456789", "fax": "0123456789"}, "fromPort": {"code": "DEHAM", "name": "Frankfurt", "countryCode": "DE", "type": "SEAPORT"}, "toPort": {"code": "DEMUC", "name": "<PERSON><PERSON><PERSON>", "countryCode": "DE", "type": "SEAPORT"}, "orderLineItems": [{"quantity": 1, "number": 1, "packaging": {"code": "EUP", "description": "Testing"}, "hsCodes": [{"hsCode": "1234567", "goods": "Test"}], "markAndNumbers": "Test", "weight": 12, "length": 100, "width": 100, "height": 100, "volume": 3.5}, {"quantity": 1, "number": 1, "packaging": {"code": "EUP", "description": "Testing"}, "hsCodes": [{"hsCode": "1234567", "goods": "Test"}], "markAndNumbers": "Test", "weight": 12, "length": 100, "width": 100, "height": 100, "volume": 100}], "references": [{"referenceType": "SHIPPING_ORDER_NUMBER", "referenceValue": "Test", "loading": true, "unloading": false}, null, {"referenceType": "QUOTATION_REFERENCE", "referenceValue": "12345", "loading": true, "unloading": false}], "division": null, "deliveryOption": null, "orderNumber": null, "furtherAddresses": [{"id": null, "name": "<PERSON><PERSON>", "name2": null, "name3": null, "street": "Airportstreet 1", "postcode": "12345", "city": "Test further", "countryCode": "DE", "supplement": null, "gln": null, "addressType": "DP", "neutralizeAddress": null, "contact": null}], "status": null, "stackable": true, "shockSensitive": false, "tailLiftCollection": false, "requestArrangement": false, "deliverToPort": false, "collectFromPort": false}