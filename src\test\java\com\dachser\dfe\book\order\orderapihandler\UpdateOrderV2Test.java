package com.dachser.dfe.book.order.orderapihandler;

import com.dachser.dfe.book.BaseOpenApiTest;
import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.document.Document;
import com.dachser.dfe.book.document.DocumentRepositoryFacade;
import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.document.DocumentTypeRepository;
import com.dachser.dfe.book.document.DocumentsTestUtil;
import com.dachser.dfe.book.document.FileStorageCleanup;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.LabelsCouldNotBeCreatedException;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.AirExportOrderDto;
import com.dachser.dfe.book.model.AirImportOrderDto;
import com.dachser.dfe.book.model.AirSeaOrderLineHsCodeDto;
import com.dachser.dfe.book.model.AirSeaOrderReferenceDto;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.CollectionTimeSlotDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.DocumentResponseDto;
import com.dachser.dfe.book.model.DocumentTypeDto;
import com.dachser.dfe.book.model.EQDangerousGoodDto;
import com.dachser.dfe.book.model.FullContainerLoadDto;
import com.dachser.dfe.book.model.GeneralProblemDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderAddressDto;
import com.dachser.dfe.book.model.OrderProcessResultDto;
import com.dachser.dfe.book.model.OrderReferenceTypeDto;
import com.dachser.dfe.book.model.OrderSaveActionDto;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.OrderStatusDto;
import com.dachser.dfe.book.model.OrderTypeDto;
import com.dachser.dfe.book.model.OrderValidationResultDto;
import com.dachser.dfe.book.model.PackingPositionDto;
import com.dachser.dfe.book.model.PortDto;
import com.dachser.dfe.book.model.PortTypeDto;
import com.dachser.dfe.book.model.RoadCollectionOrderDto;
import com.dachser.dfe.book.model.RoadForwardingOrderDto;
import com.dachser.dfe.book.model.RoadOrderLineDto;
import com.dachser.dfe.book.model.SeaExportOrderDto;
import com.dachser.dfe.book.model.SeaImportOrderDto;
import com.dachser.dfe.book.model.SeaOrderLineDto;
import com.dachser.dfe.book.model.ValidationResultEntryDto;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderApiTestUtil;
import com.dachser.dfe.book.order.OrderDefaults;
import com.dachser.dfe.book.order.OrderRepositoryFacade;
import com.dachser.dfe.book.order.OrderStatusDefaultProvider;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.pdf.ShipmentLabelPdfTransformer;
import com.dachser.dfe.book.service.label.LabelService;
import com.dachser.dfe.book.user.UserServiceMock;
import com.fasterxml.jackson.core.type.TypeReference;
import io.restassured.common.mapper.TypeRef;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import io.restassured.module.mockmvc.specification.MockMvcRequestSpecification;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.dachser.dfe.book.order.OrderApiTestUtil.CUSTOMER_NUMBER_ROAD;
import static com.dachser.dfe.book.order.OrderApiTestUtil.HTTP_STATUS_SUBMISSION_FAILED;
import static com.dachser.dfe.book.order.OrderApiTestUtil.MISSING_INPUT;
import static com.dachser.dfe.book.order.OrderApiTestUtil.STATUS_CODE_VALIDATION_FAILS;
import static com.dachser.dfe.book.order.OrderApiTestUtil.assertErrorIdInProcessResult;
import static com.dachser.dfe.book.order.OrderApiTestUtil.assertOrderStatus;
import static com.dachser.dfe.book.order.OrderApiTestUtil.createNewCollectionOrder;
import static com.dachser.dfe.book.order.OrderApiTestUtil.createNewDraftForwardingOrder;
import static com.dachser.dfe.book.order.OrderApiTestUtil.createNewForwardingOrder;
import static com.dachser.dfe.book.order.OrderApiTestUtil.createNewOrder;
import static com.dachser.dfe.book.order.OrderApiTestUtil.createValidatedForwardingOrder;
import static com.dachser.dfe.book.order.OrderApiTestUtil.getOrder;
import static com.dachser.dfe.book.order.OrderApiTestUtil.getOrderStatusFromOrderProcess;
import static com.dachser.dfe.book.order.OrderApiTestUtil.parseOrderProcessResult;
import static com.dachser.dfe.book.order.OrderApiTestUtil.parseResponse;
import static com.dachser.dfe.book.order.OrderApiTestUtil.updateOrder;
import static com.dachser.dfe.book.order.OrderApiTestUtil.updateOrderToError;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

class UpdateOrderV2Test extends BaseOpenApiTest implements ResourceLoadingTest {

	public static final String ORDERS_URL_PATH = "/v2/orders/";

	@MockBean
	ShipmentLabelPdfTransformer shipmentLabelPdfTransformer;

	@Autowired
	OrderRepositoryFacade orderRepository;

	@Autowired
	DocumentService documentService;

	@Autowired
	DocumentRepositoryFacade documentRepository;

	@Autowired
	FileStorageCleanup fileStorageCleanup;

	@Autowired
	DocumentTypeRepository documentTypeRepository;

	@Autowired
	OrderDefaults orderDefaults;

	@Autowired
	TestUtil testUtil;

	@MockBean
	OrderStatusDefaultProvider orderStatusDefaultProvider;

	@MockBean
	protected LabelService labelService;

	@BeforeEach
	public void setUp() {

		when(orderStatusDefaultProvider.getDefaultStatus()).thenReturn(OrderStatus.DRAFT);
	}

	@AfterEach
	public void cleanUp() {
		fileStorageCleanup.deleteAllFoldersInBasePath();
	}

	@Nested
			// Updates of an order without validation
	class DraftUpdateRequests {
		@Nested
		class ForwardingOrder {
			@Test
			void shouldUpdateForwardingOrderSimpleFieldUpdates() throws IOException {
				final RoadForwardingOrderDto savedOrder = createNewDraftForwardingOrder(null);

				assertNull(savedOrder.getPalletLocations());

				savedOrder.setPalletLocations(5.0);

				MockMvcRequestSpecification request = givenWriteRequest().body(savedOrder);
				final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, putResponse.getStatusCode());
				final RoadForwardingOrderDto updatedOrder = parseOrderProcessResult(putResponse, RoadForwardingOrderDto.class);

				assertEquals(5, updatedOrder.getPalletLocations());
			}

			@Test
			void shouldUpdateForwardingOrderChangingAddress() throws IOException {
				final RoadForwardingOrderDto savedOrder = createNewDraftForwardingOrder(null);

				final String street = "Tumblinger street";
				final String city = "Munich";
				final String countryCode = "DE";
				final String postcode = "80337";
				final String shipperName = "Shipper Name";
				savedOrder.setShipperAddress(new OrderAddressDto().street(street).countryCode(countryCode).city(city).postcode(postcode).gln("1234567890123").name(shipperName));

				MockMvcRequestSpecification request = givenWriteRequest().body(savedOrder);
				final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));

				assertEquals(200, putResponse.getStatusCode());
				final RoadForwardingOrderDto updatedOrder = parseOrderProcessResult(putResponse, RoadForwardingOrderDto.class);
				assertEquals(street, updatedOrder.getShipperAddress().getStreet());
				assertEquals(city, updatedOrder.getShipperAddress().getCity());
				assertEquals(countryCode, updatedOrder.getShipperAddress().getCountryCode());
				assertEquals(postcode, updatedOrder.getShipperAddress().getPostcode());
				assertEquals(shipperName, updatedOrder.getShipperAddress().getName());
				assertOrderStatus(savedOrder.getOrderId(), OrderStatusDto.DRAFT);
			}

			@Test
			void shouldUpdateForwardingOrderRemovingAddress() throws IOException {
				final RoadForwardingOrderDto order = createNewOrder(null, "orders/new-forwarding-order-complete-valid.json", OrderApiTestUtil.CUSTOMER_NUMBER_ROAD, null);
				order.setShipperAddress(null);
				MockMvcRequestSpecification request = givenWriteRequest().body(order);
				final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, putResponse.getStatusCode());
				final RoadForwardingOrderDto updatedOrder = parseOrderProcessResult(putResponse, RoadForwardingOrderDto.class);
				assertNull(updatedOrder.getShipperAddress());
			}

			@Test
			void shouldNotAllowDraftUpdatesOnCompletedOrder() throws IOException {
				when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());
				final RoadForwardingOrderDto order = createNewOrder(null, "orders/new-forwarding-order-complete-valid.json", OrderApiTestUtil.CUSTOMER_NUMBER_ROAD,
						OrderSaveActionDto.PRINT_LABELS);
				MockMvcRequestSpecification request = givenWriteRequest().body(order);
				final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(400, putResponse.getStatusCode());
			}

			@Test
			void shouldNotUpdateShipmentNumber() throws IOException {
				final RoadForwardingOrderDto savedOrder = createNewDraftForwardingOrder(null);
				final Long shipmentNumber = savedOrder.getShipmentNumber();

				savedOrder.setShipmentNumber(shipmentNumber + 10);

				MockMvcRequestSpecification request = givenWriteRequest().body(savedOrder);
				final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, putResponse.getStatusCode());
				final RoadForwardingOrderDto updatedOrder = parseOrderProcessResult(putResponse, RoadForwardingOrderDto.class);

				assertEquals(shipmentNumber, updatedOrder.getShipmentNumber());
			}

			@Test
			void shouldNotUpdateStatus() throws IOException {
				final RoadForwardingOrderDto savedOrder = createNewDraftForwardingOrder(null);

				savedOrder.setStatus(OrderStatusDto.COMPLETE);

				MockMvcRequestSpecification request = givenWriteRequest().body(savedOrder);
				final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, putResponse.getStatusCode());
				final RoadForwardingOrderDto updatedOrder = parseOrderProcessResult(putResponse, RoadForwardingOrderDto.class);

				assertEquals(OrderStatusDto.DRAFT, updatedOrder.getStatus());
			}

			@Test
			void shouldNotUpdateDivision() throws IOException {
				final RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order.json", new TypeReference<>() {
				});
				final RoadForwardingOrderDto savedOrder = createNewForwardingOrder(order, null);

				savedOrder.setDivision(Division.F.name());
				MockMvcRequestSpecification request = givenWriteRequest().body(savedOrder);
				final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, putResponse.getStatusCode());
				final RoadForwardingOrderDto updatedOrder = parseOrderProcessResult(putResponse, RoadForwardingOrderDto.class);

				assertEquals(Division.T.name(), updatedOrder.getDivision());
			}

			@Test
			void shouldNotAllowDraftUpdatesOnDeletedOrder() throws IOException {
				final RoadForwardingOrderDto savedOrder = createNewDraftForwardingOrder(null);
				MockMvcRequestSpecification request = givenWriteRequest();
				MockMvcResponse response = request.delete("/orders/{orderId}", savedOrder.getOrderId().toString());
				assertEquals(200, response.statusCode());

				request = givenWriteRequest().body(savedOrder);
				final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
				// Order to update is not found
				assertEquals(404, putResponse.getStatusCode());
			}

			@Test
			void shouldSwitchOrderType() throws IOException {
				final RoadForwardingOrderDto forwardingOrder = createNewDraftForwardingOrder(null);
				RoadCollectionOrderDto changeType = testUtil.changeType(forwardingOrder, RoadCollectionOrderDto.class);
				MockMvcRequestSpecification request = givenWriteRequest().body(changeType);
				// When
				MockMvcResponse mockMvcResponse = request.put(buildUrl(ORDERS_URL_PATH));

				// Test
				assertEquals(200, mockMvcResponse.getStatusCode());
				RoadCollectionOrderDto switchedOrderType = parseOrderProcessResult(mockMvcResponse, RoadCollectionOrderDto.class);
				assertNotEquals(forwardingOrder.getOrderId(), switchedOrderType.getOrderId());
				assertEquals(forwardingOrder.getOrderLineItems().size(), switchedOrderType.getOrderLineItems().size());
				assertEquals(0, switchedOrderType.getFurtherAddresses().size());

			}

			@Nested
			class PackingPositions {
				@Test
				void shouldUpdatePackingPositionsOfOrderLines() throws IOException {
					final RoadForwardingOrderDto savedOrder = createNewDraftForwardingOrder(null);
					PackingPositionDto packingPosition = new PackingPositionDto().quantity(1).packagingType(new OptionDto().code("BOX").description("Box"));
					packingPosition.addLinesItem(savedOrder.getOrderLineItems().get(0));
					savedOrder.getPackingPositions().add(packingPosition);
					savedOrder.setOrderLineItems(new ArrayList<>());

					MockMvcRequestSpecification request = givenWriteRequest().body(savedOrder);
					final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(200, putResponse.getStatusCode());
					final RoadForwardingOrderDto updatedOrder = parseOrderProcessResult(putResponse, RoadForwardingOrderDto.class);

					assertEquals(1, updatedOrder.getPackingPositions().size());
					assertEquals(1, updatedOrder.getPackingPositions().get(0).getLines().size());
					assertEquals(0, updatedOrder.getOrderLineItems().size());
				}

				@Test
				void shouldMoveNotAssociatedOrderLineToPackingPositionAndMoveOneOutOfPackingPosition() throws IOException {
					final RoadForwardingOrderDto savedOrder = createNewDraftForwardingOrder(null);
					RoadOrderLineDto orderLineInPackingPosition = createOrderLine("first_p_p", 0);

					PackingPositionDto packingPosition = new PackingPositionDto().quantity(1).packagingType(new OptionDto().code("BOX").description("Box"))
							.lines(List.of(orderLineInPackingPosition));

					savedOrder.getPackingPositions().add(packingPosition);

					MockMvcRequestSpecification request = givenWriteRequest().body(savedOrder);
					final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(200, putResponse.getStatusCode());
					final RoadForwardingOrderDto updatedOrder = parseOrderProcessResult(putResponse, RoadForwardingOrderDto.class);

					// Move order line out of packing position
					RoadOrderLineDto orderLine = updatedOrder.getPackingPositions().get(0).getLines().get(0);
					updatedOrder.getPackingPositions().get(0).getLines().remove(0);
					updatedOrder.getOrderLineItems().add(orderLine);

					// Move order line to packing position
					RoadOrderLineDto orderLine2 = updatedOrder.getOrderLineItems().get(0);
					updatedOrder.getOrderLineItems().remove(0);
					updatedOrder.getPackingPositions().get(0).getLines().add(orderLine2);

					request = givenWriteRequest().body(updatedOrder);
					final MockMvcResponse putResponse2 = request.put(buildUrl("/orders/"));
					assertEquals(200, putResponse2.getStatusCode());
					final RoadForwardingOrderDto updatedOrder2 = parseOrderProcessResult(putResponse2, RoadForwardingOrderDto.class);

					assertEquals(1, updatedOrder2.getPackingPositions().get(0).getLines().size());
					assertEquals(1, updatedOrder2.getOrderLineItems().size());
					assertEquals("first_p_p", updatedOrder2.getOrderLineItems().get(0).getContent());
					assertEquals("Standardzeuch", updatedOrder2.getPackingPositions().get(0).getLines().get(0).getContent());

				}
			}

			@Nested
			class DangerousGoods {

				@Test
				void shouldUpdateOrderLineWithDangerousGood() throws IOException {
					final RoadForwardingOrderDto savedOrder = createNewDraftForwardingOrder(null);
					RoadOrderLineDto orderLine = savedOrder.getOrderLineItems().get(0);

					// EQDangerousGood
					EQDangerousGoodDto eqDangerousGood = new EQDangerousGoodDto();
					eqDangerousGood.setPackaging(new OptionDto().code("1"));
					eqDangerousGood.setNoOfPackages(2);
					eqDangerousGood.setSortingPosition(0);

					orderLine.getDangerousGoods().add(eqDangerousGood);

					MockMvcRequestSpecification request = givenWriteRequest().body(savedOrder);
					final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(200, putResponse.getStatusCode());
					final RoadForwardingOrderDto updatedOrder = parseOrderProcessResult(putResponse, RoadForwardingOrderDto.class);

					assertEquals(1, updatedOrder.getOrderLineItems().size());
					assertEquals(1, updatedOrder.getOrderLineItems().get(0).getDangerousGoods().size());

					// EQDangerousGood
					EQDangerousGoodDto eqDangerousGoodDto = (EQDangerousGoodDto) updatedOrder.getOrderLineItems().get(0).getDangerousGoods().get(0);
					assertNotNull(eqDangerousGoodDto.getId());
					assertEquals("1", eqDangerousGoodDto.getPackaging().getCode());
					assertEquals(2, eqDangerousGoodDto.getNoOfPackages());
					assertEquals(0, eqDangerousGoodDto.getSortingPosition());

				}

				@Test
				void shouldUpdateDangerousGood() throws IOException {
					final RoadForwardingOrderDto savedOrder = createNewDraftForwardingOrder(null);
					RoadOrderLineDto orderLine = savedOrder.getOrderLineItems().get(0);


					// EQDangerousGood
					EQDangerousGoodDto eqDangerousGood = new EQDangerousGoodDto();
					eqDangerousGood.setPackaging(new OptionDto().code("1"));
					eqDangerousGood.setNoOfPackages(2);
					eqDangerousGood.setSortingPosition(0);

					orderLine.getDangerousGoods().add(eqDangerousGood);


					MockMvcRequestSpecification request = givenWriteRequest().body(savedOrder);
					final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(200, putResponse.getStatusCode());
					final RoadForwardingOrderDto updatedOrder = parseOrderProcessResult(putResponse, RoadForwardingOrderDto.class);

					assertEquals(1, updatedOrder.getOrderLineItems().size());
					assertEquals(1, updatedOrder.getOrderLineItems().get(0).getDangerousGoods().size());

					// EQDangerousGood
					EQDangerousGoodDto eqDangerousGoodDto = (EQDangerousGoodDto) updatedOrder.getOrderLineItems().get(0).getDangerousGoods().get(0);
					assertNotNull(eqDangerousGoodDto.getId());
					assertEquals("1", eqDangerousGoodDto.getPackaging().getCode());
					assertEquals(2, eqDangerousGoodDto.getNoOfPackages());
					assertEquals(0, eqDangerousGoodDto.getSortingPosition());

					EQDangerousGoodDto dangerousGoodDto = (EQDangerousGoodDto) updatedOrder.getOrderLineItems().get(0).getDangerousGoods().get(0);

					dangerousGoodDto.setNoOfPackages(6);

					MockMvcRequestSpecification request2 = givenWriteRequest().body(updatedOrder);
					final MockMvcResponse putResponse2 = request2.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(200, putResponse2.getStatusCode());
					final RoadForwardingOrderDto updatedOrder2 = parseOrderProcessResult(putResponse2, RoadForwardingOrderDto.class);

					// EQDangerousGood
					EQDangerousGoodDto eqDangerousGoodDto2 = (EQDangerousGoodDto) updatedOrder2.getOrderLineItems().get(0).getDangerousGoods().get(0);
					assertEquals(6, eqDangerousGoodDto2.getNoOfPackages());

				}

				@Test
				void shouldRemoveDangerousGoodContainer() throws IOException {
					final RoadForwardingOrderDto savedOrder = createNewDraftForwardingOrder(null);
					RoadOrderLineDto orderLine = savedOrder.getOrderLineItems().get(0);


					// EQDangerousGood
					EQDangerousGoodDto eqDangerousGood = new EQDangerousGoodDto();
					eqDangerousGood.setPackaging(new OptionDto().code("1"));
					eqDangerousGood.setNoOfPackages(2);
					eqDangerousGood.setSortingPosition(0);

					orderLine.getDangerousGoods().add(eqDangerousGood);


					MockMvcRequestSpecification request = givenWriteRequest().body(savedOrder);
					final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(200, putResponse.getStatusCode());
					final RoadForwardingOrderDto updatedOrder = parseOrderProcessResult(putResponse, RoadForwardingOrderDto.class);

					assertEquals(1, updatedOrder.getOrderLineItems().size());
					assertEquals(1, updatedOrder.getOrderLineItems().get(0).getDangerousGoods().size());

					// EQDangerousGood
					EQDangerousGoodDto eqDangerousGoodDto = (EQDangerousGoodDto) updatedOrder.getOrderLineItems().get(0).getDangerousGoods().get(0);
					assertNotNull(eqDangerousGoodDto.getId());
					assertEquals("1", eqDangerousGoodDto.getPackaging().getCode());
					assertEquals(2, eqDangerousGoodDto.getNoOfPackages());
					assertEquals(0, eqDangerousGoodDto.getSortingPosition());

					updatedOrder.getOrderLineItems().get(0).getDangerousGoods().clear();

					MockMvcRequestSpecification request2 = givenWriteRequest().body(updatedOrder);
					final MockMvcResponse putResponse2 = request2.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(200, putResponse2.getStatusCode());
					final RoadForwardingOrderDto updatedOrder2 = parseOrderProcessResult(putResponse2, RoadForwardingOrderDto.class);

					// EQDangerousGood
					assertEquals(0, updatedOrder2.getOrderLineItems().get(0).getDangerousGoods().size());

				}

				@Test
				void shouldRemoveDangerousGoodInsideContainer() throws IOException {
					final RoadForwardingOrderDto savedOrder = createNewDraftForwardingOrder(null);
					RoadOrderLineDto orderLine = savedOrder.getOrderLineItems().get(0);


					// EQDangerousGood
					EQDangerousGoodDto eqDangerousGood = new EQDangerousGoodDto();
					eqDangerousGood.setPackaging(new OptionDto().code("1"));
					eqDangerousGood.setNoOfPackages(2);
					eqDangerousGood.setSortingPosition(0);
					orderLine.getDangerousGoods().add(eqDangerousGood);

					// EQDangerousGood
					EQDangerousGoodDto eqDangerousGood2 = new EQDangerousGoodDto();
					eqDangerousGood2.setPackaging(new OptionDto().code("2"));
					eqDangerousGood2.setNoOfPackages(1);
					eqDangerousGood2.setSortingPosition(0);
					orderLine.getDangerousGoods().add(eqDangerousGood2);

					MockMvcRequestSpecification request = givenWriteRequest().body(savedOrder);
					final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(200, putResponse.getStatusCode());
					final RoadForwardingOrderDto updatedOrder = parseOrderProcessResult(putResponse, RoadForwardingOrderDto.class);

					assertEquals(1, updatedOrder.getOrderLineItems().size());
					assertEquals(2, updatedOrder.getOrderLineItems().get(0).getDangerousGoods().size());

					// EQDangerousGood
					EQDangerousGoodDto eqDangerousGoodDto = (EQDangerousGoodDto) updatedOrder.getOrderLineItems().get(0).getDangerousGoods().get(0);
					assertNotNull(eqDangerousGoodDto.getId());
					assertEquals("1", eqDangerousGoodDto.getPackaging().getCode());
					assertEquals(2, eqDangerousGoodDto.getNoOfPackages());
					assertEquals(0, eqDangerousGoodDto.getSortingPosition());

					EQDangerousGoodDto dangerousGoodDto = (EQDangerousGoodDto) updatedOrder.getOrderLineItems().get(0).getDangerousGoods().get(0);

					dangerousGoodDto.setNoOfPackages(6);

					updatedOrder.getOrderLineItems().get(0).getDangerousGoods().remove(1);

					MockMvcRequestSpecification request2 = givenWriteRequest().body(updatedOrder);
					final MockMvcResponse putResponse2 = request2.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(200, putResponse2.getStatusCode());
					final RoadForwardingOrderDto updatedOrder2 = parseOrderProcessResult(putResponse2, RoadForwardingOrderDto.class);

					// EQDangerousGood
					assertEquals(1, updatedOrder2.getOrderLineItems().get(0).getDangerousGoods().size());
					EQDangerousGoodDto eqDangerousGoodDto2 = (EQDangerousGoodDto) updatedOrder2.getOrderLineItems().get(0).getDangerousGoods().get(0);
					assertEquals(6, eqDangerousGoodDto2.getNoOfPackages());
				}

			}

		}

		@Nested
		class CollectionOrder{
			@Test
			void shouldSwitchOrderType() throws IOException {
				final RoadCollectionOrderDto order = createNewCollectionOrder(null, null);
				RoadForwardingOrderDto changeType = testUtil.changeType(order, RoadForwardingOrderDto.class);
				MockMvcRequestSpecification request = givenWriteRequest().body(changeType);
				// When
				MockMvcResponse mockMvcResponse = request.put(buildUrl(ORDERS_URL_PATH));

				// Test
				assertEquals(200, mockMvcResponse.getStatusCode());
				RoadForwardingOrderDto switchedOrderType = parseOrderProcessResult(mockMvcResponse, RoadForwardingOrderDto.class);
				assertNotEquals(order.getOrderId(), switchedOrderType.getOrderId());
				assertEquals(order.getOrderLineItems().size(), switchedOrderType.getOrderLineItems().size());
				assertEquals(0, switchedOrderType.getFurtherAddresses().size());

			}
		}

		@Nested
		class AirExportOrder {
			@Test
			void shouldUpdateAirOrder() throws IOException {
				final AirExportOrderDto order = loadResourceAndConvert("orders/new-air-export-order.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				MockMvcRequestSpecification request = givenWriteRequest().body(order);

				MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				AirExportOrderDto airOrder = parseOrderProcessResult(response, AirExportOrderDto.class);
				airOrder.setToIATA(new PortDto().code("FRA").type(PortTypeDto.AIRPORT));
				airOrder.setFromIATA(new PortDto().code("MUC").type(PortTypeDto.AIRPORT));
				airOrder.setDeliverToIATA(new PortDto().code("LHR").type(PortTypeDto.AIRPORT));
				airOrder.setCollectFromIATA(new PortDto().code("BGI").type(PortTypeDto.AIRPORT));

				request = givenWriteRequest().body(airOrder);
				response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				airOrder = parseOrderProcessResult(response, AirExportOrderDto.class);
				assertEquals("FRA", airOrder.getToIATA().getCode());
				assertEquals("MUC", airOrder.getFromIATA().getCode());
			}

			@Test
			void shouldNotUpdateExpiredDraft() throws IOException {
				final AirExportOrderDto order = loadResourceAndConvert("orders/new-air-export-order.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				order.setExpirationTime(OffsetDateTime.now().minusHours(1L));
				when(orderStatusDefaultProvider.getDefaultStatus()).thenReturn(OrderStatus.EXPIRED);
				MockMvcRequestSpecification request = givenWriteRequest().body(order);

				MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				AirExportOrderDto airOrder = parseOrderProcessResult(response, AirExportOrderDto.class);

				request = givenWriteRequest().body(airOrder);
				response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(HttpStatus.CONFLICT.value(), response.getStatusCode());
				GeneralProblemDto GeneralProblemDto = response.getBody().as(GeneralProblemDto.class);
				assertEquals(BookErrorId.ERR_SV_04.getErrorId(), GeneralProblemDto.getErrorId());
				assertEquals(com.dachser.dfe.book.model.GeneralProblemDto.SeverityEnum.HIGH, GeneralProblemDto.getSeverity());
			}

			@Test
			void shouldRemoveDocumentOnDraftUpdate() throws IOException {
				final AirExportOrderDto order = OrderApiTestUtil.createValidDraftAirExportOrder(documentService);

				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				order.setDocumentIds(new ArrayList<>());

				MockMvcRequestSpecification request = givenWriteRequest().body(order);

				MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				AirExportOrderDto airOrder = parseOrderProcessResult(response, AirExportOrderDto.class);

				assertEquals(0, airOrder.getDocumentIds().size());
			}

			@Test
			void shouldSwitchOrderType() throws IOException {
				final AirExportOrderDto order = OrderApiTestUtil.createValidDraftAirExportOrder(documentService);
				AirImportOrderDto changeType = testUtil.changeType(order, AirImportOrderDto.class);
				MockMvcRequestSpecification request = givenWriteRequest().body(changeType);
				// When
				MockMvcResponse mockMvcResponse = request.put(buildUrl(ORDERS_URL_PATH));

				// Test
				assertEquals(200, mockMvcResponse.getStatusCode());
				AirImportOrderDto switchedOrderType = parseOrderProcessResult(mockMvcResponse, AirImportOrderDto.class);
				assertNotEquals(order.getOrderId(), switchedOrderType.getOrderId());
				assertEquals(order.getOrderLineItems().size(), switchedOrderType.getOrderLineItems().size());
				assertEquals(order.getFurtherAddresses().get(0), switchedOrderType.getFurtherAddresses().get(0));

			}

		}

		@Nested
		class AirImportOrder {
			@Test
			void shouldUpdateAirOrder() throws IOException {
				final AirImportOrderDto order = loadResourceAndConvert("orders/new-air-import-order.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				MockMvcRequestSpecification request = givenWriteRequest().body(order);

				MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				AirImportOrderDto airOrder = parseOrderProcessResult(response, AirImportOrderDto.class);
				airOrder.setToIATA(new PortDto().code("FRA").type(PortTypeDto.AIRPORT));
				airOrder.setFromIATA(new PortDto().code("MUC").type(PortTypeDto.AIRPORT));
				airOrder.setDeliverToIATA(new PortDto().code("LHR").type(PortTypeDto.AIRPORT));
				airOrder.setCollectFromIATA(new PortDto().code("BGI").type(PortTypeDto.AIRPORT));

				request = givenWriteRequest().body(airOrder);
				response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				airOrder = parseOrderProcessResult(response, AirImportOrderDto.class);
				assertEquals("FRA", airOrder.getToIATA().getCode());
				assertEquals("MUC", airOrder.getFromIATA().getCode());
			}

			@Test
			void shouldAllowCustomerSwitchOnUpdateAirOrder() throws IOException {
				final AirImportOrderDto order = loadResourceAndConvert("orders/new-air-import-order.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				MockMvcRequestSpecification request = givenWriteRequest().body(order);

				MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				AirImportOrderDto airOrder = parseOrderProcessResult(response, AirImportOrderDto.class);
				assertEquals(UserServiceMock.VALID_CUST_NO_ASL, airOrder.getCustomerNumber());

				airOrder.setCustomerNumber(UserServiceMock.VALID_CUST_NO_ASL_2);

				request = givenWriteRequest().body(airOrder);
				response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				airOrder = parseOrderProcessResult(response, AirImportOrderDto.class);
				assertEquals(UserServiceMock.VALID_CUST_NO_ASL_2, airOrder.getCustomerNumber());
			}

			@Test
			void shouldTransferDocumentToNewCustomerNumberOnOrderUpdate() throws IOException {
				final AirImportOrderDto order = OrderApiTestUtil.createValidDraftAirImportOrder(documentService);
				List<Document> documents = documentRepository.findByOrderId(order.getOrderId());
				assertEquals(2, documents.size());
				documents.forEach(document -> Assertions.assertEquals(VALID_CUST_NO_AIR, document.getCustomerNumber()));
				order.setCustomerNumber(UserServiceMock.VALID_CUST_NO_ASL_2);
				MockMvcRequestSpecification request = givenWriteRequest().body(order);

				MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				documents = documentRepository.findByOrderId(order.getOrderId());
				documents.forEach(document -> Assertions.assertEquals(UserServiceMock.VALID_CUST_NO_ASL_2, document.getCustomerNumber()));

			}

			@Test
			void shouldNotAllowCustomerSwitchOnUpdateAirOrder() throws IOException {
				final AirImportOrderDto order = loadResourceAndConvert("orders/new-air-import-order.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				MockMvcRequestSpecification request = givenWriteRequest().body(order);

				MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				AirImportOrderDto airOrder = parseOrderProcessResult(response, AirImportOrderDto.class);
				assertEquals(UserServiceMock.VALID_CUST_NO_ASL, airOrder.getCustomerNumber());

				airOrder.setCustomerNumber(UserServiceMock.INVALID_CUST_NO);

				request = givenWriteRequest().body(airOrder);
				response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(404, response.statusCode());
			}

			@Test
			void shouldRevokeUpdatesOnExpiredOrders() throws IOException {
				final AirImportOrderDto order = loadResourceAndConvert("orders/new-air-import-order.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				order.setExpirationTime(OffsetDateTime.now().minusHours(1L));
				when(orderStatusDefaultProvider.getDefaultStatus()).thenReturn(OrderStatus.EXPIRED);
				MockMvcRequestSpecification request = givenWriteRequest().body(order);

				MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				AirImportOrderDto airOrder = parseOrderProcessResult(response, AirImportOrderDto.class);

				request = givenWriteRequest().body(airOrder);
				response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(HttpStatus.CONFLICT.value(), response.getStatusCode());
				GeneralProblemDto GeneralProblemDto = response.getBody().as(GeneralProblemDto.class);
				assertEquals(BookErrorId.ERR_SV_04.getErrorId(), GeneralProblemDto.getErrorId());
				assertEquals(com.dachser.dfe.book.model.GeneralProblemDto.SeverityEnum.HIGH, GeneralProblemDto.getSeverity());

			}

			@Test
			void shouldNotUpdatePrincipalDataFromRequest() throws IOException {
				final AirImportOrderDto order = loadResourceAndConvert("orders/new-air-import-order.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				MockMvcRequestSpecification request = givenWriteRequest().body(order);

				MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				AirImportOrderDto airOrder = parseOrderProcessResult(response, AirImportOrderDto.class);
				assertNotNull(airOrder.getPrincipalAddress());
				assertEquals("Dachser ASL", airOrder.getPrincipalAddress().getName());
				airOrder.getPrincipalAddress().setName("A different name");
				request.body(airOrder);
				response = request.put(buildUrl(ORDERS_URL_PATH));
				AirImportOrderDto airOrder2 = parseOrderProcessResult(response, AirImportOrderDto.class);
				assertEquals("Dachser ASL", airOrder2.getPrincipalAddress().getName());
				assertEquals(airOrder.getOrderId(), airOrder2.getOrderId());

			}

			@Test
			void shouldNotUpdateSeaOrderWhenToIATAIsMissing() throws IOException {
				final AirImportOrderDto order = loadResourceAndConvert("orders/new-air-import-order.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				MockMvcRequestSpecification request = givenWriteRequest().body(order);

				MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				AirImportOrderDto airImportOrder = parseOrderProcessResult(response, AirImportOrderDto.class);
				airImportOrder.setToIATA(null);

				request = givenWriteRequest().body(airImportOrder);
				response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
				assertEquals(400, response.statusCode());
				OrderProcessResultDto error = response.getBody().as(new TypeRef<>() {
				});
				assertEquals("toIATA", error.getValidationResult().getResults().stream().filter(e -> e.getField().equals("toIATA")).findFirst().get().getField());
			}

			@Test
			void shouldSwitchOrderType() throws IOException {
				final AirImportOrderDto order = OrderApiTestUtil.createValidDraftAirImportOrder(documentService);
				AirExportOrderDto changeType = testUtil.changeType(order, AirExportOrderDto.class);
				MockMvcRequestSpecification request = givenWriteRequest().body(changeType);
				// When
				MockMvcResponse mockMvcResponse = request.put(buildUrl(ORDERS_URL_PATH));

				// Test
				assertEquals(200, mockMvcResponse.getStatusCode());
				AirExportOrderDto switchedOrderType = parseOrderProcessResult(mockMvcResponse, AirExportOrderDto.class);
				assertNotEquals(order.getOrderId(), switchedOrderType.getOrderId());
				assertEquals(order.getOrderLineItems().size(), switchedOrderType.getOrderLineItems().size());
				assertEquals(order.getFurtherAddresses().get(0), switchedOrderType.getFurtherAddresses().get(0));

			}
		}

		@Nested
		class SeaExportOrder {
			@Test
			void shouldUpdateSeaOrder() throws IOException {
				final SeaExportOrderDto order = loadResourceAndConvert("orders/new-sea-order.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				MockMvcRequestSpecification request = givenWriteRequest().body(order);

				MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				SeaExportOrderDto seaExportOrder = parseOrderProcessResult(response, SeaExportOrderDto.class);
				seaExportOrder.setToPort(new PortDto().code("FRA").type(PortTypeDto.SEAPORT));
				seaExportOrder.setFromPort(new PortDto().code("MUC").type(PortTypeDto.SEAPORT));

				request = givenWriteRequest().body(seaExportOrder);
				response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				seaExportOrder = parseOrderProcessResult(response, SeaExportOrderDto.class);
				assertEquals("FRA", seaExportOrder.getToPort().getCode());
				assertEquals("MUC", seaExportOrder.getFromPort().getCode());
			}

			@Test
			void shouldNotUpdateSeaOrderWhenToPortIsMissing() throws IOException {
				final SeaExportOrderDto order = loadResourceAndConvert("orders/new-sea-order.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				MockMvcRequestSpecification request = givenWriteRequest().body(order);

				MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
				assertEquals(200, response.statusCode());
				SeaExportOrderDto seaExportOrder = parseOrderProcessResult(response, SeaExportOrderDto.class);
				seaExportOrder.setToPort(null);

				request = givenWriteRequest().body(seaExportOrder);
				response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
				assertEquals(400, response.statusCode());
				OrderProcessResultDto error = response.getBody().as(new TypeRef<>() {
				});
				assertEquals("toPort", error.getValidationResult().getResults().stream().filter(e -> e.getField().equals("toPort")).findFirst().get().getField());
			}

			@Test
			void shouldSwitchOrderType() throws IOException {
				SeaExportOrderDto validatedSeaExportOrder = OrderApiTestUtil.createValidDraftSeaExportOrder();
				SeaImportOrderDto changeType = testUtil.changeType(validatedSeaExportOrder, SeaImportOrderDto.class);
				MockMvcRequestSpecification request = givenWriteRequest().body(changeType);
				// When
				MockMvcResponse mockMvcResponse = request.put(buildUrl(ORDERS_URL_PATH));

				// Test
				assertEquals(200, mockMvcResponse.getStatusCode());
				SeaImportOrderDto switchedOrderType = parseOrderProcessResult(mockMvcResponse, SeaImportOrderDto.class);
				assertNotEquals(validatedSeaExportOrder.getOrderId(), switchedOrderType.getOrderId());
				assertEquals(1, switchedOrderType.getOrderLineItems().get(0).getHsCodes().size());

			}

			@Test
			void shouldSwitchOrderTypeFcl() throws IOException {
				SeaExportOrderDto validatedSeaExportOrder = OrderApiTestUtil.createValidDraftSeaExportOrderFcl();
				SeaImportOrderDto changeType = testUtil.changeType(validatedSeaExportOrder, SeaImportOrderDto.class);
				MockMvcRequestSpecification request = givenWriteRequest().body(changeType);
				// When
				MockMvcResponse mockMvcResponse = request.put(buildUrl(ORDERS_URL_PATH));

				// Test
				assertEquals(200, mockMvcResponse.getStatusCode());
				SeaImportOrderDto switchedOrderType = parseOrderProcessResult(mockMvcResponse, SeaImportOrderDto.class);
				assertNotEquals(validatedSeaExportOrder.getOrderId(), switchedOrderType.getOrderId());
				assertEquals(1, switchedOrderType.getFullContainerLoads().get(0).getLines().get(0).getHsCodes().size());

			}

			@Nested
			class FullContainerLoad {

				@Test
				void shouldUpdateFullContainerLoadWithAnotherOrderLine() throws IOException {
					final SeaExportOrderDto seaOrder = OrderApiTestUtil.createValidDraftSeaExportOrderFcl();

					SeaOrderLineDto orderLine = new SeaOrderLineDto().packaging(new OptionDto().code("11").description("sdfjsdljfv")).quantity(1).weight(1.0).volume(1.0).number(2);

					seaOrder.getFullContainerLoads().get(0).addLinesItem(orderLine);

					MockMvcRequestSpecification request = givenWriteRequest().body(seaOrder);
					final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(200, putResponse.getStatusCode());
					final SeaExportOrderDto updatedOrder = parseOrderProcessResult(putResponse, SeaExportOrderDto.class);

					assertEquals(1, updatedOrder.getFullContainerLoads().size());
					assertEquals(3, updatedOrder.getFullContainerLoads().get(0).getLines().size());
				}

				@Test
				void shouldDeleteOneOrderLineFromFullContainerLoad() throws IOException {
					final SeaExportOrderDto seaOrder = OrderApiTestUtil.createValidDraftSeaExportOrderFcl();

					seaOrder.getFullContainerLoads().get(0).getLines().remove(1);

					MockMvcRequestSpecification request = givenWriteRequest().body(seaOrder);
					final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(200, putResponse.getStatusCode());
					final SeaExportOrderDto updatedOrder = parseOrderProcessResult(putResponse, SeaExportOrderDto.class);

					assertEquals(1, updatedOrder.getFullContainerLoads().size());
					assertEquals(1, updatedOrder.getFullContainerLoads().get(0).getLines().size());
				}

				@Test
				void shouldUpdateOrderWithSecondContainerAndOneOrderLineInIt() throws IOException {
					final SeaExportOrderDto seaOrder = OrderApiTestUtil.createValidDraftSeaExportOrderFcl();

					FullContainerLoadDto fullContainerLoad = new FullContainerLoadDto().containerNumber("ABCU1234568").containerType(new OptionDto().code("BO").description("Box"))
							.sortingPosition(2);

					SeaOrderLineDto orderLine = new SeaOrderLineDto().packaging(new OptionDto().code("11").description("sdfjsdljfv")).quantity(1).weight(1.0).volume(1.0).number(1);

					fullContainerLoad.addLinesItem(orderLine);
					seaOrder.addFullContainerLoadsItem(fullContainerLoad);

					MockMvcRequestSpecification request = givenWriteRequest().body(seaOrder);
					final MockMvcResponse putResponse = request.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(200, putResponse.getStatusCode());
					final SeaExportOrderDto updatedOrder = parseOrderProcessResult(putResponse, SeaExportOrderDto.class);

					assertEquals(2, updatedOrder.getFullContainerLoads().size());
					assertEquals(1, updatedOrder.getFullContainerLoads().get(1).getLines().size());
				}

				@Test
				void shouldSwitchOrderType() throws IOException {
					SeaExportOrderDto validatedSeaExportOrder = OrderApiTestUtil.createValidDraftSeaExportOrder();
					SeaImportOrderDto changeType = testUtil.changeType(validatedSeaExportOrder, SeaImportOrderDto.class);
					MockMvcRequestSpecification request = givenWriteRequest().body(changeType);
					// When
					MockMvcResponse mockMvcResponse = request.put(buildUrl(ORDERS_URL_PATH));

					// Test
					assertEquals(200, mockMvcResponse.getStatusCode());
					SeaImportOrderDto switchedOrderType = parseOrderProcessResult(mockMvcResponse, SeaImportOrderDto.class);
					assertNotEquals(validatedSeaExportOrder.getOrderId(), switchedOrderType.getOrderId());
					assertEquals(1, switchedOrderType.getOrderLineItems().get(0).getHsCodes().size());

				}

			}
		}

		@Nested
		class SeaImportOrder {
			@Test
			void shouldSwitchOrderType() throws IOException {
				final SeaImportOrderDto order = OrderApiTestUtil.createValidDraftSeaImportOrder();
				SeaExportOrderDto changeType = testUtil.changeType(order, SeaExportOrderDto.class);
				MockMvcRequestSpecification request = givenWriteRequest().body(changeType);
				// When
				MockMvcResponse mockMvcResponse = request.put(buildUrl(ORDERS_URL_PATH));

				// Test
				assertEquals(200, mockMvcResponse.getStatusCode());
				SeaExportOrderDto switchedOrderType = parseOrderProcessResult(mockMvcResponse, SeaExportOrderDto.class);
				assertNotEquals(order.getOrderId(), switchedOrderType.getOrderId());
				assertEquals(order.getOrderLineItems().size(), switchedOrderType.getOrderLineItems().size());
				assertEquals(order.getFurtherAddresses().get(0), switchedOrderType.getFurtherAddresses().get(0));

			}
		}
	}

	@Nested
	class SubmitUpdateRequests {
		@Nested
		class ForwardingOrder {

			@Nested
			class SubmitSuccess {

				@Test
				void shouldSendOrderWithRequiredFields() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());
					final RoadForwardingOrderDto order = createValidatedForwardingOrder();
					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());

					assertEquals(200, response.statusCode());
					final RoadForwardingOrderDto RoadForwardingOrderDto = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
					assertEquals(OrderStatusDto.SENT, RoadForwardingOrderDto.getStatus());
					assertOrderStatus(RoadForwardingOrderDto.getOrderId(), OrderStatusDto.SENT);
				}

				@Test
				void shouldAllowSendOrderWithOnStatusCOMPLETE() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());
					final RoadForwardingOrderDto order = createValidatedForwardingOrder();
					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.PRINT_LABELS.name());
					assertEquals(200, response.statusCode());
					RoadForwardingOrderDto RoadForwardingOrderDto = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
					assertOrderStatus(RoadForwardingOrderDto.getOrderId(), OrderStatusDto.COMPLETE);

					response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());
					assertEquals(200, response.statusCode());
					RoadForwardingOrderDto = parseOrderProcessResult(response, RoadForwardingOrderDto.class);

					assertEquals(OrderStatusDto.SENT, RoadForwardingOrderDto.getStatus());
				}

				@Test
				void validationSuccessShouldNotDuplicateSSccsCodes() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());
					RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
					});
					setValidCollectionTimeForwardingOrder(forwardingOrder);
					forwardingOrder.setCustomerNumber(VALID_CUST_NO_1);
					forwardingOrder.setOrderNumber("123456");
					MockMvcResponse response = givenWriteRequest().body(forwardingOrder).put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());
					assertEquals(200, response.statusCode());
					forwardingOrder = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
					assertEquals(OrderStatusDto.SENT, forwardingOrder.getStatus());
					assertEquals(3, forwardingOrder.getGeneratedSsccs().size());

					RoadForwardingOrderDto loadedOrder = (RoadForwardingOrderDto) getOrder(forwardingOrder.getOrderId());
					assertEquals(3, loadedOrder.getGeneratedSsccs().size());

				}

				@Test
				void shouldRemoveDocumentOnSubmitUpdate() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());
					final RoadForwardingOrderDto validatedForwardingOrder = createValidatedForwardingOrder();

					validatedForwardingOrder.setDocumentIds(new ArrayList<>());

					MockMvcRequestSpecification request = givenWriteRequest().body(validatedForwardingOrder);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());

					assertEquals(200, response.statusCode());
					final RoadForwardingOrderDto RoadForwardingOrderDto = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
					assertEquals(OrderStatusDto.SENT, RoadForwardingOrderDto.getStatus());
					assertEquals(0, RoadForwardingOrderDto.getDocumentIds().size());
					assertOrderStatus(RoadForwardingOrderDto.getOrderId(), OrderStatusDto.SENT);
				}
			}

			@Nested
			class SubmitFail {

				@Test
				void shouldFailSendOrderWithPrintLabelServiceError() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());
					when(labelService.getShipmentLabelsForOrder(any(), anyInt())).thenThrow(new LabelsCouldNotBeCreatedException("mock exception"));

					final RoadForwardingOrderDto order = createValidatedForwardingOrder();

					MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json").body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());

					assertEquals(502, response.statusCode());
				}

				@Test
				void shouldFailSendOrderWithDipServiceError() throws IOException {
					when(dipService.createAndPublishXmlForOrder(any(Order.class))).thenReturn(Boolean.FALSE);
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());
					final RoadForwardingOrderDto order = createValidatedForwardingOrder();

					MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json").body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());

					assertEquals(HTTP_STATUS_SUBMISSION_FAILED, response.statusCode());
					final BasicOrderDto orderAfterUpdate = getOrder(order.getOrderId());
					assertEquals(OrderStatusDto.COMPLETE, orderAfterUpdate.getStatus());
				}

				@Test
				void shouldFailSubmitButLabelPrintStateShouldRemain() throws IOException {
					final RoadForwardingOrderDto order = createNewOrder(null, "orders/new-forwarding-order-complete-valid.json", VALID_CUST_NO_1, null);
					assertEquals(OrderStatusDto.DRAFT, order.getStatus());
					final Long orderId = order.getOrderId();

					when(dipService.createAndPublishXmlForOrder(any())).thenReturn(Boolean.FALSE);
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());

					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());

					assertEquals(HTTP_STATUS_SUBMISSION_FAILED, response.statusCode());
					final OrderProcessResultDto process = parseResponse(response, OrderProcessResultDto.class);
					assertEquals(OrderStatusDto.COMPLETE, getOrderStatusFromOrderProcess(process));
					assertOrderStatus(orderId, OrderStatusDto.COMPLETE);
				}

			}
		}

		@Nested
		class AirExportOrder {

			@Nested
			class SubmitSuccess {

				@Test
				void shouldSendOrderWithRequiredFields() throws IOException {
					final AirExportOrderDto order = createValidatedAirExportOrder();
					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());

					assertEquals(200, response.statusCode());
					final AirExportOrderDto airExportOrder = parseOrderProcessResult(response, AirExportOrderDto.class);
					assertEquals(OrderStatusDto.SENT, airExportOrder.getStatus());
					assertOrderStatus(airExportOrder.getOrderId(), OrderStatusDto.SENT);
					verify(dipService, times(1)).createAndPublishXmlForOrder(any(Order.class));
				}

				@Test
				void shouldAllowSendOrderWithOnStatusCOMPLETE() throws IOException {
					final AirExportOrderDto order = createValidatedAirExportOrder();
					MockMvcRequestSpecification request = givenWriteRequest().body(order);

					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());
					assertEquals(200, response.statusCode());
					AirExportOrderDto airExportOrder = parseOrderProcessResult(response, AirExportOrderDto.class);

					assertEquals(OrderStatusDto.SENT, airExportOrder.getStatus());

					verifyNoInteractions(shipmentLabelPdfTransformer);
				}

				@Test
				void shouldUpdateShippersReference() throws IOException {
					final AirExportOrderDto order = OrderApiTestUtil.createValidDraftAirExportOrder(documentService);
					final String shipperReference = "Custom-new-reference";
					order.setShipperReference(shipperReference);

					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
					assertEquals(200, response.statusCode());
					AirExportOrderDto airExportOrder = parseOrderProcessResult(response, AirExportOrderDto.class);

					assertEquals(OrderStatusDto.COMPLETE, airExportOrder.getStatus());
					assertEquals(shipperReference, airExportOrder.getShipperReference());

					verifyNoInteractions(shipmentLabelPdfTransformer);
				}

				@Test
				void shouldStoreFileSizeOfUploadedFile() throws IOException {
					final AirExportOrderDto order = OrderApiTestUtil.createValidDraftAirExportOrder(documentService);

					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
					assertEquals(200, response.statusCode());
					AirExportOrderDto airExportOrder = parseOrderProcessResult(response, AirExportOrderDto.class);

					assertNotNull(airExportOrder.getDocumentIds());
					List<Document> documents = documentService.getDocumentsOfOrder(order.getOrderId());
					assertFalse(documents.isEmpty(), "this is an air order and must have at least one invoice");
					assertEquals(DocumentsTestUtil.FILE.getSize() / 1024, documents.get(0).getSize());

					verifyNoInteractions(shipmentLabelPdfTransformer);
				}
			}

			@Nested
			class SubmitFail {

				@Test
				void shouldFailSendOrderWithDipServiceError() throws IOException {
					when(dipService.createAndPublishXmlForOrder(any())).thenReturn(Boolean.FALSE);
					final AirExportOrderDto order = createValidatedAirExportOrder();

					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());

					assertEquals(HTTP_STATUS_SUBMISSION_FAILED, response.statusCode());
					final BasicOrderDto orderAfterUpdate = getOrder(order.getOrderId());
					assertEquals(OrderStatusDto.COMPLETE, orderAfterUpdate.getStatus());
				}

				@Test
				void shouldFailSubmitButValidationShouldRemain() throws IOException {

					final AirExportOrderDto order = createNewOrder(null, "orders/new-air-export-order.json", VALID_CUST_NO_AIR, null);
					assertEquals(OrderStatusDto.DRAFT, order.getStatus());
					final Long orderId = order.getOrderId();
					order.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
					when(dipService.createAndPublishXmlForOrder(any())).thenReturn(Boolean.FALSE);

					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());

					assertEquals(HTTP_STATUS_SUBMISSION_FAILED, response.statusCode());
					final OrderProcessResultDto process = parseResponse(response, OrderProcessResultDto.class);
					assertEquals(OrderStatusDto.COMPLETE, getOrderStatusFromOrderProcess(process));
					assertOrderStatus(orderId, OrderStatusDto.COMPLETE);
				}

			}
		}

		@Nested
		class AirImportOrder {

			@Nested
			class SubmitSuccess {

				@Test
				void shouldSendOrderWithRequiredFields() throws IOException {
					final AirImportOrderDto order = createValidatedAirImportOrder();
					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());

					assertEquals(200, response.statusCode());
					final AirImportOrderDto airImportOrder = parseOrderProcessResult(response, AirImportOrderDto.class);
					assertEquals(OrderStatusDto.SENT, airImportOrder.getStatus());
					assertOrderStatus(airImportOrder.getOrderId(), OrderStatusDto.SENT);
					verify(dipService, times(1)).createAndPublishXmlForOrder(any(Order.class));
				}

				@Test
				void shouldAllowSendOrderWithOnStatusCOMPLETE() throws IOException {
					final AirImportOrderDto order = createValidatedAirImportOrder();
					MockMvcRequestSpecification request = givenWriteRequest().body(order);

					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());
					assertEquals(200, response.statusCode());
					AirImportOrderDto airImportOrder = parseOrderProcessResult(response, AirImportOrderDto.class);

					assertEquals(OrderStatusDto.SENT, airImportOrder.getStatus());

					verifyNoInteractions(shipmentLabelPdfTransformer);
				}

				@Test
				void shouldUpdateShippersReference() throws IOException {
					final AirImportOrderDto order = OrderApiTestUtil.createValidDraftAirImportOrder(documentService);
					final String shipperReference = "Custom-new-reference";
					order.setShipperReference(shipperReference);

					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
					assertEquals(200, response.statusCode());
					AirImportOrderDto airImportOrder = parseOrderProcessResult(response, AirImportOrderDto.class);

					assertEquals(OrderStatusDto.COMPLETE, airImportOrder.getStatus());
					assertEquals(shipperReference, airImportOrder.getShipperReference());

					verifyNoInteractions(shipmentLabelPdfTransformer);
				}

				@Test
				void shouldStoreFileSizeOfUploadedFile() throws IOException {
					final AirImportOrderDto order = OrderApiTestUtil.createValidDraftAirImportOrder(documentService);

					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
					assertEquals(200, response.statusCode());
					AirImportOrderDto airImportOrder = parseOrderProcessResult(response, AirImportOrderDto.class);

					assertNotNull(airImportOrder.getDocumentIds());
					List<Document> documents = documentService.getDocumentsOfOrder(order.getOrderId());
					assertFalse(documents.isEmpty(), "this is an air order and must have at least one invoice");
					assertEquals(DocumentsTestUtil.FILE.getSize() / 1024, documents.get(0).getSize());

					verifyNoInteractions(shipmentLabelPdfTransformer);
				}
			}

			@Nested
			class SubmitFail {

				@Test
				void shouldFailSendOrderWithEdiServiceError() throws IOException {
					when(dipService.createAndPublishXmlForOrder(any())).thenReturn(Boolean.FALSE);
					final AirImportOrderDto order = createValidatedAirImportOrder();

					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());

					assertEquals(HTTP_STATUS_SUBMISSION_FAILED, response.statusCode());
					final BasicOrderDto orderAfterUpdate = getOrder(order.getOrderId());
					assertEquals(OrderStatusDto.COMPLETE, orderAfterUpdate.getStatus());
				}

				@Test
				void shouldFailSubmitButValidationShouldRemain() throws IOException {

					final AirImportOrderDto order = createNewOrder(null, "orders/new-air-import-order.json", VALID_CUST_NO_AIR, null);
					assertEquals(OrderStatusDto.DRAFT, order.getStatus());
					final Long orderId = order.getOrderId();
					order.setDocumentIds(List.of(DocumentsTestUtil.createCommercialInvoiceAir(documentService)));
					when(dipService.createAndPublishXmlForOrder(any())).thenReturn(Boolean.FALSE);

					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());

					assertEquals(HTTP_STATUS_SUBMISSION_FAILED, response.statusCode());
					final OrderProcessResultDto process = parseResponse(response, OrderProcessResultDto.class);
					assertEquals(OrderStatusDto.COMPLETE, getOrderStatusFromOrderProcess(process));
					assertOrderStatus(orderId, OrderStatusDto.COMPLETE);
				}

			}
		}
	}

	private AirExportOrderDto createValidatedAirExportOrder() throws IOException {
		return OrderApiTestUtil.createValidatedAirExportOrder(documentService);
	}

	private AirImportOrderDto createValidatedAirImportOrder() throws IOException {
		return OrderApiTestUtil.createValidatedAirImportOrder(documentService);
	}

	@Nested
	class PrintLabelsUpdateRequests {
		@Nested
		class ForwardingOrder {

			@Nested
			class Success {

				@Test
				void shouldPrintLabelsOrderWithRequiredFields() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());

					final RoadForwardingOrderDto order = createValidatedForwardingOrder();
					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.PRINT_LABELS.name());

					assertEquals(200, response.statusCode());
					final RoadForwardingOrderDto RoadForwardingOrderDto = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
					assertEquals(OrderStatusDto.COMPLETE, RoadForwardingOrderDto.getStatus());
					verifyNoInteractions(dipService);
					assertOrderStatus(RoadForwardingOrderDto.getOrderId(), OrderStatusDto.COMPLETE);
				}

				@Test
				void shouldResetLabelPrintedAfterRecalc() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());

					final RoadForwardingOrderDto validatedForwardingOrder = createValidatedForwardingOrder();

					updateOrder(validatedForwardingOrder, OrderSaveActionDto.PRINT_LABELS);
					RoadOrder loadOrderOnValidOwner = (RoadOrder) orderRepository.loadOrderById(validatedForwardingOrder.getOrderId());
					final Integer shipmentInformationHash = loadOrderOnValidOwner.getShipmentInformationHash();
					assertNotNull(shipmentInformationHash);
					assertTrue(loadOrderOnValidOwner.getLabelPrinted());

					validatedForwardingOrder.setProduct("X");

					updateOrder(validatedForwardingOrder, OrderSaveActionDto.VALIDATE);
					loadOrderOnValidOwner = (RoadOrder) orderRepository.loadOrderById(validatedForwardingOrder.getOrderId());
					assertNotNull(loadOrderOnValidOwner.getShipmentInformationHash());
					assertNotEquals(shipmentInformationHash, loadOrderOnValidOwner.getShipmentInformationHash());
					assertFalse(loadOrderOnValidOwner.getLabelPrinted());
				}

				@Test
				void shouldAllowCustomerChangeFromDraftToPrintLabels() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());
					final RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
					});
					setValidCollectionTimeForwardingOrder(order);
					order.setOrderNumber("123456");
					order.setCustomerNumber(VALID_CUST_NO_1);
					MockMvcRequestSpecification request = givenWriteRequest().body(order);

					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(200, response.statusCode());
					RoadForwardingOrderDto forwardingOrder = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
					forwardingOrder.setCustomerNumber(UserServiceMock.VALID_CUST_NO_ROAD_2);
					assertFalse(forwardingOrder.getPrincipalLocked());
					request = givenWriteRequest().body(forwardingOrder);
					response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.PRINT_LABELS.name());
					assertEquals(HttpStatus.OK.value(), response.getStatusCode());
					forwardingOrder = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
					assertEquals(UserServiceMock.VALID_CUST_NO_ROAD_2, forwardingOrder.getCustomerNumber());
				}

				@Test
				void shouldRemoveDocumentOnPrintLabelsUpdate() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());
					final RoadForwardingOrderDto validatedForwardingOrder = createValidatedForwardingOrder();

					validatedForwardingOrder.setDocumentIds(new ArrayList<>());

					MockMvcRequestSpecification request = givenWriteRequest().body(validatedForwardingOrder);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.PRINT_LABELS.name());

					assertEquals(200, response.statusCode());
					final RoadForwardingOrderDto RoadForwardingOrderDto = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
					assertEquals(OrderStatusDto.COMPLETE, RoadForwardingOrderDto.getStatus());
					assertEquals(0, RoadForwardingOrderDto.getDocumentIds().size());
					verifyNoInteractions(dipService);
					assertOrderStatus(RoadForwardingOrderDto.getOrderId(), OrderStatusDto.COMPLETE);
				}
			}

			@Nested
			class Fail {

				@Test
				void shouldFailLabelPendingToDraft() throws IOException {
					when(dipService.createAndPublishXmlForOrder(any())).thenReturn(Boolean.TRUE);
					final RoadForwardingOrderDto order = createNewOrder(null, "orders/new-forwarding-order-complete-valid.json", OrderApiTestUtil.CUSTOMER_NUMBER_ROAD,
							OrderSaveActionDto.VALIDATE);
					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(400, response.getStatusCode());
					assertEquals("Order status LABEL_PENDING is not valid for target status DRAFT", response.getMvcResult().getResolvedException().getMessage());
				}

				@Test
				void shouldFailSendOrderWithPrintLabelServiceError() throws IOException {
					when(labelService.getShipmentLabelsForOrder(any(), anyInt())).thenThrow(new LabelsCouldNotBeCreatedException("mock exception"));

					final RoadForwardingOrderDto order = createValidatedForwardingOrder();

					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.PRINT_LABELS.name());

					assertEquals(502, response.statusCode());
					assertErrorIdInProcessResult(response.getBody(), BookErrorId.ERR_LB_01);
				}

				@Test
				void shouldFailSendOrderWithDipServiceError() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());
					when(dipService.createAndPublishXmlForOrder(any())).thenReturn(Boolean.FALSE);
					final RoadForwardingOrderDto order = createValidatedForwardingOrder();

					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.SUBMIT.name());
					OrderProcessResultDto processResult = parseResponse(response, OrderProcessResultDto.class);

					assertEquals(HTTP_STATUS_SUBMISSION_FAILED, response.statusCode());
					final BasicOrderDto orderAfterUpdate = getOrder(order.getOrderId());
					assertEquals(OrderStatusDto.COMPLETE, orderAfterUpdate.getStatus());
					assertEquals(BookErrorId.ERR_SE_01.getErrorId(), processResult.getError().getErrorId());
				}

				@Test
				void shouldNotAllowLabelPendingOnCompletedOrder() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());
					when(dipService.createAndPublishXmlForOrder(any())).thenReturn(Boolean.TRUE);
					final RoadForwardingOrderDto order = createNewOrder(null, "orders/new-forwarding-order-complete-valid.json", OrderApiTestUtil.CUSTOMER_NUMBER_ROAD,
							OrderSaveActionDto.SUBMIT);
					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.PRINT_LABELS.name());
					assertEquals(400, response.getStatusCode());
				}

				@Test
				void shouldFailPrintLabelsButValidationStateShouldRemain() throws IOException {
					final RoadForwardingOrderDto order = createNewOrder(null, "orders/new-forwarding-order-complete-valid.json", VALID_CUST_NO_1, null);
					assertEquals(OrderStatusDto.DRAFT, order.getStatus());
					final Long orderId = order.getOrderId();
					when(labelService.getShipmentLabelsForOrder(any(), anyInt())).thenThrow(new LabelsCouldNotBeCreatedException("mock exception"));
					MockMvcRequestSpecification request = givenWriteRequest().body(order);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.PRINT_LABELS.name());

					assertEquals(502, response.statusCode());
					final OrderProcessResultDto process = parseResponse(response, OrderProcessResultDto.class);
					assertEquals(OrderStatusDto.LABEL_PENDING, getOrderStatusFromOrderProcess(process));
					verifyNoInteractions(dipService);
					assertOrderStatus(orderId, OrderStatusDto.LABEL_PENDING);
					assertEquals(BookErrorId.ERR_LB_01.getErrorId(), process.getError().getErrorId());
				}

				@Test
				void shouldRevokeCustomerChangeOnLabelPending() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any())).thenReturn("mockPdf".getBytes());
					RoadForwardingOrderDto validatedForwardingOrder = createValidatedForwardingOrder();
					validatedForwardingOrder.setCustomerNumber(UserServiceMock.VALID_CUST_NO_ROAD_2);
					assertTrue(validatedForwardingOrder.getPrincipalLocked());
					MockMvcRequestSpecification request = givenWriteRequest().body(validatedForwardingOrder);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.PRINT_LABELS.name());
					assertEquals(HttpStatus.BAD_REQUEST.value(), response.getStatusCode());
					GeneralProblemDto GeneralProblemDto = response.getBody().as(GeneralProblemDto.class);
					assertEquals(BookErrorId.ERR_UN_04.getErrorId(), GeneralProblemDto.getErrorId());
				}

			}
		}
	}

	private static void setValidCollectionTimeForwardingOrder(RoadForwardingOrderDto order) {
		order.setCollectionTime(
				new CollectionTimeSlotDto().from(OffsetDateTime.now().plusDays(1)).to(OffsetDateTime.now().plusDays(1).plusHours(2)).collectionDate(LocalDate.now().plusDays(1)));
	}

	@Nested
	class ValidateOrderTest {

		@Nested
		class CollectionOrder {

			@Nested
			class ValidationFail {

				@Test
				void shouldValidateOrderWithMissingOrderLinesAsInvalid() throws IOException {
					RoadCollectionOrderDto collectionOrder = loadCollectionOrder("shipperAddressName", null);

					MockMvcRequestSpecification request = givenWriteRequest().body(collectionOrder);
					final MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
					Assertions.assertEquals(STATUS_CODE_VALIDATION_FAILS, response.statusCode());
					OrderProcessResultDto result = parseResponse(response, OrderProcessResultDto.class);
					final OrderValidationResultDto validationResult = result.getValidationResult();
					assertNotNull(validationResult.getResults());
					assertEquals(false, validationResult.getValid());
					assertEquals(OrderStatusDto.DRAFT, validationResult.getNewOrderStatus());
				}

				@Test
				void shouldValidateOrderWithMissingShipperAddressNameAsInvalid() throws IOException {
					RoadCollectionOrderDto collectionOrder = loadCollectionOrder("", 1);

					MockMvcRequestSpecification request = givenWriteRequest().body(collectionOrder);
					final MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
					Assertions.assertEquals(STATUS_CODE_VALIDATION_FAILS, response.statusCode());
					OrderValidationResultDto validation = OrderApiTestUtil.parseValidationResponse(response);
					final ValidationResultEntryDto addressNameViolation = new ValidationResultEntryDto();
					addressNameViolation.setField("shipperAddress.name");
					addressNameViolation.setDescription(MISSING_INPUT);
					addressNameViolation.setErrorType(ValidationResultEntryDto.ErrorTypeEnum.REQUIRED_FIELD_MISSING);
					assertFalse(validation.getValid());
					assertTrue(validation.getResults().contains(addressNameViolation));
					assertEquals(OrderStatusDto.DRAFT, validation.getNewOrderStatus());
				}

				@Test
				void shouldValidateOrderWithInvalidOrderLineWeightAsInvalid() throws IOException {
					RoadCollectionOrderDto collectionOrder = loadCollectionOrder("shipperAddressName", null);

					MockMvcRequestSpecification request = givenWriteRequest().body(collectionOrder);
					final MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
					Assertions.assertEquals(STATUS_CODE_VALIDATION_FAILS, response.statusCode());
					OrderValidationResultDto validation = OrderApiTestUtil.parseValidationResponse(response);
					final ValidationResultEntryDto weightViolation = new ValidationResultEntryDto();
					weightViolation.setField("orderLines[0].weight");
					weightViolation.setDescription(MISSING_INPUT);
					weightViolation.setErrorType(ValidationResultEntryDto.ErrorTypeEnum.REQUIRED_FIELD_MISSING);
					assertFalse(validation.getValid());
					assertTrue(validation.getResults().contains(weightViolation));
					assertEquals(OrderStatusDto.DRAFT, validation.getNewOrderStatus());
				}

				@Test
				void shouldValidateOrderWithMissingShipperAddressNameAndWithInvalidOrderLineWeightAsInvalid() throws IOException {
					RoadCollectionOrderDto collectionOrder = loadCollectionOrder("", null);

					MockMvcRequestSpecification request = givenWriteRequest().body(collectionOrder);
					final MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
					Assertions.assertEquals(STATUS_CODE_VALIDATION_FAILS, response.statusCode());
					OrderValidationResultDto validation = OrderApiTestUtil.parseValidationResponse(response);
					final ValidationResultEntryDto addressNameViolation = new ValidationResultEntryDto();
					addressNameViolation.setField("shipperAddress.name");
					addressNameViolation.setDescription(MISSING_INPUT);
					addressNameViolation.setErrorType(ValidationResultEntryDto.ErrorTypeEnum.REQUIRED_FIELD_MISSING);
					final ValidationResultEntryDto weightViolation = new ValidationResultEntryDto();
					weightViolation.setField("orderLines[0].weight");
					weightViolation.setDescription(MISSING_INPUT);
					weightViolation.setErrorType(ValidationResultEntryDto.ErrorTypeEnum.REQUIRED_FIELD_MISSING);
					assertFalse(validation.getValid());
					assertTrue(validation.getResults().contains(addressNameViolation));
					assertTrue(validation.getResults().contains(weightViolation));
					assertEquals(OrderStatusDto.DRAFT, validation.getNewOrderStatus());
				}

			}

			@Nested
			class ValidationSuccess {

				@Test
				void shouldValidateSuccess() throws IOException {
					RoadCollectionOrderDto collectionOrder = loadResourceAndConvert("orders/new-collection-order-complete-valid.json", new TypeReference<>() {
					});
					collectionOrder.setCustomerNumber(CUSTOMER_NUMBER_ROAD);
					collectionOrder.setOrderNumber("123456");
					collectionOrder.setFixDate(null);
					collectionOrder.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
					collectionOrder.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
					collectionOrder.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));
					MockMvcRequestSpecification request = givenWriteRequest().body(collectionOrder);
					final MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
					assertEquals(200, response.statusCode());
					OrderValidationResultDto validation = OrderApiTestUtil.parseValidationResponse(response);
					assertTrue(validation.getResults().isEmpty());
					assertEquals(true, validation.getValid());
					assertEquals(OrderStatusDto.COMPLETE, validation.getNewOrderStatus());

				}

			}

			private RoadCollectionOrderDto loadCollectionOrder(String shipperAddressName, Integer weight) throws IOException {
				final RoadCollectionOrderDto order = loadResourceAndConvert("orders/new-collection-order.json", new TypeReference<>() {
				});
				order.setCustomerNumber(CUSTOMER_NUMBER_ROAD);
				if (order.getShipperAddress() != null) {
					order.getShipperAddress().setName(shipperAddressName);
				}

				order.getOrderLineItems().stream().findFirst().ifPresent(orderLine -> orderLine.setWeight(weight));

				return order;
			}
		}

		@Nested
		class ForwardingOrder_ {

			@Nested
			class ValidationSuccess {
				@Test
				void shouldValidateOrderWithAllRequiredFieldsSet() throws IOException {
					RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
					});
					forwardingOrder.setCustomerNumber(CUSTOMER_NUMBER_ROAD);
					forwardingOrder.setOrderNumber("123456");
					forwardingOrder.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
					forwardingOrder.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
					forwardingOrder.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));
					MockMvcRequestSpecification request = givenWriteRequest().body(forwardingOrder);
					final MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
					assertEquals(200, response.statusCode());
					OrderValidationResultDto validation = OrderApiTestUtil.parseValidationResponse(response);
					assertTrue(validation.getResults().isEmpty());
					assertEquals(true, validation.getValid());
					assertEquals(OrderStatusDto.LABEL_PENDING, validation.getNewOrderStatus());
					assertEquals("99999920", forwardingOrder.getShipperAddress().getCustomerNumber());
					verifyNoInteractions(dipService);
					verifyNoInteractions(labelService);
				}

				@Test
				void shouldCalcHashOnOrderValidate() throws IOException {
					final RoadForwardingOrderDto validatedForwardingOrder = createValidatedForwardingOrder();
					final RoadOrder loadOrderOnValidOwner = (RoadOrder) orderRepository.loadOrderById(validatedForwardingOrder.getOrderId());
					assertNotNull(loadOrderOnValidOwner.getShipmentInformationHash());
					assertFalse(loadOrderOnValidOwner.getLabelPrinted());
				}

				@Test
				void shouldRecalcHashOnOrderValidate() throws IOException {
					final RoadForwardingOrderDto validatedForwardingOrder = createValidatedForwardingOrder();
					RoadOrder loadOrderOnValidOwner = (RoadOrder) orderRepository.loadOrderById(validatedForwardingOrder.getOrderId());
					final Integer shipmentInformationHash = loadOrderOnValidOwner.getShipmentInformationHash();
					assertNotNull(shipmentInformationHash);
					assertFalse(loadOrderOnValidOwner.getLabelPrinted());

					validatedForwardingOrder.setProduct("X");

					updateOrder(validatedForwardingOrder, OrderSaveActionDto.VALIDATE);
					loadOrderOnValidOwner = (RoadOrder) orderRepository.loadOrderById(validatedForwardingOrder.getOrderId());
					assertNotNull(loadOrderOnValidOwner.getShipmentInformationHash());
					assertNotEquals(shipmentInformationHash, loadOrderOnValidOwner.getShipmentInformationHash());

				}

				@Test
				void shouldSaveAvisSentDataOnAdviceSubmission() throws IOException {
					RoadForwardingOrderDto validatedForwardingOrder = createValidatedForwardingOrder();
					ForwardingOrder order = (ForwardingOrder) orderRepository.loadOrderById(validatedForwardingOrder.getOrderId());
					assertNotNull(order.getAdviceSent());
					assertTrue(order.getAdviceSent().until(Instant.now(), ChronoUnit.SECONDS) < 2);
				}
			}

			@Nested
			class ValidationFail {
				@Test
				void shouldFailOnMissingShipperAddress() throws IOException {
					RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
					});
					forwardingOrder.setShipperAddress(null);
					forwardingOrder.setCustomerNumber(CUSTOMER_NUMBER_ROAD);
					forwardingOrder.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
					forwardingOrder.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
					forwardingOrder.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));
					MockMvcRequestSpecification request = givenWriteRequest().body(forwardingOrder);
					final MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
					Assertions.assertEquals(STATUS_CODE_VALIDATION_FAILS, response.statusCode());
					OrderValidationResultDto validation = OrderApiTestUtil.parseValidationResponse(response);
					assertEquals(false, validation.getValid());
					assertNotNull(validation.getResults());
					final ValidationResultEntryDto ValidationResultEntryDto = validation.getResults().stream()
							.filter(ValidationResultEntryDto1 -> ValidationResultEntryDto1.getField().equals("shipperAddress")).findFirst().get();
					assertEquals("shipperAddress", ValidationResultEntryDto.getField());
					Assertions.assertEquals(MISSING_INPUT, ValidationResultEntryDto.getDescription());
					assertEquals(OrderStatusDto.DRAFT, validation.getNewOrderStatus());
				}

				@Test
				void shouldFailOnMissingConsigneeAddress() throws IOException {
					RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
					});
					forwardingOrder.setConsigneeAddress(null);
					forwardingOrder.setCustomerNumber(CUSTOMER_NUMBER_ROAD);
					forwardingOrder.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
					forwardingOrder.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
					forwardingOrder.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));

					MockMvcRequestSpecification request = givenWriteRequest().body(forwardingOrder);
					final MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
					Assertions.assertEquals(STATUS_CODE_VALIDATION_FAILS, response.statusCode());
					OrderValidationResultDto validation = OrderApiTestUtil.parseValidationResponse(response);
					assertEquals(false, validation.getValid());
					assertNotNull(validation.getResults());
					final ValidationResultEntryDto ValidationResultEntryDto = validation.getResults().stream()
							.filter(ValidationResultEntryDto1 -> ValidationResultEntryDto1.getField().equals("consigneeAddress")).findFirst().get();
					assertEquals("consigneeAddress", ValidationResultEntryDto.getField());
					Assertions.assertEquals(MISSING_INPUT, ValidationResultEntryDto.getDescription());
					assertEquals(OrderStatusDto.DRAFT, validation.getNewOrderStatus());
				}

				@Test
				void shouldFailOnMissingWeight() throws IOException {
					RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
					});
					forwardingOrder.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
					forwardingOrder.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
					forwardingOrder.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));

					forwardingOrder.getOrderLineItems().forEach(jsonOrderLine -> jsonOrderLine.setWeight(null));
					forwardingOrder.setCustomerNumber(CUSTOMER_NUMBER_ROAD);

					MockMvcRequestSpecification request = givenWriteRequest().body(forwardingOrder);
					final MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.getValue());
					Assertions.assertEquals(STATUS_CODE_VALIDATION_FAILS, response.statusCode());
					OrderValidationResultDto validation = OrderApiTestUtil.parseValidationResponse(response);
					assertEquals(false, validation.getValid());
					assertNotNull(validation.getResults());
					final ValidationResultEntryDto ValidationResultEntryDto = validation.getResults().stream()
							.filter(ValidationResultEntryDto1 -> ValidationResultEntryDto1.getField().equals("orderLines[0].weight")).findFirst().get();
					assertEquals("orderLines[0].weight", ValidationResultEntryDto.getField());
					Assertions.assertEquals(MISSING_INPUT, ValidationResultEntryDto.getDescription());
					assertEquals(OrderStatusDto.DRAFT, validation.getNewOrderStatus());
				}

				@Test
				void shouldFailOnWrongEKAER() throws IOException {
					RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
					});
					forwardingOrder.getReferences().get(0).setReferenceValue("X1");
					forwardingOrder.setCustomerNumber(CUSTOMER_NUMBER_ROAD);
					forwardingOrder.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
					forwardingOrder.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
					forwardingOrder.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));
					MockMvcRequestSpecification request = givenWriteRequest().body(forwardingOrder);
					final MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
					Assertions.assertEquals(STATUS_CODE_VALIDATION_FAILS, response.statusCode());
					OrderValidationResultDto validation = OrderApiTestUtil.parseValidationResponse(response);
					assertEquals(false, validation.getValid());
					assertNotNull(validation.getResults());
					final ValidationResultEntryDto ValidationResultEntryDto = validation.getResults().stream()
							.filter(ValidationResultEntryDto1 -> ValidationResultEntryDto1.getField().equals("orderReferences[0].reference")).findFirst().get();
					assertEquals("orderReferences[0].reference", ValidationResultEntryDto.getField());
					assertEquals("Enter correct EKAER number", ValidationResultEntryDto.getDescription());
					assertEquals(OrderStatusDto.DRAFT, validation.getNewOrderStatus());
				}

				@Test
				void shouldFailOnDeliveryNoteMissingIfEDNDocIsSetInUpdate() throws IOException {
					RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
					});
					forwardingOrder.setCustomerNumber(CUSTOMER_NUMBER_ROAD);
					forwardingOrder.getCollectionTime().setCollectionDate(LocalDate.now().plusDays(1));
					forwardingOrder.getCollectionTime().setFrom(OffsetDateTime.now().plusDays(1).withHour(8));
					forwardingOrder.getCollectionTime().setTo(OffsetDateTime.now().plusDays(1).withHour(12));
					MockMvcRequestSpecification request = givenWriteRequest().body(forwardingOrder);
					MockMvcResponse response = request.put(buildUrl(ORDERS_URL_PATH));
					assertEquals(200, response.getStatusCode());
					forwardingOrder = OrderApiTestUtil.parseOrderProcessResult(response, RoadForwardingOrderDto.class);

					byte[] content = "test".getBytes();
					MultipartFile mockFile = new MockMultipartFile("file", "orig", null, content);

					List<DocumentTypeDto> documents = documentService.getDocumentTypes(OrderTypeDto.ROAD_FORWARDING_ORDER);
					Optional<DocumentTypeDto> edn = documents.stream().filter(document -> document.getDescription().equalsIgnoreCase("Electronic Delivery Note")).findFirst();
					int documentTypeID = edn.get().getTypeId();// must be there otherwhise the exception is fine

					DocumentResponseDto document = documentService.uploadDocument(CUSTOMER_NUMBER_ROAD, forwardingOrder.getOrderId(), "USERNAME", documentTypeID, "Test",
							MediaType.APPLICATION_PDF, mockFile);

					forwardingOrder.setDocumentIds(List.of(document.getDocumentId()));

					request = givenWriteRequest().body(forwardingOrder);
					response = request.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
					final OrderValidationResultDto validationResult = OrderApiTestUtil.parseValidationResponse(response);
					assertNotNull(validationResult);
					assertTrue(validationResult.getResults().toString().contains(MISSING_INPUT));
					final ValidationResultEntryDto ValidationResultEntryDto = validationResult.getResults().stream()
							.filter(ValidationResultEntryDto1 -> ValidationResultEntryDto1.getField().equals("orderReferences.referenceType.delivery_note_number")).findFirst()
							.get();
					assertEquals("orderReferences.referenceType.delivery_note_number", ValidationResultEntryDto.getField());
				}
			}
		}

		@Nested
		class AirExportOrder {

			@Test
			void shouldAllowUpdateOnValidatedOrders() throws IOException {
				AirImportOrderDto validatedAirImportOrder = OrderApiTestUtil.createValidatedAirImportOrder(documentService);
				validatedAirImportOrder.setGoodsValue(1234567.0);
				validatedAirImportOrder.setGoodsCurrency("EUR");
				BasicOrderDto basicOrder = updateOrder(validatedAirImportOrder, OrderSaveActionDto.VALIDATE);
				assertEquals(OrderStatusDto.COMPLETE, basicOrder.getStatus());
				assertEquals(1234567.0, basicOrder.getGoodsValue());
			}

			@Test
			void validationSuccessShouldNotDuplicateReferences() throws IOException {
				final AirExportOrderDto validDraftAirExportOrder = OrderApiTestUtil.createValidDraftAirExportOrder(documentService);
				assertEquals(1L,
						validDraftAirExportOrder.getReferences().stream().filter(reference -> reference.getReferenceType().equals(OrderReferenceTypeDto.QUOTATION_REFERENCE))
								.count());
				final Optional<AirSeaOrderReferenceDto> firstReference = validDraftAirExportOrder.getReferences().stream()
						.filter(reference -> reference.getReferenceType().equals(OrderReferenceTypeDto.QUOTATION_REFERENCE)).findFirst();
				firstReference.get().setReferenceValue("UpdatedVALUE");
				final Long invoiceReferenceId = firstReference.get().getId();
				validDraftAirExportOrder.addReferencesItem(new AirSeaOrderReferenceDto().referenceType(OrderReferenceTypeDto.INVOICE_NUMBER).referenceValue("InvoiceVALUE"));
				updateOrder(validDraftAirExportOrder, OrderSaveActionDto.VALIDATE);

				final AirExportOrderDto reloadedOrder = getOrder(validDraftAirExportOrder.getOrderId(), AirExportOrderDto.class);
				assertEquals(4, reloadedOrder.getReferences().size());
				assertEquals(1L, reloadedOrder.getReferences().stream().filter(reference -> reference.getReferenceType().equals(OrderReferenceTypeDto.INVOICE_NUMBER)).count());
				assertEquals(1L, reloadedOrder.getReferences().stream().filter(reference -> reference.getReferenceType().equals(OrderReferenceTypeDto.SHIPPERS_REFERENCE)).count());
				final Optional<AirSeaOrderReferenceDto> firstInvoice = reloadedOrder.getReferences().stream()
						.filter(reference -> reference.getReferenceType().equals(OrderReferenceTypeDto.QUOTATION_REFERENCE)).findFirst();
				assertEquals("UpdatedVALUE", firstInvoice.get().getReferenceValue());
				assertEquals(invoiceReferenceId, firstInvoice.get().getId());
			}

			@Test
			void validationSuccessShouldNotDuplicateHsCodes() throws IOException {
				final AirExportOrderDto validDraftAirExportOrder = OrderApiTestUtil.createValidDraftAirExportOrder(documentService);
				assertEquals(2L, validDraftAirExportOrder.getOrderLineItems().stream().mapToLong(orderLine -> orderLine.getHsCodes().size()).sum());
				validDraftAirExportOrder.getOrderLineItems().get(0).getHsCodes().add(new AirSeaOrderLineHsCodeDto().hsCode("123412").goods("12313"));
				updateOrder(validDraftAirExportOrder, OrderSaveActionDto.VALIDATE);

				final AirExportOrderDto reloadedOrder = getOrder(validDraftAirExportOrder.getOrderId(), AirExportOrderDto.class);
				assertEquals(3L, reloadedOrder.getOrderLineItems().stream().mapToLong(orderLine -> orderLine.getHsCodes().size()).sum());

			}

			@Test
			void validationShouldFailOnExceedingVolume() throws IOException {
				final AirExportOrderDto validDraftAirExportOrder = OrderApiTestUtil.createValidDraftAirExportOrder(documentService);
				validDraftAirExportOrder.getOrderLineItems().stream().findFirst().ifPresent(orderLine -> orderLine.setVolume(1000000.0));

				OrderProcessResultDto OrderProcessResultDto = updateOrderToError(validDraftAirExportOrder, OrderSaveActionDto.VALIDATE);
				assertFalse(OrderProcessResultDto.getValidationResult().getValid());
				List<ValidationResultEntryDto> results = OrderProcessResultDto.getValidationResult().getResults();
				assertTrue(results.stream().anyMatch(result -> result.getField().equals("orderLineItems[0].volume")));

			}

			@Test
			void shouldTransferDocumentToNewCustomerNumberOnOrderUpdate() throws IOException {
				final AirImportOrderDto order = OrderApiTestUtil.createValidDraftAirImportOrder(documentService);
				List<Document> documents = documentRepository.findByOrderId(order.getOrderId());
				assertEquals(2, documents.size());
				documents.forEach(document -> Assertions.assertEquals(VALID_CUST_NO_AIR, document.getCustomerNumber()));
				order.setCustomerNumber(UserServiceMock.VALID_CUST_NO_ASL_2);

				final DeliveryProductDto product = new DeliveryProductDto().code("1").description("testing");
				order.setProduct(product);
				BasicOrderDto updatedOrder = updateOrder(order, OrderSaveActionDto.VALIDATE);

				assertEquals(OrderStatusDto.COMPLETE, updatedOrder.getStatus());
				documents = documentRepository.findByOrderId(order.getOrderId());
				assertEquals(2, documents.size());
				documents.forEach(document -> Assertions.assertEquals(UserServiceMock.VALID_CUST_NO_ASL_2, document.getCustomerNumber()));
				OrderApiTestUtil.assertOrderStatus(updatedOrder.getOrderId(), OrderStatusDto.COMPLETE);

			}

			@Test
			void shouldTransferDocumentToNewCustomerNumberOnAlreadyValidateOrderOnUpdate() throws IOException {
				final AirImportOrderDto order = OrderApiTestUtil.createValidDraftAirImportOrder(documentService);
				List<Document> documents = documentRepository.findByOrderId(order.getOrderId());
				assertEquals(2, documents.size());
				documents.forEach(document -> Assertions.assertEquals(VALID_CUST_NO_AIR, document.getCustomerNumber()));

				BasicOrderDto updatedOrder = updateOrder(order, OrderSaveActionDto.VALIDATE);
				assertEquals(OrderStatusDto.COMPLETE, updatedOrder.getStatus());

				order.setCustomerNumber(UserServiceMock.VALID_CUST_NO_ASL_2);

				final DeliveryProductDto product = new DeliveryProductDto().code("1").description("testing");
				order.setProduct(product);
				updatedOrder = updateOrder(order, OrderSaveActionDto.VALIDATE);
				documents = documentRepository.findByOrderId(order.getOrderId());
				assertEquals(2, documents.size());
				documents.forEach(document -> Assertions.assertEquals(UserServiceMock.VALID_CUST_NO_ASL_2, document.getCustomerNumber()));
				OrderApiTestUtil.assertOrderStatus(updatedOrder.getOrderId(), OrderStatusDto.COMPLETE);

			}

			@Test
			void shouldAutoUpdateOrderStatusSortOnUpdate() throws IOException {
				AirImportOrderDto validatedAirImportOrder = OrderApiTestUtil.createValidatedAirImportOrder(documentService);
				BasicOrderDto basicOrder = updateOrder(validatedAirImportOrder, OrderSaveActionDto.VALIDATE);
				assertEquals(OrderStatusDto.COMPLETE, basicOrder.getStatus());
				Order order = orderRepository.loadOrderById(basicOrder.getOrderId());
				assertEquals(OrderStatus.COMPLETE.getSorting(), order.getStatusSort());
			}

			@Test
			void shouldRemoveDocumentOnValidateUpdate() throws IOException {
				AirImportOrderDto validatedAirImportOrder = OrderApiTestUtil.createValidatedAirImportOrder(documentService);
				assertEquals(2, validatedAirImportOrder.getDocumentIds().size());
				validatedAirImportOrder.setDocumentIds(List.of(validatedAirImportOrder.getDocumentIds().get(0)));

				BasicOrderDto basicOrder = updateOrder(validatedAirImportOrder, OrderSaveActionDto.VALIDATE);

				assertEquals(1, basicOrder.getDocumentIds().size());
			}

			@Test
			void shouldSwitchOrderTypesImportToExport() throws IOException {
				AirImportOrderDto validDraftAirImportOrder = OrderApiTestUtil.createValidDraftAirImportOrder(documentService);
				AirExportOrderDto exportOrder = loadResourceAndConvert("orders/new-air-export-order.json", new TypeReference<>() {
				});
				exportOrder.setOrderId(validDraftAirImportOrder.getOrderId());
				exportOrder.setCustomerNumber(validDraftAirImportOrder.getCustomerNumber());
				exportOrder.setCollectionTime(validDraftAirImportOrder.getCollectionTime());
				Long commercialInvoiceAir = DocumentsTestUtil.createCommercialInvoiceAir(documentService);
				exportOrder.addDocumentIdsItem(commercialInvoiceAir);

				MockMvcRequestSpecification requestSpecification = givenWriteRequest();
				requestSpecification.body(exportOrder);
				MockMvcResponse mockMvcResponse = requestSpecification.put(buildUrl(ORDERS_URL_PATH + "{action}"), OrderSaveActionDto.VALIDATE.name());
				assertEquals(200, mockMvcResponse.getStatusCode());
				AirExportOrderDto airExportOrder = parseOrderProcessResult(mockMvcResponse, AirExportOrderDto.class);

				assertEquals(OrderStatusDto.COMPLETE, airExportOrder.getOrderStatus().getStatus());
				assertNotEquals(validDraftAirImportOrder.getOrderId(), airExportOrder.getOrderId());

			}

		}

		@Nested
		class SeaExportOrder {

			@Test
			void validationSuccessShouldNotDuplicateReferences() throws IOException {
				final SeaExportOrderDto validDraftValidDraftSeaExportOrder = OrderApiTestUtil.createValidDraftSeaExportOrder();
				assertEquals(1L, validDraftValidDraftSeaExportOrder.getReferences().stream()
						.filter(reference -> reference.getReferenceType().equals(OrderReferenceTypeDto.QUOTATION_REFERENCE)).count());
				final Optional<AirSeaOrderReferenceDto> firstReference = validDraftValidDraftSeaExportOrder.getReferences().stream()
						.filter(reference -> reference.getReferenceType().equals(OrderReferenceTypeDto.QUOTATION_REFERENCE)).findFirst();
				firstReference.get().setReferenceValue("UpdatedVALUE");
				final Long invoiceReferenceId = firstReference.get().getId();
				validDraftValidDraftSeaExportOrder.addReferencesItem(
						new AirSeaOrderReferenceDto().referenceType(OrderReferenceTypeDto.INVOICE_NUMBER).referenceValue("InvoiceVALUE"));
				updateOrder(validDraftValidDraftSeaExportOrder, OrderSaveActionDto.VALIDATE);

				final SeaExportOrderDto reloadedOrder = getOrder(validDraftValidDraftSeaExportOrder.getOrderId(), SeaExportOrderDto.class);
				assertEquals(4, reloadedOrder.getReferences().size());
				assertEquals(1L, reloadedOrder.getReferences().stream().filter(reference -> reference.getReferenceType().equals(OrderReferenceTypeDto.INVOICE_NUMBER)).count());
				assertEquals(1L, reloadedOrder.getReferences().stream().filter(reference -> reference.getReferenceType().equals(OrderReferenceTypeDto.SHIPPERS_REFERENCE)).count());
				final Optional<AirSeaOrderReferenceDto> firstInvoice = reloadedOrder.getReferences().stream()
						.filter(reference -> reference.getReferenceType().equals(OrderReferenceTypeDto.QUOTATION_REFERENCE)).findFirst();
				assertEquals("UpdatedVALUE", firstInvoice.get().getReferenceValue());
				assertEquals(invoiceReferenceId, firstInvoice.get().getId());
			}

			@Test
			void validationSuccessShouldNotDuplicateHsCodes() throws IOException {
				final SeaExportOrderDto validDraftSeaExportOrder = OrderApiTestUtil.createValidDraftSeaExportOrder();
				assertEquals(2L, validDraftSeaExportOrder.getOrderLineItems().stream().mapToLong(orderLine -> orderLine.getHsCodes().size()).sum());
				validDraftSeaExportOrder.getOrderLineItems().get(0).getHsCodes().add(new AirSeaOrderLineHsCodeDto().hsCode("123412").goods("12313"));
				updateOrder(validDraftSeaExportOrder, OrderSaveActionDto.VALIDATE);

				final SeaExportOrderDto reloadedOrder = getOrder(validDraftSeaExportOrder.getOrderId(), SeaExportOrderDto.class);
				assertEquals(3L, reloadedOrder.getOrderLineItems().stream().mapToLong(orderLine -> orderLine.getHsCodes().size()).sum());

			}

		}

	}

	private RoadOrderLineDto createOrderLine(final String name, final Integer number) {
		return new RoadOrderLineDto().number(number).content(name).quantity(1).length(100).width(100).height(100).volume(102.0d)
				.packaging(new OptionDto().code("I").description("Package"));
	}
}
