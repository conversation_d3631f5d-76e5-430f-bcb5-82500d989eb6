package com.dachser.dfe.book.masterdata.geo;

import com.dachser.dfe.book.masterdata.MasterdataGatewayConfig;
import com.dachser.dfe.book.masterdata.geo.cache.CachingLocationsApi;
import com.dachser.dfe.masterdata.geo.api.LocationsApi;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.assertInstanceOf;

class MasterdataGatewayConfigGeoApiGatewayConfigTest {

	RestTemplate restTemplate = Mockito.mock(RestTemplate.class, Mockito.RETURNS_DEEP_STUBS);

	MasterdataGatewayConfig masterdataGatewayConfig = new MasterdataGatewayConfig();

	MasterdataGatewayGeoApiGatewayConfig geoConfig = new MasterdataGatewayGeoApiGatewayConfig(restTemplate, masterdataGatewayConfig);

	@Test
	void testRoadMasterDataConfigCacheEnabled() {
		masterdataGatewayConfig.setUseCache(true);
		assertInstanceOf(CachingLocationsApi.class, geoConfig.locationsApi());
	}

	@Test
	void testRoadMasterDataConfigCacheDisabled() {
		masterdataGatewayConfig.setUseCache(false);
		assertInstanceOf(LocationsApi.class, geoConfig.locationsApi());
	}
}