databaseChangeLog:
  - changeSet:
      id: order-base-mock-data
      author: clisson
      changes:
        # 1 - road forwarding order
        - insert:
            tableName: order_base
            columns:
              - column:
                  name: order_id
                  autoIncrement: true
                  value: 1
              - column:
                  name: creator
                  type: uuid
                  value: 'bb1b5613-c916-4bc2-a54a-8064c2a5583c'
              - column:
                  name: last_editor
                  type: uuid
                  valueComputed: uuid()
              - column:
                  name: created_at
                  valueComputed: now()
              - column:
                  name: last_modified
                  valueComputed: now()
              - column:
                  name: principal_locked
                  value: 0
              - column:
                  name: datasource
                  value: 1
              - column:
                  name: shipment_number
                  value: 00000193001
              - column:
                  name: branch_id
                  value: 6 # Memmingen in Germany
              - column:
                  name: customer_number
                  value: '00000193'
              - column:
                  name: customer_number_with_segment
                  value: '00000193ROAD'
              - column:
                  name: order_type
                  value: 3 # road forwarding order
              - column:
                  name: status
                  value: 'COMPLETE'
              - column:
                  name: status_sort
                  value: 60
              - column:
                  name: principal_address_id
                  valueComputed: select order_address_id from order_address where customer_number = '00000041'
              - column:
                  name: shipper_address_id
                  valueComputed: select order_address_id from order_address where customer_number = '00000193'
              - column:
                  name: consignee_address_id
                  valueComputed: select order_address_id from order_address where customer_number = '00000502'
              - column:
                  name: goods_value
                  value: 19.99
              - column:
                  name: currency
                  value: 'EUR'
              - column:
                  name: collection_date
                  valueComputed: now()+1
        # 2 - road forwarding order having references of type 077 (booking reference)
        - insert:
            tableName: order_base
            columns:
              - column:
                  name: order_id
                  autoIncrement: true
                  value: 2
              - column:
                  name: creator
                  type: uuid
                  valueComputed: uuid()
              - column:
                  name: last_editor
                  type: uuid
                  valueComputed: uuid()
              - column:
                  name: created_at
                  valueComputed: now()
              - column:
                  name: last_modified
                  valueComputed: now()
              - column:
                  name: principal_locked
                  value: 0
              - column:
                  name: datasource
                  value: 1
              - column:
                  name: shipment_number
                  value: 00000193002
              - column:
                  name: branch_id
                  value: 6 # Memmingen in Germany
              - column:
                  name: customer_number
                  value: '00000193'
              - column:
                  name: customer_number_with_segment
                  value: '00000193ROAD'
              - column:
                  name: order_type
                  value: 3 # road forwarding order
              - column:
                  name: status
                  value: 'COMPLETE'
              - column:
                  name: status_sort
                  value: 60
              - column:
                  name: principal_address_id
                  valueComputed: select order_address_id from order_address where customer_number = '00000041'
              - column:
                  name: shipper_address_id
                  valueComputed: select order_address_id from order_address where customer_number = '00000193'
              - column:
                  name: consignee_address_id
                  valueComputed: select order_address_id from order_address where customer_number = '00000502'
              - column:
                  name: goods_value
                  value: 19.99
              - column:
                  name: currency
                  value: 'EUR'
              - column:
                  name: collection_date
                  valueComputed: now()+1
        # 3 - road forwarding order having references of type 337 (identification code transport)
        - insert:
            tableName: order_base
            columns:
              - column:
                  name: order_id
                  autoIncrement: true
                  value: 3
              - column:
                  name: creator
                  type: uuid
                  valueComputed: uuid()
              - column:
                  name: last_editor
                  type: uuid
                  valueComputed: uuid()
              - column:
                  name: created_at
                  valueComputed: now()
              - column:
                  name: last_modified
                  valueComputed: now()
              - column:
                  name: principal_locked
                  value: 0
              - column:
                  name: datasource
                  value: 1
              - column:
                  name: shipment_number
                  value: 00000193003
              - column:
                  name: branch_id
                  value: 6 # Memmingen in Germany
              - column:
                  name: customer_number
                  value: '00000193'
              - column:
                  name: customer_number_with_segment
                  value: '00000193ROAD'
              - column:
                  name: order_type
                  value: 3 # road forwarding order
              - column:
                  name: status
                  value: 'COMPLETE'
              - column:
                  name: status_sort
                  value: 60
              - column:
                  name: principal_address_id
                  valueComputed: select order_address_id from order_address where customer_number = '00000041'
              - column:
                  name: shipper_address_id
                  valueComputed: select order_address_id from order_address where customer_number = '00000193'
              - column:
                  name: consignee_address_id
                  valueComputed: select order_address_id from order_address where customer_number = '00000284'
              - column:
                  name: goods_value
                  value: 19.99
              - column:
                  name: currency
                  value: 'EUR'
              - column:
                  name: collection_date
                  valueComputed: now()+1