package com.dachser.dfe.book.order.emissionforecast;

import com.dachser.dfe.emissionforecast.model.EFForecastRequestDto;
import com.dachser.dfe.emissionforecast.model.EFForecastResponseDto;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@ConditionalOnProperty(value = "dfe.book.mock", havingValue = "true")
public class EmissionForecastAdapterMock implements EmissionForecastAdapter {

	@Override
	public Optional<EFForecastResponseDto> getEmissionForecast(EFForecastRequestDto requestDto) {
		EFForecastResponseDto responseDto = new EFForecastResponseDto();
		responseDto.setCo2Equivalents(100.0);

		return Optional.of(responseDto);

	}
}
