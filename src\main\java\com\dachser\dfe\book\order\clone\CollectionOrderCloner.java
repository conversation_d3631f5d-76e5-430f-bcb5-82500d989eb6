package com.dachser.dfe.book.order.clone;

import com.dachser.dfe.book.model.RoadCollectionOrderDto;
import com.dachser.dfe.book.order.DraftOrderProcessor;
import com.dachser.dfe.book.order.OrderRepositoryFacade;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.dachser.dfe.book.order.road.CollectionOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CollectionOrderCloner extends OrderCloner<CollectionOrder, RoadCollectionOrderDto> {

	@Autowired
	public CollectionOrderCloner(DraftOrderProcessor draftOrderProcessor, OrderMapper orderMapper, OrderRepositoryFacade orderRepositoryFacade) {
		super(draftOrderProcessor, orderMapper, orderRepositoryFacade);
	}

	@Override
	void beforeClone(RoadCollectionOrderDto order) {
		order.setReferences(null);
		clearId(order.getDifferentConsigneeAddress());
		order.getOrderLineItems().forEach(this::clearRoadOrderLineItemIds);
		order.getPackingPositions().forEach(this::clearPackingPositionIds);
	}

	@Override
	void afterClone(CollectionOrder clonedOrder) {
		// nothing to do in case of collection order
	}

}
