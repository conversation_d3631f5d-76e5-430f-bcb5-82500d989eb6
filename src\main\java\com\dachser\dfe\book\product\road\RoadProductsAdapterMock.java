package com.dachser.dfe.book.product.road;

import com.dachser.dfe.book.exception.ExtDeliveryProductServiceNotAvailable;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.order.road.RoadOrder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "true")
class RoadProductsAdapterMock implements RoadProductsAdapter {

	@Override
	public boolean validateProduct(RoadOrder roadOrder, int businessDomain) throws ExtDeliveryProductServiceNotAvailable {
		if (businessDomain == 0) {
			throw new ExtDeliveryProductServiceNotAvailable("Invalid product");
		}
		if (Objects.equals(roadOrder.getProduct(), "INVALID")) {
			return false;
		}
		return true;
	}

	@Override
	public List<DeliveryProductDto> getOnlyValidDeliveryProducts(Division division, int businessDomain, List<String> whiteList, String shipperCountry, String shipperPostcode,
			String consigneeCountry, String consigneePostcode) throws ExtDeliveryProductServiceNotAvailable {
		ArrayList<DeliveryProductDto> response = new ArrayList<>();
		if (businessDomain == 0) {
			throw new ExtDeliveryProductServiceNotAvailable("Invalid business domain");
		}
		if (!whiteList.isEmpty()) {
            response.add(createDeliveryProductDto("Product", whiteList.get(0)));
		}
		return response;
	}

	private DeliveryProductDto createDeliveryProductDto(String name, String code) {
		return new DeliveryProductDto().code(code).description(name);
	}
}
