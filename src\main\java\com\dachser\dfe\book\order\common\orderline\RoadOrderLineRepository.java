package com.dachser.dfe.book.order.common.orderline;

import com.dachser.dfe.book.order.road.RoadOrderLine;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RoadOrderLineRepository extends JpaRepository<RoadOrderLine, Long> {

	List<RoadOrderLine> findAllByOrder_OrderIdAndPackingPositionIdIsNull(Long orderId);

	List<RoadOrderLine> findTop10ByOrder_CreatorAndContentIsNotNullOrderByOrderLineIdDesc(@NotNull String orderCreator);

}
