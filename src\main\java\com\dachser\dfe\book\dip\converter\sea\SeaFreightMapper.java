package com.dachser.dfe.book.dip.converter.sea;

import com.dachser.dfe.book.dip.converter.air.AirOrderReferenceMapper;
import com.dachser.dfe.book.dip.converter.shared.AirSeaAddressMappingConfig;
import com.dachser.dfe.book.dip.converter.shared.AirSeaFreightMapper;
import com.dachser.dfe.book.dip.converter.shared.AirSeaGoodsDescriptionMapper;
import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderContactCommunicationsMapper;
import com.dachser.dfe.book.dip.converter.shared.DateMapper;
import com.dachser.dfe.book.dip.converter.shared.StringNotEmptyConditionMapper;
import com.dachser.dfe.book.dip.converter.shared.TransportMovement;
import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.model.FurtherAddressType;
import com.dachser.dfe.book.model.jaxb.order.asl.Address;
import com.dachser.dfe.book.model.jaxb.order.asl.AddressIdentification;
import com.dachser.dfe.book.model.jaxb.order.asl.Contact;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder.SeaFreightShipment.ShipmentAddress;
import com.dachser.dfe.book.model.jaxb.order.asl.GrossWeight;
import com.dachser.dfe.book.model.jaxb.order.asl.Location;
import com.dachser.dfe.book.model.jaxb.order.asl.ShipmentReference;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderBaseAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.common.fcl.FullContainerLoad;
import com.dachser.dfe.book.order.sea.SeaImportOrder;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.book.order.sea.SeaOrderReference;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

@Mapper(config = AirSeaAddressMappingConfig.class, componentModel = "spring", uses = { DateMapper.class, SeaPickupOrderMapper.class, AirSeaOrderContactCommunicationsMapper.class,
		SeaDeliveryOrderMapper.class, AirOrderReferenceMapper.class, AirSeaGoodsDescriptionMapper.class, StringNotEmptyConditionMapper.class, SeaOrderReferenceMapper.class,
		SeaOrderCategoryMapper.class, SeaContainerMapper.class })
public interface SeaFreightMapper extends AirSeaFreightMapper {

	@Mapping(target = "shipmentAddress", source = ".", qualifiedByName = "customShipmentAddressMapping")
	@Mapping(target = "messageDate", source = "sendAt", qualifiedByName = "mapInstantWithoutTimeZone")
	@Mapping(target = "orderDate", source = "sendAt", qualifiedByName = "mapInstantWithoutTimeZone")
	@Mapping(target = "typeOfCarriage", constant = "PreAndMain")
	@Mapping(target = "orderCategory", source = ".", qualifiedByName = "mapOrderCategory")
	@Mapping(target = "transportMovement", source = "order", qualifiedByName = "mapTransportMovement")
	@Mapping(target = "location", source = ".")
	@Mapping(target = "deliveryOrder", source = ".")
	@Mapping(target = "pickupOrder", source = ".")
	@Mapping(target = "termOfDelivery.value", source = "incoTerm")
	@Mapping(target = "shipmentReference", source = "orderReferences")
	@Mapping(target = "function", constant = Types.Function.ORIGINAL)
	@Mapping(target = "kindOfShipment", constant = Types.KindOfShipment.FINAL)
	@Mapping(target = "customerShipmentReference", source = "shipperReference")
	@Mapping(target = "container", source = "fullContainerLoads")
	ForwardingOrder.SeaFreightShipment map(SeaOrder order);

	default List<ForwardingOrder.SeaFreightShipment> mapList(SeaOrder order) {
		return List.of(map(order));
	}

	default List<Location> mapLocations(SeaOrder order) {
		List<Location> locations = new ArrayList<>();
		if (order.getFromPort() != null) {
			Location location = new Location();
			location.setValue(order.getFromPort());
			location.setTypeOfLocation(Types.LocationTypes.PORT_OF_LOADING);
			locations.add(location);
		}

		if (order.getToPort() != null) {
			Location location = new Location();
			location.setValue(order.getToPort());
			location.setTypeOfLocation(Types.LocationTypes.PORT_OF_UNLOADING);
			locations.add(location);
		}
		return locations;
	}

	default List<ShipmentReference> mapReferences(List<SeaOrderReference> furtherReferences) {
		return furtherReferences.stream().filter(ref -> !ref.getReferenceType().isOrderPositionReference()).map(this::mapReference).toList();
	}

	@Mapping(target = "typeOfReference", source = "referenceType.ediReferenceType")
	@Mapping(target = "value", source = "referenceValue")
	@Mapping(target = "flagLoadUnload", source = ".")
	ShipmentReference mapReference(SeaOrderReference reference);

	default String mapFlagLoadUnload(SeaOrderReference reference) {
		if (reference.isLoading() && reference.isUnloading()) {
			return Types.ShipmentReferenceFlagLoadUnload.LOAD_AND_UNLOAD;
		}
		if (reference.isLoading()) {
			return Types.ShipmentReferenceFlagLoadUnload.LOAD;
		}
		if (reference.isUnloading()) {
			return Types.ShipmentReferenceFlagLoadUnload.UNLOAD;
		}
		return null;
	}

	@Named("customShipmentAddressMapping")
	default List<ShipmentAddress> customMapShipmentAddresses(SeaOrder order) {
		final List<ShipmentAddress> shipmentAddresses = new ArrayList<>();
		// Add Orderer Address ID - we are not saving the address in the database, so we need to rely on customer number
		shipmentAddresses.add(buildAddressIdentification(order.getCustomerNumber(), Types.AddressTypes.ORDERER));
		shipmentAddresses.add(buildSeaShipmentAddressForConsignor(mapBaseAddress(order.getShipperAddress()), mapOrderContact(order.getShipperAddress().getOrderContact()),
				order.getShipperAddress().getCustomerNumber()));
		shipmentAddresses.add(buildAddressIdentification(String.format("%03d", order.getBranchId()), Types.AddressTypes.FORWARDER));

		// DFE-4338: most cases importer and consignee are the same, so we use the consignee address as importer address as well
		Optional<OrderFurtherAddress> importerAddress = order.getAddresses().stream().filter(address -> address.getAddressType().equals(FurtherAddressType.IMPORTER.getKey()))
				.findFirst();
		if (importerAddress.isPresent()) {
			shipmentAddresses.add(buildShipmentAddressFromOrderBaseAddress(importerAddress.get(), Types.AddressTypes.IMPORTER));
		} else {
			shipmentAddresses.add(buildShipmentAddressFromOrderAddress(order.getConsigneeAddress(), Types.AddressTypes.IMPORTER));
		}

		shipmentAddresses.add(buildShipmentAddressFromOrderAddress(order.getConsigneeAddress(), Types.AddressTypes.CONSIGNEE));
		if (order.getDeliveryAddress() != null) {
			shipmentAddresses.add(buildShipmentAddressFromOrderAddress(order.getDeliveryAddress(), Types.AddressTypes.ULTIMATE_CONSIGNEE));
		}

		// Note that pickup address is not mapped here, because it is mapped in the pickup order mapper

		// Add all further addresses available - Optional
		if (order.getAddresses() != null) {
			shipmentAddresses.addAll(mapFurtherAddresses(order.getAddresses()));
		}

		return shipmentAddresses;
	}

	// Workaround - We don't have any information except the branchID for the forwarder, so we use the branchId as partnerID
	default ForwardingOrder.SeaFreightShipment.ShipmentAddress buildAddressIdentification(String id, String addressType) {
		ShipmentAddress shipmentAddress = new ShipmentAddress();
		shipmentAddress.setTypeOfAddress(addressType);
		AddressIdentification addressIdentification = new AddressIdentification();
		addressIdentification.setAddressID(id);
		shipmentAddress.setAddressIdentification(addressIdentification);
		return shipmentAddress;
	}

	default ShipmentAddress buildShipmentAddressFromOrderBaseAddress(OrderBaseAddress orderAddress, String addressType) {
		ShipmentAddress shipmentAddress = new ShipmentAddress();
		if (StringUtils.isNotEmpty(orderAddress.getTaxID())) {
			shipmentAddress.setGovernmentReference(orderAddress.getTaxID());
		}
		shipmentAddress.setAddress(mapBaseAddress(orderAddress));
		shipmentAddress.setContact(mapOrderContact(orderAddress.getOrderContact()));
		shipmentAddress.setTypeOfAddress(addressType);
		return shipmentAddress;
	}

	default ShipmentAddress buildShipmentAddressFromOrderAddress(OrderAddress orderAddress, String addressType) {
		ShipmentAddress shipmentAddress = buildShipmentAddressFromOrderBaseAddress(orderAddress, addressType);
		if (orderAddress.getCustomerNumber() != null) {
			final AddressIdentification addressIdentification = new AddressIdentification();
			addressIdentification.setAddressID(orderAddress.getCustomerNumber());
			shipmentAddress.setAddressIdentification(addressIdentification);
		}
		return shipmentAddress;
	}

	List<ShipmentAddress> mapFurtherAddresses(List<OrderFurtherAddress> addresses);

	@AfterMapping
	default void removeUnmappedAddressTypes(@MappingTarget List<ShipmentAddress> shipmentAddresses) {
		shipmentAddresses.removeIf(shipmentAddress -> shipmentAddress.getTypeOfAddress() == null);
	}

	@InheritConfiguration(name = "mapAddress")
	Address mapBaseAddress(OrderBaseAddress address);

	@InheritConfiguration(name = "mapAddress")
	Address mapFurtherAddress(OrderFurtherAddress address);

	@Mapping(target = "address", source = ".")
	@Mapping(target = "typeOfAddress", source = "addressType", qualifiedByName = "mapTypeOfAddress")
	ShipmentAddress mapFurtherAddressToShipmentAddress(OrderFurtherAddress address);

	@InheritConfiguration(name = "mapContact")
	@Mapping(target = "typeOfContact", constant = Types.ContactTypes.INFORMATION)
	Contact mapOrderContact(OrderContact orderContact);

	@Named("mapTransportMovement")
	default String mapTransportMovement(SeaOrder order) {
		if (order instanceof SeaImportOrder) {
			return TransportMovement.IMPORT.getDirection();
		}
		return TransportMovement.EXPORT.getDirection();
	}

	@Mapping(source = "sortingPosition", target = "containerID", qualifiedByName = "mapSortingPosition")
	@Mapping(target = "packingType", constant = "1261")
	@Mapping(target = "grossWeight", source = "totalWeight")
	@Mapping(target = "providedBy", constant = "Forwarder")
	@Mapping(target = "goodsDescription", source = "combinedOrderLineGoods", qualifiedByName = "limitLengthTo500")
	@Mapping(target = "typeOfContainer", source = "containerType", qualifiedByName = "mapContainerType")
	@Mapping(target = "transportType", constant = "1506")
	ForwardingOrder.SeaFreightShipment.Container mapContainer(FullContainerLoad containerLoad);

	// We do have a mapping method with similar signature in AirSeaOrderLineMapper, but we need to map the weight from a different type only here
	@Mapping(target = "value", source = ".")
	@Mapping(target = "weightUnit", constant = Types.WeightUnit.KGM)
	GrossWeight mapWeight(Double weight);

	@Named("limitLengthTo500")
	default String limitLengthTo500(String input) {
		return StringUtils.abbreviate(input, 500);
	}

	@AfterMapping
	default void fillEmptyContainerNumbers(@MappingTarget ForwardingOrder.SeaFreightShipment seaFreightShipment) {
		final AtomicInteger index = new AtomicInteger(1);
		seaFreightShipment.getContainer().forEach(container -> {
			if (container.getContainerNumber() == null) {
				container.setContainerNumber(String.valueOf(index.getAndIncrement()));
			}
		});
	}

}
