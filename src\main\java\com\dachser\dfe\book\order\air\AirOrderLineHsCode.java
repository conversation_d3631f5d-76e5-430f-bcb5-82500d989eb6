package com.dachser.dfe.book.order.air;

import com.dachser.dfe.book.order.common.orderline.OrderLineHsCode;
import com.dachser.dfe.book.order.validation.air.CompleteOrderValidationAir;
import com.dachser.dfe.book.order.validation.air.ValidAirGoods;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@RequiredArgsConstructor
@ValidAirGoods(groups = CompleteOrderValidationAir.class)
@Table(name = "order_line_air_hs_code")
public class AirOrderLineHsCode extends OrderLineHsCode {

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "order_line_id", nullable = false)
	@ToString.Exclude
	@EqualsAndHashCode.Exclude
	private AirOrderLine orderLine;

	@Size(max = 1500)
	@NotNull(groups = CompleteOrderValidationAir.class, message = "{label.text.invalid_input}")
	private String goods;
}
