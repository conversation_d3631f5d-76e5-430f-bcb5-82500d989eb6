package com.dachser.dfe.book.order.clone;

import com.dachser.dfe.book.model.ADRDangerousGoodDto;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.DangerousGoodDto;
import com.dachser.dfe.book.model.LQDangerousGoodDto;
import com.dachser.dfe.book.model.OrderAddressDto;
import com.dachser.dfe.book.model.OrderContactDataDto;
import com.dachser.dfe.book.model.PackingPositionDto;
import com.dachser.dfe.book.model.RoadOrderLineDto;
import com.dachser.dfe.book.model.SourceOfOrder;
import com.dachser.dfe.book.order.DraftOrderProcessor;
import com.dachser.dfe.book.order.GenericOrderComponent;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderRepositoryFacade;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public abstract class OrderCloner<T extends Order, S extends BasicOrderDto> implements GenericOrderComponent<T> {

	private final DraftOrderProcessor draftOrderProcessor;

	private final OrderMapper orderMapper;

	private final OrderRepositoryFacade orderRepositoryFacade;

	@Transactional
	public T cloneOrder(T order) {
		S mapped = (S) orderMapper.map(order);
		cleanUpOrder(mapped);
		beforeClone(mapped);
		S newOrder = (S) draftOrderProcessor.createNewDraftOrder(null, mapped);
		T order1 = (T) orderRepositoryFacade.loadOrderById(newOrder.getOrderId());
		afterCloneDefault(order1);
		afterClone(order1);
		return orderRepositoryFacade.save(order1);
	}

	private void afterCloneDefault(T order) {
		order.setDatasource(SourceOfOrder.CLONE);
		order.setShipmentTransferId(null);
	}

	private void cleanUpOrder(BasicOrderDto order) {
		order.setOrderId(null);
		order.setDocumentIds(null);
		order.setShipmentNumber(null);
		order.setQuoteInformation(null);
		clearId(order.getShipperAddress());
		clearId(order.getConsigneeAddress());
		clearId(order.getPrincipalAddress());
		order.getFurtherAddresses().forEach(OrderCloner::clearId);
		order.getTexts().forEach(orderText -> orderText.setId(null));

	}

	protected static void clearId(OrderAddressDto orderAddress) {
		if (orderAddress != null) {
			orderAddress.setId(null);
			clearId(orderAddress.getContact());
		}
	}

	protected static void clearId(OrderContactDataDto orderContact) {
		if (orderContact != null) {
			orderContact.setId(null);
		}
	}

	protected void clearRoadOrderLineItemIds(RoadOrderLineDto item) {
		item.setId(null);
		if (item.getDangerousGoods() != null) {
			item.getDangerousGoods().forEach(this::clearDangerousGoodIds);
		}
	}

	private void clearDangerousGoodIds(DangerousGoodDto dg) {
		dg.setId(null);
		if (dg instanceof LQDangerousGoodDto lq && lq.getDangerousGoodDataItem() != null) {
			lq.getDangerousGoodDataItem().setId(null);
		} else if (dg instanceof ADRDangerousGoodDto un && un.getDangerousGoodDataItem() != null) {
			un.getDangerousGoodDataItem().setId(null);
		}
	}

	protected void clearPackingPositionIds(PackingPositionDto pos) {
		pos.setId(null);
		pos.getLines().forEach(line -> line.setId(null));
	}

	abstract void beforeClone(S mappedOrder);

	abstract void afterClone(T clonedOrder);

}
