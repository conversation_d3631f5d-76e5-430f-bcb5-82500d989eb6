package com.dachser.dfe.book.order.validation.common;

import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.order.common.AirSeaOrder;
import com.dachser.dfe.book.order.common.AirSeaOrderReference;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.validation.Messages;
import com.dachser.dfe.book.term.IncoTermService;
import jakarta.annotation.Nullable;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

@RequiredArgsConstructor
public abstract class AirSeaOrderConstraintValidator<R extends AirSeaOrderReference, O extends AirSeaOrder<R>> {

	private final Translator translator;

	private final IncoTermService incoTermService;

	public boolean isOrderValid(O order, ConstraintValidatorContext context) {
		context.disableDefaultConstraintViolation();
		AtomicBoolean anyInvalid = new AtomicBoolean(false);

		final String combinedGoodsDescription = getCombinedOrderLineGoods(order);
		if (combinedGoodsDescription.length() > 1500) {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.GOODS_DESCRIPTION_TOO_LONG)).addPropertyNode("orderLines").addConstraintViolation();
			anyInvalid.set(true);
		}

		if (hasInvalidIncoTerm(order)) {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INCO_TERM)).addPropertyNode("incoTerm").addConstraintViolation();
			anyInvalid.set(true);
		}

		if (hasInvalidShippersReference(order)) {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode("shippersReference").addConstraintViolation();
			anyInvalid.set(true);
		}

		hasDoubleEntryPerOrderReference(order).ifPresent(referenceType -> {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode("orderReferences")
					.addContainerElementNode("referenceType", RoadOrderReference.class, 0).addPropertyNode(referenceType.getReferenceType().getEdiReferenceType()).addConstraintViolation();
			anyInvalid.set(true);
		});

		return !anyInvalid.get();
	}

	private Optional<R> hasDoubleEntryPerOrderReference(O order) {
		List<R> orderReferences = order.getOrderReferences();

		if (orderReferences == null) {
			return Optional.empty();
		}

		return orderReferences.stream().filter(reference ->
						orderReferences.stream().filter(r -> r.getReferenceType().equals(reference.getReferenceType()) && r.getReferenceValue().equals(reference.getReferenceValue())).count() > 1)
				.findFirst();
	}

	protected boolean hasInvalidShippersReference(O order) {
		List<R> orderReferences = order.getOrderReferences();

		if (orderReferences == null) {
			return true;
		}

		Optional<? extends AirSeaOrderReference> shippersRef = orderReferences.stream().filter(ref -> ref.getReferenceType().equals(AirSeaOrderReferenceType.SHIPPERS_REFERENCE))
				.findFirst();
		// Invalid if shippers reference is not loading or unloading or not available at all
		return shippersRef.map(airSeaOrderReference -> (!airSeaOrderReference.isLoading() && !airSeaOrderReference.isUnloading())).orElse(true);

	}

	protected abstract boolean hasInvalidIncoTerm(O order);

	protected boolean hasInvalidIncoTerm(@Nullable String incoTerm) {
		if (incoTerm != null) {
			return !incoTermService.isIncoTermActiveByDachserCode(incoTerm);
		}
		// It is required but we do not need to handle that case here
		return false;
	}

	protected abstract String getCombinedOrderLineGoods(O order);

}
