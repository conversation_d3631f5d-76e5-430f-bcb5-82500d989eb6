package com.dachser.dfe.book.product.road;

import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.exception.ExtDeliveryProductServiceNotAvailable;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.mapper.ProductNameMapper;
import com.dachser.dfe.book.masterdata.road.RoadMasterDataApiWrapper;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.service.ext.RoadMasterDataServiceMarker;
import com.dachser.dfe.road.masterdata.model.RMDProductPilotRequestDTO;
import com.dachser.dfe.road.masterdata.model.RMDProductPilotResponseDTO;
import com.dachser.dfe.road.masterdata.model.RMDProductPilotResponsePositionDTO;
import com.dachser.dfe.road.masterdata.model.RMDValidateProductDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;

import static com.dachser.dfe.book.model.Constants.RoadMasterDataService.INPUT_STRING_NO;
import static com.dachser.dfe.book.model.Constants.RoadMasterDataService.INPUT_STRING_YES;

@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "false", matchIfMissing = true)
class RoadProductsAdapterExt implements RoadProductsAdapter, RoadMasterDataServiceMarker {

	private static final String VALIDATION_ERROR_RETURN_CODE = "FEHL";

	private final ProductNameMapper productNameMapper;

	private final RoadMasterDataApiWrapper roadMasterdataApiWrapper;

	private final CountryService countryService;

	private final GeneralDataService generalDataService;

	@Override
	public List<DeliveryProductDto> getOnlyValidDeliveryProducts(Division division, int businessDomain, List<String> whiteList, String shipperCountry, String shipperPostcode,
			String consigneeCountry, String consigneePostcode) throws ExtDeliveryProductServiceNotAvailable {
		try {
			final RMDProductPilotResponseDTO apiResponse = roadMasterdataApiWrapper.retrieveProducts(
					createProductPilotRequest(businessDomain, division, shipperCountry, shipperPostcode, consigneeCountry, consigneePostcode));
			if (apiResponse.getProducts() == null) {
				return List.of();
			}
			// @formatter:off
			final List<RMDProductPilotResponsePositionDTO> validDeliveryProducts = apiResponse.getProducts().stream()
					.filter(productDto -> productDto.getProductDescription() != null)
					.filter(productDto -> productDto.getProduct() != null)
					.filter(productDto -> whiteList.contains(productDto.getProduct()))
					.sorted(Comparator.comparing(RMDProductPilotResponsePositionDTO::getProductDescription))
					.toList();
			// @formatter:on

			return productNameMapper.mapToDtos(validDeliveryProducts);
		} catch (Exception e) {
			log.error("Exception while calling the Masterdata API: ", e);
			throw new ExtDeliveryProductServiceNotAvailable("Exception when calling external ProductsServiceExt");
		}
	}

	/**
	 * product validation concerning transport constellation
	 *
	 * @param roadOrder      - order to validate
	 * @param businessDomain - customer business domain
	 * @return true if valid, any other case false
	 */
	@Override
	public boolean validateProduct(RoadOrder roadOrder, int businessDomain) throws ExtDeliveryProductServiceNotAvailable {
		try {
			final RMDValidateProductDTO validateProductDTO = new RMDValidateProductDTO();
			validateProductDTO.setBusinessDomain(businessDomain);
			validateProductDTO.setProduct(roadOrder.getProduct());
			validateProductDTO.setDivision(roadOrder.getDivision().getKey());
			validateProductDTO.setCashOnDelivery(INPUT_STRING_NO);
			validateProductDTO.setCustoms(roadOrder.getCustomsType() != null ? INPUT_STRING_YES : INPUT_STRING_NO);
			//TODO later - validateProductDTO.setDangerousGoods
			validateProductDTO.setDangerousGoods(INPUT_STRING_NO);
			validateProductDTO.setConsigneePostCode(roadOrder.getConsigneeAddress().getPostcode());
			validateProductDTO.setConsignorPostCode(roadOrder.getShipperAddress().getPostcode());
			validateProductDTO.setConsigneeCountryCode(countryService.mapToDachserCountryCode(roadOrder.getConsigneeAddress().getCountryCode()));
			validateProductDTO.setConsignorCountryCode(countryService.mapToDachserCountryCode(roadOrder.getShipperAddress().getCountryCode()));

			final RMDValidateProductDTO apiResponse = roadMasterdataApiWrapper.validateProduct(validateProductDTO);
			if (apiResponse != null && apiResponse.getReturnCode() != null) {
				final String returnCode = apiResponse.getReturnCode().trim();
				return !returnCode.equals(VALIDATION_ERROR_RETURN_CODE);
			}
		} catch (NullPointerException e) {
			log.error("Missing data for product validation: {}", roadOrder, e);
		} catch (Exception e) {
			log.error("Error during validation of product {} ", roadOrder.getProduct(), e);
			throw new ExtDeliveryProductServiceNotAvailable("External product validation failed. " + e.getMessage());
		}
		return false;
	}

	private RMDProductPilotRequestDTO createProductPilotRequest(int businessDomain, Division division, String shipperCountry, String shipperPostCode, String consigneeCountry,
			String consigneePostcode) {
		final RMDProductPilotRequestDTO request = new RMDProductPilotRequestDTO();
		request.setBusinessDomain(businessDomain);
		request.setCustoms(INPUT_STRING_NO);
		request.setDangerousGoods(INPUT_STRING_NO);
		request.setCashOnDelivery(INPUT_STRING_NO);
		request.consigneeCountryCode(countryService.mapToDachserCountryCode(consigneeCountry));
		request.consigneePostCode(consigneePostcode);
		request.consignorCountryCode(countryService.mapToDachserCountryCode(shipperCountry));
		request.consignorPostCode(shipperPostCode);
		request.setDivision(division.getKey());
		request.setLanguageKey(generalDataService.getMasterdataLanguageForLocale(LocaleContextHolder.getLocale()));
		return request;
	}

}
