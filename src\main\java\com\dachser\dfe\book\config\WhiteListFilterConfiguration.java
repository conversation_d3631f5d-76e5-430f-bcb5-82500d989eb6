package com.dachser.dfe.book.config;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "whitelist-config")
@Data
public class WhiteListFilterConfiguration {

	@NotNull
	private FreightTermsFilter freightTermsFilter;

	@NotNull
	private CountriesFilter countries;

	@NotNull
	private PackagingOptionsFilter packagingOptionsFilter;

	@NotNull
	private DangerousGoods dangerousGoods;

	@NotNull
	private DeliveryProductFilter deliveryProducts;

	@NotNull
	private FileTypeFilter fileTypes;

	@NotNull
	private TermFilter terms;

	@NotNull
	private ContainerTypesFilter containerTypes;

	@Data
	public static class FreightTermsFilter {

		@NotEmpty
		private List<String> road;
	}

	@Data
	public static class CountriesFilter {

		@NotEmpty
		private List<String> road;
	}

	@Data
	public static class PackagingOptionsFilter {

		@NotEmpty
		private List<String> road;
		@NotEmpty
		private List<String> air;
	}

	@Data
	public static class DeliveryProductFilter {

		private Road road;

		@Data
		public static class Road {

			@NotEmpty
			private List<String> foodLogistics;

			@NotEmpty
			private List<String> europeanLogistics;

		}
	}

	@Data
	public static class FileTypeFilter {

		@NotEmpty
		private List<String> upload;
	}

	/**
	 * We use that for filtering and sorting!!!
	 */
	@Data
	public static class TermFilter {

		@NotEmpty
		private List<String> freight;

		@NotEmpty
		private List<String> inco;

		@NotEmpty
		private List<String> overview;
	}

	@Data
	public static class ContainerTypesFilter {

		@NotEmpty
		private List<String> keys;
	}

	@Data
	public static class DangerousGoods {

		private DangerousGoods.PackagingOptionsFilter packagingOptionsFilter;

		@Data
		public static class PackagingOptionsFilter {

			@NotEmpty
			private List<String> road;
		}
	}
}