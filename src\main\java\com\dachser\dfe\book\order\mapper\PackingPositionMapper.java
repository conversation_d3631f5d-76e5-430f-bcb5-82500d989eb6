package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.mapper.PersistentListMerger;
import com.dachser.dfe.book.model.PackingPositionDto;
import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = "spring", uses = OrderLineMapper.class)
public interface PackingPositionMapper {

    @Mapping(source = "packagingType.code", target = "packagingType")
    @Mapping(source = "packagingType.description", target = "packagingTypeDescription")
    @Mapping(source = "lines", target = "orderLines")
	PackingPosition mapPackingPosition(PackingPositionDto packagingPosition);

	@InheritInverseConfiguration(name = "mapPackingPosition")
	PackingPositionDto mapPackingPositionDto(PackingPosition packagingPosition);

	List<PackingPositionDto> mapPackingPositionsDtos(List<PackingPosition> packagingPositions);

	List<PackingPosition> mapPackingPositions(List<PackingPositionDto> packagingPositions);

	@InheritConfiguration(name = "mapPackingPosition")
	PackingPosition updatePackingPosition(PackingPositionDto packingPositionDto, @MappingTarget PackingPosition packingPosition);

	default void updatePackingPositions(List<PackingPositionDto> packingPositionDtos, @MappingTarget List<PackingPosition> packingPositions) {
		final PersistentListMerger<PackingPosition, PackingPositionDto> listMapper = this::updatePackingPosition;
		listMapper.updateList(packingPositionDtos, packingPositions, PackingPosition::getId, PackingPositionDto::getId, PackingPosition::new);
	}
}
