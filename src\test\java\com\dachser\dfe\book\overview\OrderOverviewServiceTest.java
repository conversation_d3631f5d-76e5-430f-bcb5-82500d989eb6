package com.dachser.dfe.book.overview;

import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.document.DocumentType;
import com.dachser.dfe.book.document.DocumentTypeRepository;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ErrorIdExternalServiceNotAvailableException;
import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.jpa.AirOrderReferenceRepository;
import com.dachser.dfe.book.jpa.OrderReferenceRepository;
import com.dachser.dfe.book.jpa.SeaOrderReferenceRepository;
import com.dachser.dfe.book.jpa.VOrderOverviewRepository;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.jpa.entity.VOrderOverview;
import com.dachser.dfe.book.mapper.OrderOverviewMapper;
import com.dachser.dfe.book.mapper.OrderOverviewMapperImpl;
import com.dachser.dfe.book.mapper.OrderStatusMapper;
import com.dachser.dfe.book.mapper.SortFilterQueryMapper;
import com.dachser.dfe.book.mapper.SortFilterQueryMapperImpl;
import com.dachser.dfe.book.mapper.custom.TranslationMapper;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.ColumnConfiguration;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.model.overview.ReferenceTranslation;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.order.air.AirOrderReference;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.exception.OrderOverviewDatabaseException;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.sea.SeaOrderReference;
import com.dachser.dfe.book.product.air.AirProductService;
import com.dachser.dfe.book.product.road.RoadProductsService;
import com.dachser.dfe.book.service.FurtherAddressTypeService;
import com.dachser.dfe.book.spring.LoggerRetryListener;
import com.dachser.dfe.book.user.BusinessDomainProvider;
import com.dachser.dfe.book.user.UserContextService;
import com.dachser.dfe.dfefiltersorthelper.service.SortAndFilterService;
import com.dachser.dfe.trackandtrace.model.TTASLOrder;
import com.dachser.dfe.trackandtrace.model.TTBasicOrderOverview;
import com.dachser.dfe.trackandtrace.model.TTOrderReference;
import com.dachser.dfe.trackandtrace.model.TTOrderType;
import com.dachser.dfe.trackandtrace.model.TTQueryObject;
import com.dachser.dfe.trackandtrace.model.TTResponsePage;
import com.dachser.dfe.trackandtrace.model.TTRoadOrder;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Captor;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.retry.annotation.EnableRetry;

import java.util.List;
import java.util.Optional;

import static com.dachser.dfe.book.user.UserServiceMock.VALID_CUST_NO_ROAD;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = { OrderOverviewService.class, OrderOverviewMapperImpl.class, SortAndFilterService.class, SortFilterQueryMapperImpl.class, ColumnConfiguration.class,
		CachedOrderOverviewService.class, LoggerRetryListener.class })
@EnableRetry
@ExtendWith(MockitoExtension.class)
public class OrderOverviewServiceTest {
	private final Long CUSTOMER_ID = 1L;

	private final int PAGE = 1;

	private final int SIZE = 10;

	@Autowired
	OrderOverviewService orderOverviewService;

	@Autowired
	OrderOverviewMapper mapper;

	@Autowired
	SortAndFilterService<VOrderOverview> sortAndFilterService;

	@Autowired
	SortFilterQueryMapper sortFilterQueryMapper;

	@MockBean
	VOrderOverviewRepository repository;

	@MockBean
	FurtherAddressTypeService furtherAddressTypeService;

	@SpyBean
	CachedOrderOverviewService cachedOrderOverviewService;

	@MockBean
	RoadProductsService productsService;

	@MockBean
	AirProductService airProductService;

	@MockBean
	DocumentService documentService;

	@MockBean
	Translator translator;

	@MockBean
	DocumentTypeRepository documentTypeRepository;

	@MockBean
	OrderReferenceRepository orderReferenceRepository;

	@MockBean
	AirOrderReferenceRepository airOrderReferenceRepository;

	@MockBean
	SeaOrderReferenceRepository seaOrderReferenceRepository;

	@MockBean
	UserContextService userContextService;

	@MockBean
	BusinessDomainProvider businessDomainProvider;

	@MockBean
	OrderStatusMapper orderStatusMapper;

	@MockBean
	TranslationMapper translationMapper;

	@Captor
	private ArgumentCaptor<String> translationLabelCaptor;

	@Nested
	@TestInstance(TestInstance.Lifecycle.PER_CLASS)
	class ValidRequests {

		@BeforeEach
		void setUp() {
			when(userContextService.getFirstFoundDivision(anyString(), any(Segment.class))).thenReturn(Division.T);
		}

		@Test
		void getPageResponseForRoadForwardingOrder() {
			// given
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderType(3);
			overview.setCustomerNumber(VALID_CUST_NO_ROAD);
			overview.setDivision(Division.T);
			Page<VOrderOverview> overviews = new PageImpl<>(List.of(overview), PageRequest.of(PAGE, SIZE), 1);
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenReturn(overviews);
			// when
			TTResponsePage responsePage = orderOverviewService.getOverviewResponsePage(PageRequest.of(PAGE, SIZE), new TTQueryObject());
			// then
			assertEquals(1, responsePage.getContent().size());
			assertEquals(TTOrderType.ROAD.name(), ((TTRoadOrder) responsePage.getContent().getFirst()).getOrderType().getValue());
		}

		@Test
		void getPageResponseForRoadCollectionOrder() {
			// given
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderType(2);
			overview.setCustomerNumber(VALID_CUST_NO_ROAD);
			overview.setDivision(Division.T);

			Page<VOrderOverview> overviews = new PageImpl<>(List.of(overview), PageRequest.of(PAGE, SIZE), 1);
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenReturn(overviews);
			// when
			TTResponsePage responsePage = orderOverviewService.getOverviewResponsePage(PageRequest.of(PAGE, SIZE), new TTQueryObject());
			// then
			assertEquals(1, responsePage.getContent().size());
			assertEquals(TTOrderType.ROAD.name(), ((TTRoadOrder) responsePage.getContent().getFirst()).getOrderType().getValue());
		}

		@Test
		void getPageResponseForAirImportOrder() {
			// given
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderType(4);
			overview.setCustomerNumber(VALID_CUST_NO_ROAD);
			overview.setDivision(Division.T);

			Page<VOrderOverview> overviews = new PageImpl<>(List.of(overview), PageRequest.of(PAGE, SIZE), 1);
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenReturn(overviews);
			// when
			TTResponsePage responsePage = orderOverviewService.getOverviewResponsePage(PageRequest.of(PAGE, SIZE), new TTQueryObject());
			// thenN
			assertEquals(1, responsePage.getContent().size());
			assertEquals(TTOrderType.AIR.name(), ((TTASLOrder) responsePage.getContent().getFirst()).getOrderType().getValue());
		}

		@Test
		void mapsProductCorrectForAir() {
			// given
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderType(4);
			overview.setCustomerNumber(VALID_CUST_NO_ROAD);
			overview.setProduct("1");
			Page<VOrderOverview> overviews = new PageImpl<>(List.of(overview), PageRequest.of(PAGE, SIZE), 1);
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenReturn(overviews);
			when(airProductService.getAirProductByProductCode(any())).thenReturn(Optional.of(new AirProductDto("1", "description 1").hint("hint").active(true)));

			TTResponsePage responsePage = orderOverviewService.getOverviewResponsePage(PageRequest.of(PAGE, SIZE), new TTQueryObject());

			assertEquals(1, responsePage.getContent().size());
			assertEquals("description 1", ((TTASLOrder) responsePage.getContent().getFirst()).getProduct().getDescription());
		}

		@Test
		void doesNotFailWithEmptyDocList() {
			// given
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderType(4);
			overview.setCustomerNumber(VALID_CUST_NO_ROAD);
			overview.setIncludedDocuments("");
			overview.setDivision(Division.T);

			Page<VOrderOverview> overviews = new PageImpl<>(List.of(overview), PageRequest.of(PAGE, SIZE), 1);
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenReturn(overviews);
			// when
			TTResponsePage responsePage = orderOverviewService.getOverviewResponsePage(PageRequest.of(PAGE, SIZE), new TTQueryObject());
			// then
			assertEquals(1, responsePage.getContent().size());
			assertEquals(TTOrderType.AIR.name(), ((TTASLOrder) responsePage.getContent().getFirst()).getOrderType().getValue());
		}

		@Test
		void translatesReferencesTexts() {
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderType(3);
			overview.setCustomerNumber(VALID_CUST_NO_ROAD);
			overview.setOrderReferences("ref1,ref2");
			overview.setDivision(Division.T);
			Page<VOrderOverview> overviews = new PageImpl<>(List.of(overview), PageRequest.of(PAGE, SIZE), 1);
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenReturn(overviews);

			RoadOrderReference ref1 = new RoadOrderReference();
			ref1.setOrder(new RoadOrder());
			ref1.setReference("ref1");
			ref1.setOrderReferenceId(1L);
			ref1.setReferenceType(ReferenceType.DELIVERY_NOTE_NUMBER);
			String ref1CodeLabel = ReferenceType.DELIVERY_NOTE_NUMBER.getDfeValue();
			String ref1CodeLabelTranslated = "translatedCodeLabel1";
			RoadOrderReference ref2 = new RoadOrderReference();
			ref2.setOrder(new RoadOrder());
			ref2.setReference("ref2");
			ref2.setOrderReferenceId(2L);
			ref2.setReferenceType(ReferenceType.EKAER_NUMBER);
			String ref2CodeLabel = ReferenceType.EKAER_NUMBER.getDfeValue();
			String ref2CodeLabelTranslated = "translatedCodeLabel2";
			var refs = List.of(ref1, ref2);
			String refSubCodeLabel = "uit_code";
			when(orderReferenceRepository.findOrderReferencesByReferenceAndOrderId(any(), any())).thenReturn(refs);
			when(translationMapper.translatedLabel(ref1CodeLabel)).thenReturn(ref1CodeLabelTranslated);
			when(translationMapper.translatedLabel(ref2CodeLabel)).thenReturn(ref2CodeLabelTranslated);

			TTResponsePage responsePage = orderOverviewService.getOverviewResponsePage(PageRequest.of(PAGE, SIZE), new TTQueryObject());
			TTBasicOrderOverview order = (TTBasicOrderOverview) responsePage.getContent().getFirst();

			assertEquals(refs.size(), order.getReferences().size());
			assertEquals(ref2CodeLabelTranslated, order.getReferences().getFirst().getText());
			assertEquals(ref1CodeLabelTranslated, order.getReferences().get(1).getText());

			verify(translationMapper, times(1)).translatedLabel(ref1CodeLabel);
			verify(translationMapper, times(1)).translatedLabel(ref2CodeLabel);
			verify(translationMapper, times(0)).translatedLabel(refSubCodeLabel);
		}

		@ParameterizedTest
		@EnumSource(value = ReferenceType.class)
		void translatesRoadReferencesTexts(ReferenceType referenceType) {
			// given
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderType(3);
			overview.setCustomerNumber(VALID_CUST_NO_ROAD);
			overview.setOrderReferences("ref1");
			overview.setDivision(Division.T);
			Page<VOrderOverview> overviews = new PageImpl<>(List.of(overview), PageRequest.of(PAGE, SIZE), 1);
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenReturn(overviews);

			RoadOrderReference ref = new RoadOrderReference();
			ref.setReference("111");
			ref.setOrderReferenceId(1L);
			ref.setReferenceType(referenceType);
			var refs = List.of(ref);
			when(orderReferenceRepository.findOrderReferencesByReferenceAndOrderId(any(), any())).thenReturn(refs);

			TTResponsePage responsePage = orderOverviewService.getOverviewResponsePage(PageRequest.of(PAGE, SIZE), new TTQueryObject());

			TTBasicOrderOverview order = (TTBasicOrderOverview) responsePage.getContent().getFirst();
			if (!order.getReferences().isEmpty()) {
				TTOrderReference.ReferenceCodeEnum referenceCode = order.getReferences().getFirst().getReferenceCode();
				Optional<ReferenceTranslation> translation = ReferenceTranslation.getByName(referenceCode.name());
				verify(translationMapper, atLeastOnce()).translatedLabel(translationLabelCaptor.capture());
				if (translation.isPresent()) {
					assertThat(translationLabelCaptor.getAllValues()).contains(translation.get().getLabel());
				} else {
					assertThat(translationLabelCaptor.getAllValues()).contains(referenceCode.getValue());
				}
			}
		}

		@ParameterizedTest
		@EnumSource(AirSeaOrderReferenceType.class)
		void translatesAirReferencesTexts(AirSeaOrderReferenceType referenceType) {
			// given
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderType(5);
			overview.setCustomerNumber(VALID_CUST_NO_ROAD);
			overview.setOrderReferences("ref1");
			Page<VOrderOverview> overviews = new PageImpl<>(List.of(overview), PageRequest.of(PAGE, SIZE), 1);
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenReturn(overviews);

			AirOrderReference ref = new AirOrderReference();
			ref.setReferenceValue("111");
			ref.setOrderReferenceId(1L);
			ref.setReferenceType(referenceType);
			var refs = List.of(ref);
			when(airOrderReferenceRepository.findAirOrderReferencesByReferenceAndOrderId(any(), any())).thenReturn(refs);

			// when
			TTResponsePage responsePage = orderOverviewService.getOverviewResponsePage(PageRequest.of(PAGE, SIZE), new TTQueryObject());
			// then
			TTBasicOrderOverview order = (TTBasicOrderOverview) responsePage.getContent().getFirst();
			if (!order.getReferences().isEmpty()) {
				TTOrderReference.ReferenceCodeASLEnum referenceCode = order.getReferences().getFirst().getReferenceCodeASL();
				Optional<ReferenceTranslation> translation = ReferenceTranslation.getByName(referenceCode.name());
				verify(translationMapper, times(refs.size())).translatedLabel(translationLabelCaptor.capture());
				if (translation.isPresent()) {
					assertThat(translationLabelCaptor.getValue()).isEqualTo(translation.get().getLabel());
				} else {
					assertThat(translationLabelCaptor.getValue()).isEqualTo(referenceCode.getValue());
				}
			}
		}

		@ParameterizedTest
		@EnumSource(AirSeaOrderReferenceType.class)
		void translatesSeaReferencesTexts(AirSeaOrderReferenceType referenceType) {
			// given
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderType(1);
			overview.setCustomerNumber(VALID_CUST_NO_ROAD);
			overview.setOrderReferences("ref1");
			overview.setDivision(Division.T);
			Page<VOrderOverview> overviews = new PageImpl<>(List.of(overview), PageRequest.of(PAGE, SIZE), 1);
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenReturn(overviews);

			SeaOrderReference ref = new SeaOrderReference();
			ref.setReferenceValue("111");
			ref.setOrderReferenceId(1L);
			ref.setReferenceType(referenceType);
			var refs = List.of(ref);
			when(seaOrderReferenceRepository.findSeaOrderReferencesByReferenceAndOrderId(any(), any())).thenReturn(refs);

			// when
			TTResponsePage responsePage = orderOverviewService.getOverviewResponsePage(PageRequest.of(PAGE, SIZE), new TTQueryObject());
			// then
			TTBasicOrderOverview order = (TTBasicOrderOverview) responsePage.getContent().getFirst();
			if (!order.getReferences().isEmpty()) {
				TTOrderReference.ReferenceCodeASLEnum referenceCode = order.getReferences().getFirst().getReferenceCodeASL();
				Optional<ReferenceTranslation> translation = ReferenceTranslation.getByName(referenceCode.name());
				verify(translationMapper, times(refs.size())).translatedLabel(translationLabelCaptor.capture());
				if (translation.isPresent()) {
					assertThat(translationLabelCaptor.getValue()).isEqualTo(translation.get().getLabel());
				} else {
					assertThat(translationLabelCaptor.getValue()).isEqualTo(referenceCode.getValue());
				}
			}
		}

		@ParameterizedTest(name = "{index} => orderStatus=''{0}''")
		@EnumSource(value = OrderStatus.class, names = { "DELETED", "DRAFT", "LABEL_PENDING", "SENT" })
		void shouldGetCorrectStatus(OrderStatus status) {
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderType(4);
			overview.setCustomerNumber(VALID_CUST_NO_ROAD);
			overview.setStatus(status);
			overview.setDivision(Division.T);

			Page<VOrderOverview> overviews = new PageImpl<>(List.of(overview), PageRequest.of(PAGE, SIZE), 1);
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenReturn(overviews);
			// when
			TTResponsePage responsePage = orderOverviewService.getOverviewResponsePage(PageRequest.of(PAGE, SIZE), new TTQueryObject());
			assertEquals(1, responsePage.getContent().size());
			TTASLOrder airImportOrder = (TTASLOrder) responsePage.getContent().getFirst();
			assertEquals(status.name(), airImportOrder.getStatus().getCode().name());
			verify(translationMapper, times(1)).translatedLabel(anyString());
		}

		@Test
		void shouldMapProductName() {
			// given
			VOrderOverview overview = new VOrderOverview();
			String productCode = "1";
			overview.setDivision(Division.T);
			overview.setProduct(productCode);
			overview.setOrderType(OrderType.ROADCOLLECTIONORDER.getId());
			String expectedProductName = "targospeed 10";
			DeliveryProductDto expectedProduct = new DeliveryProductDto().code(productCode).description(expectedProductName);
			when(productsService.getProductByKey(any(), any())).thenReturn(Optional.of(expectedProduct));
			// when
			orderOverviewService.mapProductName(overview);
			// then
			assertEquals(expectedProductName, overview.getProductName());
		}

		@Test
		void shouldNotMapProductNameIfLookupFails() {
			// given
			VOrderOverview overview = new VOrderOverview();
			String productCode = "1";
			overview.setOrderId(1L);
			overview.setProduct(productCode);
			overview.setDivision(Division.T);
			overview.setOrderType(OrderType.ROADCOLLECTIONORDER.getId());
			DeliveryProductDto expectedProduct = createDeliveryProduct();
			when(productsService.getProductByKey(overview.getProduct(), Division.T)).thenReturn(Optional.of(expectedProduct));
			// when
			orderOverviewService.mapProductName(overview);
			// then
			assertEquals(expectedProduct.getDescription(), overview.getProductName());
		}

		@NullSource
		@ParameterizedTest
		@ValueSource(strings = { "", " " })
		void shouldNotMapProductNameIfEmptyOrNull(String productCode) {
			// given
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderId(1L);
			overview.setProduct(productCode);
			overview.setDivision(Division.T);
			overview.setOrderType(OrderType.ROADCOLLECTIONORDER.getId());
			DeliveryProductDto expectedProduct = createDeliveryProduct();
			when(productsService.getProductByKey(overview.getProduct(), Division.T)).thenReturn(Optional.of(expectedProduct));

			// when
			orderOverviewService.mapProductName(overview);
			// then
			Assertions.assertNull(overview.getProductName());
		}

		@ParameterizedTest
		@EnumSource(OrderType.class)
		void shouldUseProductNameForOverviewOnlyForAir(OrderType orderType) {
			// given
			String productName = (orderType == OrderType.AIRIMPORTORDER || orderType == OrderType.AIREXPORTORDER) ? "123" : "testName";
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderId(1L);
			overview.setProduct(productName);
			overview.setOrderType(orderType.getId());
			overview.setDivision(Division.T);
			// when
			DeliveryProductDto deliveryProduct = createDeliveryProduct();
			when(productsService.getProductByKey(overview.getProduct(), Division.T)).thenReturn(Optional.of(deliveryProduct));
			when(airProductService.getAirProductByProductCode(any())).thenReturn(Optional.of(new AirProductDto("1", productName).hint("description").active(true)));

			orderOverviewService.mapProductName(overview);
			// then
			if (!OrderType.isAirOrderType(orderType)) {
				Assertions.assertNull(overview.getProductName());
			} else {
				assertEquals(productName, overview.getProductName());
				assertEquals(productName, overview.getProduct());

			}
		}

		@Test
		void shouldMapAirProductName() {
			// given
			VOrderOverview overview = new VOrderOverview();
			String productCode = "1";
			overview.setProduct(productCode);
			overview.setOrderType(OrderType.AIRIMPORTORDER.getId());
			String expectedProductName = "avigoexpress";
			AirProductDto expectedProduct = new AirProductDto("1", expectedProductName).hint("description").active(true);
			when(airProductService.getAirProductByProductCode(any())).thenReturn(Optional.of(expectedProduct));

			orderOverviewService.mapProductName(overview);
			assertEquals(expectedProductName, overview.getProductName());
		}

		@Test
		void shouldNotMapAirProductNameIfLookupFails() {
			// given
			VOrderOverview overview = new VOrderOverview();
			String productCode = "droelf";
			overview.setOrderId(1L);
			overview.setProduct(productCode);
			overview.setOrderType(OrderType.AIRIMPORTORDER.getId());

			orderOverviewService.mapProductName(overview);
			assertEquals("ERROR", overview.getProductName());
		}

		@NullSource
		@ParameterizedTest
		@ValueSource(strings = { "", " " })
		void shouldNotMapAirProductNameIfEmptyOrNull(String productCode) {
			// given
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderId(1L);
			overview.setProduct(productCode);
			overview.setOrderType(OrderType.AIRIMPORTORDER.getId());
			when(airProductService.getAirProductByProductCode(any())).thenReturn(Optional.empty());

			orderOverviewService.mapProductName(overview);
			Assertions.assertNull(overview.getProductName());
		}

		@Test
		void shouldAddErrorEntryWhenLookUpFails() {
			// given
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderId(1L);
			overview.setCustomerNumber("0");
			overview.setOrderType(3);
			String productCode = "1";
			overview.setProduct(productCode);
			overview.setDivision(Division.T);
			DeliveryProductDto expectedProduct = createDeliveryProduct();
			when(productsService.getDeliveryProductsForDivision(any())).thenReturn(List.of(expectedProduct));
			Page<VOrderOverview> overviews = new PageImpl<>(List.of(overview), PageRequest.of(PAGE, SIZE), 1);
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenReturn(overviews);
			// when
			TTResponsePage responsePage = orderOverviewService.getOverviewResponsePage(PageRequest.of(PAGE, SIZE), new TTQueryObject());
			// then

			assertEquals("ERROR", ((TTBasicOrderOverview) responsePage.getContent().getFirst()).getProduct().getDescription());
		}

		@Test
		void shouldAddErrorEntryWhenNoProductFound() {
			// given
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderId(1L);
			overview.setCustomerNumber("0");
			overview.setOrderType(3);
			String productCode = "1";
			overview.setProduct(productCode);
			overview.setDivision(Division.T);
			when(productsService.getProductByKey(anyString(), eq(Division.T))).thenReturn(Optional.empty());

			Page<VOrderOverview> overviews = new PageImpl<>(List.of(overview), PageRequest.of(PAGE, SIZE), 1);
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenReturn(overviews);
			// when
			TTResponsePage responsePage = orderOverviewService.getOverviewResponsePage(PageRequest.of(PAGE, SIZE), new TTQueryObject());
			// then
			assertEquals("ERROR", ((TTBasicOrderOverview) responsePage.getContent().getFirst()).getProduct().getDescription());
		}

		@Test
		void shouldAddErrorEntryWhenMasterDataApiDownForFurtherAddresses() {
			// given
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderId(1L);
			overview.setCustomerNumber("0");
			overview.setOrderType(3);
			overview.setDivision(Division.T);
			Page<VOrderOverview> overviews = new PageImpl<>(List.of(overview), PageRequest.of(PAGE, SIZE), 1);
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenReturn(overviews);
			when(furtherAddressTypeService.getFurtherAddressTypes(anyInt())).thenThrow(ErrorIdExternalServiceNotAvailableException.class);
			// when
			TTResponsePage responsePage = orderOverviewService.getOverviewResponsePage(PageRequest.of(PAGE, SIZE), new TTQueryObject());
			// then
			assertEquals("ERROR", ((TTBasicOrderOverview) responsePage.getContent().getFirst()).getFurtherAddressTypes().get(0).getCode());
		}

		@Test
		void canGetDivisionForProductMappingFromOverview() {
			VOrderOverview overview = new VOrderOverview();
			String productCode = "1";
			overview.setOrderId(1L);
			overview.setOrderType(OrderType.ROADCOLLECTIONORDER.getId());
			overview.setProduct(productCode);
			overview.setDivision(Division.T);
			DeliveryProductDto expectedProduct = createDeliveryProduct();
			when(productsService.getProductByKey(overview.getProduct(), Division.T)).thenReturn(Optional.of(expectedProduct));
			// when
			orderOverviewService.mapProductName(overview);
			// then
			assertEquals(expectedProduct.getDescription(), overview.getProductName());
		}

		@Test
		void canGetDivisionForProductMappingFromUserContextService() {
			VOrderOverview overview = new VOrderOverview();
			String productCode = "1";
			overview.setOrderId(1L);
			overview.setOrderType(OrderType.SEAIMPORTORDER.getId());
			overview.setCustomerNumber(VALID_CUST_NO_ROAD);
			overview.setProduct(productCode);
			overview.setDivision(Division.F);
			String expectedProductName = null;
			DeliveryProductDto expectedProduct = new DeliveryProductDto().code("0").description(expectedProductName);
			when(productsService.getProductByKey(overview.getProduct(), Division.F)).thenReturn(Optional.of(expectedProduct));
			when(userContextService.getFirstFoundDivision(String.valueOf(CUSTOMER_ID), Segment.ROAD)).thenReturn(Division.F);

			// when
			orderOverviewService.mapProductName(overview);
			// then
			assertEquals(expectedProductName, overview.getProductName());
		}

		@Test
		void throwsOverviewDatabaseException() {
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenThrow(RuntimeException.class);

			final PageRequest pageRequest = PageRequest.of(PAGE, SIZE);
			TTQueryObject queryObject = new TTQueryObject();
			OrderOverviewDatabaseException exception = assertThrows(OrderOverviewDatabaseException.class,
					() -> orderOverviewService.getOverviewResponsePage(pageRequest, queryObject));

			assertThat(exception.getErrorId()).isEqualTo(BookErrorId.ERR_OV_02);
		}

		@Test
		void shouldRetryOverviewDatabaseQueryOnceAfterFailing() {
			when(repository.findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class))).thenThrow(OrderOverviewDatabaseException.class)
					.thenThrow(OrderOverviewDatabaseException.class);

			PageRequest pageRequest = PageRequest.of(PAGE, SIZE);
			TTQueryObject queryObject = new TTQueryObject();
			OrderOverviewDatabaseException exception = assertThrows(OrderOverviewDatabaseException.class,
					() -> orderOverviewService.getOverviewResponsePage(pageRequest, queryObject));

			assertThat(exception.getErrorId()).isEqualTo(BookErrorId.ERR_OV_02);
			verify(repository, times(2)).findAll(ArgumentMatchers.<Specification<VOrderOverview>>any(), any(Pageable.class));
		}

		@Test
		void shouldGetDocumentTypeCategoryTranslation() {
			VOrderOverview overview = new VOrderOverview();
			overview.setIncludedDocuments("1,2");

			when(cachedOrderOverviewService.loadDocumentTypeById(anyString())).thenReturn(Optional.of(createDocumentType()));
			when(translator.toLocale("category")).thenReturn("categoryTranslated");

			orderOverviewService.getDocumentCategoryTranslationByDocType(overview);

			assertEquals("categoryTranslated,categoryTranslated", overview.getIncludedDocuments());
		}

		@Test
		void shouldGetPartialDocumentTypeCategoryTranslations() {
			VOrderOverview overview = new VOrderOverview();
			overview.setIncludedDocuments("1,2");

			when(cachedOrderOverviewService.loadDocumentTypeById("1")).thenReturn(Optional.empty());
			when(cachedOrderOverviewService.loadDocumentTypeById("2")).thenReturn(Optional.of(createDocumentType()));
			when(translator.toLocale("category")).thenReturn("categoryTranslated");

			orderOverviewService.getDocumentCategoryTranslationByDocType(overview);

			assertEquals("categoryTranslated", overview.getIncludedDocuments());
		}

		@Test
		void shouldGetEmptyDocumentTypeCategoryTranslataions() {
			VOrderOverview overview = new VOrderOverview();
			overview.setIncludedDocuments("1,2");

			when(cachedOrderOverviewService.loadDocumentTypeById(anyString())).thenReturn(Optional.empty());

			orderOverviewService.getDocumentCategoryTranslationByDocType(overview);

			assertEquals("", overview.getIncludedDocuments());
		}
	}

	private DeliveryProductDto createDeliveryProduct() {
		String expectedProductName = null;
		return new DeliveryProductDto().code("0").description(expectedProductName);
	}

	private DocumentType createDocumentType() {
		DocumentType documentType = new DocumentType();
		documentType.setDocumentTypeId(1);
		documentType.setCategory("category");
		documentType.setLabel("label");
		return documentType;
	}

}