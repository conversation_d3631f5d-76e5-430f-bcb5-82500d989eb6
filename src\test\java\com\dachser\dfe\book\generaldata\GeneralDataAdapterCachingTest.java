package com.dachser.dfe.book.generaldata;

import com.dachser.dfe.book.UseCacheInTest;
import com.dachser.dfe.book.cache.Cache2KConfiguration;
import com.dachser.dfe.book.cache.CacheNames;
import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.generaldata.country.GeneralDataCountryMapper;
import com.dachser.dfe.book.generaldata.country.GeneralDataCountryMapperImpl;
import com.dachser.dfe.book.generaldata.packaging.PackagingOptionMapperImpl;
import com.dachser.dfe.book.generaldata.term.TermMapper;
import com.dachser.dfe.book.mapper.custom.TranslationMapper;
import com.dachser.dfe.book.user.DefaultBusinessDomain;
import com.dachser.dfe.generaldata.api.CountryApi;
import com.dachser.dfe.generaldata.api.DeliveryApi;
import com.dachser.dfe.generaldata.api.LanguageApi;
import com.dachser.dfe.generaldata.api.PackagingApi;
import com.dachser.dfe.generaldata.api.TermsApi;
import com.dachser.dfe.generaldata.model.GDCountryDto;
import com.dachser.dfe.generaldata.model.GDTermDto;
import com.dachser.dfe.generaldata.model.GDTermTypeDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cache.CacheManager;
import org.springframework.test.annotation.DirtiesContext;

import java.util.List;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@SpringBootTest(properties = "dfe.book.mock=false", classes = { GeneralDataAdapterExt.class, Cache2KConfiguration.class, GeneralDataCountryMapperImpl.class,
		DefaultBusinessDomain.class, PackagingOptionMapperImpl.class, DeliveryProductMapperImpl.class })
@UseCacheInTest
@DirtiesContext
class GeneralDataAdapterCachingTest {

	@Autowired
	GeneralDataAdapter generalDataAdapter;

	@MockBean
	private TermsApi termsApi;

	@MockBean
	private CountryApi countryApi;

	@MockBean
	private TermMapper termMapper;

	@MockBean
	private PackagingApi packagingApi;

	@MockBean
	private DeliveryApi deliveryApi;

	@MockBean
	private WhiteListFilterConfiguration whiteListFilterConfiguration;

	@MockBean
	private TranslationMapper translationMapper;

	@MockBean
	private LanguageApi languageApi;

	@Autowired
	private CacheManager cacheManager;

	@Autowired
	private GeneralDataCountryMapper countryMapper;

	@BeforeEach
	void setUp() {
		Objects.requireNonNull(cacheManager.getCache(CacheNames.GENERAL_DATA_TERMS)).invalidate();
	}

	@Test
	void shouldUseCacheForSameParameters() {
		List<GDTermDto> expectedTerm = List.of(new GDTermDto());
		when(termsApi.getTerms(any(GDTermTypeDto.class), anyBoolean())).thenReturn(expectedTerm);

		generalDataAdapter.getAllActiveTerms(GDTermTypeDto.FREIGHT);
		generalDataAdapter.getAllActiveTerms(GDTermTypeDto.FREIGHT);

		verify(termsApi, times(1)).getTerms(GDTermTypeDto.FREIGHT, false);
	}

	@Test
	void shouldUseCacheForNullParameters() {
		List<GDTermDto> expectedTerm = List.of(new GDTermDto());
		when(termsApi.getTerms(any(GDTermTypeDto.class), anyBoolean())).thenReturn(expectedTerm);

		generalDataAdapter.getAllActiveTerms(null);
		generalDataAdapter.getAllActiveTerms(null);

		verify(termsApi, times(1)).getTerms(null, false);
	}

	@Test
	void shouldUseCacheForCountries() {
		List<GDCountryDto> expectedTerm = List.of(new GDCountryDto());
		when(countryApi.getCountries("en")).thenReturn(expectedTerm);

		generalDataAdapter.getCountries("en");
		generalDataAdapter.getCountries("en");

		verify(countryApi, times(1)).getCountries("en");
	}
}