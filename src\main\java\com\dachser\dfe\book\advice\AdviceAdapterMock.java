package com.dachser.dfe.book.advice;

import com.dachser.dfe.book.exception.AdviceSubmitFailedException;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.dachser.dfe.book.advice.AdviceService.ADVICE_STATUS_LABEL_PENDING;
import static com.dachser.dfe.book.advice.AdviceService.ADVICE_STATUS_ORDER_COMPLETE;
import static com.dachser.dfe.book.advice.AdviceService.ADVICE_STATUS_ORDER_DELETED;

@Component
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "true")
class AdviceAdapterMock implements AdviceAdapter {

	/**
	 * @return true for valid legacyStatus
	 * false for invalid legacyStatus
	 * @throws AdviceSubmitFailedException if legacyStatus == null
	 */
	@Override
	public boolean submitAdvice(ForwardingOrder order, String legacyStatus) throws AdviceSubmitFailedException {
		if (legacyStatus != null) {
			return Objects.equals(legacyStatus, ADVICE_STATUS_ORDER_COMPLETE) || Objects.equals(legacyStatus, ADVICE_STATUS_LABEL_PENDING) || Objects.equals(legacyStatus,
					ADVICE_STATUS_ORDER_DELETED);
		} else {
			throw new AdviceSubmitFailedException("External service error", new Throwable());
		}
	}
}
