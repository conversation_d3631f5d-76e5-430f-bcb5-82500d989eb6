package com.dachser.dfe.book.dip.converter.road;

import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public interface RoadShipmentLineMapper<S, M, W, H, L, W2, V, L2> {

	String UNIT_CODE_GROSS = "GRO";
	String UNIT_KILOGRAM = "KGM";
	String UNIT_METER = "MTR";
	String UNIT_CUBICMETER = "MTQ";

	@Mapping(target = "packagesQuantity", source = "quantity")
	@Mapping(target = "packingType", source = "packagingType")
	@Mapping(target = "measurements", expression = "java(mapMeasurements(orderLine))")
	@Mapping(target = "goodsGroup", source = "goodsGroup")
	@Mapping(target = "goodsGroupQuantity", source = "goodsGroupQuantity")
	@Mapping(target = "goodsDescription", source = "content")
	@Mapping(target = "packagingAidsPosition", source = "packingPositionId", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "dangerousGoods", source = "dangerousGoods")
	S mapShipmentLine(RoadOrderLine orderLine);

	@Named("mapShipmentLines")
	default List<S> mapShipmentLines(RoadOrder order) {
		final List<RoadOrderLine> mappingSource = new ArrayList<>();
		List<RoadOrderLine> orderLines = order.getOrderLines();
		List<RoadOrderLine> packingPositionOrderLines = order.getPackingPositions().stream().flatMap(packingPosition -> packingPosition.getOrderLines().stream()).toList();

		if (!orderLines.isEmpty()) {
			mappingSource.addAll(orderLines);
		}

		if (!packingPositionOrderLines.isEmpty()) {
			mappingSource.addAll(packingPositionOrderLines);
		}

		return mappingSource.stream().map(this::mapShipmentLine).toList();
	}

	@Mapping(target = "loadingMeter", expression = "java(mapLoadingMeter(orderLine.getLoadingMeter()))")
	@Mapping(target = "weight", expression = "java(orderLine.getWeight() != null ? List.of(mapWeight(orderLine.getWeight())) : List.of())")
	@Mapping(target = "height", expression = "java(mapHeight(orderLine.getHeight()))")
	@Mapping(target = "length", expression = "java(mapLength(orderLine.getLength()))")
	@Mapping(target = "width", expression = "java(mapWidth(orderLine.getWidth()))")
	@Mapping(target = "volume", expression = "java(mapVolume(orderLine.getVolume()))")
	M mapMeasurements(RoadOrderLine orderLine);

	@Mapping(target = "code", constant = UNIT_CODE_GROSS)
	@Mapping(target = "measurement.unit", constant = UNIT_KILOGRAM)
	@Mapping(target = "measurement.value", source = "weight")
	W mapWeight(BigDecimal weight);

	@Mapping(target = "measurement.unit", constant = UNIT_METER)
	@Mapping(target = "measurement.value", expression = "java(((float) integer)/100)")
	H mapHeight(Integer integer);

	@Mapping(target = "measurement.unit", constant = UNIT_METER)
	@Mapping(target = "measurement.value", expression = "java(((float) integer)/100)")
	L mapLength(Integer integer);

	@Mapping(target = "measurement.unit", constant = UNIT_METER)
	@Mapping(target = "measurement.value", expression = "java(((float) integer)/100)")
	W2 mapWidth(Integer integer);

	@Mapping(target = "measurement.unit", constant = UNIT_CUBICMETER)
	@Mapping(target = "measurement.value", source = "volume")
	V mapVolume(BigDecimal volume);

	@Mapping(target = "measurement.unit", constant = UNIT_METER)
	@Mapping(target = "measurement.value", source = "loadingMeter")
	L2 mapLoadingMeter(BigDecimal loadingMeter);

}
