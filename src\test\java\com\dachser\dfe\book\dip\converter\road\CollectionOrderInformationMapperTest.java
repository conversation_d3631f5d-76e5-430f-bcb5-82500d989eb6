package com.dachser.dfe.book.dip.converter.road;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionDangerousGoodsMapperImpl;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionDocumentHeaderMapperImpl;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionOrderInformationMapper;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionOrderInformationMapperImpl;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionOrderOriginalTermMapper;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionPackingPositionMapperImpl;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionShipmentAddressMapperImpl;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionShipmentLineMapperImpl;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionTransportMapper;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionTransportMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.DateMapperImpl;
import com.dachser.dfe.book.model.TestMockData;
import com.dachser.dfe.book.model.jaxb.order.road.collection.AdditionalReference;
import com.dachser.dfe.book.model.jaxb.order.road.collection.AddressInformation;
import com.dachser.dfe.book.model.jaxb.order.road.collection.CollectionInstruction;
import com.dachser.dfe.book.model.jaxb.order.road.collection.CollectionOrderInformation;
import com.dachser.dfe.book.model.jaxb.order.road.collection.DocumentHeader;
import com.dachser.dfe.book.model.jaxb.order.road.collection.GoodsValue;
import com.dachser.dfe.book.model.jaxb.order.road.collection.PartnerInformation;
import com.dachser.dfe.book.model.jaxb.order.road.collection.ShipmentAddress;
import com.dachser.dfe.book.model.jaxb.order.road.collection.ShipmentHeader;
import com.dachser.dfe.book.model.jaxb.order.road.collection.ShipmentLine;
import com.dachser.dfe.book.model.jaxb.order.road.collection.Transport;
import com.dachser.dfe.book.model.options.CollectionOptions;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderReferenceHelper;
import com.dachser.dfe.book.order.address.AddressTestHelper;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderText;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.order.reference.ReferenceTestHelper;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Named;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Stream;

import static com.dachser.dfe.book.TestUtil.collectionDifferentConsigneeAddress;
import static com.dachser.dfe.book.TestUtil.collectionShipperAddress;
import static com.dachser.dfe.book.model.Constants.RoadMasterDataService.INPUT_STRING_NO;
import static com.dachser.dfe.book.model.Constants.RoadMasterDataService.INPUT_STRING_YES;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = { CollectionOrderInformationMapperImpl.class, CollectionTransportMapperImpl.class, DateMapperImpl.class, CollectionDocumentHeaderMapperImpl.class,
		CollectionShipmentLineMapperImpl.class, CollectionShipmentAddressMapperImpl.class, GeneralTransportDataMapperImpl.class, CollectionPackingPositionMapperImpl.class,
		CollectionDangerousGoodsMapperImpl.class })
@Slf4j
public class CollectionOrderInformationMapperTest {

	@Autowired
	private CollectionOrderInformationMapper collectionOrderInformationMapper;

	@Autowired
	private CollectionTransportMapperImpl collectionTransportMapper;

	@MockBean
	CollectionOrderOriginalTermMapper collectionOrderOriginalTermMapper;

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	@Nested
	class MapCollectionOrder {

		final CollectionOrder collectionOrder = testUtil.generateCollectionOrder();

		@BeforeEach
		void setIDs() {
			for (PackingPosition packingPosition : collectionOrder.getPackingPositions()) {
				packingPosition.setId(new Random().nextLong(3));
				packingPosition.getOrderLines().forEach(orderLines -> orderLines.setPackingPositionId(packingPosition.getId()));
			}
		}

		@Nested
		class ToJaxb {

			@Test
			void shouldMapRequiredFields() {
				collectionOrder.setShipmentNumber(12700L);
				final CollectionOrderInformation collectionOrderInformation = collectionOrderInformationMapper.map(collectionOrder);

				final DocumentHeader documentHeader = collectionOrderInformation.getDocumentHeader();
				assertNotNull(documentHeader);
				assertDocumentHeader(documentHeader);

				final Transport transport = collectionOrderInformation.getTransport().get(0);

				final List<ShipmentHeader> shipmentHeaderList = transport.getShipmentHeader();
				assertFalse(shipmentHeaderList.isEmpty());
				final ShipmentHeader shipmentHeader = shipmentHeaderList.get(0);
				assertNotNull(shipmentHeader);
				assertShipmentHeader(shipmentHeader);

				final List<ShipmentAddress> shipmentAddressList = shipmentHeader.getShipmentAddress();
				assertFalse(shipmentAddressList.isEmpty());
				// minimum 3 required addresses
				assertTrue(shipmentAddressList.size() >= 3);
				// SHIPPER - required
				assertNotNull(shipmentAddressList.get(0));
				assertValidShipperAddress(shipmentAddressList.get(0));
				// CONSIGNEE - required
				assertNotNull(shipmentAddressList.get(1));
				assertValidConsigneeAddress(shipmentAddressList.get(1));
				// FORWARDER - required
				assertNotNull(shipmentAddressList.get(2));
				assertValidForwarderAddress(shipmentAddressList.get(2));

				final ShipmentLine shipmentLine = shipmentHeader.getShipmentLine().get(0);
				// minimum 1 required shipment line
				assertNotNull(shipmentLine);
				assertShipmentLine(shipmentLine);

				// Order number not in additional reference
				assertEquals("55667788", shipmentHeader.getCustomerShipmentReference());
				assertFalse(shipmentHeader.getAdditionalReference().stream().anyMatch(ar -> ReferenceType.ORDER_NUMBER.getCoreSystemsValue().equals(ar.getCode())));
			}

			@Test
			void shouldBeNullSafe() {
				final CollectionOrderInformation pojo = collectionOrderInformationMapper.map(null);
				assertNull(pojo);
			}

			@Test
			void mapNotSetDates() {
				collectionOrder.setShipmentNumber(12700L);
				collectionOrder.setSendAt(null);
				collectionOrder.setFixDate(null);
				final CollectionOrderInformation collectionOrderInformation = collectionOrderInformationMapper.map(collectionOrder);

				final DocumentHeader documentHeader = collectionOrderInformation.getDocumentHeader();
				assertNotNull(documentHeader);
			}

			@Test
			void mapNotSetAddresses() {
				collectionOrder.setShipmentNumber(12700L);
				collectionOrder.setConsigneeAddress(null);
				collectionOrder.setShipperAddress(null);
				collectionOrder.setAddresses(null);
				final CollectionOrderInformation collectionOrderInformation = collectionOrderInformationMapper.map(collectionOrder);

				final DocumentHeader documentHeader = collectionOrderInformation.getDocumentHeader();
				assertNotNull(documentHeader);
			}

			@Test
			void mapNotSetAddresseName2() {
				collectionOrder.setShipmentNumber(12700L);
				collectionOrder.setConsigneeAddress(null);
				final OrderAddress shipperAddress = new OrderAddress();
				shipperAddress.setName("name1");
				collectionOrder.setShipperAddress(shipperAddress);
				collectionOrder.setAddresses(null);
				final CollectionOrderInformation collectionOrderInformation = collectionOrderInformationMapper.map(collectionOrder);

				final DocumentHeader documentHeader = collectionOrderInformation.getDocumentHeader();
				assertNotNull(documentHeader);
			}

			@Test
			void mapTexts() {
				collectionOrder.setShipmentNumber(12700L);

				collectionOrder.setOrderTexts(new ArrayList<>());

				OrderText information = new OrderText();
				information.setOrderTextId(1L);
				information.setTextType("SI");
				information.setText("Sonstige Informationen");

				OrderText deliveryInstruction = new OrderText();
				deliveryInstruction.setOrderTextId(1L);
				deliveryInstruction.setTextType("ZU");
				deliveryInstruction.setText("Zustellhinweise");

				OrderText invoice = new OrderText();
				invoice.setOrderTextId(1L);
				invoice.setTextType("RE");
				invoice.setText("Rechnung");

				OrderText collectionInstructions = new OrderText();
				collectionInstructions.setOrderTextId(1L);
				collectionInstructions.setTextType("A");
				collectionInstructions.setText("Sammelanweisung");

				collectionOrder.getOrderTexts().add(information);
				collectionOrder.getOrderTexts().add(deliveryInstruction);
				collectionOrder.getOrderTexts().add(invoice);

				final CollectionOrderInformation collectionOrderInformation = collectionOrderInformationMapper.map(collectionOrder);

				final DocumentHeader documentHeader = collectionOrderInformation.getDocumentHeader();
				assertNotNull(documentHeader);
				assertNotNull(collectionOrderInformation.getTransport().get(0).getShipmentHeader().get(0).getShipmentText());

				assertEquals(8, collectionOrderInformation.getTransport().get(0).getShipmentHeader().get(0).getShipmentText().size());
			}

			@Test
			void shouldSetNeutralizeTrueForShipperAddress() {
				collectionOrder.setShipmentNumber(12700L);
				collectionOrder.setShipperAddress(collectionShipperAddress());

				final Transport collectionTransport = collectionTransportMapper.map(collectionOrder);

				assertEquals(INPUT_STRING_YES, collectionTransport.getShipmentHeader().get(0).getNeutralizeCollection());
			}

			@Test
			void shouldSetNeutralizeFalseForDifferentConsigneeAddress() {
				collectionOrder.setShipmentNumber(12700L);

				final Transport collectionTransport = collectionTransportMapper.map(collectionOrder);

				assertEquals(INPUT_STRING_NO, collectionTransport.getShipmentHeader().get(0).getNeutralizeForwarding());
			}

			@Test
			void shouldCollectionInstruction() {
				// given a road collection order
				collectionOrder.setShipmentNumber(12700L);
				// having a collection instruction
				assertCollectionInstruction(collectionOrder);
				// when mapping it as a Domino transport
				final Transport collectionTransport = collectionTransportMapper.map(collectionOrder);
				// then a shipment header is present
				ShipmentHeader header = assertShipmentHeader(collectionTransport);
				// and it has collection instruction
				CollectionInstruction collectionInstruction = header.getCollectionInstruction();
				assertNotNull(collectionInstruction);
				assertEquals("A", collectionInstruction.getInstructionType());
				// that was potentially split into multiple chunks (due to storage limitations)
				assertFalse(collectionInstruction.getTextInstruction().isEmpty());
			}

			@Test
			void shouldMapDifferentConsignorAddress() {
				// given a road collection order
				// and principal is not the shipper
				collectionOrder.setShipperAddress(collectionShipperAddress());
				log.info("principal = {}", AddressTestHelper.stringify(collectionOrder.getPrincipalAddress()));
				log.info("shipper = {}", AddressTestHelper.stringify(collectionOrder.getShipperAddress()));
				assertFalse(AddressTestHelper.is(collectionOrder.getPrincipalAddress(), collectionOrder.getShipperAddress()));
				// when mapping order for DIP
				final CollectionOrderInformation dipOrder = collectionOrderInformationMapper.map(collectionOrder);
				ShipmentHeader header = assertShipmentHeader(assertTransport(dipOrder));
				// then shipper is written as PW address type with all fields
				ShipmentAddress consignorAddress = assertShipmentAddress(header, "PW");
				assertShipmentAddress(consignorAddress);
				log.info("DIP consignor (PW) is {}", AddressTestHelper.stringify(consignorAddress));
				assertPartnerInformationFields(consignorAddress.getPartnerInformation());
				assertEquals("Example GmbH _shipper", consignorAddress.getPartnerInformation().getPartnerName().get(0));
				// and principal is written as CZ address type with only customer number field
				ShipmentAddress principalAddress = assertShipmentAddress(header, "CZ");
				assertShipmentAddress(principalAddress);
				assertPartnerInformationId(principalAddress.getPartnerInformation());
				log.info("DIP principal (CZ) is {}", AddressTestHelper.stringify(principalAddress));
			}

			@Test
			void shouldMapDifferentConsigneeAddress() {
				// given a road collection order
				// and principal is not the consignee (specific one provided)
				collectionOrder.setDifferentConsigneeAddress(collectionDifferentConsigneeAddress());
				log.info("principal = {}", AddressTestHelper.stringify(collectionOrder.getPrincipalAddress()));
				log.info("different consignee = {}", AddressTestHelper.stringify(collectionOrder.getDifferentConsigneeAddress()));
				assertFalse(AddressTestHelper.is(collectionOrder.getPrincipalAddress(), collectionOrder.getDifferentConsigneeAddress()));
				// when mapping order for DIP
				final CollectionOrderInformation dipOrder = collectionOrderInformationMapper.map(collectionOrder);
				ShipmentHeader header = assertShipmentHeader(assertTransport(dipOrder));
				// then specific consignee is written as CN address type with all fields
				ShipmentAddress consigneeAddress = assertShipmentAddress(header, "CN");
				assertShipmentAddress(consigneeAddress);
				log.info("DIP consignee (CN) is {}", AddressTestHelper.stringify(consigneeAddress));
				assertPartnerInformationFields(consigneeAddress.getPartnerInformation());
				assertEquals("Example GmbH _different_consignee", consigneeAddress.getPartnerInformation().getPartnerName().get(0));
			}

			@ParameterizedTest(name = "#{index} - order having reference of type {0} maps to DIP")
			@MethodSource("roadReferences")
			void shouldMapReferences(RoadOrderReference roadOrderReference, String expectedReferenceValue) {
				// given a road order
				// having a specific reference
				collectionOrder.getOrderReferences().clear();
				collectionOrder.getOrderReferences().add(roadOrderReference);
				// when mapping order for DIP
				final CollectionOrderInformation dipOrder = collectionOrderInformationMapper.map(collectionOrder);
				ShipmentHeader header = assertShipmentHeader(assertTransport(dipOrder));
				// then DIP order has references
				List<AdditionalReference> dipReferences = assertAdditionalReferences(header.getAdditionalReference());
				log.debug("DIP references are {}", ReferenceTestHelper.stringify(dipReferences));
				// and DIP order has the specific reference with type being a Dachser code
				Optional<AdditionalReference> dipReference = dipReferences.stream().filter(e -> roadOrderReference.getReferenceType().getCoreSystemsValue().equals(e.getCode()))
						.findAny();
				assertTrue(dipReference.isPresent());
				// and DIP order has the specific reference value as expected
				assertEquals(Optional.ofNullable(expectedReferenceValue).orElse(roadOrderReference.getReference()), dipReference.get().getReference());
			}

			private static Stream<Arguments> roadReferences() {
				// @formatter:off
				return Stream.of(
					Arguments.of(Named.of(ReferenceType.PURCHASE_ORDER_NUMBER.name(), ReferenceTestHelper.roadOrderReference(ReferenceType.PURCHASE_ORDER_NUMBER, null, "my 1st purchase")), null),
					Arguments.of(Named.of(ReferenceType.BOOKING_REFERENCE.name(), ReferenceTestHelper.roadOrderReference(ReferenceType.BOOKING_REFERENCE, null, "Amazon: 1st booking")), null),
					Arguments.of(
						Named.of(
							RoadOrderReferenceSubtype.UIT.name(),
							ReferenceTestHelper.roadOrderReference(
								ReferenceType.IDENTIFICATION_CODE_TRANSPORT,
								RoadOrderReferenceSubtype.UIT,
								TestMockData.UIT_REFERENCE_VALUE)
						),
						OrderReferenceHelper.prefix(RoadOrderReferenceSubtype.UIT).concat(StringUtils.SPACE).concat(TestMockData.UIT_REFERENCE_VALUE)
					)
				);
				// @formatter:on
			}

			@Test
			void shouldMapShipmentTextsWithFullContact() {
				collectionOrder.setCollectionOption(CollectionOptions.BOOKING);
				collectionOrder.setShipperAddress(collectionShipperAddress());

				final CollectionOrderInformation collectionOrderInformation = collectionOrderInformationMapper.map(collectionOrder);

				final ShipmentHeader shipmentHeader = collectionOrderInformation.getTransport().get(0).getShipmentHeader().get(0);
				assertFalse(shipmentHeader.getShipmentText().isEmpty());
				assertEquals("Consignora_shipper", shipmentHeader.getShipmentText().get(4).getText().get(0));
			}

			@Test
			void shouldMapShipmentTextsWithMissingTel() {
				collectionOrder.setCollectionOption(CollectionOptions.BOOKING);
				collectionOrder.setShipperAddress(collectionShipperAddress());
				collectionOrder.getShipperAddress().getOrderContact().setTelephone(null);

				final CollectionOrderInformation collectionOrderInformation = collectionOrderInformationMapper.map(collectionOrder);

				final ShipmentHeader shipmentHeader = collectionOrderInformation.getTransport().get(0).getShipmentHeader().get(0);
				assertFalse(shipmentHeader.getShipmentText().isEmpty());
				assertEquals("Consignora_shipper", shipmentHeader.getShipmentText().get(4).getText().get(0));
				assertNull(shipmentHeader.getShipmentAddress().get(4).getPartnerInformation().getContactInformation().get(0).getContactPhoneNumber());
			}

			@Test
			void shouldMapShipmentTextsWithMissingEmail() {
				collectionOrder.setCollectionOption(CollectionOptions.BOOKING);
				collectionOrder.setShipperAddress(collectionShipperAddress());
				collectionOrder.getShipperAddress().getOrderContact().setEmail(null);

				final CollectionOrderInformation collectionOrderInformation = collectionOrderInformationMapper.map(collectionOrder);

				final ShipmentHeader shipmentHeader = collectionOrderInformation.getTransport().get(0).getShipmentHeader().get(0);
				assertFalse(shipmentHeader.getShipmentText().isEmpty());
				assertEquals("Consignora_shipper", shipmentHeader.getShipmentText().get(4).getText().get(0));
			}

			@Test
			void shouldMapContactDataForCZ() {
				final CollectionOrderInformation collectionOrderInformation = collectionOrderInformationMapper.map(collectionOrder);

				final ShipmentAddress principalAddress = collectionOrderInformation.getTransport().get(0).getShipmentHeader().get(0).getShipmentAddress().get(0);
				assertNotNull(principalAddress);
				assertEquals(collectionOrder.getConsigneeAddress().getOrderContact().getName(),
						principalAddress.getPartnerInformation().getContactInformation().get(0).getContactName());

			}
		}
	}

	private void assertPartnerInformation(ShipmentAddress shipmentAddress, String addressType) {
		assertNotNull(shipmentAddress.getAddressType());
		assertEquals(addressType, shipmentAddress.getAddressType());

		final PartnerInformation partnerInformation = shipmentAddress.getPartnerInformation();
		assertNotNull(partnerInformation);
	}

	private void assertValidForwarderAddress(ShipmentAddress shipmentAddress) {
		assertPartnerInformation(shipmentAddress, CollectionTransportMapper.ADDRESS_TYPE_FORWARDER);
	}

	private void assertValidShipperAddress(ShipmentAddress shipmentAddress) {
		assertPartnerInformation(shipmentAddress, CollectionTransportMapper.ADDRESS_TYPE_SHIPPER);
		assertEquals("99999993", shipmentAddress.getPartnerInformation().getPartnerID());
	}

	private void assertValidConsigneeAddress(ShipmentAddress shipmentAddress) {
		assertPartnerInformation(shipmentAddress, CollectionTransportMapper.ADDRESS_TYPE_CONSIGNEE);
		assertShipmentAddress(shipmentAddress, "_consignee");
	}

	private void assertDocumentHeader(DocumentHeader documentHeader) {
		assertEquals("0", documentHeader.getDocumentID());
		assertEquals("4023083000008", documentHeader.getEDIReceiver().getPartnerInformation().getPartnerGLN().toString());
		assertEquals("99999993", documentHeader.getEDISender().getPartnerInformation().getPartnerID());
		assertEquals("1", documentHeader.getTestFlag());
	}

	private Transport assertTransport(CollectionOrderInformation dipOrder) {
		assertNotNull(dipOrder);
		assertNotNull(dipOrder.getTransport());
		assertFalse(dipOrder.getTransport().isEmpty());
		assertFalse(dipOrder.getTransport().stream().anyMatch(Objects::isNull));
		assertEquals(1, dipOrder.getTransport().size());
		return dipOrder.getTransport().get(0);
	}

	private ShipmentHeader assertShipmentHeader(Transport transport) {
		assertNotNull(transport);
		assertNotNull(transport.getShipmentHeader());
		assertFalse(transport.getShipmentHeader().isEmpty());
		assertFalse(transport.getShipmentHeader().stream().anyMatch(Objects::isNull));
		return transport.getShipmentHeader().get(0);
	}

	private void assertShipmentHeader(ShipmentHeader shipmentHeader) {
		assertEquals("55667788", shipmentHeader.getCustomerShipmentReference());

		final List<AdditionalReference> additionalReference = shipmentHeader.getAdditionalReference();
		assertFalse(additionalReference.isEmpty());
		assertEquals(ReferenceType.PURCHASE_ORDER_NUMBER.getCoreSystemsValue(), additionalReference.get(0).getCode());
		assertEquals("47110815", additionalReference.get(0).getReference());

		assertEquals("N", shipmentHeader.getADRflag());
		assertEquals("Y", shipmentHeader.getCustomsIndicator());
		assertEquals("Z", shipmentHeader.getDachserProduct());
		assertNotNull(shipmentHeader.getDeliveryDateFixed().getDate());
		assertEquals("T", shipmentHeader.getDivision());

		final GoodsValue goodsValue = shipmentHeader.getGoodsValue();
		assertEquals(99f, goodsValue.getAmount());
		assertEquals("EUR", goodsValue.getCurrency());

		assertEquals("A", shipmentHeader.getOrderGroup());

		assertNotNull(shipmentHeader.getShipmentText().get(0));
		assertEquals("ZU", shipmentHeader.getShipmentText().get(0).getTextType());
		assertEquals("Ein Zustelltext", shipmentHeader.getShipmentText().get(0).getText().get(0));

	}

	private ShipmentAddress assertShipmentAddress(ShipmentHeader header, String type) {
		assertFalse(StringUtils.isBlank(type));
		assertNotNull(header);
		assertNotNull(header.getShipmentAddress());
		assertFalse(header.getShipmentAddress().isEmpty());
		assertFalse(header.getShipmentAddress().stream().anyMatch(Objects::isNull));
		Optional<ShipmentAddress> address = header.getShipmentAddress().stream().filter(e -> type.equals(e.getAddressType())).findAny();
		assertNotNull(address);
		assertTrue(address.isPresent());
		return address.get();
	}

	private void assertShipmentAddress(ShipmentAddress shipmentAddress) {
		assertNotNull(shipmentAddress);
		assertNotNull(shipmentAddress.getPartnerInformation());
	}

	private void assertPartnerInformationId(PartnerInformation partner) {
		assertNotNull(partner);
		assertFalse(StringUtils.isBlank(partner.getPartnerID()));
	}

	private void assertPartnerInformationFields(PartnerInformation partner) {
		assertNotNull(partner);
		assertNotNull(partner.getPartnerName());
		assertFalse(partner.getPartnerName().isEmpty());
		assertFalse(partner.getPartnerName().stream().allMatch(StringUtils::isBlank));
		assertFalse(StringUtils.isBlank(partner.getPartnerName().get(0)));
	}

	private void assertShipmentAddress(ShipmentAddress shipmentAddress, String suffix) {
		final AddressInformation addressInformation = shipmentAddress.getPartnerInformation().getAddressInformation();
		assertNotNull(addressInformation);

		assertEquals("Newtown", addressInformation.getCity());
		assertEquals("DE", addressInformation.getCountryCode());
		assertEquals("88888", addressInformation.getPostalCode());
		assertEquals("Con Street 11", addressInformation.getStreet());
		assertEquals("supplement" + suffix, addressInformation.getSupplementInformation());

		assertEquals("9012345000004", shipmentAddress.getPartnerInformation().getPartnerGLN().toString());

		assertEquals("AP", shipmentAddress.getServiceContactType());
	}

	private void assertShipmentLine(ShipmentLine shipmentLine) {
		assertEquals("CAR SPARE", shipmentLine.getGoodsDescription());
		assertEquals("01", shipmentLine.getGoodsGroup());
		assertEquals(BigInteger.valueOf(3), shipmentLine.getGoodsGroupQuantity());

		assertEquals(50.0, shipmentLine.getMeasurements().getWeight().get(0).getMeasurement().getValue());
		assertEquals(2.40, shipmentLine.getMeasurements().getHeight().getMeasurement().getValue(), 0.001);
		assertEquals(3.60, shipmentLine.getMeasurements().getLoadingMeter().getMeasurement().getValue(), 0.01);
		assertEquals(1.20, shipmentLine.getMeasurements().getWidth().getMeasurement().getValue(), 0.001);
		assertEquals(2.60, shipmentLine.getMeasurements().getLength().getMeasurement().getValue(), 0.001);
		assertEquals(2.00, shipmentLine.getMeasurements().getVolume().getMeasurement().getValue(), 0.001);

		assertEquals(BigInteger.valueOf(2), shipmentLine.getPackagesQuantity());
		assertEquals("EU", shipmentLine.getPackingType());
	}

	private List<AdditionalReference> assertAdditionalReferences(List<AdditionalReference> references) {
		assertNotNull(references);
		assertFalse(references.isEmpty());
		assertFalse(references.stream().anyMatch(Objects::isNull));
		return references;
	}

	private void assertTexts(Order order) {
		assertNotNull(order);
		assertNotNull(order.getOrderTexts());
		assertFalse(order.getOrderTexts().isEmpty());
		assertFalse(order.getOrderTexts().stream().anyMatch(Objects::isNull));
	}

	private void assertCollectionInstruction(Order order) {
		assertTexts(order);
		assertTrue(order.getOrderTexts().stream().map(OrderText::getTextType).anyMatch("A"::equals));
	}

}
