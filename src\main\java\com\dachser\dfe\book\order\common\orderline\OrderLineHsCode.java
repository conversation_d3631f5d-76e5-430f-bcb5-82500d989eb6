package com.dachser.dfe.book.order.common.orderline;

import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import lombok.Data;

@Data
@MappedSuperclass
public abstract class OrderLineHsCode {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long orderLineHsCodeId;

	private String hsCode;

	public abstract String getGoods();
}
