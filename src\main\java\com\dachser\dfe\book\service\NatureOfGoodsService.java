package com.dachser.dfe.book.service;

import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.order.air.AirOrderLineHsCode;
import com.dachser.dfe.book.order.common.orderline.AirOrderLineHsCodeRepository;
import com.dachser.dfe.book.order.common.orderline.RoadOrderLineRepository;
import com.dachser.dfe.book.order.common.orderline.SeaOrderLineHsCodeRepository;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.sea.SeaOrderLineHsCode;
import com.dachser.dfe.book.user.UserContextService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NatureOfGoodsService {

	private static final int LAST_USED_NATURE_OF_GOODS_LIMIT = 5;

	private final RoadOrderLineRepository roadOrderLineRepository;

	private final AirOrderLineHsCodeRepository airOrderLineHsCodeRepository;

	private final SeaOrderLineHsCodeRepository seaOrderLineHsCodeRepository;

	private final UserContextService userContextService;

	public List<String> getLastUsedNatureOfGoods(Segment segment) {
		String currentUserId = userContextService.getCurrentUserId();

		// Fetch 10 entries to account for duplicates and ensure at least 5 unique items.
		// Note: Distinct filtering cannot be applied via JPA derived queries.
		// @formatter:off
		return switch (segment) {
			case ROAD -> extractDistinctLastUsedGoods(roadOrderLineRepository.findTop10ByOrder_CreatorAndContentIsNotNullOrderByOrderLineIdDesc(currentUserId),
					RoadOrderLine::getContent,
					RoadOrderLine::getOrderLineId);
			case SEA -> extractDistinctLastUsedGoods(
					seaOrderLineHsCodeRepository.findTop10ByOrderLine_Order_CreatorAndGoodsIsNotNullOrderByOrderLineHsCodeIdDesc(currentUserId),
					SeaOrderLineHsCode::getGoods,
					SeaOrderLineHsCode::getOrderLineHsCodeId);
			case AIR -> extractDistinctLastUsedGoods(
					airOrderLineHsCodeRepository.findTop10ByOrderLine_Order_CreatorAndGoodsIsNotNullOrderByOrderLineHsCodeIdDesc(currentUserId),
					AirOrderLineHsCode::getGoods,
					AirOrderLineHsCode::getOrderLineHsCodeId);
		};
		// @formatter:on
	}

	private <T> List<String> extractDistinctLastUsedGoods(List<T> orderLines, Function<T, String> goodsExtractor, Function<T, Long> idExtractor) {
		// @formatter:off
		return orderLines.stream()
				.collect(Collectors.toMap(goodsExtractor, Function.identity(), (existing, replacement) -> existing))
				.values()
				.stream()
				.sorted(Comparator.comparing(idExtractor).reversed())
				.limit(LAST_USED_NATURE_OF_GOODS_LIMIT)
				.map(goodsExtractor)
				.toList();
		// @formatter:on

	}
}
