package com.dachser.dfe.book.dangerousgoods;

import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.masterdata.thirdparty.model.MDTCalculationPoint;
import jakarta.annotation.Nonnull;

import java.util.List;

public interface DangerousGoodsThirdPartyAdapter {

	@Nonnull
	List<DangerousGoodDataItemDto> searchUnNumbersRoad(@Nonnull String unNumber, @Nonnull String countryCode);

	@Nonnull
	List<DangerousGoodDataItemDto> fetchLocalizedInformationForDgmIdsRoad(@Nonnull List<String> dgmIds, @Nonnull String countryCode);

	@Nonnull
	List<DangerousGoodsTransferListService.DgmIdAndQuantity> calculatePointsForSingleValues(@Nonnull String countryCode, @Nonnull List<MDTCalculationPoint> points);
}
