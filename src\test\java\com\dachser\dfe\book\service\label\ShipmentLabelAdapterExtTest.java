package com.dachser.dfe.book.service.label;

import com.dachser.bi.common.print.service.pri.api.label.LabelFormat;
import com.dachser.bi.common.print.service.pri.api.label.bean.ShipmentLabelBean;
import com.dachser.bi.common.print.service.pri.api.label.bean.ShipmentV2Bean;
import com.dachser.bi.common.print.service.pri.api.label.service.ShipmentLabelV2Service;
import com.dachser.dfe.book.DFEBookApplication;
import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.exception.ExtRelationLabelPrintingServiceNotAvailable;
import com.dachser.dfe.book.exception.LabelsCouldNotBeCreatedException;
import com.dachser.dfe.book.mapper.order.ShipmentPrintBeanMapper;
import com.dachser.dfe.book.masterdata.road.RoadMasterDataApiWrapper;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.service.RelationLabelPrintingService;
import com.dachser.dfe.book.service.label.shipment.ShipmentLabelAdapterExt;
import com.dachser.dfe.book.user.BusinessDomainProvider;
import com.dachser.dfe.road.masterdata.model.RMDBranchDataDTO;
import com.dachser.dfe.road.masterdata.model.RMDRelationLabelprintingDTO;
import com.dachser.framework.technical.ws.validation.DachValidationResult;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Answers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Locale;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ShipmentLabelAdapterExtTest {

	@Nested
	class UnitTest {

		@InjectMocks
		ShipmentLabelAdapterExt serviceToBeTested;

		@Mock
		BusinessDomainProvider businessDomainProvider;

		@Mock
		ShipmentPrintBeanMapper shipmentPrintBeanMapper;

		@Mock(answer = Answers.RETURNS_DEEP_STUBS)
		ShipmentV2Bean shipmentPrintBean;

		@Mock
		ShipmentLabelV2Service shipmentLabelService;

		@Mock
		DachValidationResult dachValidationResult;

		@Mock
		CountryService countryService;

		@Mock
		RelationLabelPrintingService relationLabelPrintingService;

		@Mock
		RoadMasterDataApiWrapper roadMasterdataApiWrapper;

		private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

		private static final int BUSINESS_DOMAIN = 1;

		void setUpMocks() {
			when(businessDomainProvider.getBusinessDomain()).thenReturn(BUSINESS_DOMAIN);
			when(shipmentPrintBeanMapper.mapForwardingOrder(any(ForwardingOrder.class))).thenReturn(shipmentPrintBean);
			when(shipmentPrintBean.getConsignee().getAddressInformation().getCountryDachserCode()).thenReturn("foo");
			when(countryService.mapToDachserCountryCode(any())).thenReturn("D");
			when(relationLabelPrintingService.retrieveRelation(any())).thenReturn(new RMDRelationLabelprintingDTO());
			when(roadMasterdataApiWrapper.getBranchData(anyInt(), anyInt())).thenReturn(new RMDBranchDataDTO());
		}

		@Test
		void shouldThrowLabelsCouldNotBeCreatedExceptionExceptionForNullResponse() {
			setUpMocks();
			ReflectionTestUtils.setField(serviceToBeTested, "generateTrialLabel", false);
			when(shipmentLabelService.getDachserLabel(anyInt(), any(LabelFormat.class), any(ShipmentV2Bean.class), any(), any(), any(Locale.class))).thenReturn(
					new ShipmentLabelBean(null));
			final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();
			LabelsCouldNotBeCreatedException labelsCouldNotBeCreatedException = assertThrows(LabelsCouldNotBeCreatedException.class,
					() -> serviceToBeTested.getPrintLabelsForForwardingOrder(forwardingOrder, BUSINESS_DOMAIN));
			assertThat(labelsCouldNotBeCreatedException.getMessage()).isEqualTo("Service response is null");

		}

		@Test
		void shouldThrowLabelsCouldNotBeCreatedExceptionExceptionForNullResponseAndTrial() {
			setUpMocks();
			ReflectionTestUtils.setField(serviceToBeTested, "generateTrialLabel", true);
			when(shipmentLabelService.getDachserLabelTrial(anyInt(), any(LabelFormat.class), any(ShipmentV2Bean.class), any(), any(),
					any(Locale.class))).thenReturn(new ShipmentLabelBean(null));
			final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();
			LabelsCouldNotBeCreatedException labelsCouldNotBeCreatedException = assertThrows(LabelsCouldNotBeCreatedException.class,
					() -> serviceToBeTested.getPrintLabelsForForwardingOrder(forwardingOrder, BUSINESS_DOMAIN));
			assertThat(labelsCouldNotBeCreatedException.getMessage()).isEqualTo("Service response is null");

		}

		@Test
		void shouldThrowLabelsCouldNotBeCreatedExceptionExceptionForFailedServiceValidationResponse() {
			setUpMocks();
			ShipmentLabelBean serviceResponse = new ShipmentLabelBean(null);
			serviceResponse.setResultSet(List.of(dachValidationResult));
			ReflectionTestUtils.setField(serviceToBeTested, "generateTrialLabel", false);
			when(dachValidationResult.getMessage()).thenReturn("mock-validation fail");
			when(shipmentLabelService.getDachserLabel(anyInt(), any(LabelFormat.class), any(ShipmentV2Bean.class), any(), any(), any(Locale.class))).thenReturn(
					serviceResponse);
			final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();
			LabelsCouldNotBeCreatedException labelsCouldNotBeCreatedException = assertThrows(LabelsCouldNotBeCreatedException.class,
					() -> serviceToBeTested.getPrintLabelsForForwardingOrder(forwardingOrder, BUSINESS_DOMAIN));
			assertThat(labelsCouldNotBeCreatedException.getMessage()).isEqualTo("Service validation errors [mock-validation fail]");

		}

		@Test
		void shouldThrowLabelsCouldNotBeCreatedExceptionExceptionForNullFailedServiceValidationAndTrial() {
			setUpMocks();
			ShipmentLabelBean serviceResponse = new ShipmentLabelBean(null);
			serviceResponse.setResultSet(List.of(dachValidationResult));
			ReflectionTestUtils.setField(serviceToBeTested, "generateTrialLabel", true);
			when(dachValidationResult.getMessage()).thenReturn("mock-validation fail");
			when(shipmentLabelService.getDachserLabelTrial(anyInt(), any(LabelFormat.class), any(ShipmentV2Bean.class), any(), any(),
					any(Locale.class))).thenReturn(serviceResponse);
			final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();
			LabelsCouldNotBeCreatedException labelsCouldNotBeCreatedException = assertThrows(LabelsCouldNotBeCreatedException.class,
					() -> serviceToBeTested.getPrintLabelsForForwardingOrder(forwardingOrder, BUSINESS_DOMAIN));
			assertThat(labelsCouldNotBeCreatedException.getMessage()).isEqualTo("Service validation errors [mock-validation fail]");

		}

		@Test
		void shouldThrowLabelsCouldNotBeCreatedExceptionWhenServiceResponseIsNull() {
			setUpMocks();
			ReflectionTestUtils.setField(serviceToBeTested, "generateTrialLabel", true);
			when(shipmentLabelService.getDachserLabelTrial(anyInt(), any(LabelFormat.class), any(ShipmentV2Bean.class), any(), any(),
					any(Locale.class))).thenReturn(null);
			final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();
			LabelsCouldNotBeCreatedException labelsCouldNotBeCreatedException = assertThrows(LabelsCouldNotBeCreatedException.class,
					() -> serviceToBeTested.getPrintLabelsForForwardingOrder(forwardingOrder, BUSINESS_DOMAIN));
			assertThat(labelsCouldNotBeCreatedException.getMessage()).isEqualTo("Service response is null");

		}

		@Test
		void shouldSucceedForDachserLabel() {
			setUpMocks();
			ReflectionTestUtils.setField(serviceToBeTested, "generateTrialLabel", false);
			when(shipmentLabelService.getDachserLabel(anyInt(), any(LabelFormat.class), any(ShipmentV2Bean.class), any(), any(), any(Locale.class))).thenReturn(
					new ShipmentLabelBean("test".getBytes()));
			final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();
			final byte[] label = serviceToBeTested.getPrintLabelsForForwardingOrder(forwardingOrder, BUSINESS_DOMAIN);
			assertNotNull(label);
			assertEquals("test", new String(label));
		}

		@Test
		void shouldSucceedForDachserLabelTrial() {
			setUpMocks();
			ReflectionTestUtils.setField(serviceToBeTested, "generateTrialLabel", true);
			when(shipmentLabelService.getDachserLabelTrial(anyInt(), any(LabelFormat.class), any(ShipmentV2Bean.class), any(), any(),
					any(Locale.class))).thenReturn(new ShipmentLabelBean("trialTest".getBytes()));
			final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();
			final byte[] label = serviceToBeTested.getPrintLabelsForForwardingOrder(forwardingOrder, BUSINESS_DOMAIN);
			assertNotNull(label);
			assertEquals("trialTest", new String(label));
		}

		@Test
		void shouldSucceedForDachserLabelTrialEvenIfRelationServiceNotAvailable() {
			setUpMocks();
			ReflectionTestUtils.setField(serviceToBeTested, "generateTrialLabel", true);
			when(relationLabelPrintingService.retrieveRelation(any())).thenThrow(ExtRelationLabelPrintingServiceNotAvailable.class);
			when(shipmentLabelService.getDachserLabelTrial(anyInt(), any(LabelFormat.class), any(ShipmentV2Bean.class), any(), any(),
					any(Locale.class))).thenReturn(new ShipmentLabelBean("trialTest".getBytes()));
			final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();
			final byte[] label = serviceToBeTested.getPrintLabelsForForwardingOrder(forwardingOrder, BUSINESS_DOMAIN);
			assertNotNull(label);
			assertEquals("trialTest", new String(label));
		}

		@Test
		void shouldSucceedForDachserLabelTrialEvenIfRelationIsGivenButWithNoResult() {
			when(shipmentPrintBeanMapper.mapForwardingOrder(any(ForwardingOrder.class))).thenReturn(shipmentPrintBean);
			when(countryService.mapToDachserCountryCode(any())).thenReturn("D");
			when(relationLabelPrintingService.retrieveRelation(any())).thenReturn(new RMDRelationLabelprintingDTO());
			ReflectionTestUtils.setField(serviceToBeTested, "generateTrialLabel", true);
			RMDRelationLabelprintingDTO dto = new RMDRelationLabelprintingDTO();
			dto.setDispatchRelation("0060");
			dto.setDispatchRelationCountryCode("D");
			when(relationLabelPrintingService.retrieveRelation(any())).thenReturn(dto);
			when(shipmentLabelService.getDachserLabelTrial(anyInt(), any(LabelFormat.class), any(ShipmentV2Bean.class), any(), any(),
					any(Locale.class))).thenReturn(new ShipmentLabelBean("trialTest".getBytes()));
			final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();
			final byte[] label = serviceToBeTested.getPrintLabelsForForwardingOrder(forwardingOrder, BUSINESS_DOMAIN);
			assertNotNull(label);
			assertEquals("trialTest", new String(label));
		}

		@Test
		void shouldThrowLabelsCouldNotBeCreatedExceptionExceptionForExternalService() {
			setUpMocks();
			ReflectionTestUtils.setField(serviceToBeTested, "generateTrialLabel", true);
			final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();
			when(shipmentLabelService.getDachserLabelTrial(anyInt(), any(LabelFormat.class), any(ShipmentV2Bean.class), any(), any(),
					any(Locale.class))).thenThrow(new RuntimeException("timeout"));
			LabelsCouldNotBeCreatedException labelsCouldNotBeCreatedException = assertThrows(LabelsCouldNotBeCreatedException.class,
					() -> serviceToBeTested.getPrintLabelsForForwardingOrder(forwardingOrder, BUSINESS_DOMAIN));

			assertThat(labelsCouldNotBeCreatedException.getMessage()).isEqualTo("timeout");
		}

		@Test
		void shouldThrowLabelsCouldNotBeCreatedExceptionExceptionForExternalServiceWithoutOrderLines() {
			setUpMocks();
			ReflectionTestUtils.setField(serviceToBeTested, "generateTrialLabel", true);
			final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();
			forwardingOrder.setOrderLines(null);
			when(shipmentLabelService.getDachserLabelTrial(anyInt(), any(LabelFormat.class), any(ShipmentV2Bean.class), any(), any(),
					any(Locale.class))).thenThrow(new RuntimeException("timeout"));
			LabelsCouldNotBeCreatedException labelsCouldNotBeCreatedException = assertThrows(LabelsCouldNotBeCreatedException.class,
					() -> serviceToBeTested.getPrintLabelsForForwardingOrder(forwardingOrder, BUSINESS_DOMAIN));

			assertThat(labelsCouldNotBeCreatedException.getMessage()).isEqualTo("timeout");
		}
	}

	@SpringBootTest(classes = { DFEBookApplication.class })
	@ActiveProfiles("integrationtest")
	@Tag("IntegrationTest")
	@Nested
	@Disabled
	class IntegrationTest {

		@Autowired
		LabelService labelService;

		@Autowired
		BusinessDomainProvider businessDomainProvider;

		@Autowired
		TestUtil testUtil;

		@Test
		@Disabled("Activate if you want to create labels")
		void shouldSucceedWithValidOrder() throws IOException {
			final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();
			forwardingOrder.setSsccs(TestUtil.generateSSccs(9));
			final byte[] serviceResponse = labelService.getShipmentLabelsForOrder(forwardingOrder, businessDomainProvider.getBusinessDomain());
			assertNotNull(serviceResponse);
			FileOutputStream b = new FileOutputStream("generatedTestLabel-9pages.pdf");
			b.write(serviceResponse);
			b.close();
			b.flush();
		}
	}

}
