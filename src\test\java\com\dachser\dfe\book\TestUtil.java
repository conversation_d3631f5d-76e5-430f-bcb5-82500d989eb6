package com.dachser.dfe.book;

import com.dachser.dfe.book.dip.DipOrderType;
import com.dachser.dfe.book.dip.repository.OrderXml;
import com.dachser.dfe.book.dip.xml.XmlPublishStatus;
import com.dachser.dfe.book.document.Document;
import com.dachser.dfe.book.document.DocumentStatus;
import com.dachser.dfe.book.document.DocumentType;
import com.dachser.dfe.book.jpa.entity.CustomsType;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.ADRDangerousGoodDto;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.dfe.book.model.DangerousGoodDto;
import com.dachser.dfe.book.model.EQDangerousGoodDto;
import com.dachser.dfe.book.model.FurtherAddressType;
import com.dachser.dfe.book.model.GoodsGroupDto;
import com.dachser.dfe.book.model.LQDangerousGoodDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderLineDetailDto;
import com.dachser.dfe.book.model.OrderRequestBodyDto;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.PackingPositionDto;
import com.dachser.dfe.book.model.RoadOrderLineDto;
import com.dachser.dfe.book.model.SourceOfOrder;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.AdditionalReference;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.AddressInformation;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.COD;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ContactInformation;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.DocumentDate;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.DocumentHeader;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.EDIReceiver;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.EDISender;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ForwardingOrderInformation;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.GoodsValue;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.Measurement;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.Measurements;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.PackageIdentification;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.PartnerInformation;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.PreliminaryShipment;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentAddress;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentDate;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentHeader;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentLine;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentText;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.Transport;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.Weight;
import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderDefaults;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.air.AirOrderLine;
import com.dachser.dfe.book.order.air.AirOrderLineHsCode;
import com.dachser.dfe.book.order.air.AirOrderReference;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.common.OrderSscc;
import com.dachser.dfe.book.order.common.OrderText;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.common.dangerousgood.ADRDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGoodDataItem;
import com.dachser.dfe.book.order.common.dangerousgood.EQDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.LQDangerousGood;
import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.order.generator.OrderGenerator;
import com.dachser.dfe.book.order.reference.ReferenceTestHelper;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaImportOrder;
import com.dachser.dfe.book.order.sea.SeaOrderLine;
import com.dachser.dfe.book.quote.QuoteInformation;
import com.dachser.dfe.book.quote.RoadQuoteInformation;
import com.dachser.dfe.book.transferlist.entity.VTransferList;
import com.dachser.dfe.book.transferlist.model.TransferListAddressType;
import com.dachser.dfe.book.user.UserContextService;
import com.dachser.dfe.book.user.UserServiceMock;
import com.dachser.dfe.book.user.model.UserPreferences;
import com.dachser.dfe.platform.model.PlatformAslCustomerV5;
import com.dachser.dfe.platform.model.PlatformCompanyV5;
import com.dachser.dfe.platform.model.PlatformPrincipalAddressV5;
import com.dachser.dfe.platform.model.PlatformRoadCustomerV5;
import com.dachser.dfe.platform.model.PlatformRoadOrderOptionsV5;
import com.dachser.dfe.platform.model.PlatformUserV5;
import com.dachser.dfe.sharedservices.masterdata.businesspartner.model.SSMDBPBranch;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.io.IOException;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static com.dachser.dfe.book.order.generator.OrderGenerator.generateOrderAddress;
import static com.dachser.dfe.book.order.generator.OrderGenerator.generateOrderFurtherAddress;
import static com.dachser.dfe.book.user.UserServiceMock.VALID_CUST_NO_ASL;
import static com.dachser.dfe.book.user.UserServiceMock.VALID_CUST_NO_ROAD;

@Component
@RequiredArgsConstructor
@Slf4j
public class TestUtil implements ResourceLoadingTest {

	public static final int BRANCH_ID = 10;

	private final UserContextService userContextService;

	public final OrderGenerator orderGenerator;

	public ForwardingOrder generateForwardingOrder() {
		final ForwardingOrder forwardingOrder = new ForwardingOrder();
		forwardingOrder.setDatasource(SourceOfOrder.BOOK);
		forwardingOrder.setCustomerNumber("99999993");
		forwardingOrder.setBranchId(BRANCH_ID);

		forwardingOrder.setOrderNumber("55667788");
		forwardingOrder.setOrderGroup("A");
		forwardingOrder.setProduct("Z");
		forwardingOrder.setDivision(Division.T);
		forwardingOrder.setTransportName("44332211");
		final OrderContact deliveryContact = new OrderContact();
		deliveryContact.setEmail("<EMAIL>");
		deliveryContact.setName("Delivery Contact");
		deliveryContact.setMobile("0000777777778");
		deliveryContact.setTelephone("0000777777777");
		forwardingOrder.setDeliveryContact(deliveryContact);
		forwardingOrder.setFreightTerm("031");
		forwardingOrder.setSendAt(LocalDateTime.of(2022, 12, 10, 0, 0, 0).toInstant(ZoneOffset.UTC));
		forwardingOrder.addOrderReference(ReferenceTestHelper.roadOrderReference(ReferenceType.PURCHASE_ORDER_NUMBER, null, "47110815"));
		forwardingOrder.addOrderReference(ReferenceTestHelper.roadOrderReference(ReferenceType.ORDER_NUMBER, null, "55667788"));

		forwardingOrder.setTailLiftDelivery(true);
		forwardingOrder.setDeliveryOption(DeliveryOptions.AP);
		forwardingOrder.setGoodsValue(BigDecimal.valueOf(99));
		forwardingOrder.setCurrency("EUR");
		final RoadOrderLine orderLine = generateRoadOrderLine();
		forwardingOrder.addOrderLine(orderLine);
		orderLine.setOrder(forwardingOrder);
		forwardingOrder.setSelfCollection(true);
		forwardingOrder.setCollectionDate(LocalDate.of(2022, 9, 1));
		forwardingOrder.setCollectionFrom(OffsetDateTime.of(2022, 9, 1, 10, 0, 0, 0, ZoneOffset.UTC));
		forwardingOrder.setCollectionTo(OffsetDateTime.of(2022, 9, 1, 15, 0, 0, 0, ZoneOffset.UTC));
		OrderAddress consigneeAddress = generateOrderAddress("_consignee", "DE", true, null);
		consigneeAddress.setDropOfLocation("dropOfLocation");
		forwardingOrder.setConsigneeAddress(consigneeAddress);
		forwardingOrder.setShipperAddress(OrderGenerator.generateOrderAddress("_shipper", "DE", true, "9999960"));
		forwardingOrder.setFixDate(LocalDate.now());
		forwardingOrder.addAddress(OrderGenerator.generateOrderFurtherAddress("_further_address", "DE", "DA"));
		generateOrderText().forEach(forwardingOrder::addOrderText);
		generateOrderSScc("00340258761102639537").forEach(forwardingOrder::addOrderSscc);
		OrderDefaults.fillCreationFieldsOfAllModels(forwardingOrder, userContextService);
		forwardingOrder.setPackingPositions(Collections.emptyList());

		final RoadOrderLine packingOrderLine = generateRoadOrderLine();
		packingOrderLine.setOrder(forwardingOrder);
		final PackingPosition packingPosition = generatePackingPosition(2, packingOrderLine);
		packingOrderLine.setPackingPositionId(packingPosition.getId());
		List<RoadOrderLine> packingOrderLines = new ArrayList<>();
		packingOrderLines.add(packingOrderLine);
		packingPosition.setOrderLines(packingOrderLines);
		packingPosition.setOrder(forwardingOrder);
		List<PackingPosition> packingPositions = new ArrayList<>();
		packingPositions.add(packingPosition);
		forwardingOrder.setPackingPositions(packingPositions);

		return forwardingOrder;
	}

	public ForwardingOrder generateForwardingOrderWithDangerousGoods() {
		ForwardingOrder forwardingOrder = generateForwardingOrder();

		forwardingOrder.addOrderLine(generateRoadDangerousGoodOrderLine());
		forwardingOrder.setPackingPositions(Collections.emptyList());
		return forwardingOrder;
	}

	public CollectionOrder generateCollectionOrderWithDangerousGoods() {
		CollectionOrder collectionOrder = generateCollectionOrder();

		collectionOrder.addOrderLine(generateRoadDangerousGoodOrderLine());
		collectionOrder.setPackingPositions(Collections.emptyList());
		return collectionOrder;

	}

	public CollectionOrder generateCollectionOrder() {
		final CollectionOrder collectionOrder = orderGenerator.collectionOrderSupplier.get();
		collectionOrder.setDatasource(SourceOfOrder.BOOK);
		collectionOrder.setCustomerNumber("99999993");
		collectionOrder.setBranchId(BRANCH_ID);
		collectionOrder.setShipmentNumber(4918712902L);
		collectionOrder.setOrderNumber("55667788");
		collectionOrder.setOrderGroup("A");
		collectionOrder.setProduct("Z");
		collectionOrder.setDivision(Division.T);
		final OrderContact deliveryContact = new OrderContact();
		deliveryContact.setEmail("<EMAIL>");
		deliveryContact.setName("Delivery Contact");
		deliveryContact.setMobile("0000777777778");
		deliveryContact.setTelephone("0000777777777");
		collectionOrder.setDeliveryContact(deliveryContact);
		collectionOrder.setFreightTerm("031");
		collectionOrder.setSendAt(LocalDateTime.of(2022, 12, 10, 0, 0, 0).toInstant(ZoneOffset.UTC));
		collectionOrder.addOrderReference(ReferenceTestHelper.roadOrderReference(ReferenceType.PURCHASE_ORDER_NUMBER, null, "47110815"));
		collectionOrder.addOrderReference(ReferenceTestHelper.roadOrderReference(ReferenceType.ORDER_NUMBER, null, "55667788"));

		collectionOrder.setTailLiftDelivery(true);
		collectionOrder.setDeliveryOption(DeliveryOptions.AP);
		collectionOrder.setGoodsValue(BigDecimal.valueOf(99));
		collectionOrder.setCustomsGoods(true);
		collectionOrder.setCustomsType(CustomsType.DACHSER);
		collectionOrder.setCurrency("EUR");
		final RoadOrderLine orderLine = generateRoadOrderLine();
		collectionOrder.addOrderLine(orderLine);
		orderLine.setOrder(collectionOrder);
		collectionOrder.setCollectionDate(LocalDate.of(2022, 9, 1));
		collectionOrder.setCollectionFrom(OffsetDateTime.of(2022, 9, 1, 10, 0, 0, 0, ZoneOffset.UTC));
		collectionOrder.setCollectionTo(OffsetDateTime.of(2022, 9, 1, 15, 0, 0, 0, ZoneOffset.UTC));
		OrderAddress consigneeAddress = generateOrderAddress("_consignee", "DE", true, null);
		OrderAddress principalAddress = generateOrderAddress("_principal", "DE", true, null);
		consigneeAddress.setDropOfLocation("dropOfLocation");
		collectionOrder.setConsigneeAddress(consigneeAddress);
		collectionOrder.setPrincipalAddress(principalAddress);
		collectionOrder.setFixDate(LocalDate.now());
		collectionOrder.addAddress(OrderGenerator.generateOrderFurtherAddress("_further_address", "DE", "DA"));
		generateOrderText().forEach(collectionOrder::addOrderText);
		OrderText orderText = new OrderText();
		orderText.setTextType("SI");
		orderText.setText("Pallets not stackable");
		collectionOrder.addOrderText(orderText);
		OrderText collectionInstruction = new OrderText();
		collectionInstruction.setTextType("A");
		collectionInstruction.setText(
				"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer nec odio. Praesent libero. Sed cursus ante dapibus diam. Sed nisi. Nulla quis sem at nibh elementum imperdiet.");
		collectionOrder.addOrderText(collectionInstruction);
		OrderDefaults.fillCreationFieldsOfAllModels(collectionOrder, userContextService);

		final RoadOrderLine packingOrderLine = generateRoadOrderLine();
		packingOrderLine.setOrder(collectionOrder);
		final PackingPosition packingPosition = generatePackingPosition(2, packingOrderLine);
		List<RoadOrderLine> packingOrderLines = new ArrayList<>();
		packingOrderLines.add(packingOrderLine);
		packingPosition.setOrderLines(packingOrderLines);
		packingOrderLine.setPackingPositionId(packingPosition.getId());
		packingPosition.setOrder(collectionOrder);
		ArrayList<PackingPosition> packingPositions = new ArrayList<>();
		packingPositions.add(packingPosition);
		collectionOrder.setPackingPositions(packingPositions);

		return collectionOrder;
	}

	public RoadOrderLine generateRoadDangerousGoodOrderLine() {
		RoadOrderLine dangerousGoodOrderLine = generateRoadOrderLine();

		// EQDangerousGood
		EQDangerousGood eqDangerousGood = new EQDangerousGood();
		eqDangerousGood.setPackagingKey("IVD");
		eqDangerousGood.setNoOfPackages(2);
		eqDangerousGood.setSortingPosition(0);
		dangerousGoodOrderLine.getDangerousGoods().add(eqDangerousGood);

		// LQDangerousGood
		LQDangerousGood lqDangerousGood = new LQDangerousGood();
		lqDangerousGood.setGrossMass(BigDecimal.TEN);
		lqDangerousGood.setNos("TEST N.O.S.");
		lqDangerousGood.setSortingPosition(0);
		lqDangerousGood.setDangerousGoodDataItem(createUnNumberDataSet("1111"));
		dangerousGoodOrderLine.getDangerousGoods().add(lqDangerousGood);

		// ADRDangerousGood
		ADRDangerousGood adrDangerousGood = new ADRDangerousGood();
		adrDangerousGood.setNos("TEST N.O.S.");
		adrDangerousGood.setSortingPosition(0);
		eqDangerousGood.setPackagingKey("IVD");
		adrDangerousGood.setEnvironmentallyHazardous(true);
		adrDangerousGood.setGrossMass(BigDecimal.TEN);
		adrDangerousGood.setNoOfPackages(2);
		adrDangerousGood.setDangerousGoodDataItem(createUnNumberDataSet("1112"));
		dangerousGoodOrderLine.getDangerousGoods().add(adrDangerousGood);

		return dangerousGoodOrderLine;
	}

	public static OrderAddress collectionShipperAddress() {
		OrderAddress shipperAddress = generateOrderAddress("_shipper", "DE", true, "9999960");
		shipperAddress.setNeutralize(true);
		return shipperAddress;
	}

	public static OrderAddress collectionDifferentConsigneeAddress() {
		OrderAddress differentConsigneeAddress = generateOrderAddress("_different_consignee", "DE", true, null);
		differentConsigneeAddress.setNeutralize(false);
		return differentConsigneeAddress;
	}

	public AirExportOrder generateAirExportOrder() {
		final AirExportOrder airExportOrder = new AirExportOrder();
		airExportOrder.setDatasource(SourceOfOrder.BOOK);
		airExportOrder.setCustomerNumber(VALID_CUST_NO_ASL);
		airExportOrder.setStatus(OrderStatus.DRAFT);
		airExportOrder.setBranchId(BRANCH_ID);
		airExportOrder.setToIATA("LHR");
		airExportOrder.setFromIATA("MUC");
		airExportOrder.setSendAt(LocalDateTime.of(2022, 12, 10, 1, 0, 0).toInstant(ZoneOffset.UTC));
		generateAirOrderReferences().forEach(airExportOrder::addOrderReference);
		airExportOrder.setShipmentTransferId(UUID.randomUUID().toString());
		airExportOrder.setTailLiftCollection(true);
		airExportOrder.setGoodsValue(BigDecimal.valueOf(99));
		airExportOrder.setCurrency("EUR");
		final AirOrderLine orderLine = generateAirOrderLine();
		airExportOrder.addOrderLine(orderLine);
		airExportOrder.setProductCode(null);
		airExportOrder.setCollectionDate(LocalDate.now().plusDays(2));
		airExportOrder.setCollectionFrom(OffsetDateTime.now().plusDays(2).plusHours(2));
		airExportOrder.setCollectionTo(OffsetDateTime.now().plusDays(2).plusHours(6));
		airExportOrder.setConsigneeAddress(OrderGenerator.generateOrderAddress("_consignee", "DE", true, null, true, "tax1234"));
		OrderAddress shipperAddress = generateOrderAddress("_shipper", "DE", false, "9999920");
		shipperAddress.setOrderContact(OrderContact.builder().name("shipper_contact").email("email").mobile("12345").telephone("54321").fax("fax23").build());
		airExportOrder.setShipperAddress(shipperAddress);
		final OrderAddress deliveryAddress = OrderGenerator.generateOrderAddress("_delivery", "DE", false, null);
		deliveryAddress.setOrderContact(OrderContact.builder().name("delivery_contact").email("email").mobile("12345").telephone("54321").fax("fax23").build());
		airExportOrder.setDeliveryAddress(deliveryAddress);
		airExportOrder.setPickupAddress(OrderGenerator.generateOrderAddress("_pickup", "DE", false, null, true, "tax4567"));
		airExportOrder.addAddress(OrderGenerator.generateOrderFurtherAddress("_notification", "DE", "N1"));
		generateOrderText().forEach(airExportOrder::addOrderText);
		airExportOrder.setShipperReference("shipper_reference123");
		airExportOrder.addOrderReference(
				AirOrderReference.builder().referenceType(AirSeaOrderReferenceType.SHIPPERS_REFERENCE).referenceValue("shipper_reference123").loading(false).unloading(true)
						.build());
		airExportOrder.setIncoTerm("EXW");
		airExportOrder.setShipmentNumber(4711L);
		OrderDefaults.fillCreationFieldsOfAllModels(airExportOrder, userContextService);
		return airExportOrder;
	}

	public AirImportOrder generateAirImportOrder() {
		final AirImportOrder airImportOrder = new AirImportOrder();
		airImportOrder.setDatasource(SourceOfOrder.BOOK);
		airImportOrder.setCustomerNumber(VALID_CUST_NO_ASL);
		airImportOrder.setStatus(OrderStatus.DRAFT);
		airImportOrder.setBranchId(BRANCH_ID);
		airImportOrder.setToIATA("LHR");
		airImportOrder.setFromIATA("MUC");
		airImportOrder.setSendAt(LocalDateTime.of(2022, 12, 10, 1, 0, 0).toInstant(ZoneOffset.UTC));
		generateAirOrderReferences().forEach(airImportOrder::addOrderReference);
		airImportOrder.setShipmentTransferId(UUID.randomUUID().toString());
		airImportOrder.setTailLiftCollection(true);
		airImportOrder.setGoodsValue(BigDecimal.valueOf(99));
		airImportOrder.setCurrency("EUR");
		final AirOrderLine orderLine = generateAirOrderLine();
		airImportOrder.addOrderLine(orderLine);
		airImportOrder.setProductCode(null);
		airImportOrder.setCollectionDate(LocalDate.now().plusDays(2));
		airImportOrder.setCollectionFrom(OffsetDateTime.now().plusDays(2).plusHours(2));
		airImportOrder.setCollectionTo(OffsetDateTime.now().plusDays(2).plusHours(6));
		airImportOrder.setConsigneeAddress(generateOrderAddress("_consignee", "DE", true, null, true, "tax1234"));
		OrderAddress shipperAddress = generateOrderAddress("_shipper", "DE", false, "9999920");
		shipperAddress.setOrderContact(OrderContact.builder().name("shipper_contact").email("email").mobile("12345").telephone("54321").fax("fax23").build());
		airImportOrder.setShipperAddress(shipperAddress);
		final OrderAddress deliveryAddress = generateOrderAddress("_delivery", "DE", false, null);
		deliveryAddress.setOrderContact(OrderContact.builder().name("delivery_contact").email("email").mobile("12345").telephone("54321").fax("fax23").build());
		airImportOrder.setDeliveryAddress(deliveryAddress);
		airImportOrder.setPickupAddress(generateOrderAddress("_pickup", "DE", false, null, true, "tax4567"));
		airImportOrder.addAddress(generateOrderFurtherAddress("_notification", "DE", "N1"));
		generateOrderText().forEach(airImportOrder::addOrderText);
		airImportOrder.setShipperReference("shipper_reference123");
		airImportOrder.addOrderReference(
				AirOrderReference.builder().referenceType(AirSeaOrderReferenceType.SHIPPERS_REFERENCE).referenceValue("shipper_reference123").loading(false).unloading(true)
						.build());
		airImportOrder.setIncoTerm("EXW");
		airImportOrder.setShipmentNumber(4711L);
		OrderDefaults.fillCreationFieldsOfAllModels(airImportOrder, userContextService);
		return airImportOrder;
	}

	public SeaExportOrder generateSeaExportOrder() {
		return orderGenerator.generateSeaExportOrder();
	}

	public SeaImportOrder generateSeaImportOrder() {
		return orderGenerator.generateSeaImportOrder();
	}

	public static RoadOrderLine generateRoadOrderLine() {
		return generateRoadOrderLine("EU", "Euroflachpalette", 2, null);
	}

	public static OrderLineDetailDto generateOrderLineDetailDto() {
		return generateOrderLineDetailDto("EU", "Euroflachpalette", 2, null, Collections.emptyList());
	}

	public static RoadOrderLine generateRoadOrderLine(String packagingType, String packagingTypeDescription, int quantity, Long packingPositionId) {
		final RoadOrderLine orderLine = new RoadOrderLine();
		orderLine.setNumber(1);
		orderLine.setContent("content");
		orderLine.setWeight(BigDecimal.valueOf(50L));
		orderLine.setHeight(240);
		orderLine.setWidth(120);
		orderLine.setLength(260);
		orderLine.setVolume(BigDecimal.valueOf(2L));
		orderLine.setLoadingMeter(BigDecimal.valueOf(3.60));
		orderLine.setPackagingType(packagingType);
		orderLine.setPackagingTypeDescription(packagingTypeDescription);
		orderLine.setQuantity(quantity);
		orderLine.setGoodsGroupQuantity(3);
		orderLine.setGoodsGroup("01");
		orderLine.setContent("CAR SPARE");
		orderLine.setPackingPositionId(packingPositionId);
		return orderLine;
	}

	public static RoadOrderLineDto generateRoadOrderLineDto() {
		return generateRoadOrderLineDto("EU", "Euroflachpalette", 2, null);
	}

	public static RoadOrderLineDto generateRoadOrderLineDto(String packagingType, String packagingTypeDescription, int quantity, Long packingPositionId) {
		final RoadOrderLineDto orderLine = new RoadOrderLineDto();
		orderLine.setNumber(1);
		orderLine.setContent("content");
		orderLine.setWeight(50);
		orderLine.setHeight(240);
		orderLine.setWidth(120);
		orderLine.setLength(260);
		orderLine.setVolume(2.0);
		orderLine.setLoadingMeter(3.60);
		orderLine.setPackaging(new OptionDto().code(packagingType).description(packagingTypeDescription));
		orderLine.setQuantity(quantity);
		orderLine.setGoodsGroup(new GoodsGroupDto().code("01").quantity(3));
		orderLine.setContent("CAR SPARE");
		orderLine.setPackingPositionId(packingPositionId);
		return orderLine;
	}

	public static OrderLineDetailDto generateOrderLineDetailDto(String packagingType, String packagingTypeDescription, int quantity, Long packingPositionId,
			List<DangerousGoodDto> dangerousGoodDtos) {
		final OrderLineDetailDto orderLine = new OrderLineDetailDto();
		orderLine.setNumber(1);
		orderLine.setContent("content");
		orderLine.setWeight(50);
		orderLine.setHeight(240);
		orderLine.setWidth(120);
		orderLine.setLength(260);
		orderLine.setVolume(2.0);
		orderLine.setLoadingMeter(3.60);
		orderLine.setPackaging(new OptionDto().code(packagingType).description(packagingTypeDescription));
		orderLine.setQuantity(quantity);
		orderLine.setGoodsGroup(new GoodsGroupDto().code("01").quantity(3));
		if (packingPositionId != null) {
			orderLine.setPackingAidPosition(String.format("%03d", packingPositionId));
		}
		orderLine.setDangerousGoodsADR(dangerousGoodDtos.stream().filter(ADRDangerousGoodDto.class::isInstance).map(ADRDangerousGoodDto.class::cast).toList());
		orderLine.setDangerousGoodsEQ(dangerousGoodDtos.stream().filter(EQDangerousGoodDto.class::isInstance).map(EQDangerousGoodDto.class::cast).toList());
		orderLine.setDangerousGoodsLQ(dangerousGoodDtos.stream().filter(LQDangerousGoodDto.class::isInstance).map(LQDangerousGoodDto.class::cast).toList());
		return orderLine;
	}

	public static RoadOrderLine generateRoadOrderLine(String packagingType, String packagingTypeDescription, int quantity) {
		return generateRoadOrderLine(packagingType, packagingTypeDescription, quantity, null);
	}

	public static PackingPosition generatePackingPosition(int quantity, RoadOrderLine... roadOrderLines) {
		final PackingPosition packingPosition = new PackingPosition();
		packingPosition.setQuantity(quantity);
		packingPosition.setPackagingType("EU");
		packingPosition.setPackagingTypeDescription("Euroflachpalette");
		packingPosition.setOrderLines(List.of(roadOrderLines));
		return packingPosition;
	}

	public static PackingPositionDto generatePackingPositionDto(int quantity, RoadOrderLineDto... roadOrderLines) {
		final PackingPositionDto packingPosition = new PackingPositionDto();
		packingPosition.setQuantity(quantity);
		packingPosition.setPackagingType(new OptionDto().code("EU").description("Euroflachpalette"));
		packingPosition.setLines(List.of(roadOrderLines));
		return packingPosition;
	}

	public static EQDangerousGoodDto generateEQDangerousGoodDto(int noOfPackages) {
		final EQDangerousGoodDto eqDangerousGoodDto = new EQDangerousGoodDto();
		eqDangerousGoodDto.setNoOfPackages(noOfPackages);
		eqDangerousGoodDto.setDangerousGoodType("EQ_DANGEROUS_GOOD");
		eqDangerousGoodDto.setSortingPosition(0);
		eqDangerousGoodDto.setPackaging(new OptionDto().code("1vd").description("IVD - IATA Dangerous Goods"));
		return eqDangerousGoodDto;
	}

	public static LQDangerousGoodDto generateLQDangerousGoodDto(BigDecimal grossMass, String transportCategory) {
		final LQDangerousGoodDto lqDangerousGoodDto = new LQDangerousGoodDto();
		lqDangerousGoodDto.setNos("N.O.S");
		lqDangerousGoodDto.setDangerousGoodType("LQ_DANGEROUS_GOOD");
		lqDangerousGoodDto.setGrossMass(grossMass);
		lqDangerousGoodDto.setDangerousGoodDataItem(generateDangerousGoodDataItemDto("1111", transportCategory));

		return lqDangerousGoodDto;
	}

	public static ADRDangerousGoodDto generateADRDangerousGoodDto(int noOfPackages, String transportCategory) {
		final ADRDangerousGoodDto adrDangerousGoodDto = new ADRDangerousGoodDto();
		adrDangerousGoodDto.setNos("N.O.S");
		adrDangerousGoodDto.setDangerousGoodType("ADR_DANGEROUS_GOOD");
		adrDangerousGoodDto.setEnvironmentallyHazardous(true);
		adrDangerousGoodDto.setNoOfPackages(noOfPackages);
		adrDangerousGoodDto.setDangerousGoodDataItem(generateDangerousGoodDataItemDto("1112", transportCategory));
		adrDangerousGoodDto.setCalculatedPoints(100);

		return adrDangerousGoodDto;
	}

	public static DangerousGoodDataItemDto generateDangerousGoodDataItemDto(String unNumber, String transportCategory) {
		final DangerousGoodDataItemDto dangerousGoodDataItemDto = new DangerousGoodDataItemDto();
		dangerousGoodDataItemDto.setUnNumber(unNumber);
		dangerousGoodDataItemDto.setPackingGroup("II");
		dangerousGoodDataItemDto.setMainDanger("1");
		dangerousGoodDataItemDto.setDescription("Test N.O.S.");
		dangerousGoodDataItemDto.setClassificationCode("1.1D");
		dangerousGoodDataItemDto.setDgmId("1303");
		dangerousGoodDataItemDto.setNosRequired(true);
		dangerousGoodDataItemDto.setSubsidiaryHazardOne("1");
		dangerousGoodDataItemDto.setSubsidiaryHazardTwo("2");
		dangerousGoodDataItemDto.setSubsidiaryHazardThree("3");
		dangerousGoodDataItemDto.setTransportCategory(transportCategory);
		dangerousGoodDataItemDto.setTunnelCode("D/E");
		return dangerousGoodDataItemDto;
	}


	public AirOrderLine generateAirOrderLine() {
		final AirOrderLine orderLine = new AirOrderLine();
		orderLine.setWeight(BigDecimal.valueOf(50L));
		orderLine.setHeight(240);
		orderLine.setWidth(120);
		orderLine.setLength(260);
		orderLine.setVolume(BigDecimal.valueOf(2L));
		orderLine.setPackagingType("EU");
		orderLine.setPackagingTypeDescription("Euroflachpalette");
		orderLine.setQuantity(2);
		orderLine.setNumber(1);
		orderLine.setMarkAndNumbers("markAndNumbers - text - content");
		final ArrayList<AirOrderLineHsCode> hsCodes = new ArrayList<>();
		final AirOrderLineHsCode airOrderLineHsCode1 = new AirOrderLineHsCode();
		airOrderLineHsCode1.setHsCode("4564654");
		airOrderLineHsCode1.setGoods("goods1");
		airOrderLineHsCode1.setOrderLine(orderLine);
		hsCodes.add(airOrderLineHsCode1);
		final AirOrderLineHsCode airOrderLineHsCode2 = new AirOrderLineHsCode();
		airOrderLineHsCode2.setHsCode("1234567");
		airOrderLineHsCode2.setGoods("goods2");
		airOrderLineHsCode2.setOrderLine(orderLine);
		hsCodes.add(airOrderLineHsCode2);
		final AirOrderLineHsCode airOrderLineHsCode3 = new AirOrderLineHsCode();
		airOrderLineHsCode3.setHsCode(null);
		airOrderLineHsCode3.setGoods("goods3");
		airOrderLineHsCode3.setOrderLine(orderLine);
		hsCodes.add(airOrderLineHsCode3);
		orderLine.setHsCodes(hsCodes);
		return orderLine;
	}

	public SeaOrderLine generateSeaOrderLine() {
		return OrderGenerator.generateSeaOrderLine();
	}

	public static List<OrderSscc> generateSSccs(int amount) {
		final ArrayList<OrderSscc> list = new ArrayList<>();

		for (int i = 0; i < amount; i++) {
			int length = 20;
			String generatedString = RandomStringUtils.randomNumeric(length);

			final OrderSscc orderSscc = new OrderSscc();

			orderSscc.setSscc(generatedString);
			list.add(orderSscc);
		}
		return list;
	}

	public ADRDangerousGood adrDangerousGoods(RoadOrderLine orderLine) {
		ADRDangerousGood adrDangerousGood = new ADRDangerousGood();
		adrDangerousGood.setId(1L);
		adrDangerousGood.setSortingPosition(1);
		adrDangerousGood.setRoadOrderLine(orderLine);
		adrDangerousGood.setNos("Nos text");
		adrDangerousGood.setGrossMass(BigDecimal.valueOf(10.75));
		adrDangerousGood.setDangerousGoodDataItem(generateDGDataItem());
		adrDangerousGood.setEnvironmentallyHazardous(true);
		adrDangerousGood.setNoOfPackages(20);
		adrDangerousGood.setPackagingKey("XD");
		return adrDangerousGood;
	}

	public LQDangerousGood lqDangerousGoods(RoadOrderLine orderLine) {
		LQDangerousGood lqDangerousGood = new LQDangerousGood();
		lqDangerousGood.setId(1L);
		lqDangerousGood.setSortingPosition(1);
		lqDangerousGood.setRoadOrderLine(orderLine);
		lqDangerousGood.setNos("Nos text");
		lqDangerousGood.setGrossMass(BigDecimal.valueOf(10.50));
		lqDangerousGood.setDangerousGoodDataItem(generateDGDataItem());
		return lqDangerousGood;
	}

	public EQDangerousGood eqDangerousGoods(RoadOrderLine orderLine) {
		EQDangerousGood eqDangerousGood = new EQDangerousGood();
		eqDangerousGood.setId(1L);
		eqDangerousGood.setSortingPosition(1);
		eqDangerousGood.setRoadOrderLine(orderLine);
		eqDangerousGood.setPackagingKey("BA");
		eqDangerousGood.setNoOfPackages(10);
		return eqDangerousGood;
	}

	public DangerousGoodDataItem generateDGDataItem() {
		DangerousGoodDataItem dangerousGoodDataItem = new DangerousGoodDataItem();
		dangerousGoodDataItem.setId(100L);
		dangerousGoodDataItem.setMainDanger("8");
		dangerousGoodDataItem.setSubsidiaryHazardOne("2");
		dangerousGoodDataItem.setSubsidiaryHazardTwo("3");
		dangerousGoodDataItem.setDescription("Test Description");
		dangerousGoodDataItem.setPackingGroup("II");
		dangerousGoodDataItem.setNosRequired(true);
		dangerousGoodDataItem.setUnNumber("1791");
		dangerousGoodDataItem.setClassificationCode("C9");

		return dangerousGoodDataItem;
	}

	private static List<OrderSscc> generateOrderSScc(String sscc) {
		final OrderSscc orderSscc = new OrderSscc();
		orderSscc.setSscc(sscc);
		final ArrayList<OrderSscc> list = new ArrayList<>();
		list.add(orderSscc);
		return list;
	}

	private static List<OrderText> generateOrderText() {
		final OrderText orderText = new OrderText();
		orderText.setTextType("ZU");
		orderText.setText("Ein Zustelltext");
		return List.of(orderText);
	}

	private static List<AirOrderReference> generateAirOrderReferences() {
		final ArrayList<AirOrderReference> airOrderReferences = new ArrayList<>();
		airOrderReferences.add(buildFurtherAirReference(AirSeaOrderReferenceType.INVOICE_NUMBER, "FurtherRef1-InvoiceNumber").loading(false).build());
		airOrderReferences.add(buildFurtherAirReference(AirSeaOrderReferenceType.PURCHASE_ORDER_NUMBER, "PurchaseOrderNo").build());
		airOrderReferences.add(buildFurtherAirReference(AirSeaOrderReferenceType.CONSIGNEE_REFERENCE_NUMBER, "ConsigneeRefNo").unloading(false).build());
		airOrderReferences.add(buildFurtherAirReference(AirSeaOrderReferenceType.COMMERCIAL_INVOICE_NUMBER, "Commercial").unloading(false).loading(false).build());
		airOrderReferences.add(buildFurtherAirReference(AirSeaOrderReferenceType.CUSTOMS_DOC_NUMBER, "Customs doc").build());
		airOrderReferences.add(buildFurtherAirReference(AirSeaOrderReferenceType.QUOTATION_REFERENCE, "QU127766").unloading(false).loading(false).build());
		airOrderReferences.add(buildFurtherAirReference(AirSeaOrderReferenceType.MARKS_AND_NUMBERS, "Marks and numbers").unloading(false).loading(false).build());
		airOrderReferences.add(buildFurtherAirReference(AirSeaOrderReferenceType.DELIVERY_NOTE_NUMBER, "Delivery note number").unloading(false).loading(false).build());
		airOrderReferences.add(buildFurtherAirReference(AirSeaOrderReferenceType.PACKAGING_LIST_NUMBER, "Packaging list").unloading(false).loading(false).build());
		airOrderReferences.add(buildFurtherAirReference(AirSeaOrderReferenceType.PROVIDER_SHIPMENT_NUMBER, "Provider shipment").unloading(true).loading(false).build());
		airOrderReferences.add(buildFurtherAirReference(AirSeaOrderReferenceType.SUPPLIER_SHIPMENT_NUMBER, "Supplier shipment").unloading(false).loading(true).build());
		airOrderReferences.add(buildFurtherAirReference(AirSeaOrderReferenceType.OTHERS, "Other reference").unloading(false).loading(false).build());
		return airOrderReferences;
	}

	@NotNull
	private static AirOrderReference.AirOrderReferenceBuilder<?, ?> buildFurtherAirReference(AirSeaOrderReferenceType type, String value) {
		final AirOrderReference.AirOrderReferenceBuilder<?, ?> e = AirOrderReference.builder();
		e.referenceValue(value);
		e.referenceType(type);
		e.loading(true);
		e.unloading(true);
		return e;
	}

	public static ForwardingOrderInformation generateForwardingOrderInformation() {
		final LocalDateTime defaultDate = LocalDateTime.parse("2016-12-17T09:29:53");
		final ForwardingOrderInformation order = new ForwardingOrderInformation();
		final DocumentHeader header = new DocumentHeader();
		header.setDocumentID("0");
		header.setTestFlag("1");

		final DocumentDate documentDate = new DocumentDate();
		documentDate.setDate(gregorianCalendarDate(defaultDate));
		header.setDocumentDate(documentDate);

		final EDIReceiver ediReceiver = new EDIReceiver();
		final PartnerInformation partnerInformation = new PartnerInformation();
		partnerInformation.setPartnerGLN(BigInteger.valueOf(9012345000004L));
		ediReceiver.setPartnerInformation(partnerInformation);
		header.setEDIReceiver(ediReceiver);

		final EDISender ediSender = new EDISender();
		ediSender.setPartnerInformation(partnerInformation);
		header.setEDISender(ediSender);

		final ShipmentHeader shipmentHeader = new ShipmentHeader();
		shipmentHeader.setCustomerShipmentReference("55667788");
		ShipmentAddress shipmentAddress = new ShipmentAddress();
		shipmentAddress.setAddressType("CZ");
		final AddressInformation addressInformation = new AddressInformation();
		addressInformation.setStreet("Con Street 11");
		addressInformation.setCity("Newtown");
		addressInformation.setPostalCode("88888");
		addressInformation.setCountryCode("DE");
		final ContactInformation contactInformation = new ContactInformation();
		contactInformation.setContactName("Consignora");
		contactInformation.setContactPhoneNumber("0000777777777");
		contactInformation.setContactEmail("<EMAIL>");

		final PartnerInformation shippmentPartnerInformation = new PartnerInformation();
		shippmentPartnerInformation.setPartnerGLN(BigInteger.valueOf(9012345000004L));
		shippmentPartnerInformation.setAddressInformation(addressInformation);
		shippmentPartnerInformation.getContactInformation().add(contactInformation);
		shipmentAddress.setPartnerInformation(shippmentPartnerInformation);
		shipmentHeader.getShipmentAddress().add(shipmentAddress);
		shipmentHeader.setOriginalTerm("031");
		shipmentHeader.setOriginalTermLocation("HAMBURG");
		shipmentHeader.setDivision("T");
		shipmentHeader.setDachserProduct("Z");
		shipmentHeader.setOrderGroup("A");
		ShipmentDate shipmentDate = new ShipmentDate();
		shipmentDate.setDate(gregorianCalendarDate(defaultDate));
		shipmentHeader.setShipmentDate(shipmentDate);

		final GoodsValue goodsValue = new GoodsValue();
		goodsValue.setAmount(100);
		goodsValue.setCurrency("EUR");
		shipmentHeader.setGoodsValue(goodsValue);
		shipmentHeader.setADRflag("Y");
		shipmentHeader.setTailLiftRequired("Y");
		shipmentHeader.setConsigneeCollectionIndicator("N");
		shipmentHeader.setDispatchRelation("8765");
		shipmentHeader.setSubRelation(BigInteger.valueOf(432));

		final PreliminaryShipment preliminaryShipment = new PreliminaryShipment();
		preliminaryShipment.setAction("9");
		preliminaryShipment.setCollectionDateFrom(gregorianCalendarDate(defaultDate));
		preliminaryShipment.setCollectionDateUntil(gregorianCalendarDate(defaultDate));
		preliminaryShipment.setLoadingPoint("GATE 3");
		shipmentHeader.setPreliminaryShipment(preliminaryShipment);

		final COD cod = new COD();
		cod.setCode("01");
		cod.setAmount(90);
		cod.setCurrency("EUR");
		shipmentHeader.setCOD(cod);

		final ShipmentLine shipmentLine = new ShipmentLine();
		shipmentLine.setPackagesQuantity(BigInteger.valueOf(2));
		shipmentLine.setPackingType("EU");
		final Measurements measurements = new Measurements();
		final Weight weight = new Weight();
		weight.setCode("GRO");
		Measurement measurement = new Measurement();
		measurement.setUnit("KGM");
		measurement.setValue(500);
		weight.setMeasurement(measurement);
		measurements.getWeight().add(weight);
		shipmentLine.setMeasurements(measurements);

		shipmentLine.setGoodsDescription("CAR SPARE");
		shipmentLine.setGoodsGroup("01");
		shipmentLine.setGoodsGroupQuantity(BigInteger.valueOf(2));
		shipmentLine.setMarker("01010101");
		shipmentLine.setPackagingAidsPosition(BigInteger.valueOf(1));
		shipmentHeader.getShipmentLine().add(shipmentLine);

		final AdditionalReference additionalReference = new AdditionalReference();
		additionalReference.setCode("040");
		additionalReference.setReference("1.25");
		additionalReference.setReferenceDescription("storage places");
		additionalReference.setDate(gregorianCalendarDate(defaultDate));
		shipmentHeader.getAdditionalReference().add(additionalReference);

		final AdditionalReference additionalReference1 = new AdditionalReference();
		additionalReference1.setCode("EDE");
		additionalReference1.setReference("PLATFORM_MANUAL_ENTRY");
		shipmentHeader.getAdditionalReference().add(additionalReference1);

		final ShipmentText shipmentText = new ShipmentText();
		shipmentText.setTextType("SI");
		shipmentText.getText().add("AG for texts");
		shipmentText.setTextLanguage("EN");
		shipmentHeader.getShipmentText().add(shipmentText);

		final PackageIdentification packageIdentification = new PackageIdentification();
		packageIdentification.setSSCCBarCode("00340258761102639537");
		shipmentHeader.getPackageIdentification().add(packageIdentification);

		order.setDocumentHeader(header);

		final Transport transport = new Transport();
		transport.setNumber("44332211");
		transport.getShipmentHeader().add(shipmentHeader);

		order.getTransport().add(transport);

		return order;
	}

	private static XMLGregorianCalendar gregorianCalendarDate(LocalDateTime date) {
		XMLGregorianCalendar cal = null;
		try {
			cal = DatatypeFactory.newInstance().newXMLGregorianCalendar(date.toString());
		} catch (DatatypeConfigurationException e) {
			log.error("failed to build calendar from date ".concat(date.toString()), e);
		}
		return cal;
	}

	public static PlatformUserV5 createPlatformUser() {
		final PlatformUserV5 platformUser = new PlatformUserV5();
		platformUser.setId(UUID.randomUUID());
		platformUser.setActive(Boolean.TRUE.toString());
		platformUser.setEmail("<EMAIL>");
		platformUser.setName("Test User");
		platformUser.setFirstName("Test");
		platformUser.setLastName("User");

		final PlatformCompanyV5 platformCompany = new PlatformCompanyV5();
		platformUser.setCompany(platformCompany);

		platformCompany.setId(UUID.randomUUID());
		platformCompany.setStatus("ACTIVE");
		platformCompany.setName("Test Company");

		final PlatformAslCustomerV5 platformAslCustomer = new PlatformAslCustomerV5();
		platformCompany.setCustomersASL(List.of(platformAslCustomer));

		platformAslCustomer.setId(UUID.randomUUID());
		platformAslCustomer.setNumber(VALID_CUST_NO_ASL);
		platformAslCustomer.setName("Test ASL Customer");
		platformAslCustomer.setAirProducts(true);
		platformAslCustomer.setLinkedCustomerNumber(VALID_CUST_NO_ROAD);
		platformAslCustomer.setPrincipalAddress(createPlatformPrincipalAddress());
		platformAslCustomer.setBookExportAirBranchNumber(10);
		platformAslCustomer.setBookImportAirBranchNumber(10);
		platformAslCustomer.setBookImportFclBranchNumber(10);
		platformAslCustomer.setBookExportFclBranchNumber(10);
		platformAslCustomer.setBookExportLclBranchNumber(10);
		platformAslCustomer.setBookImportLclBranchNumber(10);

		final PlatformRoadCustomerV5 platformRoadCustomer = new PlatformRoadCustomerV5();
		platformCompany.setCustomersROAD(List.of(platformRoadCustomer));

		platformRoadCustomer.setId(UUID.randomUUID());
		platformRoadCustomer.setNumber(VALID_CUST_NO_ROAD);
		platformRoadCustomer.setName("Test ROAD Customer");
		platformRoadCustomer.setBookBranchNumber(10);
		platformRoadCustomer.setLinkedCustomerNumber(VALID_CUST_NO_ASL);
		platformRoadCustomer.setPrincipalAddress(createPlatformPrincipalAddress());
		platformRoadCustomer.setOrderOptions(new PlatformRoadOrderOptionsV5().frostProtection(true).palletLocation(false).selfCollection(true).manualNumberSscc(true));
		platformRoadCustomer.setBusinessLine(PlatformRoadCustomerV5.BusinessLineEnum.FL);
		platformRoadCustomer.setBookForwardingElBranchNumber(10);
		platformRoadCustomer.setBookForwardingFlBranchNumber(10);
		platformRoadCustomer.setBookCollectingElBranchNumber(10);
		platformRoadCustomer.setBookCollectingFlBranchNumber(10);

		return platformUser;
	}

	public static PlatformPrincipalAddressV5 createPlatformPrincipalAddress() {
		final PlatformPrincipalAddressV5 platformPrincipalAddress = new PlatformPrincipalAddressV5();

		platformPrincipalAddress.setName("name");
		platformPrincipalAddress.setName1("name1");
		platformPrincipalAddress.setName2("name2");
		platformPrincipalAddress.setName3("name3");
		platformPrincipalAddress.setStreet("street");
		platformPrincipalAddress.setPostcode("12345");
		platformPrincipalAddress.setCity("city");
		platformPrincipalAddress.setCountry("Germany");
		platformPrincipalAddress.setCountryCode("DE");
		platformPrincipalAddress.setGln("gln");

		return platformPrincipalAddress;
	}

	public static VTransferList generateVTransferList(TransferListAddressType addressType, OrderAddress consigneeAddress, OrderAddress principalAddress,
			OrderFurtherAddress lpAddress) {
		return generateVTransferList(addressType, consigneeAddress, principalAddress, lpAddress, LocalDate.of(2024, 1, 1));
	}

	public static VTransferList generateVTransferList(TransferListAddressType addressType, OrderAddress consigneeAddress, OrderAddress principalAddress,
			OrderFurtherAddress lpAddress, LocalDate pickUpDateFrom) {
		VTransferList transferList = new VTransferList();
		transferList.setPickUpDateFrom(pickUpDateFrom);
		transferList.setCustomerNumber("123456");
		transferList.setOrderId(99L);
		transferList.setShipmentNumber("7");
		transferList.setPrincipalAddress(principalAddress);
		transferList.setLpAddress(lpAddress);
		transferList.setProduct("N");
		transferList.setProductName("productName");
		transferList.setDivision(Division.T);
		transferList.setAddressType(addressType);
		transferList.setConsigneeAddress(consigneeAddress);
		transferList.setFreightTerm("MOC");
		transferList.setAddressHash("addressHash");
		LocalDate date = LocalDate.of(2024, 1, 1);
		OffsetDateTime offsetDateTime = date.atStartOfDay().atOffset(ZoneOffset.systemDefault().getRules().getOffset(Instant.now()));
		transferList.setDeliveryDate(offsetDateTime);
		return transferList;
	}

	public static SSMDBPBranch createBranch(Integer branchNumber) {
		SSMDBPBranch ssmdBranch = new SSMDBPBranch();
		ssmdBranch.setBusinessPartnerBranchNumber(branchNumber);
		ssmdBranch.setBusinessPartnerName1("Business Partner Name 1");
		ssmdBranch.setBusinessPartnerName2("Business Partner Name 2");
		ssmdBranch.setBusinessPartnerName3("Business Partner Name 3");
		ssmdBranch.setBusinessPartnerStreet("Business Partner Street");
		ssmdBranch.setBusinessPartnerPostcode("Business Partner Postcode");
		ssmdBranch.setBusinessPartnerCity("Business Partner City");
		ssmdBranch.setBusinessPartnerCountryCode("Business Partner Country Code");
		return ssmdBranch;
	}

	public static OrderXml createTestOrderXml(Long orderId, DipOrderType orderType, String customerNumber) {
		OrderXml orderXml = new OrderXml();
		orderXml.setOrderId(Objects.requireNonNullElse(orderId, 1L));
		orderXml.setShipmentNumber(12345L);
		orderXml.setCustomerNumber(Objects.requireNonNullElse(customerNumber, "12345678"));
		orderXml.setOrderType(Objects.requireNonNullElse(orderType, DipOrderType.COLLECTION));
		orderXml.setStatus(XmlPublishStatus.READY);
		orderXml.setPublishAt(Instant.now());
		orderXml.setRetryCount(0);
		orderXml.setShipmentTransferId(UUID.randomUUID().toString());
		return orderXml;
	}

	public static Document createTestDocument(Long documentId, Long orderId) {
		DocumentType documentType = new DocumentType();
		documentType.setDocumentTypeId(1);
		documentType.setCategory("ecd");
		documentType.setType("invoice");

		Document document = new Document();
		document.setDocumentId(Objects.requireNonNullElse(documentId, 1L));
		document.setOrderId(Objects.requireNonNullElse(orderId, 1L));
		document.setStatus(DocumentStatus.NEW);
		document.setDocumentType(documentType);
		return document;
	}

	public static TestUtil createInstanceNonSpring() {
		UserServiceMock userServiceMock = new UserServiceMock();
		return new TestUtil(userServiceMock, new OrderGenerator(userServiceMock));
	}

	public static Order generateBasicOrder(OrderType orderType) {
		return new Order() {
			@Override
			public OrderType getOrderType() {
				return orderType;
			}

			@Override
			public double getTotalOrderVolume() {
				return 0;
			}

			@Override
			public Number getTotalOrderWeight() {
				return 0;
			}

			@Override
			public int getTotalAmountPackages() {
				return 0;
			}
		};
	}

	public static QuoteInformation createRoadQuoteInformation() {

		RoadQuoteInformation roadQuoteInformation = new RoadQuoteInformation();
		roadQuoteInformation.setQuoteRequestId(1L);
		roadQuoteInformation.setProduct("E");
		roadQuoteInformation.setCustomerNumber("12345678");
		roadQuoteInformation.setDivision(Division.F);
		roadQuoteInformation.setOrderType(OrderType.ROADFORWARDINGORDER);
		roadQuoteInformation.setCollectionDate(LocalDate.now());
		roadQuoteInformation.setTermCode("DPU");
		roadQuoteInformation.setTailLift(false);
		roadQuoteInformation.setFrostProtected(false);
		roadQuoteInformation.setGoodsCurrency("EUR");
		roadQuoteInformation.setQuoteExpiryDate(OffsetDateTime.now().plusHours(1));
		roadQuoteInformation.setDeliveryOption(DeliveryOptions.AC);
		roadQuoteInformation.setGoodsValue(new BigDecimal(300));
		roadQuoteInformation.setPalletLocations(new BigDecimal(10));
		roadQuoteInformation.setSelfCollector(false);

		return roadQuoteInformation;
	}

	public UserPreferences generateUserPreferences(String presetName, Integer width, Integer height) {
		UserPreferences userPreferences = new UserPreferences();
		userPreferences.setPrinterPresetName(presetName);
		userPreferences.setPrinterWidth(width);
		userPreferences.setPrinterHeight(height);
		return userPreferences;
	}

	public static OrderFurtherAddress buildSeaImporterAddress() {
		OrderFurtherAddress orderFurtherAddress = new OrderFurtherAddress();
		orderFurtherAddress.setAddressType(FurtherAddressType.IMPORTER.getKey());
		orderFurtherAddress.setName("Example GmbH _importer");
		orderFurtherAddress.setName2("Importer 2");
		orderFurtherAddress.setStreet("Imp Street 11");
		orderFurtherAddress.setPostcode("88888");
		orderFurtherAddress.setCity("Newtown");
		orderFurtherAddress.setCountryCode("DE");
		orderFurtherAddress.setTaxID("taxImp");
		OrderContact orderContact = new OrderContact();
		orderContact.setName("Consignora_importeur");
		orderContact.setTelephone("0000777777777");
		orderContact.setMobile("0000777777778");
		orderContact.setEmail("<EMAIL>");
		orderFurtherAddress.setOrderContact(orderContact);
		return orderFurtherAddress;
	}

	public <T extends BasicOrderDto, K extends BasicOrderDto> T changeType(K input, Class<T> clazzType) throws IOException {
		ObjectMapper mapper = getMapper();
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		StringWriter stringWriter = new StringWriter();
		mapper.writeValue(stringWriter, input);
		String jsonContent = stringWriter.toString();
		jsonContent = jsonContent.replace(input.getOrderType(), Objects.requireNonNull(extractTypeFromDto(clazzType)));
		T t = mapper.readValue(jsonContent, clazzType);
		return t;
	}

	private DangerousGoodDataItem createUnNumberDataSet(String unNumber) {
		DangerousGoodDataItem dataSet = new DangerousGoodDataItem();
		dataSet.setUnNumber(unNumber);
		dataSet.setDescription("Test");
		dataSet.setClassificationCode("1");
		dataSet.setPackingGroup("II");
		dataSet.setMainDanger("1");
		dataSet.setNosRequired(true);
		dataSet.setSubsidiaryHazardOne("1");
		dataSet.setSubsidiaryHazardTwo("2.1");
		dataSet.setSubsidiaryHazardThree("3");
		dataSet.setTunnelCode("E");
		return dataSet;

	}

	@Nullable
	private <T extends BasicOrderDto> String extractTypeFromDto(Class<T> clazz) {
		// Get the JsonSubTypes annotation from OrderRequestBodyDto
		JsonSubTypes annotation = OrderRequestBodyDto.class.getAnnotation(JsonSubTypes.class);

		if (annotation != null) {
			for (JsonSubTypes.Type type : annotation.value()) {
				if (type.value().getSimpleName().equals(clazz.getSimpleName())) {
					return type.name();
				}
			}
		} else {
			throw new RuntimeException("Expected annotation on OrderRequestBodyDto; not found");
		}
		return null;
	}
}
