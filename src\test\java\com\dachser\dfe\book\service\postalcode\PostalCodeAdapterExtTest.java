package com.dachser.dfe.book.service.postalcode;

import com.dachser.dfe.book.country.PostalCodeMapper;
import com.dachser.dfe.book.country.PostalCodeMapperImpl;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ErrorIdExternalServiceNotAvailableException;
import com.dachser.dfe.book.model.IrelandPostalCodeDto;
import com.dachser.dfe.book.model.PostcodeValidationDto;
import com.dachser.dfe.masterdata.geo.api.LocationsApi;
import com.dachser.dfe.masterdata.geo.model.GMDDachserPostalCode;
import com.dachser.dfe.masterdata.geo.model.GMDPostalCodeValidation;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestClientException;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PostalCodeAdapterExtTest {

	@InjectMocks
	private PostalCodeAdapterExt dachserPostalCodeAdapterExt;

	@Spy
	private PostalCodeMapper postalCodeMapper = new PostalCodeMapperImpl();

	@Mock
	private LocationsApi locationsApi;

	@Nested
	class FindDachserPostalcodes {

		private List<GMDDachserPostalCode> constructValidDTOs() {
			GMDDachserPostalCode dachserPostalCode = new GMDDachserPostalCode();
			dachserPostalCode.setTown("Dublin");
			dachserPostalCode.setCounty("Testing");
			dachserPostalCode.setEircode("H12");
			dachserPostalCode.setDachserPLZ("12322");
			return List.of(dachserPostalCode);
		}

		@Test
		void returnsPostalcodesWhenApiCallSucceeds() {
			String eircode = "D02";

			when(locationsApi.findDachserPostalcodes(eircode)).thenReturn(constructValidDTOs());

			List<IrelandPostalCodeDto> result = dachserPostalCodeAdapterExt.findDachserPostalcodes(eircode);

			assertEquals(postalCodeMapper.map(constructValidDTOs()), result);
		}

		@Test
		void shouldReturnEmptyListWhenApiReturnsEmptyList() {
			String eircode = "D02";

			when(locationsApi.findDachserPostalcodes(eircode)).thenReturn(List.of());

			List<IrelandPostalCodeDto> result = dachserPostalCodeAdapterExt.findDachserPostalcodes(eircode);

			assertEquals(0, result.size());
		}

		@Test
		void shouldReturnEmptyListWhenApiReturnsNull() {
			String eircode = "D02";

			when(locationsApi.findDachserPostalcodes(eircode)).thenReturn(null);

			List<IrelandPostalCodeDto> result = dachserPostalCodeAdapterExt.findDachserPostalcodes(eircode);

			assertEquals(0, result.size());
		}

		@Test
		void throwsExceptionWhenApiCallFails() {
			String eircode = "D02X285";

			when(locationsApi.findDachserPostalcodes(eircode)).thenThrow(new RestClientException("Service not available"));

			ErrorIdExternalServiceNotAvailableException exception = assertThrows(ErrorIdExternalServiceNotAvailableException.class,
					() -> dachserPostalCodeAdapterExt.findDachserPostalcodes(eircode));

			assertEquals(BookErrorId.ERR_AL_28, exception.getErrorId());
		}

	}

	@Nested
	class ValidatePostCode {

		@Test
		void returnsValidPostCodeWhenApiCallSucceeds() {
			String countryCode = "IE";
			String postalCode = "D02X285";
			PostcodeValidationDto expected = new PostcodeValidationDto().valid(true);

			when(locationsApi.validatePostalCode(anyString(), anyString())).thenReturn(
					new GMDPostalCodeValidation().exampleZipCode(postalCode).valid(true).countryPostalCodeState(GMDPostalCodeValidation.CountryPostalCodeStateEnum.UNDEFINED)
							.validRegex(true));

			PostcodeValidationDto result = dachserPostalCodeAdapterExt.validatePostCode(countryCode, postalCode);

			assertEquals(expected, result);
		}

		@Test
		void returnsInvalidPostCodeWhenApiCallSucceedsWithInvalidRegex() {
			String countryCode = "IE";
			String postalCode = "D02X285";
			String exampleZipCode = "D02X000";
			String regex = "D02X[0-9]{3}";
			PostcodeValidationDto expected = new PostcodeValidationDto().valid(false).examplePostcode(exampleZipCode).validatedPattern(regex);

			when(locationsApi.validatePostalCode(anyString(), anyString())).thenReturn(
					new GMDPostalCodeValidation().exampleZipCode(exampleZipCode).valid(false).countryPostalCodeState(GMDPostalCodeValidation.CountryPostalCodeStateEnum.MANDATORY)
							.validRegex(false).regularExpression(regex));

			PostcodeValidationDto result = dachserPostalCodeAdapterExt.validatePostCode(countryCode, postalCode);

			assertEquals(expected, result);
		}

		@Test
		void returnsInvalidPostCodeWhenApiCallSucceeds() {
			String countryCode = "IE";
			String postalCode = "D02X285";
			PostcodeValidationDto expected = new PostcodeValidationDto().valid(false);

			when(locationsApi.validatePostalCode(anyString(), anyString())).thenReturn(
					new GMDPostalCodeValidation().exampleZipCode(postalCode).valid(false).countryPostalCodeState(GMDPostalCodeValidation.CountryPostalCodeStateEnum.UNDEFINED).validRegex(false));

			PostcodeValidationDto result = dachserPostalCodeAdapterExt.validatePostCode(countryCode, postalCode);

			assertEquals(expected, result);
		}

		@Test
		void returnsValidPostCodeWhenApiCallSucceedsWithValidRegex() {
			String countryCode = "IE";
			String postalCode = "D02X285";
			PostcodeValidationDto expected = new PostcodeValidationDto().valid(true);

			when(locationsApi.validatePostalCode(anyString(), anyString())).thenReturn(
					new GMDPostalCodeValidation().exampleZipCode(postalCode).valid(true).countryPostalCodeState(GMDPostalCodeValidation.CountryPostalCodeStateEnum.MANDATORY)
							.validRegex(true));

			PostcodeValidationDto result = dachserPostalCodeAdapterExt.validatePostCode(countryCode, postalCode);

			assertEquals(expected, result);
		}

		@Test
		void shouldReturnTrueIfValidationIsNotPossibleWhenServiceIsNotAvailable() {
			String countryCode = "IE";
			String postalCode = "D02X285";

			when(locationsApi.validatePostalCode(anyString(), anyString())).thenThrow(new RestClientException("Service not available"));

			PostcodeValidationDto result = dachserPostalCodeAdapterExt.validatePostCode(countryCode, postalCode);

			assertTrue(result.getValid());
		}

		@Test
		void shouldReturnTrueIfValidationIsNotPossibleWhenExceptionOccurs() {
			String countryCode = "IE";
			String postalCode = "D02X285";

			when(locationsApi.validatePostalCode(anyString(), anyString())).thenThrow(new RuntimeException("Unexpected error"));

			PostcodeValidationDto result = dachserPostalCodeAdapterExt.validatePostCode(countryCode, postalCode);

			assertTrue(result.getValid());
		}
	}

}