package com.dachser.dfe.book.generaldata;

import com.dachser.dfe.book.SpringBasedLocalMockTest;
import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.country.Country;
import com.dachser.dfe.book.generaldata.country.GeneralDataCountryMapper;
import com.dachser.dfe.book.generaldata.country.GeneralDataCountryMapperImpl;
import com.dachser.dfe.book.generaldata.term.TermMapper;
import com.dachser.dfe.generaldata.api.CountryApi;
import com.dachser.dfe.generaldata.api.LanguageApi;
import com.dachser.dfe.generaldata.api.PackagingApi;
import com.dachser.dfe.generaldata.api.TermsApi;
import com.dachser.dfe.generaldata.model.GDContainerTypeDto;
import com.dachser.dfe.generaldata.model.GDCountryDto;
import com.dachser.dfe.generaldata.model.GDTermDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GeneralDataAdapterTest {

	@Nested
	class UnitTest {

		@InjectMocks
		GeneralDataAdapterExt generalDataAdapter;

		@Mock
		CountryApi countryApi;

		@Mock
		TermsApi termsApi;

		@Mock
		LanguageApi languageApi;

		@Mock
		private TermMapper termMapper;

		@Spy
		private WhiteListFilterConfiguration whiteListFilterConfiguration;

		@Spy
		private GeneralDataCountryMapper countryMapper = new GeneralDataCountryMapperImpl();

		@BeforeEach
		void setupWhiteListFilters() {
			final WhiteListFilterConfiguration.TermFilter termFilter = new WhiteListFilterConfiguration.TermFilter();
			termFilter.setFreight(List.of("011", "031", "081", "082", "083", "084"));
			termFilter.setInco(List.of());
			whiteListFilterConfiguration.setTerms(termFilter);
		}

		@Mock
		PackagingApi packagingApi;

		@Test
		void getContainerTypesTest() {
			GDContainerTypeDto containerType = new GDContainerTypeDto();
			containerType.setKey("OT-20");
			containerType.setSize(20);
			containerType.setType("OT");
			containerType.setDescription("Open Top");

			when(packagingApi.getContainerTypes()).thenReturn(List.of(containerType));

			final List<GDContainerTypeDto> containerTypes = generalDataAdapter.getContainerTypes();

			assertEquals(1, containerTypes.size());
		}

		@Test
		void shouldGetCountries() {
			when(countryApi.getCountries("en")).thenReturn(countryList());
			List<Country> countries = generalDataAdapter.getCountries("en");
			assertEquals(3, countries.size());
		}

		@Test
		void shouldGetMasterdataLanguages() {
			when(languageApi.getLanguageMapping()).thenReturn(Map.of("en", "001", "de", " "));
			Map<String, String> languages = generalDataAdapter.getLanguages();
			assertEquals(2, languages.size());
		}

		private List<GDCountryDto> countryList() {
			return List.of(new GDCountryDto().countryCode("DE").dachserCode("D").label("Germany"), new GDCountryDto().countryCode("FR").dachserCode("F").label("France"),
					new GDCountryDto().countryCode("GB").dachserCode("GB").label("Great Britain"));
		}

	}

	@Nested
	@SpringBasedLocalMockTest
	class SpringTest {

		@Autowired
		GeneralDataAdapter generalDataAdapter;

		@Test
		void getMockedTerms_shouldReturnResult() {
			List<GDTermDto> terms = generalDataAdapter.getAllActiveTerms(null);
			assertThat(terms).isNotEmpty();
		}

		@Test
		void getMockedContainerTypesTest() {
			final List<GDContainerTypeDto> containerTypes = generalDataAdapter.getContainerTypes();
			assertEquals(5, containerTypes.size());

		}

		@Nested
		class GetAllCountries {
			@Test
			void shouldReturnMockedCountryList() {
				final List<Country> allCountries = generalDataAdapter.getCountries("en");
				assertEquals(GeneralDataAdapterMock.getCountriesCount(), allCountries.size());
				assertEquals(GeneralDataAdapterMock.getCountriesCount() - 1, allCountries.stream().filter(Country::isPostcodeMandatory).count());
			}

		}

		@Nested
		class GetAllMasterdataLanguages {
			@Test
			void shouldReturnMockedMasterdataLanguageList() {
				final Map<String, String> allMasterdataLanguages = generalDataAdapter.getLanguages();
				assertEquals(GeneralDataAdapterMock.getMasterdataLanguagesCount(), allMasterdataLanguages.size());
			}

		}
	}

}