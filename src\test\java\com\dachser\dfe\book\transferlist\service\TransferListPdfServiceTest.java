package com.dachser.dfe.book.transferlist.service;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.dangerousgoods.DangerousGoodsTransferListService;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.DachserCodeNotMappedException;
import com.dachser.dfe.book.jpa.OrderSsccRepository;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.DangerousGoodDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OptionIntegerDto;
import com.dachser.dfe.book.model.OrderLineDetailDto;
import com.dachser.dfe.book.model.PackagingInfoDto;
import com.dachser.dfe.book.model.PackingPositionDto;
import com.dachser.dfe.book.model.RoadOrderLineDto;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.model.TransferListAddressDto;
import com.dachser.dfe.book.model.TransferListPdfItemDto;
import com.dachser.dfe.book.model.TransferListPdfQueryObjectDto;
import com.dachser.dfe.book.model.TransferListPdfRequestDto;
import com.dachser.dfe.book.model.TransferListQueryObjectDto;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderRepository;
import com.dachser.dfe.book.order.OrderRepositoryFacade;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderSscc;
import com.dachser.dfe.book.order.generator.OrderGenerator;
import com.dachser.dfe.book.order.mapper.FreightTermCodeMapperImpl;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.overview.CachedOrderOverviewService;
import com.dachser.dfe.book.packaging.PackagingOptionsService;
import com.dachser.dfe.book.product.road.RoadProductsService;
import com.dachser.dfe.book.service.freightterm.FreightTermService;
import com.dachser.dfe.book.transferlist.entity.VTransferList;
import com.dachser.dfe.book.transferlist.exception.PdfGeneratorException;
import com.dachser.dfe.book.transferlist.exception.PdfTransferListNoDataException;
import com.dachser.dfe.book.transferlist.exception.TrackablePackingAidServiceNotAvailableException;
import com.dachser.dfe.book.transferlist.mapper.TransferListPdfMapperImpl;
import com.dachser.dfe.book.transferlist.model.TransferListAddressType;
import com.dachser.dfe.book.transferlist.pdf.PdfService;
import com.dachser.dfe.book.transferlist.repository.VTransferListRepository;
import com.dachser.dfe.book.transferlist.supervisingbranch.BranchService;
import com.dachser.dfe.book.transferlist.supervisingbranch.BranchServiceMock;
import com.dachser.dfe.book.transferlist.trackablepackingaid.TrackablePackingAidService;
import com.dachser.dfe.book.transferlist.trackablepackingaid.TrackablePackingAidServiceMock;
import com.dachser.dfe.book.user.UserContextService;
import com.dachser.dfe.dfefiltersorthelper.service.SortAndFilterService;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.i18n.LocaleContextHolder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Locale;
import java.util.Optional;

import static com.dachser.dfe.book.TestUtil.generateEQDangerousGoodDto;
import static com.dachser.dfe.book.TestUtil.generateLQDangerousGoodDto;
import static com.dachser.dfe.book.TestUtil.generateOrderLineDetailDto;
import static com.dachser.dfe.book.TestUtil.generatePackingPositionDto;
import static com.dachser.dfe.book.TestUtil.generateVTransferList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.notNull;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = { TransferListPdfServiceImpl.class, FreightTermCodeMapperImpl.class, FreightTermService.class, WhiteListFilterConfiguration.class })
@ExtendWith(MockitoExtension.class)
class TransferListPdfServiceTest {
	private static final int BRANCH_ID = 100;

	private static final String TRANSFER_LIST_DATE = "2024-01-01";

	private static final String PRINT_DATE_TIME = "2023-09-27, 02:45 PM";

	private static final String NUMBER_LOCALE_EN = "en";

	private static final String NUMBER_LOCALE_DE = "de";

	private static final String COUNTRY_CODE = "DE";

	private static final String TRANSFER_LIST_NAME = "transfer-list-20240101-p12345-1.pdf";

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	@Autowired
	private TransferListPdfService sut;

	@MockBean
	private SortAndFilterService<VTransferList> sortAndFilterService;

	@MockBean
	private FreightTermService freightTermService;

	@MockBean
	private UserContextService userContextService;

	@Autowired
	private WhiteListFilterConfiguration whiteListFilterConfiguration;

	@Autowired
	private FreightTermCodeMapperImpl freightTermMapper;

	@MockBean
	private VTransferListRepository vTransferListRepository;

	@MockBean
	private RoadOrderLineService roadOrderLineService;

	@MockBean
	private PackingPositionService packingPositionService;

	@MockBean
	private RoadProductsService productsService;

	@MockBean
	private CachedOrderOverviewService cachedOrderOverviewService;

	@SpyBean
	private TransferListPdfMapperImpl transferListPdfMapper;

	@MockBean
	private CountryService countryService;

	@MockBean
	private OrderSsccRepository orderSsccRepository;

	@MockBean
	private PdfService pdfService;

	@MockBean
	private BranchService branchService;

	@MockBean
	private TrackablePackingAidService trackablePackingAidService;

	@MockBean
	private PackagingOptionsService packagingOptionsService;

	@MockBean
	private OrderRepository orderRepository;

	@MockBean
	private DangerousGoodsTransferListService dangerousGoodsTransferListService;

	@MockBean
	private OrderRepositoryFacade orderRepositoryFacade;

	private static Locale defaultLocale;

	private TrackablePackingAidServiceMock trackablePackingAidServiceMock;

	@BeforeAll
	static void setUp() {
		defaultLocale = LocaleContextHolder.getLocale();
	}

	@BeforeEach
	void beforeEach() {
		when(countryService.mapToDachserCountryCode(anyString())).thenReturn("D  ");
		Order order = new ForwardingOrder();
		order.setBranchId(BRANCH_ID);
		when(orderRepositoryFacade.loadOrderById(anyLong())).thenReturn(order);
		LocaleContextHolder.setLocale(Locale.forLanguageTag(NUMBER_LOCALE_DE));
		trackablePackingAidServiceMock = new TrackablePackingAidServiceMock();
	}

	@AfterAll
	static void cleanUp() {
		LocaleContextHolder.setLocale(defaultLocale);
	}

	@Nested
	class GetTransferListResponsePage {

		@Test
		void createTransferListPdfRequest() {
			LocaleContextHolder.setLocale(Locale.ENGLISH);
			TransferListPdfQueryObjectDto pdfQueryObject = createPdfQueryObject(TRANSFER_LIST_DATE, PRINT_DATE_TIME, NUMBER_LOCALE_EN, "E2454AD567");
			List<OrderSscc> orderSsccs = List.of(generateOrderSsccs("1"), generateOrderSsccs("2"));
			LocalDate collectionDate = LocalDate.of(2024, 6, 19);
			OptionDto packagingOptionEU = new OptionDto().code("EU").description("Euroflachpalette");
			OptionDto packagingOptionBX = new OptionDto().code("BX").description("Box");
			OrderAddress consigneeAddress = OrderGenerator.generateOrderAddress("_consignee", COUNTRY_CODE, false, null);
			OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", COUNTRY_CODE, false, null);
			principalAddress.setOrderAddressId(12345L);
			VTransferList vTransferList = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddress, principalAddress, null);

			List<OrderLineDetailDto> orderLineDetailDtos = List.of(TestUtil.generateOrderLineDetailDto("EU", "Palette", 2, null, List.of()),
					TestUtil.generateOrderLineDetailDto("EU", "Palette", 3, null, List.of()), TestUtil.generateOrderLineDetailDto("KT", "Carton", 6, null, List.of()),
					TestUtil.generateOrderLineDetailDto("BX", "Box", 1, null, List.of()));
			List<PackingPositionDto> packingPositionDtos = List.of(generatePackingPositionDto(1, TestUtil.generateRoadOrderLineDto()),
					generatePackingPositionDto(2, TestUtil.generateRoadOrderLineDto()));

			DeliveryProductDto product = new DeliveryProductDto().code(vTransferList.getProduct()).description("description");
			BranchServiceMock branchServiceMock = new BranchServiceMock();
			Optional<TransferListAddressDto> supervisingBranchAddress = branchServiceMock.getBranchAddress(1);

			when(trackablePackingAidService.categorizeTransferList(anyString(), anyString(), any(), any(), any())).thenReturn(
					trackablePackingAidServiceMock.categorizeTransferList(COUNTRY_CODE, COUNTRY_CODE, Division.F, LocalDate.parse(TRANSFER_LIST_DATE), List.of("EU")));

			String dateString = transferListPdfMapper.formattedDateFromOffsetDateTime(OffsetDateTime.now(), "yyyy-MM-dd", Locale.ENGLISH);
			String printDateTimeString = transferListPdfMapper.formattedDateTimeFromOffsetDateTime(OffsetDateTime.now(), "yyyy-MM-dd, hh:mm a", Locale.ENGLISH);

			when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList));
			when(orderRepository.findById(any())).thenReturn(Optional.of(new ForwardingOrder()));
			when(userContextService.getBusinessDomain()).thenReturn(1);
			when(productsService.getProductByKey(anyString(), any(Division.class))).thenReturn(Optional.of(product));
			when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(orderLineDetailDtos);
			when(packingPositionService.getRoadPackingPositions(any())).thenReturn(packingPositionDtos);
			when(orderSsccRepository.countOrderSsccsByOrderIds(any())).thenReturn(orderSsccs.size());
			when(branchService.getBranchAddress(anyInt())).thenReturn(supervisingBranchAddress);
			when(transferListPdfMapper.formattedDateFromOffsetDateTime(any(), anyString(), notNull())).thenReturn(printDateTimeString);
			when(transferListPdfMapper.formattedDateFromLocalDate(any(), anyString(), notNull())).thenReturn(dateString);
			when(trackablePackingAidService.categorizeTransferList(anyString(), anyString(), any(), any(), any())).thenReturn(
					trackablePackingAidServiceMock.categorizeTransferList(COUNTRY_CODE, COUNTRY_CODE, Division.F, collectionDate, List.of("EU")));
			when(packagingOptionsService.getPackagingOptions(Segment.ROAD)).thenReturn(List.of(packagingOptionEU, packagingOptionBX));

			sut.getTransferListPdf(pdfQueryObject);

			TransferListPdfRequestDto requestDto = createRequestDto(List.of(vTransferList), orderLineDetailDtos, packingPositionDtos, List.of(), printDateTimeString, dateString,
					NUMBER_LOCALE_EN);
			supervisingBranchAddress.ifPresent(jsonTransferListAddress -> requestDto.setSupervisingBranch(
					new TransferListAddressDto().name(jsonTransferListAddress.getName()).name2(jsonTransferListAddress.getName2()).name3(jsonTransferListAddress.getName3())
							.postcode(jsonTransferListAddress.getPostcode()).city(jsonTransferListAddress.getCity()).countryCode(jsonTransferListAddress.getCountryCode())
							.street(jsonTransferListAddress.getStreet()).street2(jsonTransferListAddress.getStreet2())));
			requestDto.setNumberOfNveSccs(orderSsccs.size());
			requestDto.setTrackablePackingAids(List.of(new PackagingInfoDto().aid("Euroflachpalette").count(12), new PackagingInfoDto().aid("KT").count(6)));
			requestDto.setNonTrackablePackingAids(List.of(new PackagingInfoDto().aid("Box").count(1)));

			verify(pdfService).createPdf(requestDto, TRANSFER_LIST_NAME, NUMBER_LOCALE_EN);
			verify(transferListPdfMapper, times(1)).mapTransferListPdfItem(any(VTransferList.class), anyString(), any(Locale.class));
			verify(countryService, times(2)).mapToDachserCountryCode(anyString());
			verify(branchService).getBranchAddress(BRANCH_ID);
			verify(orderRepositoryFacade, times(2)).loadOrderById(vTransferList.getOrderId());
		}

		@Test
		void createTransferListPdfRequest_sortsByConsigneeAddressName() {
			TransferListPdfQueryObjectDto pdfQueryObject = createPdfQueryObject(TRANSFER_LIST_DATE, PRINT_DATE_TIME, NUMBER_LOCALE_DE, "E2454AD567");

			OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", COUNTRY_CODE, false, null);
			principalAddress.setOrderAddressId(12345L);

			OrderAddress consigneeAddressB = OrderGenerator.generateOrderAddress("B", COUNTRY_CODE, false, null);
			VTransferList vTransferList1 = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddressB, principalAddress, null);
			OrderAddress consigneeAddressA = OrderGenerator.generateOrderAddress("A", COUNTRY_CODE, false, null);
			VTransferList vTransferList2 = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddressA, principalAddress, null);
			OrderAddress consigneeAddressC = OrderGenerator.generateOrderAddress("C", COUNTRY_CODE, false, null);
			VTransferList vTransferList3 = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddressC, principalAddress, null);
			OrderAddress consigneeAddressNullName = OrderGenerator.generateOrderAddress("", COUNTRY_CODE, false, null);
			consigneeAddressNullName.setName(null);
			VTransferList vTransferListNullName = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddressNullName, principalAddress, null);

			String dateString = transferListPdfMapper.formattedDateFromOffsetDateTime(OffsetDateTime.now(), "yyyy-MM-dd", Locale.ENGLISH);

			when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(
					List.of(vTransferList1, vTransferListNullName, vTransferList2, vTransferList3));
			when(orderRepository.findById(any())).thenReturn(Optional.of(new ForwardingOrder()));
			when(productsService.getProductByKey(anyString(), any(Division.class))).thenReturn(Optional.empty());
			when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(List.of());
			when(packingPositionService.getRoadPackingPositions(any())).thenReturn(List.of());
			when(orderSsccRepository.countOrderSsccsByOrderIds(any())).thenReturn(0);
			when(branchService.getBranchAddress(anyInt())).thenReturn(Optional.empty());
			when(transferListPdfMapper.formattedDateFromOffsetDateTime(any(), anyString(), notNull())).thenReturn(dateString);
			when(transferListPdfMapper.formattedDateFromLocalDate(any(), anyString(), notNull())).thenReturn(dateString);
			when(trackablePackingAidService.categorizeTransferList(anyString(), anyString(), any(), any(), any())).thenReturn(
					trackablePackingAidServiceMock.categorizeTransferList(COUNTRY_CODE, COUNTRY_CODE, Division.F, LocalDate.now(), List.of("EU")));

			sut.getTransferListPdf(pdfQueryObject);

			TransferListPdfRequestDto requestDto = createRequestDto(List.of(vTransferList2, vTransferList1, vTransferList3, vTransferListNullName), List.of(), List.of(), List.of(),
					dateString, dateString, NUMBER_LOCALE_DE);

			verify(pdfService).createPdf(requestDto, TRANSFER_LIST_NAME, NUMBER_LOCALE_DE);
		}

		@Test
		void createTransferListPdfRequestHasEmptyTrackablePackingAidsWhenCountryServiceThrowsException() {
			TransferListPdfQueryObjectDto pdfQueryObject = createPdfQueryObject(TRANSFER_LIST_DATE, PRINT_DATE_TIME, NUMBER_LOCALE_DE, "E2454AD567");

			OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", COUNTRY_CODE, false, null);
			principalAddress.setOrderAddressId(12345L);

			OrderAddress consigneeAddressB = OrderGenerator.generateOrderAddress("B", COUNTRY_CODE, false, null);
			VTransferList vTransferList1 = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddressB, principalAddress, null);

			String dateString = transferListPdfMapper.formattedDateFromOffsetDateTime(OffsetDateTime.now(), "yyyy-MM-dd", Locale.ENGLISH);

			List<OrderLineDetailDto> orderLineDetailDtos = List.of(TestUtil.generateOrderLineDetailDto("EU", "Palette", 2, null, List.of()),
					TestUtil.generateOrderLineDetailDto("EU", "Palette", 3, null, List.of()), TestUtil.generateOrderLineDetailDto("KT", "Carton", 6, null, List.of()),
					TestUtil.generateOrderLineDetailDto("BX", "Box", 1, null, List.of()));
			List<PackingPositionDto> packingPositionDtos = List.of(generatePackingPositionDto(1, TestUtil.generateRoadOrderLineDto()),
					generatePackingPositionDto(2, TestUtil.generateRoadOrderLineDto()));

			when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList1));
			when(orderRepository.findById(any())).thenReturn(Optional.of(new ForwardingOrder()));
			when(productsService.getProductByKey(anyString(), any(Division.class))).thenReturn(Optional.empty());
			when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(orderLineDetailDtos);
			when(packingPositionService.getRoadPackingPositions(any())).thenReturn(packingPositionDtos);
			when(orderSsccRepository.countOrderSsccsByOrderIds(any())).thenReturn(0);
			when(branchService.getBranchAddress(anyInt())).thenReturn(Optional.empty());
			when(transferListPdfMapper.formattedDateFromOffsetDateTime(any(), anyString(), notNull())).thenReturn(dateString);
			when(transferListPdfMapper.formattedDateFromLocalDate(any(), anyString(), notNull())).thenReturn(dateString);
			when(trackablePackingAidService.categorizeTransferList(anyString(), anyString(), any(), any(), any())).thenReturn(
					trackablePackingAidServiceMock.categorizeTransferList(COUNTRY_CODE, COUNTRY_CODE, Division.F, LocalDate.now(), List.of("EU")));

			when(countryService.mapToDachserCountryCode(anyString())).thenThrow(new DachserCodeNotMappedException("Country code not mapped"));

			sut.getTransferListPdf(pdfQueryObject);

			TransferListPdfRequestDto requestDto = createRequestDto(List.of(vTransferList1), orderLineDetailDtos, packingPositionDtos, List.of(), dateString, dateString,
					NUMBER_LOCALE_DE);

			verify(pdfService).createPdf(requestDto, TRANSFER_LIST_NAME, NUMBER_LOCALE_DE);
			verify(trackablePackingAidService, never()).categorizeTransferList(anyString(), anyString(), any(), any(), anyList());
		}

		@Test
		void createTransferListPdfRequestHasEmptyTrackablePackingAidsWhenTrackablePackingAidServiceThrowsException() {
			TransferListPdfQueryObjectDto pdfQueryObject = createPdfQueryObject(TRANSFER_LIST_DATE, PRINT_DATE_TIME, NUMBER_LOCALE_DE, "E2454AD567");

			OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", COUNTRY_CODE, false, null);
			principalAddress.setOrderAddressId(12345L);

			OrderAddress consigneeAddressB = OrderGenerator.generateOrderAddress("B", COUNTRY_CODE, false, null);
			VTransferList vTransferList1 = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddressB, principalAddress, null);

			String dateString = transferListPdfMapper.formattedDateFromOffsetDateTime(OffsetDateTime.now(), "yyyy-MM-dd", Locale.ENGLISH);

			List<OrderLineDetailDto> orderLineDetailDtos = List.of(TestUtil.generateOrderLineDetailDto("EU", "Palette", 2, null, List.of()),
					TestUtil.generateOrderLineDetailDto("EU", "Palette", 3, null, List.of()), TestUtil.generateOrderLineDetailDto("KT", "Carton", 6, null, List.of()),
					TestUtil.generateOrderLineDetailDto("BX", "Box", 1, null, List.of()));
			List<PackingPositionDto> packingPositionDtos = List.of(generatePackingPositionDto(1, TestUtil.generateRoadOrderLineDto()),
					generatePackingPositionDto(2, TestUtil.generateRoadOrderLineDto()));

			when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList1));
			when(orderRepository.findById(any())).thenReturn(Optional.of(new ForwardingOrder()));
			when(productsService.getProductByKey(anyString(), any(Division.class))).thenReturn(Optional.empty());
			when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(orderLineDetailDtos);
			when(packingPositionService.getRoadPackingPositions(any())).thenReturn(packingPositionDtos);
			when(orderSsccRepository.countOrderSsccsByOrderIds(any())).thenReturn(0);
			when(branchService.getBranchAddress(anyInt())).thenReturn(Optional.empty());
			when(transferListPdfMapper.formattedDateFromOffsetDateTime(any(), anyString(), notNull())).thenReturn(dateString);
			when(transferListPdfMapper.formattedDateFromLocalDate(any(), anyString(), notNull())).thenReturn(dateString);
			when(trackablePackingAidService.categorizeTransferList(anyString(), anyString(), any(), any(), anyList())).thenThrow(
					new TrackablePackingAidServiceNotAvailableException("External service not available: TrackablePackingAidServiceExt"));
			when(countryService.mapToDachserCountryCode(anyString())).thenThrow(new DachserCodeNotMappedException("Country code not mapped"));

			sut.getTransferListPdf(pdfQueryObject);

			TransferListPdfRequestDto requestDto = createRequestDto(List.of(vTransferList1), orderLineDetailDtos, packingPositionDtos, List.of(), dateString, dateString,
					NUMBER_LOCALE_DE);

			verify(pdfService).createPdf(requestDto, TRANSFER_LIST_NAME, NUMBER_LOCALE_DE);
		}

		@Test
		void getTransferListPdf() {
			TransferListPdfQueryObjectDto pdfQueryObject = createPdfQueryObject(TRANSFER_LIST_DATE, PRINT_DATE_TIME, NUMBER_LOCALE_DE, "E2454AD567");
			LocalDate collectionDate = LocalDate.of(2024, 6, 19);
			OrderAddress consigneeAddress = OrderGenerator.generateOrderAddress("_consignee", COUNTRY_CODE, false, null);
			OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", COUNTRY_CODE, false, null);
			principalAddress.setOrderAddressId(12345L);
			VTransferList vTransferList = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddress, principalAddress, null);

			List<OrderLineDetailDto> orderLineDetailDtos = List.of(generateOrderLineDetailDto(), generateOrderLineDetailDto());
			DeliveryProductDto product = new DeliveryProductDto().code(vTransferList.getProduct()).description("description");
			List<PackingPositionDto> packingPositionDtos = List.of(generatePackingPositionDto(1, TestUtil.generateRoadOrderLineDto()),
					generatePackingPositionDto(2, TestUtil.generateRoadOrderLineDto()));

			when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList));
			when(orderRepository.findById(any())).thenReturn(Optional.of(new ForwardingOrder()));
			when(productsService.getProductByKey(anyString(), any(Division.class))).thenReturn(Optional.of(product));
			when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(orderLineDetailDtos);
			when(packingPositionService.getRoadPackingPositions(any())).thenReturn(packingPositionDtos);
			BranchServiceMock branchServiceMock = new BranchServiceMock();
			when(branchService.getBranchAddress(anyInt())).thenReturn(branchServiceMock.getBranchAddress(1));
			when(trackablePackingAidService.categorizeTransferList(anyString(), anyString(), any(), any(), any())).thenReturn(
					trackablePackingAidServiceMock.categorizeTransferList(COUNTRY_CODE, COUNTRY_CODE, Division.F, collectionDate, List.of("EU")));
			when(packagingOptionsService.getPackagingOptions(Segment.ROAD)).thenReturn(List.of(new OptionDto()));

			sut.getTransferListPdf(pdfQueryObject);

			verify(transferListPdfMapper, times(1)).mapTransferListPdfItem(any(VTransferList.class), anyString(), any(Locale.class));
		}

		@Test
		void getTransferListPdfThrowsPdfGeneratorException() {
			TransferListPdfQueryObjectDto pdfQueryObject = createPdfQueryObject(TRANSFER_LIST_DATE, PRINT_DATE_TIME, NUMBER_LOCALE_DE, "E2454AD567");
			OrderAddress consigneeAddress = OrderGenerator.generateOrderAddress("_consignee", COUNTRY_CODE, false, null);
			OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", COUNTRY_CODE, false, null);
			principalAddress.setOrderAddressId(12345L);
			VTransferList vTransferList = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddress, principalAddress, null);
			LocalDate collectionDate = LocalDate.of(2024, 6, 19);

			when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList));
			when(pdfService.createPdf(any(), any(), any())).thenThrow(RuntimeException.class);
			when(trackablePackingAidService.categorizeTransferList(anyString(), anyString(), any(), any(), any())).thenReturn(
					trackablePackingAidServiceMock.categorizeTransferList(COUNTRY_CODE, COUNTRY_CODE, Division.F, collectionDate, List.of("EU")));
			when(packagingOptionsService.getPackagingOptions(Segment.ROAD)).thenReturn(List.of(new OptionDto()));

			PdfGeneratorException exception = assertThrows(PdfGeneratorException.class, () -> sut.getTransferListPdf(pdfQueryObject));
			assertThat(exception.getErrorId()).isEqualTo(BookErrorId.ERR_PF_01);
		}

		@Test
		void getTransferListPdfThrowsPdfTransferListNoDataException() {
			final String transferListAddressHash = "ABCDEF";
			TransferListPdfQueryObjectDto pdfQueryObject = createPdfQueryObject(TRANSFER_LIST_DATE, PRINT_DATE_TIME, NUMBER_LOCALE_DE, transferListAddressHash);

			when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of());
			when(pdfService.createPdf(any(), any(), any())).thenThrow(RuntimeException.class);

			PdfTransferListNoDataException exception = assertThrows(PdfTransferListNoDataException.class, () -> sut.getTransferListPdf(pdfQueryObject));
			String message = String.format("No transfer list items found for date '%s' and address hash '%s'", TRANSFER_LIST_DATE, transferListAddressHash);
			assertThat(exception.getMessage()).isEqualTo(message);
		}

		@Nested
		class DangerousGoods {

			@Test
			void generateTransferListPdfWithDangerousGoodsEQ() {
				TransferListPdfQueryObjectDto pdfQueryObject = createPdfQueryObject(TRANSFER_LIST_DATE, PRINT_DATE_TIME, NUMBER_LOCALE_DE, "E2454AD567");

				OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", COUNTRY_CODE, false, null);
				principalAddress.setOrderAddressId(12345L);

				OrderAddress consigneeAddressB = OrderGenerator.generateOrderAddress("B", COUNTRY_CODE, false, null);
				VTransferList vTransferList1 = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddressB, principalAddress, null);

				String dateString = transferListPdfMapper.formattedDateFromOffsetDateTime(OffsetDateTime.now(), "yyyy-MM-dd", Locale.ENGLISH);

				List<DangerousGoodDto> dangerousGoodDtos = List.of(TestUtil.generateEQDangerousGoodDto(1));
				List<OrderLineDetailDto> orderLineDetailDtos = List.of(TestUtil.generateOrderLineDetailDto("EU", "Palette", 2, null, dangerousGoodDtos),
						TestUtil.generateOrderLineDetailDto("EU", "Palette", 3, null, List.of()), TestUtil.generateOrderLineDetailDto("KT", "Carton", 6, null, List.of()),
						TestUtil.generateOrderLineDetailDto("BX", "Box", 1, null, List.of()));


				when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList1));
				when(orderRepository.findById(any())).thenReturn(Optional.of(new ForwardingOrder()));
				when(productsService.getProductByKey(anyString(), any(Division.class))).thenReturn(Optional.empty());
				when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(orderLineDetailDtos);
				when(orderSsccRepository.countOrderSsccsByOrderIds(any())).thenReturn(0);
				when(branchService.getBranchAddress(anyInt())).thenReturn(Optional.empty());
				when(transferListPdfMapper.formattedDateFromOffsetDateTime(any(), anyString(), notNull())).thenReturn(dateString);
				when(transferListPdfMapper.formattedDateFromLocalDate(any(), anyString(), notNull())).thenReturn(dateString);
				when(trackablePackingAidService.categorizeTransferList(anyString(), anyString(), any(), any(), any())).thenReturn(
						trackablePackingAidServiceMock.categorizeTransferList(COUNTRY_CODE, COUNTRY_CODE, Division.F, LocalDate.now(), List.of("EU")));

				when(countryService.mapToDachserCountryCode(anyString())).thenThrow(new DachserCodeNotMappedException("Country code not mapped"));

				sut.getTransferListPdf(pdfQueryObject);

				TransferListPdfRequestDto requestDto = createRequestDtoDangerousGoods(List.of(vTransferList1), orderLineDetailDtos, List.of(), dangerousGoodDtos, dateString,
						dateString, NUMBER_LOCALE_DE, 1, null, List.of(), List.of(new OptionIntegerDto().code(4).value(0)), 1, null);

				verify(pdfService).createPdf(requestDto, TRANSFER_LIST_NAME, NUMBER_LOCALE_DE);
				verify(trackablePackingAidService, never()).categorizeTransferList(anyString(), anyString(), any(), any(), anyList());
			}

			@Test
			void generateTransferListPdfWithDangerousGoodsLQ() {
				TransferListPdfQueryObjectDto pdfQueryObject = createPdfQueryObject(TRANSFER_LIST_DATE, PRINT_DATE_TIME, NUMBER_LOCALE_DE, "E2454AD567");

				OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", COUNTRY_CODE, false, null);
				principalAddress.setOrderAddressId(12345L);

				OrderAddress consigneeAddressB = OrderGenerator.generateOrderAddress("B", COUNTRY_CODE, false, null);
				VTransferList vTransferList1 = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddressB, principalAddress, null);

				String dateString = transferListPdfMapper.formattedDateFromOffsetDateTime(OffsetDateTime.now(), "yyyy-MM-dd", Locale.ENGLISH);

				List<DangerousGoodDto> dangerousGoodDtos = List.of(TestUtil.generateLQDangerousGoodDto(BigDecimal.TEN, "2"));
				List<OrderLineDetailDto> orderLineDetailDtos = List.of(TestUtil.generateOrderLineDetailDto("EU", "Palette", 2, null, dangerousGoodDtos),
						TestUtil.generateOrderLineDetailDto("EU", "Palette", 3, null, List.of()), TestUtil.generateOrderLineDetailDto("KT", "Carton", 6, null, List.of()),
						TestUtil.generateOrderLineDetailDto("BX", "Box", 1, null, List.of()));


				when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList1));
				when(productsService.getProductByKey(anyString(), any(Division.class))).thenReturn(Optional.empty());
				when(orderRepository.findById(any())).thenReturn(Optional.of(new ForwardingOrder()));
				when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(orderLineDetailDtos);
				when(orderSsccRepository.countOrderSsccsByOrderIds(any())).thenReturn(0);
				when(branchService.getBranchAddress(anyInt())).thenReturn(Optional.empty());
				when(transferListPdfMapper.formattedDateFromOffsetDateTime(any(), anyString(), notNull())).thenReturn(dateString);
				when(transferListPdfMapper.formattedDateFromLocalDate(any(), anyString(), notNull())).thenReturn(dateString);
				when(trackablePackingAidService.categorizeTransferList(anyString(), anyString(), any(), any(), any())).thenReturn(
						trackablePackingAidServiceMock.categorizeTransferList(COUNTRY_CODE, COUNTRY_CODE, Division.F, LocalDate.now(), List.of("EU")));

				when(countryService.mapToDachserCountryCode(anyString())).thenThrow(new DachserCodeNotMappedException("Country code not mapped"));

				sut.getTransferListPdf(pdfQueryObject);

				TransferListPdfRequestDto requestDto = createRequestDtoDangerousGoods(List.of(vTransferList1), orderLineDetailDtos, List.of(), dangerousGoodDtos, dateString,
						dateString, NUMBER_LOCALE_DE, 1, null, List.of(), List.of(new OptionIntegerDto().code(4).value(0)), null, 10);

				verify(pdfService).createPdf(requestDto, TRANSFER_LIST_NAME, NUMBER_LOCALE_DE);
				verify(trackablePackingAidService, never()).categorizeTransferList(anyString(), anyString(), any(), any(), anyList());
			}

			@Test
			void generateTransferListPdfWithDangerousGoodsADR() {
				TransferListPdfQueryObjectDto pdfQueryObject = createPdfQueryObject(TRANSFER_LIST_DATE, PRINT_DATE_TIME, NUMBER_LOCALE_DE, "E2454AD567");

				OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", COUNTRY_CODE, false, null);
				principalAddress.setOrderAddressId(12345L);

				OrderAddress consigneeAddressB = OrderGenerator.generateOrderAddress("B", COUNTRY_CODE, false, null);
				VTransferList vTransferList1 = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddressB, principalAddress, null);

				String dateString = transferListPdfMapper.formattedDateFromOffsetDateTime(OffsetDateTime.now(), "yyyy-MM-dd", Locale.ENGLISH);
				List<DangerousGoodDto> dangerousGoodDtos = List.of(TestUtil.generateADRDangerousGoodDto(2, "2"));

				List<OrderLineDetailDto> orderLineDetailDtos = List.of(TestUtil.generateOrderLineDetailDto("EU", "Palette", 2, null, dangerousGoodDtos),
						TestUtil.generateOrderLineDetailDto("EU", "Palette", 3, null, List.of()), TestUtil.generateOrderLineDetailDto("KT", "Carton", 6, null, List.of()),
						TestUtil.generateOrderLineDetailDto("BX", "Box", 1, null, List.of()));


				when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList1));
				when(orderRepository.findById(any())).thenReturn(Optional.of(new ForwardingOrder()));
				when(productsService.getProductByKey(anyString(), any(Division.class))).thenReturn(Optional.empty());
				when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(orderLineDetailDtos);
				when(orderSsccRepository.countOrderSsccsByOrderIds(any())).thenReturn(0);
				when(branchService.getBranchAddress(anyInt())).thenReturn(Optional.empty());
				when(transferListPdfMapper.formattedDateFromOffsetDateTime(any(), anyString(), notNull())).thenReturn(dateString);
				when(transferListPdfMapper.formattedDateFromLocalDate(any(), anyString(), notNull())).thenReturn(dateString);
				when(trackablePackingAidService.categorizeTransferList(anyString(), anyString(), any(), any(), any())).thenReturn(
						trackablePackingAidServiceMock.categorizeTransferList(COUNTRY_CODE, COUNTRY_CODE, Division.F, LocalDate.now(), List.of("EU")));

				when(countryService.mapToDachserCountryCode(anyString())).thenThrow(new DachserCodeNotMappedException("Country code not mapped"));

				sut.getTransferListPdf(pdfQueryObject);

				TransferListPdfRequestDto requestDto = createRequestDtoDangerousGoods(List.of(vTransferList1), orderLineDetailDtos, List.of(), dangerousGoodDtos, dateString,
						dateString, NUMBER_LOCALE_DE, 1, 100, List.of(), List.of(new OptionIntegerDto().code(2).value(100)), null, null);

				verify(pdfService).createPdf(requestDto, TRANSFER_LIST_NAME, NUMBER_LOCALE_DE);
				verify(trackablePackingAidService, never()).categorizeTransferList(anyString(), anyString(), any(), any(), anyList());
			}

			@Test
			void generateTransferListPdfWithDangerousGoodsADRAndEQAndLQ() {
				TransferListPdfQueryObjectDto pdfQueryObject = createPdfQueryObject(TRANSFER_LIST_DATE, PRINT_DATE_TIME, NUMBER_LOCALE_DE, "E2454AD567");

				OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", COUNTRY_CODE, false, null);
				principalAddress.setOrderAddressId(12345L);

				OrderAddress consigneeAddressB = OrderGenerator.generateOrderAddress("B", COUNTRY_CODE, false, null);
				VTransferList vTransferList1 = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddressB, principalAddress, null);

				String dateString = transferListPdfMapper.formattedDateFromOffsetDateTime(OffsetDateTime.now(), "yyyy-MM-dd", Locale.ENGLISH);


				List<DangerousGoodDto> dangerousGoodDtos = List.of(TestUtil.generateADRDangerousGoodDto(2, "2"), TestUtil.generateADRDangerousGoodDto(5, "3"),
						generateLQDangerousGoodDto(BigDecimal.TEN, "2"), generateEQDangerousGoodDto(1), generateEQDangerousGoodDto(4));

				List<OrderLineDetailDto> orderLineDetailDtos = List.of(TestUtil.generateOrderLineDetailDto("EU", "Palette", 2, null, dangerousGoodDtos),
						TestUtil.generateOrderLineDetailDto("EU", "Palette", 3, null, List.of()), TestUtil.generateOrderLineDetailDto("KT", "Carton", 6, null, List.of()),
						TestUtil.generateOrderLineDetailDto("BX", "Box", 1, null, List.of()));


				when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList1));
				when(orderRepository.findById(any())).thenReturn(Optional.of(new ForwardingOrder()));
				when(productsService.getProductByKey(anyString(), any(Division.class))).thenReturn(Optional.empty());
				when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(orderLineDetailDtos);
				when(orderSsccRepository.countOrderSsccsByOrderIds(any())).thenReturn(0);
				when(branchService.getBranchAddress(anyInt())).thenReturn(Optional.empty());
				when(transferListPdfMapper.formattedDateFromOffsetDateTime(any(), anyString(), notNull())).thenReturn(dateString);
				when(transferListPdfMapper.formattedDateFromLocalDate(any(), anyString(), notNull())).thenReturn(dateString);
				when(trackablePackingAidService.categorizeTransferList(anyString(), anyString(), any(), any(), any())).thenReturn(
						trackablePackingAidServiceMock.categorizeTransferList(COUNTRY_CODE, COUNTRY_CODE, Division.F, LocalDate.now(), List.of("EU")));

				when(countryService.mapToDachserCountryCode(anyString())).thenThrow(new DachserCodeNotMappedException("Country code not mapped"));

				sut.getTransferListPdf(pdfQueryObject);

				TransferListPdfRequestDto requestDto = createRequestDtoDangerousGoods(List.of(vTransferList1), orderLineDetailDtos, List.of(), dangerousGoodDtos, dateString,
						dateString, NUMBER_LOCALE_DE, 1, 200, List.of(),
						List.of(new OptionIntegerDto().code(2).value(100), new OptionIntegerDto().code(3).value(100), new OptionIntegerDto().code(4).value(0)), 5, 10);

				verify(pdfService).createPdf(requestDto, TRANSFER_LIST_NAME, NUMBER_LOCALE_DE);
				verify(trackablePackingAidService, never()).categorizeTransferList(anyString(), anyString(), any(), any(), anyList());
			}

			@Test
			void shouldCollectMissingDangerousGoodsInformation() {
				TransferListPdfQueryObjectDto pdfQueryObject = createPdfQueryObject(TRANSFER_LIST_DATE, PRINT_DATE_TIME, NUMBER_LOCALE_DE, "E2454AD567");

				OrderAddress principalAddress = OrderGenerator.generateOrderAddress("_principal", COUNTRY_CODE, false, null);
				principalAddress.setOrderAddressId(12345L);

				OrderAddress consigneeAddressB = OrderGenerator.generateOrderAddress("B", COUNTRY_CODE, false, null);
				VTransferList vTransferList1 = generateVTransferList(TransferListAddressType.PRINCIPAL, consigneeAddressB, principalAddress, null);

				String dateString = transferListPdfMapper.formattedDateFromOffsetDateTime(OffsetDateTime.now(), "yyyy-MM-dd", Locale.ENGLISH);


				List<DangerousGoodDto> dangerousGoodDtos = List.of(TestUtil.generateADRDangerousGoodDto(2, "2"), TestUtil.generateADRDangerousGoodDto(5, "3"),
						generateLQDangerousGoodDto(BigDecimal.TEN, "2"), generateEQDangerousGoodDto(1), generateEQDangerousGoodDto(4));

				List<OrderLineDetailDto> orderLineDetailDtos = List.of(TestUtil.generateOrderLineDetailDto("EU", "Palette", 2, null, dangerousGoodDtos),
						TestUtil.generateOrderLineDetailDto("EU", "Palette", 3, null, List.of()), TestUtil.generateOrderLineDetailDto("KT", "Carton", 6, null, List.of()),
						TestUtil.generateOrderLineDetailDto("BX", "Box", 1, null, List.of()));

				ForwardingOrder forwardingOrder = testUtil.generateForwardingOrderWithDangerousGoods();

				when(vTransferListRepository.findAll(anyList(), anyString(), any(LocalDate.class))).thenReturn(List.of(vTransferList1));
				when(orderRepositoryFacade.loadOrderById(any())).thenReturn(forwardingOrder);
				when(dangerousGoodsTransferListService.fetchLocalizedDescriptionForDangerousGoods(eq(forwardingOrder), anyString())).thenReturn(forwardingOrder);
				when(dangerousGoodsTransferListService.calculateDangerousGoodsPoints(forwardingOrder)).thenReturn(true);
				when(productsService.getProductByKey(anyString(), any(Division.class))).thenReturn(Optional.empty());
				when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(orderLineDetailDtos);
				when(orderSsccRepository.countOrderSsccsByOrderIds(any())).thenReturn(0);
				when(branchService.getBranchAddress(anyInt())).thenReturn(Optional.empty());
				when(transferListPdfMapper.formattedDateFromOffsetDateTime(any(), anyString(), notNull())).thenReturn(dateString);
				when(transferListPdfMapper.formattedDateFromLocalDate(any(), anyString(), notNull())).thenReturn(dateString);
				when(trackablePackingAidService.categorizeTransferList(anyString(), anyString(), any(), any(), any())).thenReturn(
						trackablePackingAidServiceMock.categorizeTransferList(COUNTRY_CODE, COUNTRY_CODE, Division.F, LocalDate.now(), List.of("EU")));

				when(countryService.mapToDachserCountryCode(anyString())).thenThrow(new DachserCodeNotMappedException("Country code not mapped"));

				sut.getTransferListPdf(pdfQueryObject);

				TransferListPdfRequestDto requestDto = createRequestDtoDangerousGoods(List.of(vTransferList1), orderLineDetailDtos, List.of(), dangerousGoodDtos, dateString,
						dateString, NUMBER_LOCALE_DE, 1, 200, List.of(),
						List.of(new OptionIntegerDto().code(2).value(100), new OptionIntegerDto().code(3).value(100), new OptionIntegerDto().code(4).value(0)), 5, 10);

				verify(pdfService).createPdf(requestDto, TRANSFER_LIST_NAME, NUMBER_LOCALE_DE);
				verify(dangerousGoodsTransferListService, times(1)).calculateDangerousGoodsPoints(any());
				verify(dangerousGoodsTransferListService, times(1)).fetchLocalizedDescriptionForDangerousGoods(any(), anyString());
				verify(trackablePackingAidService, never()).categorizeTransferList(anyString(), anyString(), any(), any(), anyList());
			}
		}

		private OrderSscc generateOrderSsccs(String sscc) {
			OrderSscc orderSscc = new OrderSscc();
			orderSscc.setSscc(sscc);
			return orderSscc;
		}

		private TransferListPdfQueryObjectDto createPdfQueryObject(String transferListDate, String printDateTime, String numberLocale, String addressHash) {
			TransferListPdfQueryObjectDto pdfQueryObject = new TransferListPdfQueryObjectDto();
			pdfQueryObject.setDateFormat("yyyy-MM-dd");
			pdfQueryObject.setPrintDateTime(printDateTime);
			pdfQueryObject.setNumberLocale(numberLocale);
			TransferListQueryObjectDto queryObject = new TransferListQueryObjectDto();
			queryObject.setAddressHash(addressHash);
			if (transferListDate != null) {
				queryObject.setPickupDate(LocalDate.parse(transferListDate));
			}
			pdfQueryObject.setQueryObject(queryObject);

			return pdfQueryObject;
		}
	}

	// @formatter:off
	private static TransferListPdfRequestDto createRequestDto(
			List<VTransferList> vTransferLists, List<OrderLineDetailDto> orderLineDetailDtos, List<PackingPositionDto> packingPositionDtos,
			List<DangerousGoodDto> dangerousGoodDtos, String deliveryDate, String transferListDate, String numberLocale) {

		List<TransferListPdfItemDto> shipments = vTransferLists.stream().map((vTransferList -> new TransferListPdfItemDto()
				.customerNumber(vTransferList.getCustomerNumber())
				.orderId(vTransferList.getOrderId())
				.orderNumber(vTransferList.getOrderNumber())
				.shipmentNumber(vTransferList.getShipmentNumber())
				.consigneeAddress(new TransferListAddressDto()
						.name(vTransferList.getConsigneeAddress().getName())
						.name2(vTransferList.getConsigneeAddress().getName2())
						.name3(vTransferList.getConsigneeAddress().getName3())
						.street(vTransferList.getConsigneeAddress().getStreet())
						.postcode(vTransferList.getConsigneeAddress().getPostcode())
						.city(vTransferList.getConsigneeAddress().getCity())
						.countryCode(vTransferList.getConsigneeAddress().getCountryCode())
						.street(vTransferList.getConsigneeAddress().getStreet())
						.street2(vTransferList.getConsigneeAddress().getStreet2()))
				.product(new OptionDto().code(vTransferList.getProduct()))
				.quantity(Long.valueOf(orderLineDetailDtos.stream().reduce(0, (acc, orderLineDetailDto) -> acc + orderLineDetailDto.getQuantity(), Integer::sum)))
				.orderLineItems(orderLineDetailDtos)
				.packingPositions(packingPositionDtos)
				.deliveryDate(deliveryDate))).toList();

		return new TransferListPdfRequestDto()
				.totalElements(vTransferLists.size())
				.from(new TransferListAddressDto()
						.id(vTransferLists.get(0).getPrincipalAddress().getOrderAddressId())
						.name(vTransferLists.get(0).getPrincipalAddress().getName())
						.name2(vTransferLists.get(0).getPrincipalAddress().getName2())
						.name3(vTransferLists.get(0).getPrincipalAddress().getName3())
						.street(vTransferLists.get(0).getPrincipalAddress().getStreet())
						.postcode(vTransferLists.get(0).getPrincipalAddress().getPostcode())
						.city(vTransferLists.get(0).getPrincipalAddress().getCity())
						.countryCode(vTransferLists.get(0).getPrincipalAddress().getCountryCode())
						.street(vTransferLists.get(0).getPrincipalAddress().getStreet())
						.street2(vTransferLists.get(0).getPrincipalAddress().getStreet2()))
				.supervisingBranch(new TransferListAddressDto())
				.customerNumber(vTransferLists.get(0).getCustomerNumber())
				.shipment(shipments)
				.numberOfShipments(vTransferLists.size())
				.numberOfNveSccs(0)
				.trackablePackingAids(List.of())
				.nonTrackablePackingAids(List.of())
				.totalWeight(packingPositionDtos.stream()
						.flatMap(position -> position.getLines().stream())
						.mapToInt(RoadOrderLineDto::getWeight)
						.sum() + orderLineDetailDtos.stream().mapToInt(OrderLineDetailDto::getWeight).sum())
				.totalVolume(packingPositionDtos.stream()
						.flatMap(position -> position.getLines().stream())
						.mapToDouble(RoadOrderLineDto::getVolume)
						.sum() + orderLineDetailDtos.stream().mapToDouble(OrderLineDetailDto::getVolume).sum())
				.printDateTime(PRINT_DATE_TIME)
				.transferListDate(transferListDate)
				.numberLocale(numberLocale);
	}

	private static TransferListPdfRequestDto createRequestDtoDangerousGoods(
			List<VTransferList> vTransferLists, List<OrderLineDetailDto> orderLineDetailDtos, List<PackingPositionDto> packingPositionDtos,
			List<DangerousGoodDto> dangerousGoodDtos, String deliveryDate, String transferListDate, String numberLocale,
			Integer numberOfShipmentsWithDG, Integer totalCalculatedPoints, List<OptionIntegerDto> bulkPerTransport, List<OptionIntegerDto> valuePerTransportCategory,
			Integer exemptedQuantities, Integer limitedQuantities) {

		TransferListPdfRequestDto requestDto = createRequestDto(vTransferLists, orderLineDetailDtos, packingPositionDtos, dangerousGoodDtos, deliveryDate, transferListDate, numberLocale);
		requestDto.setNumberOfShipmentsWithDangerousGoods(numberOfShipmentsWithDG);
		requestDto.setTotalCalculatedPoints(totalCalculatedPoints);
		requestDto.setTotalBulkPerTransportCategory(bulkPerTransport);
		requestDto.setValuePerTransportCategory(valuePerTransportCategory);
		requestDto.setLimitedQuantities(limitedQuantities);
		requestDto.setExemptedQuantities(exemptedQuantities);



		return requestDto;

	}
	// @formatter:on
}

