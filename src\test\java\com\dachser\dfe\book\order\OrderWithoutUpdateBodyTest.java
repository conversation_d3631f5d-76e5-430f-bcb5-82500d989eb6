package com.dachser.dfe.book.order;

import com.dachser.bi.common.security.service.pri.api.scan.bean.FileWrapper;
import com.dachser.dfe.book.BaseOpenApiTest;
import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.advice.AdviceService;
import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.document.FileStorageCleanup;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.LabelsCouldNotBeMergedException;
import com.dachser.dfe.book.model.AirExportOrderDto;
import com.dachser.dfe.book.model.BadRequestProblemDto;
import com.dachser.dfe.book.model.GeneralProblemDto;
import com.dachser.dfe.book.model.OrderActionWithoutValidationDto;
import com.dachser.dfe.book.model.OrderProcessResultDto;
import com.dachser.dfe.book.model.OrderStatusDto;
import com.dachser.dfe.book.model.PrintLabelStartPosition;
import com.dachser.dfe.book.model.RoadForwardingOrderDto;
import com.dachser.dfe.book.model.SeaExportOrderDto;
import com.dachser.dfe.book.pdf.ShipmentLabelPdfTransformer;
import com.dachser.dfe.book.service.PlatformConfigService;
import com.dachser.dfe.book.service.SsccGenerationService;
import com.dachser.dfe.pdfexport.model.NotFoundProblem;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import io.restassured.module.mockmvc.specification.MockMvcRequestSpecification;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.security.test.context.support.WithMockUser;

import java.io.IOException;

import static com.dachser.dfe.book.order.OrderApiTestUtil.createNewForwardingOrder;
import static com.dachser.dfe.book.order.OrderApiTestUtil.createValidatedForwardingOrder;
import static com.dachser.dfe.book.order.OrderApiTestUtil.getOrderStatusFromOrderProcess;
import static com.dachser.dfe.book.order.OrderApiTestUtil.parseOrderProcessResult;
import static com.dachser.dfe.book.order.OrderApiTestUtil.parseResponse;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class OrderWithoutUpdateBodyTest extends BaseOpenApiTest implements ResourceLoadingTest {

	@MockBean
	ShipmentLabelPdfTransformer shipmentLabelPdfTransformer;

	@Autowired
	DocumentService documentService;

	@Autowired
	FileStorageCleanup fileStorageCleanup;

	@MockBean
	PlatformConfigService platformConfigService;

	@SpyBean
	SsccGenerationService ssccGenerationService;

	@Autowired
	OrderRepositoryFacade orderRepositoryFacade;

	@Autowired
	OrderRepository orderRepository;
	
	@MockBean
	AdviceService adviceService;

	@BeforeEach
	public void setUp() {
	}

	@AfterEach
	void tearDown() {
		fileStorageCleanup.deleteAllFoldersInBasePath();
	}

	@Nested
	class PrintLabels {
		@Nested
		@WithMockUser("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
		class ForwardingOrder {

			@Nested
			class Success {

				@Test
				void shouldSucceedForValidOrder() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any(PrintLabelStartPosition.class))).thenReturn("mockPdf".getBytes());
					final RoadForwardingOrderDto order = createValidatedForwardingOrder();
					final Long orderId = order.getOrderId();

					MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json");
					final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.PRINT_LABELS.name());
					assertEquals(200, response.statusCode());

					final OrderProcessResultDto orderProcessDto = parseResponse(response, OrderProcessResultDto.class);
					assertEquals(OrderStatusDto.COMPLETE, getOrderStatusFromOrderProcess(orderProcessDto));
					final String responseContent = orderProcessDto.getOrderLabel();
					assertNotNull(responseContent);
				}

				@Test
				void shouldSucceedWithoutValidation() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any(PrintLabelStartPosition.class))).thenReturn("mockPdf".getBytes());
					final RoadForwardingOrderDto order = createValidatedForwardingOrder();
					final Long orderId = order.getOrderId();

					MockMvcRequestSpecification request;
					request = givenWriteRequest().header("accept", "application/json");
					final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.PRINT_LABELS.name());
					assertEquals(200, response.statusCode());

					final OrderProcessResultDto processResult = parseResponse(response, OrderProcessResultDto.class);
					final String responseContent = processResult.getOrderLabel();
					assertNotNull(responseContent);
				}

				/**
				 * Principal data should not change on order updates that haven't been submitted from form (having body content)
				 */
				@Test
				void shouldNotUpdatePrincipalDataOnLabelPrint() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any(PrintLabelStartPosition.class))).thenReturn("mockPdf".getBytes());
					final RoadForwardingOrderDto order = createValidatedForwardingOrder();
					Long orderId = order.getOrderId();
					Order order1 = orderRepositoryFacade.loadOrderById(orderId);
					order1.getPrincipalAddress().setName("XYZ Comp");
					orderRepository.save(order1);

					MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json");
					final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.PRINT_LABELS.name());
					assertEquals(200, response.statusCode());

					RoadForwardingOrderDto forwardingOrderDto = parseOrderProcessResult(response, RoadForwardingOrderDto.class);

					assertEquals("XYZ Comp", forwardingOrderDto.getPrincipalAddress().getName());
				}
			}

			@Nested
			class Fail {

				@Test
				void shouldFailForMissingOrder() {
					final Long orderId = -1L;

					final MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json");
					final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.PRINT_LABELS.name());

					assertEquals(404, response.statusCode());
				}

				@Test
				void shouldFailForNonValidatedOrder() throws IOException {
					RoadForwardingOrderDto forwardingOrder = createNewForwardingOrder(null);

					final MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json");
					final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), forwardingOrder.getOrderId().toString(),
							OrderActionWithoutValidationDto.PRINT_LABELS.name());

					assertEquals(400, response.statusCode());
				}

				@Test
				void shouldFailForServiceError() throws IOException {
					final RoadForwardingOrderDto order = createValidatedForwardingOrder();
					final Long orderId = order.getOrderId();

					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any(PrintLabelStartPosition.class))).thenThrow(
							new LabelsCouldNotBeMergedException("mocked exception"));

					final MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json");
					final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.PRINT_LABELS.name());
					assertEquals(500, response.statusCode());
					GeneralProblemDto generalProblemDto = response.getBody().as(GeneralProblemDto.class);
					assertEquals(BookErrorId.ERR_LB_01.getErrorId(), generalProblemDto.getErrorId());
				}

				@Test
				void shouldFailForUnknownError() throws IOException {
					final RoadForwardingOrderDto order = createValidatedForwardingOrder();
					final Long orderId = order.getOrderId();

					when(ssccGenerationService.generateMissingSsccs(any(com.dachser.dfe.book.order.road.ForwardingOrder.class), anyInt(), anyInt()))
							.thenThrow(new RuntimeException("mocked runtime exception"));

					final MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json");
					final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.PRINT_LABELS.name());
					assertEquals(500, response.statusCode());
					GeneralProblemDto generalProblemDto = response.getBody().as(GeneralProblemDto.class);
					assertEquals(BookErrorId.ERR_LB_01.getErrorId(), generalProblemDto.getErrorId());
				}

			}

		}
	}

	@Nested
	class SubmitOrder {

		@Nested
		@WithMockUser("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
		class ForwardingOrder {
			@Nested
			class Success {

				@Test
				void shouldSucceedForValidOrder() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any(PrintLabelStartPosition.class))).thenReturn("mockPdf".getBytes());
					final RoadForwardingOrderDto order = createValidatedForwardingOrder();
					final Long orderId = order.getOrderId();

					MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json");
					final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.SUBMIT.name());
					assertEquals(200, response.statusCode());

					final OrderProcessResultDto orderProcessDto = parseResponse(response, OrderProcessResultDto.class);
					assertEquals(OrderStatusDto.SENT, getOrderStatusFromOrderProcess(orderProcessDto));
					final String responseContent = orderProcessDto.getOrderLabel();
					assertNotNull(responseContent);
				}

				@Test
				void shouldSucceedWithoutValidation() throws IOException {
					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any(PrintLabelStartPosition.class))).thenReturn("mockPdf".getBytes());
					final RoadForwardingOrderDto order = createValidatedForwardingOrder();
					final Long orderId = order.getOrderId();

					MockMvcRequestSpecification request;
					request = givenWriteRequest().header("accept", "application/json");
					final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.SUBMIT.name());
					assertEquals(200, response.statusCode());

					final OrderProcessResultDto processResult = parseResponse(response, OrderProcessResultDto.class);
					final String responseContent = processResult.getOrderLabel();
					assertNotNull(responseContent);
				}
			}

			@Nested
			class Fail {

				@Test
				void shouldFailForMissingOrder() {
					final Long orderId = -1L;

					final MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json");
					final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.SUBMIT.name());

					assertEquals(404, response.statusCode());
				}

				@Test
				void shouldFailForServiceError() throws IOException {
					final RoadForwardingOrderDto order = createValidatedForwardingOrder();
					final Long orderId = order.getOrderId();

					when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any(PrintLabelStartPosition.class))).thenReturn(new byte[] {});
					when(dipService.createAndPublishXmlForOrder(any(Order.class))).thenReturn(false);

					final MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json");
					final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.SUBMIT.name());
					assertEquals(500, response.statusCode());

					GeneralProblemDto generalProblemDto = response.getBody().as(GeneralProblemDto.class);
					assertEquals(BookErrorId.ERR_SE_01.getErrorId(), generalProblemDto.getErrorId());
				}
			}
		}

		@Nested
		@WithMockUser("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
		class AirOrder {

			private AirExportOrderDto order;

			@BeforeEach
			void setup() throws IOException {
				when(fileScanService.isThisFileHarmless(any(FileWrapper.class), anyString(), anyString())).thenReturn(Boolean.TRUE);
				order = OrderApiTestUtil.createValidatedAirExportOrder(documentService);
			}

			@Test
			void shouldSucceedForValidOrder() {
				final Long orderId = order.getOrderId();

				MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json");
				final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.SUBMIT.name());
				assertEquals(200, response.statusCode());

				final OrderProcessResultDto orderProcessDto = parseResponse(response, OrderProcessResultDto.class);
				assertEquals(OrderStatusDto.SENT, getOrderStatusFromOrderProcess(orderProcessDto));
				verify(dipService, times(1)).createAndPublishXmlForOrder(any());
			}

			/**
			 * Principal data should not change on order updates that haven't been submitted from form (having body content)
			 */
			@Test
			void shouldNotUpdatePrincipalDataOnSubmit() {
				final Long orderId = order.getOrderId();
				Order order = orderRepositoryFacade.loadOrderById(orderId);
				order.getPrincipalAddress().setName("XYZ Comp");
				orderRepository.save(order);

				MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json");
				final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.SUBMIT.name());
				assertEquals(200, response.statusCode());

				AirExportOrderDto forwardingOrderDto = parseOrderProcessResult(response, AirExportOrderDto.class);

				assertEquals("XYZ Comp", forwardingOrderDto.getPrincipalAddress().getName());
			}

		}

		@Nested
		@WithMockUser("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
		class SeaOrder {

			private SeaExportOrderDto order;

			@BeforeEach
			void setup() throws IOException {
				when(fileScanService.isThisFileHarmless(any(FileWrapper.class), anyString(), anyString())).thenReturn(Boolean.TRUE);
				order = OrderApiTestUtil.createValidatedSeaExportOrder();
			}

			@Test
			void shouldSucceedForValidOrder() {
				final Long orderId = order.getOrderId();

				MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json");
				final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.SUBMIT.name());
				assertEquals(200, response.statusCode());

				final OrderProcessResultDto orderProcessDto = parseResponse(response, OrderProcessResultDto.class);
				assertEquals(OrderStatusDto.SENT, getOrderStatusFromOrderProcess(orderProcessDto));
				final String responseContent = orderProcessDto.getOrderLabel();
				verify(dipService, times(1)).createAndPublishXmlForOrder(any());
			}

		}

	}

	@Nested
	class DeleteOrder {

		@Nested
		@WithMockUser("e2e590e6-dfe6-40a6-8b80-0e7098ee26d9")
		class Failure {

			@Test
			void shouldReturnBadRequestProblemForDeletionException() throws IOException {
				when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any(PrintLabelStartPosition.class))).thenReturn("mockPdf".getBytes());
				final RoadForwardingOrderDto order = createValidatedForwardingOrder();
				final Long orderId = order.getOrderId();

				MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json");
				final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.SUBMIT.name());
				assertEquals(200, response.statusCode());

				final MockMvcRequestSpecification requestDelete = givenRequest();

				MockMvcResponse deleteResponse = requestDelete.delete(buildUrl("/orders/"+orderId));

				assertEquals(400, deleteResponse.getStatusCode());
				final BadRequestProblemDto problem = deleteResponse.getBody().as(BadRequestProblemDto.class);
				assertEquals("errDL-03", problem.getErrorId());
			}

			@Test
			void shouldReturnNotFoundProblemOnMissingOrder() throws IOException {
				final MockMvcRequestSpecification requestDelete = givenRequest();
				
				MockMvcResponse deleteResponse = requestDelete.delete(buildUrl("/orders/1500"));
				assertEquals(404, deleteResponse.getStatusCode());
				final NotFoundProblem problem = deleteResponse.getBody().as(NotFoundProblem.class);
				assertEquals("errUN-07", problem.getErrorId());
			}

			@Test
			void shouldReturnGeneralProblemOnBackendError() throws IOException {
				when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any(PrintLabelStartPosition.class))).thenReturn("mockPdf".getBytes());
				final RoadForwardingOrderDto order = createValidatedForwardingOrder();
				final Long orderId = order.getOrderId();

				when(adviceService.sendAdviceDataForOrderWithStatusCode(any(), any())).thenThrow(new RuntimeException("buhu"));
				
				MockMvcRequestSpecification request = givenWriteRequest().header("accept", "application/json");
				final MockMvcResponse response = request.put(buildUrl("/orders/{orderId}/{action}"), orderId.toString(), OrderActionWithoutValidationDto.PRINT_LABELS.name());
				assertEquals(200, response.statusCode());

				final MockMvcRequestSpecification requestDelete = givenRequest();

				MockMvcResponse deleteResponse = requestDelete.delete(buildUrl("/orders/"+orderId));

				assertEquals(500, deleteResponse.getStatusCode());
				final GeneralProblemDto problem = deleteResponse.getBody().as(GeneralProblemDto.class);
				assertEquals("errDL-02", problem.getErrorId());
			}
		}
	}
}
