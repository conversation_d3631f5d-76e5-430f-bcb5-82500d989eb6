package com.dachser.dfe.book.order.common.dangerousgood;

import jakarta.persistence.CascadeType;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@Entity
@Table(name = "order_adr_dangerous_good")
@EqualsAndHashCode(callSuper = true)
@DiscriminatorValue("3")
public class ADRDangerousGood extends DangerousGood {

	@OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
	@JoinColumn(name = "dangerous_good_data_item_id")
	private DangerousGoodDataItem dangerousGoodDataItem;

	private String nos;

	@Min(1)
	private int noOfPackages;

	private String packagingKey;

	@DecimalMin("0.0")
	private BigDecimal grossMass;

	private Boolean environmentallyHazardous;

	private Integer calculatedPoints;

	@Override
	public DangerousGoodType getDangerousGoodType() {
		return DangerousGoodType.ADR_DANGEROUS_GOOD;
	}
}
