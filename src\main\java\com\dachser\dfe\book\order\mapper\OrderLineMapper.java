package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.mapper.PersistentListMerger;
import com.dachser.dfe.book.model.AirOrderLineDto;
import com.dachser.dfe.book.model.GoodsGroupDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderLineDetailDto;
import com.dachser.dfe.book.model.RoadOrderLineDto;
import com.dachser.dfe.book.model.SeaOrderLineDto;
import com.dachser.dfe.book.order.air.AirOrderLine;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.sea.SeaOrderLine;
import com.dachser.dfe.book.quote.QuoteOrderLine;
import org.mapstruct.AfterMapping;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

import java.math.BigDecimal;
import java.util.List;

@Mapper(uses = { OrderLineHsCodeMapper.class, OptionDto.class, DangerousGoodsMapper.class }, componentModel = "spring")
public interface OrderLineMapper {

	@Mapping(source = "id", target = "orderLineId")
	@Mapping(source = "packaging.code", target = "packagingType")
	@Mapping(source = "packaging.description", target = "packagingTypeDescription")
	@Mapping(source = "goodsGroup.code", target = "goodsGroup")
	@Mapping(source = "goodsGroup.quantity", target = "goodsGroupQuantity")
	@Mapping(source = "dangerousGoods", target = "dangerousGoods")
	@Mapping(target = "volume", expression = "java( mapVolume(orderLineDto.getVolume()) )")
	RoadOrderLine mapRoadOrderLine(RoadOrderLineDto orderLineDto);

	@Mapping(source = "id", target = "orderLineId")
	@Mapping(source = "packaging.code", target = "packagingType")
	@Mapping(source = "packaging.description", target = "packagingTypeDescription")
	@Mapping(source = "originalPackaging.code", target = "originalPackagingType")
	@Mapping(source = "originalPackaging.description", target = "originalPackagingTypeDescription")
	@Mapping(target = "volume", expression = "java( mapVolume(orderLineDto.getVolume()) )")
	AirOrderLine mapAirOrderLine(AirOrderLineDto orderLineDto);

	@Mapping(source = "id", target = "orderLineId")
	@Mapping(source = "packaging.code", target = "packagingType")
	@Mapping(source = "packaging.description", target = "packagingTypeDescription")
	@Mapping(target = "volume", expression = "java( mapVolume(orderLineDto.getVolume()) )")
	SeaOrderLine mapSeaOrderLine(SeaOrderLineDto orderLineDto);

	@Named("mapOrderLineDetailDtos")
	@Mapping(target = "id", source = "orderLineId")
	@Mapping(target = "packaging.code", source = "packagingType")
	@Mapping(target = "packaging.description", source = "packagingTypeDescription")
	@Mapping(target = "goodsGroup.code", source = "goodsGroup")
	@Mapping(target = "goodsGroup.quantity", source = "goodsGroupQuantity")
	@Mapping(target = "dangerousGoodsADR", source = ".", qualifiedByName = "mapADR")
	@Mapping(target = "dangerousGoodsLQ", source = ".", qualifiedByName = "mapLQ")
	@Mapping(target = "dangerousGoodsEQ", source = ".", qualifiedByName = "mapEQ")
	OrderLineDetailDto mapOrderLineDetailDto(RoadOrderLine orderLine);

	@IterableMapping(qualifiedByName = "mapOrderLineDetailDtos")
	List<OrderLineDetailDto> mapOrderLineDetailDtos(List<RoadOrderLine> orderLines);

	@InheritInverseConfiguration
	@Mapping(source = "id", target = "orderLineId")
	@Mapping(source = "packaging.code", target = "packagingType")
	@Mapping(source = "packaging.description", target = "packagingTypeDescription")
	@Mapping(source = "goodsGroup.code", target = "goodsGroupCode")
	@Mapping(source = "goodsGroup.quantity", target = "goodsGroupQuantity")
	QuoteOrderLine mapQuoteRoadOrderLineDto(RoadOrderLineDto orderLineDto);

	@InheritInverseConfiguration
	@Mapping(source = "id", target = "orderLineId")
	@Mapping(source = "packaging.code", target = "packagingType")
	@Mapping(source = "packaging.description", target = "packagingTypeDescription")
	@Mapping(source = "originalPackaging.code", target = "originalPackagingType")
	@Mapping(source = "originalPackaging.description", target = "originalPackagingTypeDescription")
	QuoteOrderLine mapQuoteAirOrderLineDto(AirOrderLineDto orderLineDto);

	@InheritInverseConfiguration
	@Mapping(source = "id", target = "orderLineId")
	@Mapping(source = "packaging.code", target = "packagingType")
	@Mapping(source = "packaging.description", target = "packagingTypeDescription")
	QuoteOrderLine mapQuoteSeaOrderLineDto(SeaOrderLineDto orderLineDto);

	List<QuoteOrderLine> mapQuoteRoadOrderLines(List<RoadOrderLine> orderLine);

	@Mapping(source = ".", target = ".")
	@Mapping(source = "goodsGroup", target = "goodsGroupCode")
	QuoteOrderLine mapQuoteRoadOrderLine(RoadOrderLine orderLine);

	List<QuoteOrderLine> mapQuoteAirOrderLines(List<AirOrderLine> orderLine);

	@Mapping(source = ".", target = ".")
	QuoteOrderLine mapQuoteAirOrderLine(AirOrderLine orderLine);

	List<QuoteOrderLine> mapQuoteSeaOrderLines(List<SeaOrderLine> orderLine);

	@Mapping(source = ".", target = ".")
	QuoteOrderLine mapQuoteSeaOrderLine(SeaOrderLine orderLine);

	@InheritConfiguration
	RoadOrderLine updateOrderLine(RoadOrderLineDto orderLineDto, @MappingTarget RoadOrderLine orderLine);

	@InheritConfiguration
	AirOrderLine updateAirOrderLine(AirOrderLineDto orderLineDto, @MappingTarget AirOrderLine orderLine);

	@InheritConfiguration
	SeaOrderLine updateSeaOrderLine(SeaOrderLineDto orderLineDto, @MappingTarget SeaOrderLine orderLine);

	@InheritInverseConfiguration(name = "mapRoadOrderLine")
	RoadOrderLineDto mapRoadOrderLine(RoadOrderLine orderLine);

	@InheritInverseConfiguration(name = "mapAirOrderLine")
	AirOrderLineDto mapAirOrderLine(AirOrderLine orderLine);

	@InheritInverseConfiguration(name = "mapSeaOrderLine")
	SeaOrderLineDto mapSeaOrderLine(SeaOrderLine orderLine);

	@AfterMapping
	default void afterMappingOrderLine(RoadOrderLine orderLine, @MappingTarget RoadOrderLineDto orderLineDto) {
		final GoodsGroupDto goodsGroup = orderLineDto.getGoodsGroup();
		// Remove the complete sub object if code is not available
		if (goodsGroup != null && orderLine.getGoodsGroup() == null) {
			orderLineDto.setGoodsGroup(null);
		}
	}

	@AfterMapping
	default void afterMappingRoadOrderLine(RoadOrderLineDto orderLineDto, @MappingTarget RoadOrderLine orderLine) {
		if (orderLine.getDangerousGoods() != null) {
			orderLine.getDangerousGoods().forEach(roadDangerousGoods -> roadDangerousGoods.setRoadOrderLine(orderLine));
		}
	}

	List<RoadOrderLine> mapRoadOrderLines(List<RoadOrderLineDto> orderLineDto);

	List<AirOrderLine> mapAirOrderLines(List<AirOrderLineDto> orderLineDto);

	List<SeaOrderLine> mapSeaOrderLines(List<SeaOrderLineDto> orderLineDto);

	List<RoadOrderLineDto> mapOrderLineDtos(List<RoadOrderLine> orderLineDto);

	default List<RoadOrderLine> mapOrderLines(List<RoadOrderLineDto> orderLineDto, @MappingTarget List<RoadOrderLine> orderLines) {
		final PersistentListMerger<RoadOrderLine, RoadOrderLineDto> listMapper = this::updateOrderLine;
		return listMapper.updateList(orderLineDto, orderLines, RoadOrderLine::getOrderLineId, RoadOrderLineDto::getId, RoadOrderLine::new);
	}

	default List<AirOrderLine> mapAirOrderLines(List<AirOrderLineDto> orderLineDto, @MappingTarget List<AirOrderLine> orderLines) {
		final PersistentListMerger<AirOrderLine, AirOrderLineDto> listMapper = this::updateAirOrderLine;
		return listMapper.updateList(orderLineDto, orderLines, AirOrderLine::getOrderLineId, AirOrderLineDto::getId, AirOrderLine::new);
	}

	default List<SeaOrderLine> mapSeaOrderLines(List<SeaOrderLineDto> orderLineDto, @MappingTarget List<SeaOrderLine> orderLines) {
		final PersistentListMerger<SeaOrderLine, SeaOrderLineDto> listMapper = this::updateSeaOrderLine;
		return listMapper.updateList(orderLineDto, orderLines, SeaOrderLine::getOrderLineId, SeaOrderLineDto::getId, SeaOrderLine::new);
	}

	@Named("mapVolume")
	default BigDecimal mapVolume(Double volume) {
		return volume != null ? BigDecimal.valueOf(volume).setScale(4, java.math.RoundingMode.HALF_UP) : null;
	}

}
