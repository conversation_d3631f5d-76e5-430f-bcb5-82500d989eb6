package com.dachser.dfe.book.dip;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.dip.converter.DipOrderConverter;
import com.dachser.dfe.book.dip.converter.air.AirExportDipOrderConverter;
import com.dachser.dfe.book.dip.converter.road.collection.CollectionDipOrderConverter;
import com.dachser.dfe.book.dip.converter.road.forwarding.ForwardingDipOrderConverter;
import com.dachser.dfe.book.dip.publisher.DipPublisher;
import com.dachser.dfe.book.dip.repository.OrderXml;
import com.dachser.dfe.book.dip.repository.OrderXmlRepository;
import com.dachser.dfe.book.dip.xml.XmlFileStorageProvider;
import com.dachser.dfe.book.dip.xml.XmlPublishStatus;
import com.dachser.dfe.book.document.Document;
import com.dachser.dfe.book.document.DocumentRepositoryFacade;
import com.dachser.dfe.book.document.DocumentStatus;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DipServiceTest {

	@Mock
	private XmlFileStorageProvider xmlFileStorage;

	@Mock
	private DipPublisher dipPublisher;

	@Mock
	private OrderXmlRepository orderXmlRepository;

	@Mock
	private DocumentRepositoryFacade documentRepository;

	@Mock
	private List<DipOrderConverter<? extends Order>> converters;

	@InjectMocks
	private DipService dipService;

	private Order order;

	private OrderXml orderXml;

	@BeforeEach
	void setUp() {
		order = new CollectionOrder();
		order.setOrderId(1L);
		order.setShipmentNumber(12345L);
		order.setCustomerNumber("12345678");
		order.setCollectionDate(Instant.now().atZone(ZoneOffset.UTC).toLocalDate());

		orderXml = TestUtil.createTestOrderXml(1L, DipOrderType.COLLECTION, "12345678");
	}

	@Nested
	class CreateAndPublishXmlForOrder {

		@Test
		void shouldCreateAndPublishXmlForTodayForwardingOrder() throws IOException {
			DipOrderConverter<ForwardingOrder> converter = mock(ForwardingDipOrderConverter.class);
			when(converters.stream()).thenReturn(Stream.of(converter));
			when(converter.supports(any(Order.class))).thenReturn(true);
			when(converter.convertToXML(any(ForwardingOrder.class))).thenReturn("<order>content</order>");
			doNothing().when(xmlFileStorage).saveXml(anyString(), anyLong());
			when(orderXmlRepository.save(any(OrderXml.class))).thenReturn(orderXml);
			when(dipPublisher.publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString())).thenReturn(true);

			ForwardingOrder forwardingOrder = new ForwardingOrder();
			forwardingOrder.setOrderId(1L);
			forwardingOrder.setShipmentNumber(12345L);
			forwardingOrder.setCustomerNumber("12345678");
			forwardingOrder.setCollectionDate(Instant.now().atZone(ZoneOffset.UTC).toLocalDate());

			boolean result = dipService.createAndPublishXmlForOrder(forwardingOrder);

			verify(dipPublisher, times(1)).publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString());
			assertTrue(result);
		}

		@Test
		void shouldCreateAndNotPublishXmlForFutureForwardingOrder() throws IOException {
			DipOrderConverter<ForwardingOrder> converter = mock(ForwardingDipOrderConverter.class);
			when(converters.stream()).thenReturn(Stream.of(converter));
			when(converter.supports(any(Order.class))).thenReturn(true);
			when(converter.convertToXML(any(ForwardingOrder.class))).thenReturn("<order>content</order>");
			doNothing().when(xmlFileStorage).saveXml(anyString(), anyLong());
			when(orderXmlRepository.save(any(OrderXml.class))).thenReturn(orderXml);

			ForwardingOrder forwardingOrder = new ForwardingOrder();
			forwardingOrder.setOrderId(1L);
			forwardingOrder.setShipmentNumber(12345L);
			forwardingOrder.setCustomerNumber("12345678");
			forwardingOrder.setCollectionDate(Instant.now().atZone(ZoneOffset.UTC).toLocalDate().plusDays(1));

			boolean result = dipService.createAndPublishXmlForOrder(forwardingOrder);

			verify(dipPublisher, never()).publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString());
			assertTrue(result);
		}

		@Test
		void shouldCreateAndPublishXmlForCollectionOrderWithDocuments() throws IOException {
			DipOrderConverter<CollectionOrder> converter = mock(CollectionDipOrderConverter.class);
			when(converters.stream()).thenReturn(Stream.of(converter));
			when(converter.supports(any(Order.class))).thenReturn(true);
			when(converter.convertToXML(any(CollectionOrder.class))).thenReturn("<order>content</order>");
			doNothing().when(xmlFileStorage).saveXml(anyString(), anyLong());
			when(orderXmlRepository.save(any(OrderXml.class))).thenReturn(orderXml);
			when(dipPublisher.publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString())).thenReturn(true);

			Document document = TestUtil.createTestDocument(1L, 2L);

			when(documentRepository.findByOrderId(anyLong())).thenReturn(List.of(document));

			boolean result = dipService.createAndPublishXmlForOrder(order);

			assertTrue(result);
			verify(dipPublisher, times(1)).publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString());
			verify(documentRepository, times(1)).save(document);
			assertEquals(DocumentStatus.READY, document.getStatus());
		}

		@Test
		void shouldUpdateOrderXmlEntryIfAlreadyExists() throws IOException {
			DipOrderConverter<CollectionOrder> converter = mock(CollectionDipOrderConverter.class);
			when(converters.stream()).thenReturn(Stream.of(converter));
			when(converter.supports(any(Order.class))).thenReturn(true);
			when(converter.convertToXML(any(CollectionOrder.class))).thenReturn("<order>content</order>");
			doNothing().when(xmlFileStorage).saveXml(anyString(), anyLong());
			when(orderXmlRepository.findByStatusAndCustomerNumberAndShipmentNumberAndOrderId(any(XmlPublishStatus.class), anyString(), anyLong(), anyLong())).thenReturn(
					Optional.of(orderXml));
			when(orderXmlRepository.save(any(OrderXml.class))).thenReturn(orderXml);
			when(dipPublisher.publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString())).thenReturn(true);

			boolean result = dipService.createAndPublishXmlForOrder(order);

			assertTrue(result);
			verify(orderXmlRepository, times(1)).findByStatusAndCustomerNumberAndShipmentNumberAndOrderId(eq(XmlPublishStatus.READY), eq("12345678"), eq(12345L), eq(1L));
		}

		@Test
		void shouldCreateAndPublishXmlForAirExportOrder() throws IOException {
			AirExportOrder airExportOrder = new AirExportOrder();
			airExportOrder.setOrderId(3L);
			airExportOrder.setShipmentNumber(54321L);
			airExportOrder.setCustomerNumber("98765432");
			airExportOrder.setCollectionDate(Instant.now().atZone(ZoneOffset.UTC).toLocalDate());

			Document document = TestUtil.createTestDocument(2L, 3L);

			DipOrderConverter<AirExportOrder> converter = mock(AirExportDipOrderConverter.class);
			when(converters.stream()).thenReturn(Stream.of(converter));
			when(converter.supports(any(Order.class))).thenReturn(true);
			when(converter.convertToXML(any())).thenReturn("<order>content</order>");
			doNothing().when(xmlFileStorage).saveXml(anyString(), anyLong());
			when(orderXmlRepository.save(any(OrderXml.class))).thenReturn(orderXml);
			when(dipPublisher.publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString())).thenReturn(true);
			when(documentRepository.findByOrderId(anyLong())).thenReturn(List.of(document));

			boolean result = dipService.createAndPublishXmlForOrder(airExportOrder);

			assertTrue(result);
			verify(dipPublisher, times(1)).publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString());
			verify(documentRepository, times(1)).save(document);
			assertEquals(DocumentStatus.READY, document.getStatus());
		}

		@Test
		void shouldNotPublishIfConverterNotFound() {
			when(converters.stream()).thenReturn(Stream.empty());

			boolean result = dipService.createAndPublishXmlForOrder(order);

			assertFalse(result);
			verify(dipPublisher, never()).publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString());
		}

		@Test
		void shouldHandleExceptionDuringPublishing() throws IOException {
			DipOrderConverter<CollectionOrder> converter = mock(CollectionDipOrderConverter.class);
			when(converters.stream()).thenReturn(Stream.of(converter));
			when(converter.supports(any(Order.class))).thenReturn(true);
			when(converter.convertToXML(any(CollectionOrder.class))).thenReturn("<order>content</order>");
			doNothing().when(xmlFileStorage).saveXml(anyString(), anyLong());
			when(orderXmlRepository.save(any(OrderXml.class))).thenReturn(orderXml);
			doThrow(new RuntimeException("Test Exception")).when(dipPublisher).publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString());

			boolean result = dipService.createAndPublishXmlForOrder(order);

			assertFalse(result);
			verify(dipPublisher, times(1)).publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString());
		}

		@Test
		void shouldHandleIOExceptionDuringXmlSave() throws IOException {
			DipOrderConverter<CollectionOrder> converter = mock(CollectionDipOrderConverter.class);
			when(converters.stream()).thenReturn(Stream.of(converter));
			when(converter.supports(any(Order.class))).thenReturn(true);
			when(converter.convertToXML(any(CollectionOrder.class))).thenReturn("<order>content</order>");
			doThrow(new IOException("Test IOException")).when(xmlFileStorage).saveXml(anyString(), anyLong());

			boolean result = dipService.createAndPublishXmlForOrder(order);

			assertFalse(result);
			verify(dipPublisher, never()).publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString());
			verify(orderXmlRepository, never()).save(any(OrderXml.class));
		}

		@Test
		void shouldHandleRetryMechanismWhenDipPublisherIsUnreachable() throws IOException {
			DipOrderConverter<CollectionOrder> converter = mock(CollectionDipOrderConverter.class);
			when(converters.stream()).thenReturn(Stream.of(converter));
			when(converter.supports(any(Order.class))).thenReturn(true);
			when(converter.convertToXML(any(CollectionOrder.class))).thenReturn("<order>content</order>");
			doNothing().when(xmlFileStorage).saveXml(anyString(), anyLong());
			when(orderXmlRepository.save(any(OrderXml.class))).thenReturn(orderXml);
			when(dipPublisher.publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString())).thenReturn(false);

			boolean result = dipService.createAndPublishXmlForOrder(order);

			assertFalse(result);
			verify(xmlFileStorage, times(1)).saveXml(anyString(), anyLong());
			verify(orderXmlRepository, times(2)).save(any(OrderXml.class)); // Once for initial save, once for retry count update
			assertEquals(1, orderXml.getRetryCount());
		}

	}

	@Nested
	class TryToPublish {

		@Test
		void shouldPublishSuccessfully() {
			when(dipPublisher.publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString())).thenReturn(true);
			when(documentRepository.findByOrderId(anyLong())).thenReturn(List.of());

			boolean result = dipService.tryToPublish("<order>content</order>", orderXml);

			assertTrue(result);
			verify(orderXmlRepository, times(1)).save(any(OrderXml.class));
		}

		@Test
		void shouldHandlePublishingFailure() {
			when(dipPublisher.publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString())).thenReturn(false);

			boolean result = dipService.tryToPublish("<order>content</order>", orderXml);

			assertFalse(result);
			verify(orderXmlRepository, times(1)).save(any(OrderXml.class));
		}

		@Test
		void shouldHandleExceptionDuringPublishing() {
			doThrow(new RuntimeException("Test Exception")).when(dipPublisher).publishObject(anyString(), anyString(), anyLong(), any(DipOrderType.class), anyString());

			boolean result = dipService.tryToPublish("<order>content</order>", orderXml);

			assertFalse(result);
			verify(orderXmlRepository, times(1)).save(any(OrderXml.class));
		}
	}
}