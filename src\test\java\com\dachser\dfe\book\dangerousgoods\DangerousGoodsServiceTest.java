package com.dachser.dfe.book.dangerousgoods;

import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.generaldata.GeneralDataAdapterMock;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.Segment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DangerousGoodsServiceTest {

	@Spy
	GeneralDataAdapterMock generalDataAdapterMock = new GeneralDataAdapterMock();

	@Mock
	GeneralDataService generalDataService;

	@Spy
	DangerousGoodsThirdPartyAdapterMock dangerousGoodsThirdPartyAdapterMock = new DangerousGoodsThirdPartyAdapterMock();

	@Spy
	WhiteListFilterConfiguration whiteListFilterConfiguration = new WhiteListFilterConfiguration();

	@InjectMocks
	private DangerousGoodsService dangerousGoodsService;

	@BeforeEach
	void setUp() {
		final WhiteListFilterConfiguration.DangerousGoods dangerousGoods = new WhiteListFilterConfiguration.DangerousGoods();
		final WhiteListFilterConfiguration.DangerousGoods.PackagingOptionsFilter packagingOptionsFilter = new WhiteListFilterConfiguration.DangerousGoods.PackagingOptionsFilter();
		packagingOptionsFilter.setRoad(List.of("BV", "DG", "F"));
		dangerousGoods.setPackagingOptionsFilter(packagingOptionsFilter);
		whiteListFilterConfiguration.setDangerousGoods(dangerousGoods);

	}

	@Test
	void getDangerousGoodsPackagingOptionsRoad() {
		List<OptionDto> serviceResponse = generalDataAdapterMock.getPackagingOptionsRoad("en");
		when(generalDataService.getPackagingOptionsRoad(anyString())).thenReturn(serviceResponse);

		List<OptionDto> dgPackagingOptions = dangerousGoodsService.getDangerousGoodsPackagingOptions(Segment.ROAD);
		assertNotNull(dgPackagingOptions);
		assertFalse(dgPackagingOptions.isEmpty());
		assertEquals(3, dgPackagingOptions.size());
		assertTrue(dgPackagingOptions.stream().anyMatch(option -> option.getCode().equals("BV")));
		assertTrue(dgPackagingOptions.stream().anyMatch(option -> option.getCode().equals("DG")));
		assertTrue(dgPackagingOptions.stream().anyMatch(option -> option.getCode().equals("F")));
	}

	@Test
	void getDangerousGoodsPackagingOptionsAir() {
		List<OptionDto> dgPackagingOptions = dangerousGoodsService.getDangerousGoodsPackagingOptions(Segment.AIR);
		assertNotNull(dgPackagingOptions);
		assertTrue(dgPackagingOptions.isEmpty());
		assertEquals(0, dgPackagingOptions.size());
	}

	@Test
	void shouldReturnDangerousGoodsRoad() {
		List<DangerousGoodDataItemDto> dangerousGoodsByUNNumber = dangerousGoodsService.findDangerousGoodsByUNNumber(Segment.ROAD, "1791");

		assertEquals(2, dangerousGoodsByUNNumber.size());
	}

}