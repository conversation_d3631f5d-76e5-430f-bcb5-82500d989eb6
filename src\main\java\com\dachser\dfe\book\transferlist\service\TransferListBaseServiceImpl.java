package com.dachser.dfe.book.transferlist.service;

import com.dachser.dfe.book.jpa.OrderSsccRepository;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderLineDetailDto;
import com.dachser.dfe.book.model.PackingPositionDto;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.model.TransferListQueryObjectDto;
import com.dachser.dfe.book.overview.CachedOrderOverviewService;
import com.dachser.dfe.book.packaging.PackagingOptionsService;
import com.dachser.dfe.book.transferlist.entity.VTransferList;
import com.dachser.dfe.book.transferlist.exception.TransferListDatabaseException;
import com.dachser.dfe.book.transferlist.repository.VTransferListRepository;
import com.dachser.dfe.book.user.UserContextService;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

@NoArgsConstructor(force = true)
@Slf4j
public abstract class TransferListBaseServiceImpl {

	private final VTransferListRepository transferListRepository;

	protected final UserContextService userContextService;

	private final RoadOrderLineService roadOrderLineService;

	private final PackingPositionService packingPositionService;

	private final CachedOrderOverviewService cachedOrderOverviewService;

	private final PackagingOptionsService packagingOptionsService;

	private final OrderSsccRepository orderSsccRepository;

	protected TransferListBaseServiceImpl(VTransferListRepository transferListRepository, UserContextService userContextService,
			CachedOrderOverviewService cachedOrderOverviewService, RoadOrderLineService roadOrderLineService, PackingPositionService packingPositionService,
			PackagingOptionsService packagingOptionsService, OrderSsccRepository orderSsccRepository) {
		this.transferListRepository = transferListRepository;
		this.userContextService = userContextService;
		this.roadOrderLineService = roadOrderLineService;
		this.packingPositionService = packingPositionService;
		this.cachedOrderOverviewService = cachedOrderOverviewService;
		this.packagingOptionsService = packagingOptionsService;
		this.orderSsccRepository = orderSsccRepository;
	}

	protected LocalDate parse(String value) {
		try {
			return LocalDate.parse(value);
		} catch (Exception e) {
			log.info("Failed to parse DATE: {}", e.getMessage());
			return null;
		}
	}

	protected boolean validate(TransferListQueryObjectDto queryObject) {
		return queryObject != null && Objects.nonNull(queryObject.getPickupDate()) && Objects.nonNull(queryObject.getAddressHash());
	}

	protected List<VTransferList> fetchTransferList(TransferListQueryObjectDto queryObject) {
		if (!validate(queryObject)) {
			return List.of();
		}
		try {
			return transferListRepository.findAll(
					userContextService.getAllBookPermittedCustomerIdsWithSegment(),
					queryObject.getAddressHash(),
					queryObject.getPickupDate()
			);
		} catch (Exception ex) {
			String errorMsg = String.format("Transfer List: Error while retrieving data from database: %s.", ex.getMessage());
			log.error(errorMsg, ex);
			throw new TransferListDatabaseException(errorMsg);
		}
	}

	protected String lookupPackagingDescription(String packagingAid) {
		return packagingOptionsService.getPackagingOptions(Segment.ROAD).stream().filter(aid -> packagingAid.equals(aid.getCode()))
				.findFirst().map(OptionDto::getDescription).orElseGet(() -> {
					log.warn("No description found for packaging aid: " + packagingAid);
					return packagingAid;
				});
	}

	protected List<OrderLineDetailDto> getRoadOrderLines(Long orderId) {
		List<OrderLineDetailDto> roadOrderLines = roadOrderLineService.getRoadOrderLines(orderId);
		return roadOrderLines == null ? Collections.emptyList() : roadOrderLines;
	}

	protected List<PackingPositionDto> getPackingPositions(Long orderId) {
		List<PackingPositionDto> roadPackingPositions = packingPositionService.getRoadPackingPositions(orderId);
		return roadPackingPositions == null ? Collections.emptyList() : roadPackingPositions;
	}

	protected void mapPackingPosition(List<PackingPositionDto> packingPositions) {
		packingPositions.forEach(packingPosition -> {
			OptionDto packagingType = packingPosition.getPackagingType();
			packagingType.setDescription(lookupPackagingDescription(packagingType.getCode()));

			packingPosition.getLines().forEach(orderLine -> {
				OptionDto packaging = orderLine.getPackaging();
				packaging.setDescription(lookupPackagingDescription(packaging.getCode()));
			});
		});
	}

	protected void mapProductName(VTransferList transferListItem) {
		String productName = cachedOrderOverviewService.getProductNameOrError(transferListItem.getProduct(), transferListItem.getDivision());
		transferListItem.setProductName(productName);
	}

	protected <T> Comparator<T> compareByConsigneeAddressName(Function<T, String> getConsigneeAddressName) {
		return Comparator.comparing(o -> {
			String name = getConsigneeAddressName.apply(o);
			return name != null ? name : null;
		}, Comparator.nullsLast(Comparator.naturalOrder()));
	}

	protected int getTotalLabelCount(List<VTransferList> transferList) {
		return orderSsccRepository.countOrderSsccsByOrderIds(transferList.stream().map(VTransferList::getOrderId).toList());
	}

	protected int getLabelCount(Long orderId) {
		return orderSsccRepository.countOrderSsccsByOrderIds(List.of(orderId));
	}

}
