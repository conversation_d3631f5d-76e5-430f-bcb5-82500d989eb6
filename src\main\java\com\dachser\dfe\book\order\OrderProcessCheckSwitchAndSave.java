package com.dachser.dfe.book.order;

import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.OrderValidationResultDto;
import com.dachser.dfe.book.order.emissionforecast.EmissionForecastService;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class OrderProcessCheckSwitchAndSave {

	private final OrderValidator orderValidator;

	private final OrderRepositoryFacade orderRepository;

	private final DocumentService documentService;

	private final EntityManager entityManager;

	private final OrderTypeSwitchService orderTypeSwitchService;

	private final EmissionForecastService emissionForecastService;

	@Transactional(Transactional.TxType.REQUIRES_NEW)
	public CheckSwitchAndSaveResult checkSwitchAndSave(Order currentOrder, BasicOrderDto orderDto) {
		// Need to have the switch here because of the transactional boundary
		currentOrder = orderTypeSwitchService.switchOrderType(currentOrder, orderDto);

		// Will abort on validation error and handle conversion in global exception handler
		OrderValidationResultDto validationResult = orderValidator.checkValidateOrder(currentOrder);
		// Because we are using an object from another transaction as input we need to manually sync it back to state, in case of transfer of customer
		// numbers an exception would occur otherwise
		entityManager.merge(currentOrder);
		documentService.deleteDocuments(orderDto, currentOrder);
		documentService.overrideCustomerNumberInDocuments(currentOrder.getDocumentIds(), currentOrder.getOrderId(), currentOrder.getCustomerNumber());
		fillEmissionForecast(currentOrder);

		checkForShipmentInformationChangesOnForwardingOrder(currentOrder);
		orderRepository.save(currentOrder);

		return new CheckSwitchAndSaveResult(currentOrder, validationResult);
	}

	private void checkForShipmentInformationChangesOnForwardingOrder(Order currentOrder) {
		if (currentOrder instanceof ForwardingOrder currentForwardingOrder) {
			final Integer previousHash = currentForwardingOrder.getShipmentInformationHash();
			currentForwardingOrder.setShipmentInformationHash(currentForwardingOrder.calculateShipmentInformationHash());
			if (!Objects.equals(currentForwardingOrder.getShipmentInformationHash(), previousHash)) {
				currentForwardingOrder.setLabelPrinted(false);
			}
		}
	}

	private void fillEmissionForecast(Order currentOrder) {
		if (currentOrder.getEmissionForecast() == null) {
			Optional<Double> emissionForecastForOrder = emissionForecastService.getEmissionForecastForOrder(currentOrder);

			if (emissionForecastForOrder.isPresent()) {
				currentOrder.setEmissionForecast(emissionForecastForOrder.get());
			} else {
				currentOrder.setEmissionForecast(null);
				currentOrder.setEmissionForecastRetryCount(1);
			}
		}
	}

	public record CheckSwitchAndSaveResult(Order order, OrderValidationResultDto validationResult) {
	}

}
