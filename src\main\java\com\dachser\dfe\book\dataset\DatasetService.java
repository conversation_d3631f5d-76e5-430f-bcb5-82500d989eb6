package com.dachser.dfe.book.dataset;

import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.dataset.model.CountryDataset;
import com.dachser.dfe.book.dataset.model.CountrySampleData;
import com.dachser.dfe.book.dataset.model.ForwardingOrderDataset;
import com.dachser.dfe.book.dataset.model.IncoTermDataset;
import com.dachser.dfe.book.dataset.model.ProductDataset;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.mapper.order.ConsignmentPrintBeanMapper;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.product.road.RoadProductsAdapter;
import com.dachser.dfe.book.term.IncoTermService;
import com.dachser.dfe.masterdata.geo.api.LocationsApi;
import com.dachser.dfe.masterdata.geo.model.GMDPostalCodeValidation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Service
@Profile({ "apitest", "local", "dev", "test" })
@RequiredArgsConstructor
@Slf4j
class DatasetService {

	private final CountryService countryService;

	private final LocationsApi locationsApi;

	private final ConsignmentPrintBeanMapper consignmentPrintBeanMapper;

	private final RoadProductsAdapter roadProductsAdapter;

	private final IncoTermService incoTermService;

	private final WhiteListFilterConfiguration whiteListFilterConfiguration;

	ForwardingOrderDataset getForwardingOrderDataset(String consigneeCountryCode, String consigneePostalCode, String shipperCountryCode, String shipperPostalCode) {
		final ForwardingOrderDataset forwardingOrderDataset = new ForwardingOrderDataset();

		forwardingOrderDataset.getCountries().addAll(getCountryDatasets());
		forwardingOrderDataset.getProducts().addAll(getProductDatasets(consigneeCountryCode, consigneePostalCode, shipperCountryCode, shipperPostalCode));
		forwardingOrderDataset.getIncoTerms().addAll(getIncotermDatasets(consigneeCountryCode, consigneePostalCode));

		return forwardingOrderDataset;
	}

	private List<CountryDataset> getCountryDatasets() {
		final List<CountrySampleData> countrySampleData = CountrySampleData.getCountryRecords();

		final List<CountryDataset> countryDatasets = countrySampleData.stream().map(countrySample -> {
			final CountryDataset countryDataset = new CountryDataset();
			countryDataset.setIsoCountryCode(countrySample.isoCountryCode());
			countryDataset.setDescription(countrySample.description());
			countryDataset.setExamplePostcode(countrySample.examplePostcode());
			return countryDataset;
		}).toList();

		final List<CompletableFuture<Void>> futures = countryDatasets.stream().map(dataset -> CompletableFuture.runAsync(() -> {
			try {
				enrichWithDachserCode(dataset);
				validateAndSetPostcode(dataset);
			} catch (Exception e) {
				log.warn("Error while enriching {}: {}", dataset.getIsoCountryCode(), e.getMessage());
			}
		})).toList();

		CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

		return countryDatasets;
	}

	private void enrichWithDachserCode(CountryDataset countryDataset) {
		countryDataset.setDachserCountryCode(countryService.mapToDachserCountryCode(countryDataset.getIsoCountryCode()));
	}

	private void validateAndSetPostcode(CountryDataset countryDataset) {
		GMDPostalCodeValidation zipCodeValidation = locationsApi.validatePostalCode(countryDataset.getIsoCountryCode(), countryDataset.getExamplePostcode());
		countryDataset.setPostCodeFromDataset(countryDataset.getExamplePostcode());
		countryDataset.setCleanedUpPostcode(consignmentPrintBeanMapper.cleanPostCode(countryDataset.getExamplePostcode()));
		countryDataset.setExamplePostcode(zipCodeValidation.getExampleZipCode());
		countryDataset.setPostcodeValid(zipCodeValidation.getValidRegex());
	}

	private List<ProductDataset> getProductDatasets(String consigneeCountryCode, String consigneePostalCode, String shipperCountryCode, String shipperPostalCode) {
		final List<ProductDataset> availableProducts = new ArrayList<>();

		List<DeliveryProductDto> deliveryProductsEuropeanLogistics = roadProductsAdapter.getOnlyValidDeliveryProducts(Division.T, 1,
				whiteListFilterConfiguration.getDeliveryProducts().getRoad().getEuropeanLogistics(), shipperCountryCode, shipperPostalCode, consigneeCountryCode,
				consigneePostalCode);

		for (DeliveryProductDto deliveryProduct : deliveryProductsEuropeanLogistics) {
			final ProductDataset productDataset = new ProductDataset();
			productDataset.setProductCode(deliveryProduct.getCode());
			productDataset.setDescription(deliveryProduct.getDescription());
			productDataset.setDivision(Division.T.getDivisionName());
			availableProducts.add(productDataset);
		}

		List<DeliveryProductDto> deliveryProductsFoodLogistics = roadProductsAdapter.getOnlyValidDeliveryProducts(Division.F, 1,
				whiteListFilterConfiguration.getDeliveryProducts().getRoad().getFoodLogistics(), shipperCountryCode, shipperPostalCode, consigneeCountryCode, consigneePostalCode);

		for (DeliveryProductDto deliveryProduct : deliveryProductsFoodLogistics) {
			final ProductDataset productDataset = new ProductDataset();
			productDataset.setProductCode(deliveryProduct.getCode());
			productDataset.setDescription(deliveryProduct.getDescription());
			productDataset.setDivision(Division.F.getDivisionName());
			availableProducts.add(productDataset);
		}

		log.info("Found {} products for country code {} and postal code {}", availableProducts.size(), consigneeCountryCode, consigneePostalCode);
		return availableProducts;
	}

	private List<IncoTermDataset> getIncotermDatasets(String consigneeCountryCode, String consigneePostalCode) {
		final List<IncoTermDataset> availableIncoterms = new ArrayList<>();

		incoTermService.getAllActiveIncoTerms().forEach(incoTerm -> {
			final IncoTermDataset incoTermDataset = new IncoTermDataset();
			incoTermDataset.setDachserTerm(incoTerm.getDachserCode());
			incoTermDataset.setDescription(incoTerm.getDescription());
			incoTermDataset.setIncoTerm(incoTerm.getCode());
			availableIncoterms.add(incoTermDataset);
		});

		log.info("Found {} incoTerms for country code {} and postal code {}", availableIncoterms.size(), consigneeCountryCode, consigneePostalCode);
		return availableIncoterms;
	}
}
