package com.dachser.dfe.book.order.validation.air;

import com.dachser.dfe.book.document.Document;
import com.dachser.dfe.book.document.DocumentConst;
import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.air.AirOrderReference;
import com.dachser.dfe.book.order.validation.PayloadProvidingValidator;
import com.dachser.dfe.book.order.validation.common.AirSeaOrderConstraintValidator;
import com.dachser.dfe.book.term.IncoTermService;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
@Slf4j
final class AirOrderConstraintValidator extends AirSeaOrderConstraintValidator<AirOrderReference, AirOrder>
		implements ConstraintValidator<ValidAirOrder, AirOrder>, PayloadProvidingValidator {

	private final DocumentService documentService;

	private final Translator translator;

	@Autowired
	public AirOrderConstraintValidator(DocumentService documentService, Translator translator, IncoTermService incoTermService) {
		super(translator, incoTermService);
		this.documentService = documentService;
		this.translator = translator;
	}

	@Override
	public boolean isValid(AirOrder order, ConstraintValidatorContext context) {
		context.disableDefaultConstraintViolation();
		AtomicBoolean anyInvalid = new AtomicBoolean(false);
		// Only validate document dependant topics if there is already an order id, which is the case when the order is already persisted
		final List<Document> documentsOfOrder = documentService.getDocumentsOfOrder((Order) order);

		if (!hasAtLeastOneCommercialInvoiceAdded(documentsOfOrder, order)) {
			context.buildConstraintViolationWithTemplate(translator.toLocale("label.text.commercial_invoice_necessary")).addPropertyNode("orderDocuments").addConstraintViolation();
			anyInvalid.set(true);
		}

		if (!isOrderValid(order, context)) {
			anyInvalid.set(true);
		}
		return !anyInvalid.get();
	}

	protected String getCombinedOrderLineGoods(AirOrder order) {
		return order.getCombinedOrderLineGoods();
	}

	@Override
	protected boolean hasInvalidIncoTerm(AirOrder order) {
		return hasInvalidIncoTerm(order.getIncoTerm());
	}

	boolean hasAtLeastOneCommercialInvoiceAdded(List<Document> documentsOfOrder, AirOrder airOrder) {
		List<Long> notYetDeletedDocuments = documentsOfOrder.stream().map(Document::getDocumentId).filter(id -> !airOrder.getDocumentIds().contains(id)).toList();

		List<Document> commercialInvoices = documentsOfOrder.stream().filter(document -> document.getDocumentType() != null)
				.filter(document -> document.getDocumentType().getType().equals(DocumentConst.TYPE_COMMERCIAL_INVOICE)).toList();

		if (!notYetDeletedDocuments.isEmpty()) {
			List<Long> difference = commercialInvoices.stream().map(Document::getDocumentId).filter(id -> !notYetDeletedDocuments.contains(id)).toList();
			return !difference.isEmpty();
		}

		return !commercialInvoices.isEmpty();
	}

}
