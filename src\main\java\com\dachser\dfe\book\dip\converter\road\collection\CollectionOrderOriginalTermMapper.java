package com.dachser.dfe.book.dip.converter.road.collection;

import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.FreightPayerType;
import com.dachser.dfe.book.service.ThirdCountryService;
import com.dachser.dfe.book.user.BusinessDomainProvider;
import lombok.RequiredArgsConstructor;
import org.mapstruct.Named;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@RequiredArgsConstructor
public class CollectionOrderOriginalTermMapper {

	private static final String TERM_EX_WORKS = "011";

	private static final String TERM_FREE_DELIVERED = "031";

	private static final String TERM_FREE_DELIVERED_UNCLEARED_BEFORE_TAX = "081";

	private final ThirdCountryService service;

	private final BusinessDomainProvider businessDomainProvider;

	@Named("mapOriginalTerm")
	public String mapOriginalTerm(CollectionOrder order) {
		FreightPayerType freightPayer = order.getFreightPayer();

		if (Objects.isNull(freightPayer)) {
			return TERM_EX_WORKS;
		}

		// Case 1: Consignee pays and a different consignee address is provided (consignee != principal)
		if (Objects.nonNull(order.getDifferentConsigneeAddress()) && freightPayer.equals(FreightPayerType.CONSIGNEE)) {
			return TERM_EX_WORKS;
		}

		// Case 2: Consignee == Principal → always "011"
		if (Objects.isNull(order.getDifferentConsigneeAddress()) && freightPayer.equals(FreightPayerType.PRINCIPAL)) {
			return TERM_EX_WORKS;
		}

		// Case 3: Principal pays and a different consignee address is provided
		if (Objects.nonNull(order.getDifferentConsigneeAddress()) && freightPayer.equals(FreightPayerType.PRINCIPAL)) {
			String collectionCountry = order.getShipperAddress().getCountryCode();
			String consigneeCountry = order.getDifferentConsigneeAddress().getCountryCode();

			boolean noThirdCountryConstellation = !service.isThirdCountryConstellation(businessDomainProvider.getBusinessDomain(), collectionCountry, consigneeCountry);

			// Case 4: If the collection country is the same as the consignee country or there is no third country constellation
			if (Objects.equals(collectionCountry, consigneeCountry) || noThirdCountryConstellation) {
				return TERM_FREE_DELIVERED;
			} else {
				// Case 5: If there is a third country constellation, return "081"
				return TERM_FREE_DELIVERED_UNCLEARED_BEFORE_TAX;
			}
		}

		// Default case: If none of the above conditions apply
		return TERM_EX_WORKS;
	}
}
