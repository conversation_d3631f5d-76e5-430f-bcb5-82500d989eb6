package com.dachser.dfe.book.order;

import com.dachser.dfe.book.order.road.ForwardingOrder;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class OrderLabelContainerTest {

	@Test
	void testAddShipmentLabel() {
		OrderLabelContainer container = new OrderLabelContainer();
		byte[] label = new byte[] { 1, 2, 3 };
		Long orderId = 123L;

		container.addShipmentLabel(label, orderId);

		assertFalse(container.getRawShipmentLabels().isEmpty());
		assertEquals(1, container.getRawShipmentLabels().size());
		assertEquals(label, container.getRawShipmentLabels().get(0));
		assertEquals(orderId, container.getOrderIds().get(0));
	}

	@Test
	void testAddConsignmentLabel() {
		OrderLabelContainer container = new OrderLabelContainer();
		byte[] label = new byte[] { 1, 2, 3 };

		container.addConsignmentLabel(label);

		assertFalse(container.getRawConsignmentLabels().isEmpty());
		assertEquals(1, container.getRawConsignmentLabels().size());
		assertEquals(label, container.getRawConsignmentLabels().get(0));
	}

	@Test
	void testMerge() {
		OrderLabelContainer container1 = new OrderLabelContainer();
		OrderLabelContainer container2 = new OrderLabelContainer();
		byte[] label1 = new byte[] { 1, 2, 3 };
		byte[] label2 = new byte[] { 4, 5, 6 };
		Long orderId1 = 123L;
		Long orderId2 = 456L;

		container1.addShipmentLabel(label1, orderId1);
		container2.addShipmentLabel(label2, orderId2);

		container1.merge(container2);

		assertEquals(2, container1.getRawShipmentLabels().size());
		assertEquals(2, container1.getOrderIds().size());
		assertEquals(label1, container1.getRawShipmentLabels().get(0));
		assertEquals(label2, container1.getRawShipmentLabels().get(1));
		assertEquals(orderId1, container1.getOrderIds().get(0));
		assertEquals(orderId2, container1.getOrderIds().get(1));
	}

	@Test
	void testMergeConsignmentLabels() {
		OrderLabelContainer container1 = new OrderLabelContainer();
		OrderLabelContainer container2 = new OrderLabelContainer();
		byte[] consignmentLabel1 = new byte[] { 1, 2, 3 };
		byte[] consignmentLabel2 = new byte[] { 4, 5, 6 };
		ForwardingOrder forwardingOrder = new ForwardingOrder();

		container1.addConsignmentLabel(consignmentLabel1);
		container2.setForwardingOrder(forwardingOrder);
		container2.addConsignmentLabel(consignmentLabel2);

		container1.merge(container2);

		assertEquals(2, container1.getRawConsignmentLabels().size());
		assertEquals(consignmentLabel1, container1.getRawConsignmentLabels().get(0));
		assertEquals(consignmentLabel2, container1.getRawConsignmentLabels().get(1));
		assertEquals(forwardingOrder, container1.getForwardingOrder());
	}

	@Test
	void testLockWithTransformed() {
		OrderLabelContainer container = new OrderLabelContainer();
		byte[] transformed = new byte[] { 1, 2, 3 };

		container.lockWithTransformed(transformed);

		assertTrue(container.isLocked());
		assertEquals(transformed, container.getTransformedLabels());
		assertNotNull(container.getTransformedBase64());
		assertNull(container.getRawShipmentLabels());
		assertNull(container.getRawConsignmentLabels());
	}

	@Test
	void testAddShipmentLabelToLockedContainer() {
		OrderLabelContainer container = new OrderLabelContainer();
		byte[] transformed = new byte[] { 1, 2, 3 };
		container.lockWithTransformed(transformed);

		Exception exception = assertThrows(IllegalStateException.class, () -> container.addShipmentLabel(new byte[] { 4, 5, 6 }, 123L));

		assertEquals("Cannot add shipment label to locked container", exception.getMessage());
	}

	@Test
	void testAddConsignmentLabelToLockedContainer() {
		OrderLabelContainer container = new OrderLabelContainer();
		byte[] transformed = new byte[] { 1, 2, 3 };
		container.lockWithTransformed(transformed);

		Exception exception = assertThrows(IllegalStateException.class, () -> container.addConsignmentLabel(new byte[] { 4, 5, 6 }));

		assertEquals("Cannot add consignment label to locked container", exception.getMessage());
	}

	@Test
	void testMergeLockedContainers() {
		OrderLabelContainer container1 = new OrderLabelContainer();
		OrderLabelContainer container2 = new OrderLabelContainer();
		byte[] transformed = new byte[] { 1, 2, 3 };
		container1.lockWithTransformed(transformed);

		Exception exception = assertThrows(IllegalStateException.class, () -> container1.merge(container2));

		assertEquals("Cannot merge locked containers", exception.getMessage());
	}

}