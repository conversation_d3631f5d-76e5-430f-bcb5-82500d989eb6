package com.dachser.dfe.book.user.model;

import com.dachser.dfe.book.jpa.entity.Division;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RoadCustomer implements Customer{

	private UUID id;

	private String name;

	private String customerNumber;

	private OrderOptions orderOptions;

	private Integer branchId;

	private Division division;

	private Address address;

	private Integer bookForwardingElBranchNumber;

	private Integer bookForwardingFlBranchNumber;

	private Integer bookCollectingElBranchNumber;

	private Integer bookCollectingFlBranchNumber;

	private boolean cashOnDelivery;

}
