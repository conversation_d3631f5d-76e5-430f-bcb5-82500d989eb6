package com.dachser.dfe.book.dip.converter.road.forwarding;

import com.dachser.dfe.book.dip.converter.road.RoadShipmentLineMapper;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.Height;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.Length;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.LoadingMeter;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.Measurements;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.ShipmentLine;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.Volume;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.Weight;
import com.dachser.dfe.book.model.jaxb.order.road.forwarding.Width;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, uses = { ForwardingDangerousGoodsMapper.class}, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface ForwardingShipmentLineMapper extends RoadShipmentLineMapper<ShipmentLine, Measurements, Weight, Height, Length, Width, Volume, LoadingMeter> {
}
