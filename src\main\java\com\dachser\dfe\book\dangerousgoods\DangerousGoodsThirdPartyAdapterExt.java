package com.dachser.dfe.book.dangerousgoods;

import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.dfe.book.service.Interruptible;
import com.dachser.dfe.masterdata.thirdparty.api.DangerousGoodsApiApi;
import com.dachser.masterdata.thirdparty.model.MDTCalculationPoint;
import com.dachser.masterdata.thirdparty.model.MDTCalculationPointResult;
import com.dachser.masterdata.thirdparty.model.MDTDangerousGoodInformation;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@RequiredArgsConstructor
@Component
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "false", matchIfMissing = true)
@Interruptible
@Slf4j
public class DangerousGoodsThirdPartyAdapterExt implements DangerousGoodsThirdPartyAdapter {

	private final DangerousGoodsApiApi dangerousGoodsApi;

	private final DangerousGoodsMasterDataMapper dangerousGoodsMasterDataMapper;

	@Nonnull
	@Override
	public List<DangerousGoodDataItemDto> searchUnNumbersRoad(@Nonnull String unNumber, @Nonnull String countryCode) {
		log.debug("Requesting unNumber: {} for countryCode: {}", unNumber, countryCode);
		try {
			final Optional<List<MDTDangerousGoodInformation>> dangerousGoodsExtendedOpt = Optional.ofNullable(
					dangerousGoodsApi.findDangerousGoodsExtended(" ", unNumber, countryCode));
			final List<DangerousGoodDataItemDto> dangerousGoodDataItemDtos = mapDangerousGoodsInformationToDto(dangerousGoodsExtendedOpt);
			if (dangerousGoodDataItemDtos.isEmpty()) {
				log.debug("No dangerous goods found for unNumber: {} and countryCode: {}", unNumber, countryCode);
			} else {
				log.debug("Found {} dangerous goods for unNumber: {} and countryCode: {}", dangerousGoodDataItemDtos.size(), unNumber, countryCode);
			}
			return dangerousGoodDataItemDtos;
		} catch (RestClientException e) {
			log.error("Error while searching UN numbers for unNumber: {} and countryCode: {}", unNumber, countryCode, e.getMessage());
		}
		return List.of();
	}

	/*
	 * Fetches localized dangerous goods information for a list of DGM IDs.
	 * This method retrieves the dangerous goods information based on the provided DGM IDs and language code.
	 * @param dgmIds List of DGM IDs to fetch information for.
	 * @param isoLanguage The ISO language code for localization.
	 * @return A list of DangerousGoodDataItemDto containing the localized dangerous goods information.
	 *
	 * The api defines a countryCode parameter for language specific requests, which is actually just used as a language code.
	 * We don't provide a countryCode in the locale context, so we use the language code instead.
	 */
	@Nonnull
	@Override
	public List<DangerousGoodDataItemDto> fetchLocalizedInformationForDgmIdsRoad(@Nonnull List<String> dgmIds, @Nonnull String isoLanguage) {
		log.debug("Fetching dangerous goods information for dgmIds: {} and language: {}", dgmIds, isoLanguage);
		try {
			final Optional<List<MDTDangerousGoodInformation>> dangerousGoodsExtendedOpt = Optional.ofNullable(dangerousGoodsApi.getInformationMulti(isoLanguage, dgmIds));
			List<DangerousGoodDataItemDto> dangerousGoodDataItemDtos = mapDangerousGoodsInformationToDto(dangerousGoodsExtendedOpt);
			if (dangerousGoodDataItemDtos.isEmpty()) {
				log.debug("No dangerous goods found for unNumbers: {} and language: {}", dgmIds, isoLanguage);
			} else {
				log.debug("Found {} dangerous goods for unNumbers: {} and language: {}", dangerousGoodDataItemDtos.size(), dgmIds, isoLanguage);
			}
			return dangerousGoodDataItemDtos;
		} catch (RestClientException e) {
			log.error("Error while fetching localized dangerous goods information for dgmIds: {} and language: {}", dgmIds, isoLanguage, e);
			throw new RuntimeException("Failed to fetch localized dangerous goods information", e);
		}
	}

	@Nonnull
	@Override
	public List<DangerousGoodsTransferListService.DgmIdAndQuantity> calculatePointsForSingleValues(@Nonnull String shipperCountryCode, @Nonnull List<MDTCalculationPoint> points) {
		try {
			log.debug("Calculating dangerous goods points for countryCode: {} with {} points", shipperCountryCode, points.size());
			List<MDTCalculationPointResult> pointResults = dangerousGoodsApi.singleValuesOfCalculationPoints(shipperCountryCode, points);
			return pointResults.stream().filter(Objects::nonNull).map(dangerousGoodsMasterDataMapper::mapDgmIdAndQuantity).toList();
		} catch (RestClientException e) {
			log.error("Error while calculating dangerous goods points", e);
			throw new RuntimeException("Failed to calculate dangerous goods points", e);
		}
	}

	@Nonnull
	private List<DangerousGoodDataItemDto> mapDangerousGoodsInformationToDto(Optional<List<MDTDangerousGoodInformation>> dangerousGoodsExtendedOpt) {
		return dangerousGoodsExtendedOpt.stream().flatMap(List::stream).filter(Objects::nonNull).map(dangerousGoodsMasterDataMapper::mapRoad)
				.filter(dgi -> StringUtils.isNotEmpty(dgi.getUnNumber())).toList();
	}
}
