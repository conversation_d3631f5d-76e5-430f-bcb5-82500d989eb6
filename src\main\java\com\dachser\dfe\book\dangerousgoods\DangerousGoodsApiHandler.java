package com.dachser.dfe.book.dangerousgoods;

import com.dachser.dfe.book.api.DangerousGoodsApiDelegate;
import com.dachser.dfe.book.customer.SegmentMapper;
import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.SegmentDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class DangerousGoodsApiHandler implements DangerousGoodsApiDelegate {

	private final DangerousGoodsService dangerousGoodsService;

	private final SegmentMapper segmentMapper;

	@Override
	public ResponseEntity<List<DangerousGoodDataItemDto>> findUnNumbers(String searchFor, SegmentDto customerSegment) {
		List<DangerousGoodDataItemDto> dangerousGoodsByUNNumber = dangerousGoodsService.findDangerousGoodsByUNNumber(segmentMapper.map(customerSegment), searchFor);
		if (dangerousGoodsByUNNumber.isEmpty()) {
			log.debug("No dangerous goods found for UN number: {}", searchFor);
			return ResponseEntity.noContent().build();
		} else {
			log.debug("Found dangerous goods for UN number {}: {}", searchFor, dangerousGoodsByUNNumber);
			return ResponseEntity.ok(dangerousGoodsByUNNumber);
		}
	}

	@Override
	public ResponseEntity<List<OptionDto>> findPackagingOptions(SegmentDto customerSegment) {
		List<OptionDto> packagingOptions = dangerousGoodsService.getDangerousGoodsPackagingOptions(segmentMapper.map(customerSegment));
		if (packagingOptions.isEmpty()) {
			log.debug("No packaging options found for dangerous goods.");
			return ResponseEntity.noContent().build();
		} else {
			log.debug("Found packaging options for dangerous goods: {}", packagingOptions);
			return ResponseEntity.ok(packagingOptions);
		}
	}
}
