package com.dachser.dfe.book.order;

import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.OrderResponseBodyDto;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class DraftOrderProcessor {

	private final OrderDefaults orderDefaults;

	private final OrderMapper orderMapper;

	private final DocumentService documentService;

	private final OrderRepositoryFacade orderRepository;

	public OrderResponseBodyDto createNewDraftOrder(@Nullable final Order order, @Nullable final BasicOrderDto orderDto) {
		if (order == null && orderDto == null) {
			log.warn("Order and OrderDto are both null, either Order or OrderDto must be provided!");
			throw new IllegalArgumentException("Order or OrderDto must be provided");
		}

		final Order orderToCreate = order != null ? order : orderMapper.map(orderDto);

		try {
			log.debug("Recording new draft order of type {} being created for customer {} ", orderToCreate.getOrderType(), orderToCreate.getCustomerNumber());
			final Order createdOrder = createNewOrder(orderToCreate);
			documentService.overrideCustomerNumberInDocuments(createdOrder.getOrderId(), createdOrder.getCustomerNumber());
			return orderMapper.map(createdOrder);
		} catch (Exception exception) {
			log.error("Error while creating new draft order of type {} being created for content {}", orderToCreate.getOrderType(), orderDto);
			log.debug("Exception while creating draft order ", exception);
			throw exception;
		}
	}

	<T extends Order> T createNewOrder(final T order) {
		orderDefaults.prefillDefaults(order);
		final T savedOrder = orderRepository.save(order);
		log.info("Saved new order with id #{} and shipment number {} for customer {}", order.getOrderId(), order.getShipmentNumber(), order.getCustomerNumber());
		return savedOrder;
	}
}
