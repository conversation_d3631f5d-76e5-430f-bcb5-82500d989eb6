package com.dachser.dfe.book.resthandler;

import com.dachser.dfe.book.BaseOpenApiTest;
import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.document.Document;
import com.dachser.dfe.book.document.DocumentRepositoryFacade;
import com.dachser.dfe.book.document.DocumentStatus;
import com.dachser.dfe.book.document.DocumentType;
import com.dachser.dfe.book.document.DocumentTypeRepository;
import com.dachser.dfe.book.document.FileType;
import com.dachser.dfe.book.jpa.AirOrderReferenceRepository;
import com.dachser.dfe.book.jpa.OrderFurtherAddressRepository;
import com.dachser.dfe.book.jpa.OrderReferenceRepository;
import com.dachser.dfe.book.jpa.SeaOrderReferenceRepository;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.order.OrderRepositoryFacade;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirOrderLine;
import com.dachser.dfe.book.order.air.AirOrderReference;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaOrderReference;
import com.dachser.dfe.book.product.air.AirProductService;
import com.dachser.dfe.trackandtrace.model.TTFilter;
import com.dachser.dfe.trackandtrace.model.TTKeys;
import com.dachser.dfe.trackandtrace.model.TTOrderType;
import com.dachser.dfe.trackandtrace.model.TTQueryObject;
import com.dachser.dfe.trackandtrace.model.TTSortDirection;
import com.dachser.dfe.trackandtrace.model.TTSorting;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import io.restassured.module.mockmvc.specification.MockMvcRequestSpecification;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class OverviewApiHandlerTest extends BaseOpenApiTest implements ResourceLoadingTest {
	private static final String CUSTOMER_NUMBER = "00005000";

	private static final LocalDateTime FORWARDING_ORDER_ORDER_SENT = LocalDateTime.of(2020, 10, 10, 0, 0);

	private static final LocalDateTime COLLECTION_ORDER_ORDER_SENT = LocalDateTime.of(2019, 10, 10, 0, 0);

	@Autowired
	OrderRepositoryFacade orderRepository;

	@Autowired
	DocumentRepositoryFacade documentRepository;

	@Autowired
	OrderReferenceRepository orderReferenceRepository;

	@Autowired
	AirOrderReferenceRepository airOrderReferenceRepository;

	@Autowired
	SeaOrderReferenceRepository seaOrderReferenceRepository;

	@Autowired
	DocumentTypeRepository documentTypeRepository;

	@Autowired
	OrderFurtherAddressRepository orderFurtherAddressRepository;

	@MockBean
	AirProductService airProductService;

	@BeforeEach
	void setupMocks() {
		when(airProductService.getAirProductByProductCode(anyString())).thenAnswer(
				invocation -> Optional.of(new AirProductDto(invocation.getArgument(0), "test").hint("description").active(true)));
	}

	@Nested
	@TestInstance(TestInstance.Lifecycle.PER_CLASS)
	class WithValidUser {

		@BeforeAll
		void dataPrep() {
			prepareData();
		}

		@Nested
		class SortingRequests {

			@Test
			void testSortingCollectionAddressDesc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.DESC);
				sorting.setKey(TTKeys.COLLECTION_ADDRESS_NAME);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, HashMap<String, String>>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals("co-a", orderOverviewContent.get(0).get("collectionAddress").get("name"));
			}

			@Test
			void testSortingCollectionAddressAsc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.ASC);
				sorting.setKey(TTKeys.COLLECTION_ADDRESS_NAME);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, HashMap<String, String>>> orderOverviewContent = response.getBody().jsonPath().get("content");
				Comparator<HashMap<String, HashMap<String, String>>> nameComparator = Comparator.comparing(o -> o.get("collectionAddress").get("name"));
				assertEquals(orderOverviewContent.stream().filter(content -> (content.get("collectionAddress") != null && (content.get("collectionAddress").get("name") != null)))
								.sorted(nameComparator).toList(),
						orderOverviewContent.stream().filter(content -> (content.get("collectionAddress") != null && (content.get("collectionAddress").get("name") != null)))
								.toList());

			}

			@Test
			void testSortingQuantitiesDesc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.DESC);
				sorting.setKey(TTKeys.QUANTITY);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, Integer>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(80, orderOverviewContent.get(0).get(sorting.getKey().toString()));
				assertEquals(2, orderOverviewContent.get(1).get(sorting.getKey().toString()));

			}

			@Test
			void testSortingQuantitiesAsc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.ASC);
				sorting.setKey(TTKeys.QUANTITY);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, Integer>> orderOverviewContent = response.getBody().jsonPath().get("content");
				Comparator<HashMap<String, Integer>> quantityComparator = Comparator.comparing(o -> o.get(TTKeys.QUANTITY.getValue()));
				assertEquals(orderOverviewContent.stream().sorted(quantityComparator).toList(), orderOverviewContent);
			}

			@Test
			void testSortingWeightDesc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.DESC);
				sorting.setKey(TTKeys.WEIGHT);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, Float>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(200, orderOverviewContent.get(0).get(sorting.getKey().toString()));
				assertEquals(20.0f, orderOverviewContent.get(1).get(sorting.getKey().toString()));
			}

			@Test
			void testSortingWeightAsc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.ASC);
				sorting.setKey(TTKeys.WEIGHT);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, Float>> orderOverviewContent = response.getBody().jsonPath().get("content");
				Comparator<HashMap<String, Float>> weightComparator = Comparator.comparing(o -> o.get(TTKeys.WEIGHT.getValue()));
				assertEquals(orderOverviewContent.stream().sorted(weightComparator).toList(), orderOverviewContent);
			}

			@Test
			void testSortingStatusDesc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.DESC);
				sorting.setKey(TTKeys.STATUS);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, HashMap>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertTrue(orderOverviewContent.get(0).get(sorting.getKey().toString()).toString().contains("EXPIRED"));
				assertTrue(orderOverviewContent.get(1).get(sorting.getKey().toString()).toString().contains("SENT"));
				assertTrue(orderOverviewContent.get(2).get(sorting.getKey().toString()).toString().contains("LABEL_PENDING"));
			}

			@Test
			void testSortingStatusAsc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.ASC);
				sorting.setKey(TTKeys.STATUS);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, HashMap>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertTrue(orderOverviewContent.get(0).get(sorting.getKey().toString()).toString().contains("LABEL_PENDING"));
				assertTrue(orderOverviewContent.get(1).get(sorting.getKey().toString()).toString().contains("LABEL_PENDING"));
				assertTrue(orderOverviewContent.get(2).get(sorting.getKey().toString()).toString().contains("SENT"));
				assertTrue(orderOverviewContent.get(3).get(sorting.getKey().toString()).toString().contains("EXPIRED"));

			}

			@Test
			void testSortingDocumentsDesc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.DESC);
				sorting.setKey(TTKeys.INCLUDED_DOCUMENTS);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, List<String>>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.get(0).get(sorting.getKey().toString()).size());
			}

			@Test
			void testSortingOrderTypeDesc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.DESC);
				sorting.setKey(TTKeys.ORDER_TYPE);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				Comparator<HashMap<String, String>> orderTypeComparator = Comparator.comparing(o -> o.get(TTKeys.ORDER_TYPE.getValue()));
				assertEquals(orderOverviewContent.stream().sorted(orderTypeComparator).toList(), orderOverviewContent);
			}

			@Test
			void testSortingOrderTypeAsc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.ASC);
				sorting.setKey(TTKeys.ORDER_TYPE);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				Comparator<HashMap<String, String>> orderTypeComparator = Comparator.comparing(o -> o.get(TTKeys.ORDER_TYPE.getValue()));
				assertEquals(orderOverviewContent.stream().sorted(orderTypeComparator.reversed()).toList(), orderOverviewContent);
			}

			@Test
			void testSortingOrderSentDesc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.DESC);
				sorting.setKey(TTKeys.ORDER_SENT);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertTrue(orderOverviewContent.get(0).get(sorting.getKey().toString()).contains(FORWARDING_ORDER_ORDER_SENT.toLocalDate().toString()));
			}

			@Test
			void testSortingOrderSentAsc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.ASC);
				sorting.setKey(TTKeys.ORDER_SENT);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				Comparator<HashMap<String, String>> dateComparator = Comparator.comparing(o -> OffsetDateTime.parse(o.get("orderSent")));
				assertEquals(orderOverviewContent.stream().sorted(dateComparator).toList(), orderOverviewContent);
			}

			@Test
			void testSortingConsigneeAddressDesc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.DESC);
				sorting.setKey(TTKeys.CONSIGNEE_ADDRESS_NAME);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, HashMap<String, String>>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals("co-a", orderOverviewContent.get(0).get("collectionAddress").get("name"));
			}

			@Test
			void testSortingConsigneeAddressAsc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.ASC);
				sorting.setKey(TTKeys.CONSIGNEE_ADDRESS_NAME);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, HashMap<String, String>>> orderOverviewContent = response.getBody().jsonPath().get("content");
				Comparator<HashMap<String, HashMap<String, String>>> nameComparator = Comparator.comparing(o -> o.get("consigneeAddress").get("name"));
				assertEquals(orderOverviewContent.stream().filter(content -> (content.get("consigneeAddress") != null && (content.get("consigneeAddress").get("name") != null)))
								.sorted(nameComparator).toList(),
						orderOverviewContent.stream().filter(content -> (content.get("consigneeAddress") != null && (content.get("consigneeAddress").get("name") != null)))
								.toList());
			}

			@Test
			void testSortingOrderNumberDesc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.DESC);
				sorting.setKey(TTKeys.SHIPMENT_NUMBER);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				Comparator<HashMap<String, String>> stringComparator = Comparator.comparing(o -> (o.get(sorting.getKey().toString())));
				assertEquals(orderOverviewContent.stream().sorted(stringComparator.reversed()).toList(), orderOverviewContent);
			}

			@Test
			void testSortingShipmentNumberAsc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.ASC);
				sorting.setKey(TTKeys.SHIPMENT_NUMBER);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				Comparator<HashMap<String, String>> stringComparator = Comparator.comparing(o -> (o.get(sorting.getKey().toString())));
				assertEquals(orderOverviewContent.stream().sorted(stringComparator).toList(), orderOverviewContent);
			}

			@Test
			void testSortingValueOfGoodsDesc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.DESC);
				sorting.setKey(TTKeys.GOODS_VALUE);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, Float>> orderOverviewContent = response.getBody().jsonPath().get("content");
				Comparator<HashMap<String, Float>> floatComparator = Comparator.comparing(o -> (o.get(sorting.getKey().toString())));
				assertEquals(orderOverviewContent.stream().sorted(floatComparator.reversed()).toList(), orderOverviewContent);
			}

			@Test
			void testSortingValueOfGoodsAsc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.ASC);
				sorting.setKey(TTKeys.GOODS_VALUE);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, Float>> orderOverviewContent = response.getBody().jsonPath().get("content");
				Comparator<HashMap<String, Float>> floatComparator = Comparator.comparing(o -> (o.get(sorting.getKey().toString())));
				assertEquals(orderOverviewContent.stream().sorted(floatComparator).toList(), orderOverviewContent);
			}

			@Test
			void testSortingPickUpDateAsc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.ASC);
				sorting.setKey(TTKeys.PICKUP_DATE_TIME);
				String keyInOverview = "pickupDateTime";
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, HashMap<String, String>>> orderOverviewContent = response.getBody().jsonPath().get("content");
				ArrayList<HashMap<String, HashMap<String, Boolean>>> orderOverviewContentBool = response.getBody().jsonPath().get("content");

				//new sorting: lowest rank - null, then requestArrangement true, afterwards common sorting by date
				// default date equals null -> because we use default values in the mapping
				assertEquals(OffsetDateTime.of(1970, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC), OffsetDateTime.parse(orderOverviewContent.get(0).get(keyInOverview).get("from")));
				//requestArrangement = true
				assertTrue(orderOverviewContentBool.get(1).get(keyInOverview).get("requestArrangement"));
				assertTrue(OffsetDateTime.parse(orderOverviewContent.get(1).get(keyInOverview).get("from"))
						.isAfter(OffsetDateTime.parse(orderOverviewContent.get(2).get(keyInOverview).get("from"))));

				assertTrue(OffsetDateTime.parse(orderOverviewContent.get(2).get(keyInOverview).get("from"))
						.isBefore(OffsetDateTime.parse(orderOverviewContent.get(3).get(keyInOverview).get("from"))));
			}

			@Test
			void testSortingPickUpDateDesc() {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.DESC);
				sorting.setKey(TTKeys.PICKUP_DATE_TIME);
				String keyInOverview = "pickupDateTime";
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, HashMap<String, String>>> orderOverviewContent = response.getBody().jsonPath().get("content");
				ArrayList<HashMap<String, HashMap<String, Boolean>>> orderOverviewContentBool = response.getBody().jsonPath().get("content");

				//new sorting: lowest rank - null, then requestArrangement true, afterwards common sorting by date
				// default date equals null -> because we use default values in the mapping
				assertEquals(OffsetDateTime.of(1970, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC), OffsetDateTime.parse(orderOverviewContent.get(3).get(keyInOverview).get("from")));
				//requestArrangement = true
				assertTrue(orderOverviewContentBool.get(2).get(keyInOverview).get("requestArrangement"));
				assertTrue(OffsetDateTime.parse(orderOverviewContent.get(2).get(keyInOverview).get("from"))
						.isAfter(OffsetDateTime.parse(orderOverviewContent.get(1).get(keyInOverview).get("from"))));

				assertTrue(OffsetDateTime.parse(orderOverviewContent.get(1).get(keyInOverview).get("from"))
						.isBefore(OffsetDateTime.parse(orderOverviewContent.get(0).get(keyInOverview).get("from"))));
			}

			@ParameterizedTest
			@CsvSource({ "DESC, orders sorted ascending by lastModified", "ASC, orders sorted ascending by lastModified" })
			void testSortingLastModified(String sortDirection, String message) {
				TTSorting sorting = new TTSorting();
				sorting.setDirection(TTSortDirection.valueOf(sortDirection));
				sorting.setKey(TTKeys.LAST_MODIFIED);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, Integer>> orderOverviewContent = response.getBody().jsonPath().get("content");
				int orderId1 = orderOverviewContent.get(1).get("orderId");
				int orderId2 = orderOverviewContent.get(2).get("orderId");
				if (sortDirection.equals("DESC")) {
					Assertions.assertTrue(orderId2 < orderId1, message);
				} else {
					Assertions.assertTrue(orderId2 > orderId1, message);
				}
			}

			@Test
			void shouldSortByLastModifiedDescByDefault() {
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				// uses default sort column "lastModified" DESC
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(4, orderOverviewContent.size());
				// with default sorting top order is a SEA order
				var firstOrder = orderOverviewContent.get(0);
				assertEquals(TTOrderType.SEA.toString(), firstOrder.get("orderType"));
				assertEquals("7689678", firstOrder.get("shipmentNumber"));
			}
		}

		@Nested
		class InvalidFilterRequests {
			@ParameterizedTest
			@ValueSource(strings = { "räf2", "r'f2", "👍1234", "abcd.&/\\%$", "Æ ��È" })
			void testFilteringReferencesWithSpecialChar(String filterValue) {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.REFERENCES);
				filter.setValue(filterValue);
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
			}
		}

		@Nested
		class FilterRequests {

			@Test
			void testFilteringShipmentNumber() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.SHIPMENT_NUMBER);
				filter.setValue("12345");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertEquals("12345", orderOverviewContent.get(0).get(filter.getKey().toString()));
			}

			@Test
			void testFilteringShipmentNumberLike() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.SHIPMENT_NUMBER_LIKE);
				filter.setValue("1");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertEquals("12345", orderOverviewContent.get(0).get("shipmentNumber"));
			}

			@Test
			void testFilteringReferences() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.REFERENCES);
				filter.setValue("2");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, List<String>>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertEquals(1, orderOverviewContent.get(0).get("references").size());
			}

			@Test
			void testFilteringQuantity() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.QUANTITY);
				filter.setValue("2");
				filter.setValueTo("4");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, Integer>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertEquals(2, orderOverviewContent.get(0).get(filter.getKey().toString()));
			}

			@Test
			void testFilteringWeight() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.WEIGHT);
				filter.setValue("11");
				filter.setValueTo("24");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, Float>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertEquals(20.0f, orderOverviewContent.get(0).get(filter.getKey().toString()));
			}

			@Test
			void testFilteringValueOfGoods() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.GOODS_VALUE);
				filter.setValue("5");
				filter.setValueTo("10");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, Float>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertEquals(10.0f, orderOverviewContent.get(0).get(filter.getKey().toString()));
			}

			@Test
			void testFilteringDangerousGoodsTrue() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.CONTAINS_DANGEROUS_GOODS);
				filter.setValue("true");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, Float>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(0, orderOverviewContent.size());
			}

			@Test
			void testFilteringDangerousGoodsFalse() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.CONTAINS_DANGEROUS_GOODS);
				filter.setValue("false");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, Boolean>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(4, orderOverviewContent.size());
				assertEquals(false, orderOverviewContent.get(0).get("containsDangerousGoods"));

			}

			@Test
			void testFilteringFurtherAddressesExists() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.FURTHER_ADDRESS_TYPES);
				filter.setValue("true");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertEquals("12345", orderOverviewContent.get(0).get("shipmentNumber"));
			}

			@Test
			void testFilteringFurtherAddressesExistsNot() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.FURTHER_ADDRESS_TYPES);
				filter.setValue("false");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, List<String>>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(0, orderOverviewContent.get(0).get("furtherAddressTypes").size());
			}

			@ParameterizedTest
			@ValueSource(strings = { "AIR", "SEA", "ROAD" })
			void testFilteringOrderType(String orderType) {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.ORDER_TYPE);
				filter.setValueList(List.of(orderType));
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				Assertions.assertFalse(orderOverviewContent.isEmpty());
				if (orderType.equals("AIR")) {
					assertTrue(orderOverviewContent.stream().allMatch(overview -> overview.get("orderType").equals("AIR")));
				} else if (orderType.equals("SEA")) {
					assertTrue(orderOverviewContent.stream().allMatch(overview -> overview.get("orderType").equals("SEA")));
				} else if (orderType.equals("ROAD")) {
					assertTrue(orderOverviewContent.stream().allMatch(overview -> overview.get("orderType").equals("ROAD")));
				} else {
					Assertions.fail(); // mixed order types in filtered response
				}
			}

			@Test
			void testFilteringRequestArrangement() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.REQUEST_ARRANGEMENT);
				filter.setValue("true");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, HashMap<String, Boolean>>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(2, orderOverviewContent.size());
				assertTrue(orderOverviewContent.get(0).get("pickupDateTime").get("requestArrangement"));
			}

			@Test
			void testFilteringCollectionAddressName() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.COLLECTION_ADDRESS_NAME);
				filter.setValue("co");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, LinkedHashMap<String, String>>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertEquals("co-a", orderOverviewContent.get(0).get("collectionAddress").get("name"));
			}

			@Test
			void testFilteringCollectionAddressCity() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.COLLECTION_ADDRESS_CITY);
				filter.setValue("Mun");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, LinkedHashMap<String, String>>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertEquals("Munich", orderOverviewContent.get(0).get("collectionAddress").get("city"));
			}

			@Test
			void testFilteringConsigneeAddressPlz() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.CONSIGNEE_ADDRESS_POSTCODE);
				filter.setValue("99");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, LinkedHashMap<String, String>>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertEquals("009900", orderOverviewContent.get(0).get("consigneeAddress").get("postcode"));
			}

			@Test
			void testFilteringConsigneeAddressCountry() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.CONSIGNEE_ADDRESS_COUNTRY);
				filter.setValue("DE");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
			}

			@Test
			void testFilteringTerms() {
				TTFilter filter = new TTFilter();
				String termCode = "031";
				filter.setKey(TTKeys.TERM_CODE);
				filter.setValueList(List.of(termCode));
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertEquals(termCode, orderOverviewContent.get(0).get("termCode"));
			}

			@Test
			void testFilteringIncludedDocuments() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.INCLUDED_DOCUMENTS);
				filter.setValue("true");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertNotNull(orderOverviewContent.get(0).get("includedDocuments"));
			}

			@Test
			void testFilteringProductsDivisionT() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.PRODUCT_EUROPEAN);
				filter.setValueList(List.of("Z"));
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				List<Map<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");

				List<Map<String, Map<String, String>>> orderOverviewContentMap = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertEquals("T", orderOverviewContent.get(0).get("division"));
				assertEquals("Z", orderOverviewContentMap.get(0).get("product").get("code"));

			}

			@Test
			void testFilteringProductsDivisionF() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.PRODUCT_FOOD);
				filter.setValueList(List.of("Z"));
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				List<Map<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				List<Map<String, Map<String, String>>> orderOverviewContentMap = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertEquals("F", orderOverviewContent.get(0).get("division"));
				assertEquals("Z", orderOverviewContentMap.get(0).get("product").get("code"));
			}

			@Test
			void testFilteringProductsAir() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.PRODUCT_AIR);
				filter.setValueList(List.of("1"));
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				List<Map<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");

				List<Map<String, Map<String, String>>> orderOverviewContentMap = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertEquals("1", orderOverviewContentMap.get(0).get("product").get("code"));
				assertEquals("test", orderOverviewContentMap.get(0).get("product").get("description"));

			}

			@Test
			void testFilteringPortDeparture() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.PORT_FROM);
				filter.setValue("MUC");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				List<Map<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				List<Map<String, Map<String, Map<String, String>>>> orderOverviewContentMap = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertTrue(orderOverviewContentMap.get(0).get("portInfo").get("from").get("code").contains("MUC"));
			}

			@Test
			void testFilteringPortDestination() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.PORT_TO);
				filter.setValue("BER");
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				List<Map<String, String>> orderOverviewContent = response.getBody().jsonPath().get("content");
				List<Map<String, Map<String, Map<String, String>>>> orderOverviewContentMap = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContent.size());
				assertTrue(orderOverviewContentMap.get(0).get("portInfo").get("to").get("code").contains("BER"));
			}

			@Test
			void testFilteringPickUpDate() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.PICKUP_DATE_TIME);
				filter.setValue(OffsetDateTime.now().plusHours(48).toString());
				filter.setValueTo(OffsetDateTime.now().plusHours(52).toString());
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				List<Map<String, Map<String, String>>> orderOverviewContentMap = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContentMap.size());
				assertTrue(orderOverviewContentMap.get(0).get("pickupDateTime").get("from").contains(OffsetDateTime.now().plusHours(50).toLocalDate().toString()));
			}

			@Test
			void doesNeverContainDeletedOrders() {
				TTSorting sorting = new TTSorting();
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(sorting);
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());
				ArrayList<HashMap<String, HashMap>> orderOverviewContent = response.getBody().jsonPath().get("content");
				assertTrue(orderOverviewContent.stream().noneMatch(content -> content.get(TTKeys.STATUS.toString()).toString().contains("DELETED")));
			}

			@Test
			void testFilteringStatus() {
				TTFilter filter = new TTFilter();
				filter.setKey(TTKeys.STATUS);
				filter.setValueList(List.of("EXPIRED"));
				TTQueryObject queryObject = new TTQueryObject();
				queryObject.setSort(new TTSorting());
				queryObject.setFilter(List.of(filter));
				MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
				final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
				assertEquals(200, response.statusCode());

				List<Map<String, Map<String, String>>> orderOverviewContentMap = response.getBody().jsonPath().get("content");
				assertEquals(1, orderOverviewContentMap.size());
				assertEquals("EXPIRED", orderOverviewContentMap.get(0).get("status").get("code"));

			}
		}

		@Test
		void shouldMapReferencesToReferenceColumn() {
			TTQueryObject queryObject = new TTQueryObject();
			queryObject.setSort(new TTSorting());
			// uses default sort column "lastModified" DESC
			MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
			final MockMvcResponse response = request.post(buildUrl(TRACK_AND_TRACE_VERSION + "/principals/orders/overview?page=0&size=10"));
			assertEquals(200, response.statusCode());
			ArrayList<HashMap<String, List<String>>> orderOverviewContent = response.getBody().jsonPath().get("content");
			assertEquals(4, orderOverviewContent.size());
			// with default sorting top order is a SEA order
			var firstOrder = orderOverviewContent.get(0);
			assertEquals(1, firstOrder.get("references").size());
		}
	}

	void prepareData() {
		OrderAddress address1 = new OrderAddress();
		address1.setName("a");
		OrderAddress address2 = new OrderAddress();
		address2.setName("b");
		ForwardingOrder forwardingOrder = new ForwardingOrder();
		forwardingOrder.setCustomerNumber(CUSTOMER_NUMBER);
		forwardingOrder.setOrderNumber("13");
		forwardingOrder.setBranchId(10);
		forwardingOrder.setCreator(UUID.randomUUID().toString());
		forwardingOrder.setCreatedAt(Instant.now());
		forwardingOrder.setLastEditor(UUID.randomUUID().toString());
		forwardingOrder.setLastModified(Instant.now());
		forwardingOrder.setCollectionFrom(OffsetDateTime.now().plusHours(5));
		forwardingOrder.setCollectionTo(OffsetDateTime.now().plusHours(10));
		forwardingOrder.setShipperAddress(address1);
		forwardingOrder.setStatus(OrderStatus.LABEL_PENDING);
		forwardingOrder.setShipmentNumber(12345L);
		forwardingOrder.setProduct("Z");
		forwardingOrder.setSendAt(FORWARDING_ORDER_ORDER_SENT.toInstant(ZoneOffset.UTC));
		forwardingOrder.setFixDate(forwardingOrder.getSendAt().atOffset(ZoneOffset.UTC).toLocalDate());
		forwardingOrder.setConsigneeAddress(address2);
		forwardingOrder.setGoodsValue(BigDecimal.valueOf(10));
		forwardingOrder.setDivision(Division.F);
		forwardingOrder.setFreightTerm("031");
		RoadOrderLine orderLine1 = new RoadOrderLine();
		orderLine1.setNumber(1);
		orderLine1.setWeight(BigDecimal.valueOf(10L));
		orderLine1.setQuantity(1);
		orderLine1.setOrder(forwardingOrder);
		forwardingOrder.setOrderLines(List.of(orderLine1));
		final ForwardingOrder saved = orderRepository.save(forwardingOrder);

		OrderAddress address3 = new OrderAddress();
		address3.setName("co-a");
		address3.setCity("Munich");
		OrderAddress address4 = new OrderAddress();
		address4.setName("co-b");
		address4.setCountryCode("DE");
		address4.setPostcode("009900");
		CollectionOrder collectionOrder = new CollectionOrder();
		collectionOrder.setCustomerNumber(CUSTOMER_NUMBER);
		collectionOrder.setOrderNumber("12");
		collectionOrder.setBranchId(10);
		collectionOrder.setDivision(Division.T);
		collectionOrder.setProduct("Z");
		collectionOrder.setStatus(OrderStatus.DELETED);
		collectionOrder.setCreator(UUID.randomUUID().toString());
		collectionOrder.setCreatedAt(Instant.now());
		collectionOrder.setLastEditor(UUID.randomUUID().toString());
		collectionOrder.setLastModified(Instant.now());
		collectionOrder.setCollectionFrom(OffsetDateTime.now().plusHours(0));
		collectionOrder.setCollectionTo(OffsetDateTime.now().plusHours(10));
		collectionOrder.setShipperAddress(address3);
		collectionOrder.setStatus(OrderStatus.SENT);
		collectionOrder.setShipmentNumber(4445L);
		collectionOrder.setSendAt(COLLECTION_ORDER_ORDER_SENT.toInstant(ZoneOffset.UTC));
		collectionOrder.setFixDate(collectionOrder.getSendAt().atOffset(ZoneOffset.UTC).toLocalDate());
		collectionOrder.setConsigneeAddress(address4);
		collectionOrder.setGoodsValue(BigDecimal.valueOf(20));
		collectionOrder.setDivision(Division.T);
		collectionOrder.setFreightTerm("032");
		RoadOrderLine orderLine2 = new RoadOrderLine();
		orderLine2.setQuantity(2);
		orderLine2.setWeight(BigDecimal.valueOf(20L));
		orderLine2.setNumber(1);
		orderLine2.setOrder(collectionOrder);
		collectionOrder.setOrderLines(List.of(orderLine2));
		orderRepository.save(collectionOrder);

		AirExportOrder airExportOrder = new AirExportOrder();
		airExportOrder.setCustomerNumber(CUSTOMER_NUMBER);
		airExportOrder.setCreator(UUID.randomUUID().toString());
		airExportOrder.setCreatedAt(Instant.now());
		airExportOrder.setLastEditor(UUID.randomUUID().toString());
		airExportOrder.setLastModified(Instant.now());
		airExportOrder.setCollectionFrom(OffsetDateTime.now().plusHours(50));
		airExportOrder.setCollectionTo(OffsetDateTime.now().plusHours(100));
		airExportOrder.setRequestArrangement(true);
		airExportOrder.setCollectionDate(OffsetDateTime.now().plusHours(50).toLocalDate());
		airExportOrder.setStatus(OrderStatus.EXPIRED);
		airExportOrder.setShipmentNumber(567L);
		airExportOrder.setGoodsValue(BigDecimal.valueOf(20));
		airExportOrder.setIncoTerm("DAP");
		airExportOrder.setProductCode(1);
		airExportOrder.setProductName("test");
		AirOrderLine orderLine3 = new AirOrderLine();
		orderLine3.setQuantity(80);
		orderLine3.setWeight(BigDecimal.valueOf(200.00));
		orderLine3.setNumber(10);
		orderLine3.setOrder(airExportOrder);
		airExportOrder.setOrderLines(List.of(orderLine3));
		airExportOrder.setBranchId(1);
		airExportOrder.setShipperReference("shipper");
		airExportOrder.setFromIATA("MUC");
		airExportOrder.setToIATA("BER");
		orderRepository.save(airExportOrder);

		AirExportOrder airExportOrder2 = new AirExportOrder();
		airExportOrder2.setCustomerNumber(CUSTOMER_NUMBER);
		airExportOrder2.setCreator(UUID.randomUUID().toString());
		airExportOrder2.setCreatedAt(Instant.now());
		airExportOrder2.setLastEditor(UUID.randomUUID().toString());
		airExportOrder2.setLastModified(Instant.now());
		airExportOrder2.setCollectionDate(OffsetDateTime.now().plusHours(50).toLocalDate());
		airExportOrder2.setStatus(OrderStatus.DELETED);
		airExportOrder2.setShipmentNumber(5678L);
		airExportOrder2.setBranchId(1);
		airExportOrder2.setShipperReference("shipper");
		airExportOrder2.setProductCode(2);
		orderRepository.save(airExportOrder2);

		SeaExportOrder seaExportOrder = new SeaExportOrder();
		seaExportOrder.setCustomerNumber(CUSTOMER_NUMBER);
		seaExportOrder.setCreator(UUID.randomUUID().toString());
		seaExportOrder.setCreatedAt(Instant.now());
		seaExportOrder.setLastEditor(UUID.randomUUID().toString());
		seaExportOrder.setLastModified(Instant.now());
		seaExportOrder.setStatus(OrderStatus.LABEL_PENDING);
		seaExportOrder.setShipmentNumber(7689678L);
		seaExportOrder.setBranchId(1);
		seaExportOrder.setRequestArrangement(true);
		seaExportOrder.setShipperReference("sea");
		orderRepository.save(seaExportOrder);

		DocumentType documentType = new DocumentType();
		documentType.setType("bla");
		documentType.setCategory("cat-bla");
		documentType = documentTypeRepository.save(documentType);

		Document document1 = new Document();
		document1.setSize(12L);
		document1.setOrderId(saved.getOrderId());
		document1.setCustomerNumber(CUSTOMER_NUMBER);
		document1.setDocumentType(documentType);
		document1.setDocumentName("Test Doc");
		document1.setFileType(FileType.PDF);
		document1.setStatus(DocumentStatus.NEW);
		document1.setCreator(UUID.randomUUID().toString());
		document1.setCreatedAt(Instant.now());
		document1.setLastEditor(UUID.randomUUID().toString());
		document1.setLastModified(Instant.now());
		documentRepository.save(document1);

		AirOrderReference airOrderReference = new AirOrderReference();
		airOrderReference.setOrder(airExportOrder);
		airOrderReference.setReferenceValue("aiiir");
		airOrderReference.setOrderReferenceId(4L);
		airOrderReference.setReferenceType(AirSeaOrderReferenceType.INVOICE_NUMBER);
		airOrderReferenceRepository.save(airOrderReference);

		AirOrderReference airOrderShipperReference = new AirOrderReference();
		airOrderShipperReference.setOrder(airExportOrder);
		airOrderShipperReference.setReferenceValue("SHIPPER");
		airOrderShipperReference.setOrderReferenceId(8L);
		airOrderShipperReference.setReferenceType(AirSeaOrderReferenceType.SHIPPERS_REFERENCE);
		airOrderReferenceRepository.save(airOrderShipperReference);

		SeaOrderReference seaOrderReference = new SeaOrderReference();
		seaOrderReference.setOrder(seaExportOrder);
		seaOrderReference.setReferenceValue("seaa");
		seaOrderReference.setOrderReferenceId(1L);
		seaOrderReference.setReferenceType(AirSeaOrderReferenceType.INVOICE_NUMBER);
		seaOrderReferenceRepository.save(seaOrderReference);

		RoadOrderReference roadOrderReference1 = new RoadOrderReference();
		roadOrderReference1.setOrder(forwardingOrder);
		roadOrderReference1.setReference("ref1");
		roadOrderReference1.setOrderReferenceId(1L);
		roadOrderReference1.setReferenceType(ReferenceType.PURCHASE_ORDER_NUMBER);
		orderReferenceRepository.save(roadOrderReference1);

		RoadOrderReference roadOrderReference2 = new RoadOrderReference();
		roadOrderReference2.setOrder(forwardingOrder);
		roadOrderReference2.setReference("ref2");
		roadOrderReference2.setOrderReferenceId(2L);
		roadOrderReference2.setReferenceType(ReferenceType.PURCHASE_ORDER_NUMBER);
		orderReferenceRepository.save(roadOrderReference2);

		RoadOrderReference roadOrderReference3 = new RoadOrderReference();
		roadOrderReference3.setOrder(collectionOrder);
		roadOrderReference3.setReference("ref3");
		roadOrderReference3.setOrderReferenceId(3L);
		roadOrderReference3.setReferenceType(ReferenceType.PURCHASE_ORDER_NUMBER);
		orderReferenceRepository.save(roadOrderReference3);

		OrderFurtherAddress orderFurtherAddress = new OrderFurtherAddress();
		orderFurtherAddress.setAddressType("DP");
		orderFurtherAddress.setOrder(forwardingOrder);
		orderFurtherAddressRepository.save(orderFurtherAddress);

	}
}