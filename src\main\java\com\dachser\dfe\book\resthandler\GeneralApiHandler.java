package com.dachser.dfe.book.resthandler;

import com.dachser.dfe.book.api.GeneralApiDelegate;
import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.customer.SegmentMapper;
import com.dachser.dfe.book.hscode.HsCode;
import com.dachser.dfe.book.hscode.HsCodeService;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.mapper.DivisionMapper;
import com.dachser.dfe.book.mapper.OptionMapper;
import com.dachser.dfe.book.mockdata.MockData;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.BooleanResultDto;
import com.dachser.dfe.book.model.CountryDto;
import com.dachser.dfe.book.model.CurrencyDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.DisplayableOrderStatusDto;
import com.dachser.dfe.book.model.DivisionDto;
import com.dachser.dfe.book.model.FreightTermDto;
import com.dachser.dfe.book.model.IncoTermDto;
import com.dachser.dfe.book.model.IrelandPostalCodeDto;
import com.dachser.dfe.book.model.Option;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderAddressDto;
import com.dachser.dfe.book.model.OrderTypeDto;
import com.dachser.dfe.book.model.PackagingOptionsDto;
import com.dachser.dfe.book.model.PostcodeValidationDto;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.model.SegmentDto;
import com.dachser.dfe.book.model.ValidationResultDto;
import com.dachser.dfe.book.model.ValidationResultEntryDto;
import com.dachser.dfe.book.order.mapper.OrderAddressMapper;
import com.dachser.dfe.book.order.mapper.OrderTypeMapper;
import com.dachser.dfe.book.order.validation.OrderAddressValidator;
import com.dachser.dfe.book.packaging.PackagingOptionsService;
import com.dachser.dfe.book.product.air.AirProductService;
import com.dachser.dfe.book.product.road.RoadProductsService;
import com.dachser.dfe.book.service.CollectionInterpreterOptionsService;
import com.dachser.dfe.book.service.CollectionOptionsService;
import com.dachser.dfe.book.service.DeliveryOptionsService;
import com.dachser.dfe.book.service.EmbargoService;
import com.dachser.dfe.book.service.FurtherAddressTypeService;
import com.dachser.dfe.book.service.NatureOfGoodsService;
import com.dachser.dfe.book.service.OrderStatusesService;
import com.dachser.dfe.book.service.ThirdCountryService;
import com.dachser.dfe.book.service.freightterm.FreightTermService;
import com.dachser.dfe.book.service.postalcode.PostalCodeService;
import com.dachser.dfe.book.term.IncoTermService;
import com.dachser.dfe.book.user.BusinessDomainProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class GeneralApiHandler implements GeneralApiDelegate {

	private final CollectionInterpreterOptionsService collectionInterpreterOptionsService;

	private final CollectionOptionsService collectionOptionService;

	private final DeliveryOptionsService deliveryOptionsService;

	private final FreightTermService freightTermService;

	private final IncoTermService incoTermService;

	private final ThirdCountryService thirdCountryService;

	private final OrderStatusesService orderStatusesService;

	private final OptionMapper optionMapper;

	private final BusinessDomainProvider businessDomainProvider;

	private final CountryService countryService;

	private final HsCodeService hsCodeService;

	private final RoadProductsService roadProductsService;

	private final DivisionMapper divisionMapper;

	private final SegmentMapper segmentMapper;

	private final EmbargoService embargoService;

	private final OrderAddressValidator addressValidator;

	private final OrderAddressMapper orderAddressMapper;

	private final OrderTypeMapper orderTypeMapper;

	private final AirProductService airProductService;

	private final PostalCodeService postalCodeService;

	private final NatureOfGoodsService natureOfGoodsService;

	private final PackagingOptionsService packagingOptionsService;

	private final FurtherAddressTypeService furtherAddressTypeService;

	@Override
	public ResponseEntity<List<OptionDto>> getCollectionInterpreterOptions() {
		return ResponseEntity.ok(optionMapper.toOptionDtos(collectionInterpreterOptionsService.getInterpreterOptions()));
	}

	@Override
	public ResponseEntity<List<OptionDto>> getCollectionOptions() {
		final List<Option> collectionOptions = collectionOptionService.getCollectionOptions();
		return ResponseEntity.ok(optionMapper.toOptionDtos(collectionOptions));
	}

	@Override
	@PreAuthorize("@dfeEL.isBookUser() || @dfeEL.isTraceUser()") // can be removed when track and trace is no longer using that endpoints
	public ResponseEntity<List<CountryDto>> getCountries(SegmentDto segment) {
		final List<CountryDto> sortedCountries = countryService.getSortedAndFilteredCountries(LocaleContextHolder.getLocale().getLanguage(),
				Optional.ofNullable(segment).map(segmentMapper::map).orElse(Segment.ROAD));
		return ResponseEntity.ok(sortedCountries);
	}

	@Override
	public ResponseEntity<List<OptionDto>> getDeliveryOptions() {
		final List<Option> deliveryOptions = deliveryOptionsService.getDeliveryOptions();

		return ResponseEntity.ok(optionMapper.toOptionDtos(deliveryOptions));
	}

	@Override
	public ResponseEntity<BooleanResultDto> isThirdCountryConstellation(String fromCountryCode, String toCountryCode) {
		final boolean validateThirdCountry = thirdCountryService.isThirdCountryConstellation(businessDomainProvider.getBusinessDomain(), fromCountryCode, toCountryCode);
		return ResponseEntity.ok(new BooleanResultDto().result(validateThirdCountry));
	}

	@Override
	@PreAuthorize("@dfeEL.isBookUser() || @dfeEL.isTraceUser()") // can be removed when track and trace is no longer using that endpoints
	public ResponseEntity<List<FreightTermDto>> getFreightTerms() {
		final List<FreightTermDto> fromFreightTermOptions = freightTermService.getAllValidFreightTermOptions();
		return ResponseEntity.ok(fromFreightTermOptions);
	}

	@Override
	public ResponseEntity<List<OptionDto>> getHsCodeOptions(String searchFor) {
		final List<HsCode> hsCodes = hsCodeService.searchHsCodes(searchFor);
		return ResponseEntity.ok(hsCodes.stream().map(hsCode -> new OptionDto().code(hsCode.getId()).description(hsCode.getDescription())).toList());
	}

	/**
	 * @deprecated use general data service instead as soon as available
	 */
	@Deprecated(forRemoval = true)
	@Override
	@PreAuthorize("@dfeEL.isBookUser() || @dfeEL.isTraceUser()") // can be removed when track and trace is no longer using that endpoints
	public ResponseEntity<List<DeliveryProductDto>> getDivisonDeliveryProducts(DivisionDto division) {
		final Division mappedDivision = divisionMapper.map(division);
		final List<DeliveryProductDto> products = roadProductsService.getDeliveryProductsForDivision(mappedDivision);
		return ResponseEntity.ok(products);
	}

	@Override
	@PreAuthorize("@dfeEL.isBookUser() || @dfeEL.isTraceUser()") // can be removed when track and trace is no longer using that endpoints
	public ResponseEntity<List<IncoTermDto>> getIncoterms() {
		return ResponseEntity.ok(incoTermService.getAllActiveIncoTerms());
	}

	@Override
	public ResponseEntity<List<DisplayableOrderStatusDto>> getOrderStatuses() {
		List<DisplayableOrderStatusDto> statuses = orderStatusesService.getAllOrderStatuses();
		return ResponseEntity.ok(statuses);
	}

	@Override
	public ResponseEntity<List<CurrencyDto>> getAllCurrencies() {
		return ResponseEntity.ok(MockData.getCurrencies());
	}

	@Override
	public ResponseEntity<Boolean> hasEmbargo(final String fromCountryCode, final String toCountryCode) {
		return ResponseEntity.ok(embargoService.isEmbargoed(fromCountryCode, toCountryCode));
	}

	@Override
	public ResponseEntity<ValidationResultDto> validateAddress(OrderTypeDto orderType, OrderAddressDto orderAddressDto) {
		List<ValidationResultEntryDto> validationResultEntry = addressValidator.validateAddressBasedOnOrderType(orderAddressMapper.mapAddress(orderAddressDto),
				orderTypeMapper.map(orderType));
		return ResponseEntity.ok(new ValidationResultDto().results(validationResultEntry).valid(validationResultEntry.isEmpty()));
	}

	@Override
	@PreAuthorize("@dfeEL.isBookUser() || @dfeEL.isTraceUser()") // can be removed when track and trace is no longer using that endpoints
	public ResponseEntity<List<AirProductDto>> getV1AirProducts(Boolean includeDeactivated) {
		return ResponseEntity.ok(airProductService.getAirProducts(includeDeactivated));
	}

	@Override
	public ResponseEntity<List<IrelandPostalCodeDto>> getIrelandDachserPostalCodes(String postalCode) {
		return ResponseEntity.ok(postalCodeService.findDachserPostalcodes(postalCode));
	}

	@Override
	public ResponseEntity<PostcodeValidationDto> validatePostcode(final String postcode, final String countryCode) {
		return ResponseEntity.ok(postalCodeService.validatePostCode(countryCode, postcode));
	}

	@Override
	public ResponseEntity<List<String>> getLastUsedNatureOfGoods(SegmentDto segment) {
		return ResponseEntity.ok(natureOfGoodsService.getLastUsedNatureOfGoods(segmentMapper.map(segment)));
	}

	@Override
	public ResponseEntity<PackagingOptionsDto> getPackagingOptions(SegmentDto customerSegment) {
		return ResponseEntity.ok(new PackagingOptionsDto().packagingOptions(packagingOptionsService.getPackagingOptions(segmentMapper.map(customerSegment))));
	}

	@Override
	public ResponseEntity<List<OptionDto>> getAllFurtherAddressTypes() {
		return ResponseEntity.ok(furtherAddressTypeService.getFurtherAddressTypes(businessDomainProvider.getBusinessDomain()));
	}
}

