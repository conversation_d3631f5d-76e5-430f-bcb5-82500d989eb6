<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dachser.dfe</groupId>
        <artifactId>dfe-backend-parent</artifactId>
        <version>5.0.0-alpha.62</version>
    </parent>
    <name>DFE book backend</name>
    <description>REST API implementation for DFE book API</description>

    <artifactId>dfe-book-backend</artifactId>
    <version>2.9.0</version>
    <groupId>com.dachser.dfe.book</groupId>

    <properties>
        <dfe.book-api.version>8.3.0</dfe.book-api.version>
        <dfe.trackandtrace-api.version>5.12.0</dfe.trackandtrace-api.version>
        <dfe-filtersort-helper.version>2.1.0</dfe-filtersort-helper.version>
        <dfe-pdf-export-api.version>1.1.0</dfe-pdf-export-api.version>
        <dfe-general-data-api.version>2.9.0</dfe-general-data-api.version>
        <dfe.client.sharedservice.masterdata.businesspartner.version>2.1.0
        </dfe.client.sharedservice.masterdata.businesspartner.version>
        <dfe-backend-i18n.version>1.0.4</dfe-backend-i18n.version>
        <dfe-vault.version>3.0.1</dfe-vault.version>
        <dfe-plattform-api.version>4.7.0</dfe-plattform-api.version>
        <dfe-security-starter.version>4.2.0</dfe-security-starter.version>
        <dfe-emissions-api.version>1.4.0</dfe-emissions-api.version>
        <spring-cloud-starter-contract-stub-runner.version>4.1.4</spring-cloud-starter-contract-stub-runner.version>

        <dachser.road.ohs.version>2.1.0</dachser.road.ohs.version>
        <dachser.bi.common.security.version>3.0.2</dachser.bi.common.security.version>
        <dachser.bi.common.shipment.version>3.3.0</dachser.bi.common.shipment.version>
        <dachser.bi.common.print.version>3.4.1</dachser.bi.common.print.version>
        <dachser.bi.common.validation.version>3.1.2</dachser.bi.common.validation.version>
        <dachser.order.pub.service.version>3.1.0</dachser.order.pub.service.version>
        <dachser.advice-legacy.version>1.6.0</dachser.advice-legacy.version>
        <dachser.archive.service.version>4.23.0</dachser.archive.service.version>
        <masterdata.geo.version>2.4.0</masterdata.geo.version>
        <masterdata.thirdparty.version>1.9.0</masterdata.thirdparty.version>

        <shedlock.version>5.4.0</shedlock.version>
        <softwareag.messaging.version>10.15.0</softwareag.messaging.version>
        <softwareag.integration.version>10.11.0</softwareag.integration.version>

        <openpdf.version>2.0.2</openpdf.version>
        <pdfcompare.version>1.1.61</pdfcompare.version>
        <problem.version>0.27.1</problem.version>

        <cucumber.version>7.11.2</cucumber.version>
        <junit-platform-suite-api.version>1.9.2</junit-platform-suite-api.version>

        <gitflow-maven-plugin.version>1.19.0</gitflow-maven-plugin.version>

        <!-- Do not update jaxb2, see comment below -->
        <jaxb2-maven-plugin.version>3.1.0</jaxb2-maven-plugin.version>
        <splunk-library.version>1.11.7</splunk-library.version>

        <springdoc-openapi.version>2.3.0</springdoc-openapi.version>
        <swagger-annotations.version>2.2.20</swagger-annotations.version>

        <!-- To generate the DFE Platform API -->
        <openapi-generator.version>7.12.0</openapi-generator.version>

        <!-- Contains a critical bugfix that occurred in 3.6.0 -->
        <dachser.bi.common.version>3.8.0</dachser.bi.common.version>
    </properties>


    <dependencies>

        <dependency>
            <groupId>com.dachser.dfe</groupId>
            <artifactId>dfe-client-shared-service-masterdata-business-partner</artifactId>
            <version>${dfe.client.sharedservice.masterdata.businesspartner.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.dachser.dfe</groupId>
            <artifactId>dfe-filtersort-helper</artifactId>
            <version>${dfe-filtersort-helper.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dachser.dfe</groupId>
            <artifactId>spring-cloud-start-dachser-vault</artifactId>
            <version>${dfe-vault.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dachser.dfe</groupId>
            <artifactId>dfe-security-starter</artifactId>
            <version>${dfe-security-starter.version}</version>
        </dependency>


        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>

        <!-- Adds support for java.time.LocalDate in Jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>

        <!-- Spring -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Logging -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-layout-template-json</artifactId>
        </dependency>

        <dependency>
            <groupId>com.splunk.logging</groupId>
            <artifactId>splunk-library-javalogging</artifactId>
            <version>${splunk-library.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.microsoft.sqlserver/mssql-jdbc -->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>


        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.xml.bind</groupId>
                    <artifactId>jaxb-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-spring</artifactId>
            <version>${shedlock.version}</version>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-jdbc-template</artifactId>
            <version>${shedlock.version}</version>
        </dependency>

        <!-- Required by dependencies -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-common</artifactId>
            <version>${springdoc-openapi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
            <version>${springdoc-openapi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>${springdoc-openapi.version}</version>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
        </dependency>

        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.jms</groupId>
            <artifactId>jakarta.jms-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dachser.bi</groupId>
            <artifactId>bi.common</artifactId>
            <version>${dachser.bi.common.version}</version>
        </dependency>

        <!-- elogistics -->
        <!-- needed as long as generated wsclient doesnt use at least version 7.0.1 (last checked incompatible version: 6.1.0-SNAPSHOT) -->
        <dependency>
            <groupId>com.dachser.framework</groupId>
            <artifactId>framework.wsclient</artifactId>
            <version>7.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.dachser.road</groupId>
            <artifactId>road.ohs.pub.api</artifactId>
            <version>${dachser.road.ohs.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dachser.road</groupId>
            <artifactId>road.ohs.pub.wsclient</artifactId>
            <version>${dachser.road.ohs.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dachser.bi</groupId>
            <artifactId>bi.common.security.service.pri.api</artifactId>
            <version>${dachser.bi.common.security.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dachser.bi</groupId>
            <artifactId>bi.common.security.service.pri.wsclient</artifactId>
            <scope>runtime</scope>
            <version>${dachser.bi.common.security.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dachser.elogistics</groupId>
            <artifactId>elogistics.order.service.pub.api</artifactId>
            <version>${dachser.order.pub.service.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dachser.elogistics</groupId>
            <artifactId>elogistics.order.service.pub.wsclient</artifactId>
            <version>${dachser.order.pub.service.version}</version>
        </dependency>

        <!-- Dib -->
        <!-- UMS https://confluence.dach041.dachser.com/x/hpEnF -->
        <!-- Downgraded spring-jms is intentional and needed. Do not remove/update unless there is an update for the com.softwareag.messaging dependencies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jms</artifactId>
            <version>5.3.24</version>
        </dependency>
        <dependency>
           <groupId>com.softwareag.messaging</groupId>
            <artifactId>nClient</artifactId>
            <version>${softwareag.messaging.version}</version>
        </dependency>
        <dependency>
            <groupId>com.softwareag.messaging</groupId>
            <artifactId>nJMS</artifactId>
            <version>${softwareag.messaging.version}</version>
        </dependency>
        <dependency>
            <groupId>com.softwareag.integration</groupId>
            <artifactId>wm-isclient</artifactId>
            <version>${softwareag.integration.version}</version>
        </dependency>
        <!-- required for nJMS -->
        <dependency>
            <groupId>javax.jms</groupId>
            <artifactId>javax.jms-api</artifactId>
            <version>2.0.1</version>
        </dependency>

        <!-- SSCC Generation Service -->
        <dependency>
            <groupId>com.dachser.bi</groupId>
            <artifactId>bi.common.shipment.service.pri.wsclient</artifactId>
            <scope>runtime</scope>
            <version>${dachser.bi.common.shipment.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dachser.bi</groupId>
            <artifactId>bi.common.shipment.service.pri.api</artifactId>
            <version>${dachser.bi.common.shipment.version}</version>
        </dependency>

        <!-- LabelPrint Service -->
        <dependency>
            <groupId>com.dachser.bi</groupId>
            <artifactId>bi.common.print.service.pri.wsclient</artifactId>
            <version>${dachser.bi.common.print.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dachser.bi</groupId>
            <artifactId>bi.common.print.service.pri.api</artifactId>
            <version>${dachser.bi.common.print.version}</version>
        </dependency>

        <!-- AVIS -->
        <dependency>
            <groupId>com.dachser.dfe.legacy.advice</groupId>
            <artifactId>dfe-legacy-advice-client</artifactId>
            <version>${dachser.advice-legacy.version}</version>
        </dependency>

        <!-- JAXB -->
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
        </dependency>

        <!-- Problem JSON -->
        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>problem</artifactId>
            <version>${problem.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.ws</groupId>
            <artifactId>jaxws-rt</artifactId>
            <version>4.0.2</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>jackson-datatype-problem</artifactId>
            <version>${problem.version}</version>
        </dependency>

        <!-- PDF Tools -->
        <dependency>
            <groupId>com.dachser.bi</groupId>
            <artifactId>bi.common.validation.service.pri.api</artifactId>
            <version>${dachser.bi.common.validation.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dachser.bi</groupId>
            <artifactId>bi.common.validation.service.pri.wsclient</artifactId>
            <version>${dachser.bi.common.validation.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.github.librepdf</groupId>
            <artifactId>openpdf</artifactId>
            <version>${openpdf.version}</version>
        </dependency>
        <dependency>
            <groupId>de.redsix</groupId>
            <artifactId>pdfcompare</artifactId>
            <version>${pdfcompare.version}</version>
            <!-- see current version in the maven central tag above -->
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Archive Service -->
        <dependency>
            <groupId>com.dachser.common</groupId>
            <artifactId>common.pub.archive.api</artifactId>
            <version>${dachser.archive.service.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dachser.common</groupId>
            <artifactId>common.pub.archive.wsclient</artifactId>
            <version>${dachser.archive.service.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>

        <!-- Testing -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>

            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-contract-stub-runner</artifactId>
            <version>${spring-cloud-starter-contract-stub-runner.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>spring-mock-mvc</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-java</artifactId>
            <version>${cucumber.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-spring</artifactId>
            <version>${cucumber.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-junit-platform-engine</artifactId>
            <version>${cucumber.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-suite-api</artifactId>
            <version>${junit-platform-suite-api.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.dachser.dfe</groupId>
                <artifactId>dfe-backend-i18n</artifactId>
                <version>${dfe-backend-i18n.version}</version>
                <executions>
                    <execution>
                        <id>generate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <applicationNumbers>
                                <applicationNumber>159</applicationNumber>
                            </applicationNumbers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>jaxb2-maven-plugin</artifactId>
                <version>${jaxb2-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>generate-road-forwarding</id>
                        <goals>
                            <goal>xjc</goal>
                        </goals>
                        <configuration>
                            <packageName>com.dachser.dfe.book.model.jaxb.order.road.forwarding</packageName>
                            <!-- Warning: Generation fails in case of any whitespace character within the absolute XSD path -->
                            <sources>
                                <source>${project.basedir}/src/main/resources/xsd/ForwardingOrder-Transport.xsd</source>
                            </sources>
                            <clearOutputDir>false</clearOutputDir>
                            <!-- Suppress the generation of a file header. Using this makes the generated code more diff-friendly -->
                            <noGeneratedHeaderComments>false</noGeneratedHeaderComments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-air</id>
                        <goals>
                            <goal>xjc</goal>
                        </goals>
                        <configuration>
                            <packageName>com.dachser.dfe.book.model.jaxb.order.asl</packageName>
                            <!-- Warning: Generation fails in case of any whitespace character within the absolute XSD path -->
                            <sources>
                                <source>${project.basedir}/src/main/resources/xsd/ForwardingOrder-ASL.xsd</source>
                            </sources>
                            <clearOutputDir>false</clearOutputDir>
                            <!-- Suppress the generation of a file header. Using this makes the generated code more diff-friendly -->
                            <noGeneratedHeaderComments>false</noGeneratedHeaderComments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-road-collection</id>
                        <goals>
                            <goal>xjc</goal>
                        </goals>
                        <configuration>
                            <packageName>com.dachser.dfe.book.model.jaxb.order.road.collection</packageName>
                            <!-- Warning: Generation fails in case of any whitespace character within the absolute XSD path -->
                            <sources>
                                <source>${project.basedir}/src/main/resources/xsd/CollectionOrder_Transport.xsd</source>
                            </sources>
                            <clearOutputDir>false</clearOutputDir>
                            <!-- Suppress the generation of a file header. Using this makes the generated code more diff-friendly -->
                            <noGeneratedHeaderComments>false</noGeneratedHeaderComments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.amashchenko.maven.plugin</groupId>
                <artifactId>gitflow-maven-plugin</artifactId>
                <version>${gitflow-maven-plugin.version}</version>
                <configuration>
                    <allowSnapshots>true</allowSnapshots>
                    <gitFlowConfig>
                        <developmentBranch>beta</developmentBranch>
                    </gitFlowConfig>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>${openapi-generator.version}</version>
                <executions>
                    <execution>
                        <id>generate-road-masterdata-client</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/openapi/road-masterdata-api.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <!-- spring webclient generator type -->
                            <library>resttemplate</library>
                            <apiPackage>com.dachser.dfe.road.masterdata.api</apiPackage>
                            <modelPackage>com.dachser.dfe.road.masterdata.model</modelPackage>
                            <modelNamePrefix>RMD</modelNamePrefix>
                            <supportingFilesToGenerate>
                                BaseApi.java,ApiClient.java,JavaTimeFormatter.java,RFC3339DateFormat.java,StringUtil.java,Authentication.java,HttpBasicAuth.java,HttpBearerAuth.java,ApiKeyAuth.java
                            </supportingFilesToGenerate>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                            <configOptions>
                                <implicitHeaders>true</implicitHeaders>
                                <!-- currently using legacy as the services does not provide valid dates - see MDD-2573, change to java8 or remove when issue is fixed -->
                                <dateLibrary>java8</dateLibrary>
                                <useSpringBoot3>true</useSpringBoot3>
                                <useJakartaEe>true</useJakartaEe>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-road-consignmentlabel-client</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/openapi/road-consignmentlabel-api.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <!-- spring webclient generator type -->
                            <library>resttemplate</library>
                            <apiPackage>com.dachser.dfe.road.consignmentlabel.api</apiPackage>
                            <modelPackage>com.dachser.dfe.road.consignmentlabel.model</modelPackage>
                            <modelNamePrefix>RCL</modelNamePrefix>
                            <supportingFilesToGenerate>
                                BaseApi.java,ApiClient.java,JavaTimeFormatter.java,RFC3339DateFormat.java,Authentication.java,HttpBasicAuth.java
                            </supportingFilesToGenerate>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                            <configOptions>
                                <implicitHeaders>true</implicitHeaders>
                                <!-- currently using legacy as the services does not provide valid dates - see MDD-2573, change to java8 or remove when issue is fixed -->
                                <dateLibrary>java8</dateLibrary>
                                <useSpringBoot3>true</useSpringBoot3>
                                <useTags>true</useTags>
                                <useJakartaEe>true</useJakartaEe>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-platform-backend-client</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.build.directory}/platform-backend-api/specification/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <!-- spring webclient generator type -->
                            <library>resttemplate</library>
                            <apiPackage>com.dachser.dfe.platform.api</apiPackage>
                            <modelPackage>com.dachser.dfe.platform.model</modelPackage>
                            <modelNamePrefix>Platform</modelNamePrefix>
                            <supportingFilesToGenerate>
                                BaseApi.java,ApiClient.java,Authentication.java,OAuth.java,JavaTimeFormatter.java,RFC3339DateFormat.java
                            </supportingFilesToGenerate>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                            <configOptions>
                                <implicitHeaders>true</implicitHeaders>
                                <dateLibrary>java8</dateLibrary>
                                <useTags>true</useTags>
                                <useJakartaEe>true</useJakartaEe>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-general-data-client</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.build.directory}/general-data-backend-api/specification/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <apiPackage>com.dachser.dfe.generaldata.api</apiPackage>
                            <modelPackage>com.dachser.dfe.generaldata.model</modelPackage>
                            <modelNamePrefix>GD</modelNamePrefix>
                            <modelNameSuffix>Dto</modelNameSuffix>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                            <configOptions>
                                <implicitHeaders>false</implicitHeaders>
                                <delegatePattern>true</delegatePattern>
                                <!-- Java 8 native JSR310 (preferred for jdk 1.8+) -->
                                <dataLibrary>java8</dataLibrary>
                                <useJakartaEe>true</useJakartaEe>
                                <useTags>true</useTags>
                            </configOptions>
                            <additionalProperties>removeEnumValuePrefix=false</additionalProperties>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-airsea-masterdata-client</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/openapi/airsea-masterdata-api.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <!-- spring webclient generator type -->
                            <library>resttemplate</library>
                            <apiPackage>com.dachser.dfe.airsea.masterdata.api</apiPackage>
                            <modelPackage>com.dachser.dfe.airsea.masterdata.model</modelPackage>
                            <modelNamePrefix>ASMD</modelNamePrefix>
                            <supportingFilesToGenerate>
                                BaseApi.java,ApiClient.java,JavaTimeFormatter.java,RFC3339DateFormat.java,StringUtil.java,Authentication.java,HttpBasicAuth.java,HttpBearerAuth.java,ApiKeyAuth.java
                            </supportingFilesToGenerate>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                            <configOptions>
                                <implicitHeaders>true</implicitHeaders>
                                <!-- currently using legacy as the services does not provide valid dates - see MDD-2573, change to java8 or remove when issue is fixed -->
                                <dateLibrary>java8</dateLibrary>
                                <useSpringBoot3>true</useSpringBoot3>
                                <useJakartaEe>true</useJakartaEe>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-geo-masterdata-client</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.build.directory}/masterdata-geo-api/api-docs.json
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <!-- spring webclient generator type -->
                            <library>resttemplate</library>
                            <apiPackage>com.dachser.dfe.masterdata.geo.api</apiPackage>
                            <modelPackage>com.dachser.dfe.masterdata.geo.model</modelPackage>
                            <modelNamePrefix>GMD</modelNamePrefix>
                            <supportingFilesToGenerate>
                                BaseApi.java,ApiClient.java,JavaTimeFormatter.java,RFC3339DateFormat.java,StringUtil.java,Authentication.java,HttpBasicAuth.java,HttpBearerAuth.java,ApiKeyAuth.java
                            </supportingFilesToGenerate>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                            <configOptions>
                                <implicitHeaders>true</implicitHeaders>
                                <!-- currently using legacy as the services does not provide valid dates - see MDD-2573, change to java8 or remove when issue is fixed -->
                                <dateLibrary>java8</dateLibrary>
                                <useSpringBoot3>true</useSpringBoot3>
                                <useJakartaEe>true</useJakartaEe>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-thirdparty-masterdata-client</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.build.directory}/masterdata-third-party-api/api-docs.json
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <!-- spring webclient generator type -->
                            <library>resttemplate</library>
                            <apiPackage>com.dachser.dfe.masterdata.thirdparty.api</apiPackage>
                            <modelPackage>com.dachser.masterdata.thirdparty.model</modelPackage>
                            <modelNamePrefix>MDT</modelNamePrefix>
                            <supportingFilesToGenerate>
                                BaseApi.java,ApiClient.java,JavaTimeFormatter.java,RFC3339DateFormat.java,StringUtil.java,Authentication.java,HttpBasicAuth.java,HttpBearerAuth.java,ApiKeyAuth.java
                            </supportingFilesToGenerate>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                            <configOptions>
                                <implicitHeaders>true</implicitHeaders>
                                <!-- currently using legacy as the services does not provide valid dates - see MDD-2573, change to java8 or remove when issue is fixed -->
                                <dateLibrary>java8</dateLibrary>
                                <useSpringBoot3>true</useSpringBoot3>
                                <useJakartaEe>true</useJakartaEe>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-pdf-export-client</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.build.directory}/pdf-export-backend-api/specification/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <apiPackage>com.dachser.dfe.pdfexport.api</apiPackage>
                            <modelPackage>com.dachser.dfe.pdfexport.model</modelPackage>
                            <invokerPackage>com.dachser.dfe.pdfexport.support</invokerPackage>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                            <configOptions>
                                <implicitHeaders>false</implicitHeaders>
                                <delegatePattern>true</delegatePattern>
                                <!-- Java 8 native JSR310 (preferred for jdk 1.8+) -->
                                <dataLibrary>java8</dataLibrary>
                                <useTags>true</useTags>
                                <useJakartaEe>true</useJakartaEe>
                                <useSpringBoot3>true</useSpringBoot3>
                            </configOptions>
                            <additionalProperties>removeEnumValuePrefix=false</additionalProperties>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-track-and-trace-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.build.directory}/track-and-trace-api/specification/openapi.yaml
                            </inputSpec>
                            <generatorName>spring</generatorName>
                            <apiPackage>com.dachser.dfe.trackandtrace.api</apiPackage>
                            <modelPackage>com.dachser.dfe.trackandtrace.model</modelPackage>
                            <invokerPackage>com.dachser.dfe.trackandtrace.api</invokerPackage>
                            <modelNamePrefix>TT</modelNamePrefix>
                            <supportingFilesToGenerate>
                                ApiUtil.java
                            </supportingFilesToGenerate>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                            <configOptions>
                                <implicitHeaders>true</implicitHeaders>
                                <delegatePattern>true</delegatePattern>
                                <useTags>true</useTags>
                                <useJakartaEe>true</useJakartaEe>
                                <useSpringBoot3>true</useSpringBoot3>
                                <additionalModelTypeAnnotations>
                                    @com.dachser.dfe.book.overview.TrackAndTraceApiImplementation
                                </additionalModelTypeAnnotations>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-book-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.build.directory}/book-backend-api/specification/openapi.yaml
                            </inputSpec>
                            <generatorName>spring</generatorName>
                            <apiPackage>com.dachser.dfe.book.api</apiPackage>
                            <modelPackage>com.dachser.dfe.book.model</modelPackage>
                            <invokerPackage>com.dachser.dfe.book.api</invokerPackage>
                            <modelNameSuffix>Dto</modelNameSuffix>
                            <supportingFilesToGenerate>
                                ApiUtil.java
                            </supportingFilesToGenerate>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                            <configOptions>
                                <implicitHeaders>true</implicitHeaders>
                                <delegatePattern>true</delegatePattern>
                                <useTags>true</useTags>
                                <useJakartaEe>true</useJakartaEe>
                                <useSpringBoot3>true</useSpringBoot3>
                            </configOptions>
                        </configuration>
                    </execution>

                    <execution>
                        <id>generate-emissionforecast-client</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.build.directory}/emissionforecast-api/specification/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <apiPackage>com.dachser.dfe.emissionforecast.api</apiPackage>
                            <modelPackage>com.dachser.dfe.emissionforecast.model</modelPackage>
                            <invokerPackage>com.dachser.dfe.emissionforecast.support</invokerPackage>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <modelNamePrefix>EF</modelNamePrefix>
                            <modelNameSuffix>Dto</modelNameSuffix>
                            <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                            <configOptions>
                                <implicitHeaders>false</implicitHeaders>
                                <delegatePattern>true</delegatePattern>
                                <!-- Java 8 native JSR310 (preferred for jdk 1.8+) -->
                                <dataLibrary>java8</dataLibrary>
                                <useTags>true</useTags>
                                <useJakartaEe>true</useJakartaEe>
                                <useSpringBoot3>true</useSpringBoot3>
                            </configOptions>
                            <additionalProperties>removeEnumValuePrefix=false</additionalProperties>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <configuration>
                    <artifactItems>
                        <artifactItem>
                            <groupId>com.dachser.dfe.plattform</groupId>
                            <artifactId>dfe-plattform-api</artifactId>
                            <version>${dfe-plattform-api.version}</version>
                            <includes>specification/openapi.yaml</includes>
                            <overWrite>true</overWrite>
                            <outputDirectory>${project.build.directory}/platform-backend-api</outputDirectory>
                        </artifactItem>
                        <artifactItem>
                            <groupId>com.dachser.dfe.book</groupId>
                            <artifactId>dfe-book-api</artifactId>
                            <version>${dfe.book-api.version}</version>
                            <includes>specification/openapi.yaml</includes>
                            <overWrite>true</overWrite>
                            <outputDirectory>${project.build.directory}/book-backend-api</outputDirectory>
                        </artifactItem>
                        <artifactItem>
                            <groupId>com.dachser.dfe.pdfexport</groupId>
                            <artifactId>dfe-pdf-export-api</artifactId>
                            <version>${dfe-pdf-export-api.version}</version>
                            <includes>specification/openapi.yaml</includes>
                            <overWrite>true</overWrite>
                            <outputDirectory>${project.build.directory}/pdf-export-backend-api</outputDirectory>
                        </artifactItem>
                        <artifactItem>
                            <groupId>com.dachser.dfe.generaldata</groupId>
                            <artifactId>dfe-general-data-api</artifactId>
                            <version>${dfe-general-data-api.version}</version>
                            <includes>specification/openapi.yaml</includes>
                            <overWrite>true</overWrite>
                            <outputDirectory>${project.build.directory}/general-data-backend-api</outputDirectory>
                        </artifactItem>
                        <artifactItem>
                            <groupId>com.dachser.dfe.trackandtrace</groupId>
                            <artifactId>dfe-trackandtrace-api</artifactId>
                            <version>${dfe.trackandtrace-api.version}</version>
                            <includes>specification/openapi.yaml</includes>
                            <overWrite>true</overWrite>
                            <outputDirectory>${project.build.directory}/track-and-trace-api</outputDirectory>
                        </artifactItem>

                        <artifactItem>
                            <groupId>com.dachser.dfe.emissionforecast</groupId>
                            <artifactId>dfe-emissionforecast-api</artifactId>
                            <version>${dfe-emissions-api.version}</version>
                            <includes>specification/openapi.yaml</includes>
                            <overWrite>true</overWrite>
                            <outputDirectory>${project.build.directory}/emissionforecast-api</outputDirectory>
                        </artifactItem>
                        <artifactItem>
                            <groupId>com.dachser.sharedservices</groupId>
                            <artifactId>masterdata.geo.service</artifactId>
                            <classifier>api-docs</classifier>
                            <version>${masterdata.geo.version}</version>
                            <includes>api-docs.json</includes>
                            <overWrite>true</overWrite>
                            <outputDirectory>${project.build.directory}/masterdata-geo-api</outputDirectory>
                        </artifactItem>
                        <artifactItem>
                            <groupId>com.dachser.sharedservices</groupId>
                            <artifactId>masterdata.third.party.integration.service</artifactId>
                            <classifier>api-docs</classifier>
                            <version>${masterdata.thirdparty.version}</version>
                            <includes>api-docs.json</includes>
                            <overWrite>true</overWrite>
                            <outputDirectory>${project.build.directory}/masterdata-third-party-api</outputDirectory>
                        </artifactItem>
                    </artifactItems>
                </configuration>
                <executions>
                    <execution>
                        <id>unpack-api-definitions</id>
                        <phase>initialize</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>integration-tests</id>
        </profile>
    </profiles>
</project>