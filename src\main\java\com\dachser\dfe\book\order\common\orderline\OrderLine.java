package com.dachser.dfe.book.order.common.orderline;

import com.dachser.dfe.book.order.validation.CompleteOrderValidation;
import com.dachser.dfe.book.order.validation.air.CompleteOrderValidationAir;
import com.dachser.dfe.book.order.validation.sea.CompleteOrderValidationSea;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

@Data
@MappedSuperclass
public abstract class OrderLine {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long orderLineId;

	@NotNull
	private Integer number;

	@NotNull(groups = { CompleteOrderValidation.class, CompleteOrderValidationAir.class}, message = "{label.text.invalid_input}")
	@Min(value = 1, groups = { CompleteOrderValidation.class, CompleteOrderValidationAir.class }, message = "{label.text.invalid_input}")
	@Max(value = 999, groups = { CompleteOrderValidation.class, CompleteOrderValidationAir.class, CompleteOrderValidationSea.class }, message = "{label.text.invalid_input}")
	private Integer quantity;

	@Size(max = 3)
	@NotNull(groups = CompleteOrderValidation.class, message = "{label.text.invalid_input}")
	private String packagingType;

	@Size(max = 50)
	@NotNull(groups = CompleteOrderValidation.class, message = "{label.text.invalid_input}")
	private String packagingTypeDescription;

	@NotNull(groups = { CompleteOrderValidationAir.class, CompleteOrderValidation.class }, message = "{label.text.invalid_input}")
	@Min(value = 1, message = "{label.text.invalid_input}")
	@Max(value = 999, message = "{label.text.invalid_input}")
	private Integer length;

	@NotNull(groups = { CompleteOrderValidationAir.class, CompleteOrderValidation.class }, message = "{label.text.invalid_input}")
	@Min(value = 1, message = "{label.text.invalid_input}")
	@Max(value = 999, message = "{label.text.invalid_input}")
	private Integer width;

	@NotNull(groups = { CompleteOrderValidationAir.class, CompleteOrderValidation.class }, message = "{label.text.invalid_input}")
	@Min(value = 1, message = "{label.text.invalid_input}")
	@Max(value = 999, message = "{label.text.invalid_input}")
	private Integer height;

	@NotNull(groups = { CompleteOrderValidationAir.class, CompleteOrderValidation.class }, message = "{label.text.invalid_input}")
	@DecimalMin(value = "0.001")
	@DecimalMax(value = "999.999")
	private BigDecimal volume;

	public abstract BigDecimal getWeight();

	public abstract void setWeight(BigDecimal weight);

}
