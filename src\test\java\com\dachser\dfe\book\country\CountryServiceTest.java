package com.dachser.dfe.book.country;

import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.exception.DachserCodeNotMappedException;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.jpa.query.QueryService;
import com.dachser.dfe.book.model.CountryDto;
import com.dachser.dfe.book.model.FavoriteCountriesDto;
import com.dachser.dfe.book.model.Segment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CountryServiceTest {

	@Mock
	private GeneralDataService generalDataService;

	@Mock
	private QueryService queryService;

	@Spy
	private WhiteListFilterConfiguration whiteListFilterConfiguration;

	@InjectMocks
	private CountryService countryService;

	@Nested
	class GetSortedAndFilteredCountries {

		@BeforeEach
		void setupCountries() {
			final WhiteListFilterConfiguration.CountriesFilter countriesFilter = new WhiteListFilterConfiguration.CountriesFilter();
			countriesFilter.setRoad(List.of("DE", "FR", "GB"));
			whiteListFilterConfiguration.setCountries(countriesFilter);
		}

		@Test
		void shouldFilterNonWhitelistedCountryCodesRoad() {
			List<Country> countryCodes = getCountryCodes();
			when(generalDataService.getCountries("en")).thenReturn(countryCodes);
			final List<CountryDto> collect = countryService.getSortedAndFilteredCountries("en", Segment.ROAD);
			assertEquals(3, collect.size());
			assertEquals("FR", collect.get(0).getCountryCode());
			assertTrue(collect.get(0).getIsPostCodeMandatory());
		}

		@Test
		void shouldFilterNonWhitelistedCountryCodesRoadAndSortEvenWithoutNames() {
			List<Country> countryCodes = getCountryCodes();
			countryCodes.forEach(country -> country.setCountryName(null));
			when(generalDataService.getCountries("XX")).thenReturn(countryCodes);
			final List<CountryDto> collect = countryService.getSortedAndFilteredCountries("XX", Segment.ROAD);
			assertEquals(3, collect.size());
			assertEquals("DE", collect.get(0).getCountryCode());
			assertTrue(collect.get(0).getIsPostCodeMandatory());
		}

		@Test
		void shouldNotFilterCountriesForASL() {
			List<Country> countryCodes = getCountryCodes();
			when(generalDataService.getCountries("en")).thenReturn(countryCodes);
			final List<CountryDto> collect = countryService.getSortedAndFilteredCountries("en", Segment.AIR);
			assertEquals(5, collect.size());
			assertEquals("AU", collect.get(0).getCountryCode());
			assertTrue(collect.get(0).getIsPostCodeMandatory());
		}

		@Test
		void shouldFilterCountriesWithLanguageCollator() {
			List<Country> countryCodes = getFakeCountryCodes();
			when(generalDataService.getCountries("de")).thenReturn(countryCodes);
			final List<CountryDto> collect = countryService.getSortedAndFilteredCountries("de", Segment.AIR);
			assertEquals(5, collect.size());
			assertEquals("US", collect.get(0).getCountryCode());
			assertEquals("DE", collect.get(1).getCountryCode());
			assertEquals("GB", collect.get(2).getCountryCode());
			assertEquals("FR", collect.get(3).getCountryCode());
			assertEquals("AU", collect.get(4).getCountryCode());
		}

		@Test
		void shouldNotFailOnEmptyResult() {
			when(generalDataService.getCountries("en")).thenReturn(List.of());
			final List<CountryDto> collect = countryService.getSortedAndFilteredCountries("en", Segment.AIR);
			assertEquals(0, collect.size());
		}
	}

	@Nested
	class MapToDachserCountryCode {
		@Test
		void shouldMapCountryCodeToDachserCountryCode() {
			List<Country> countryCodes = getCountryCodes();
			when(generalDataService.getCountries("en")).thenReturn(countryCodes);
			final String dachserCountryCode = countryService.mapToDachserCountryCode("DE");
			assertEquals("D", dachserCountryCode);
		}

		@Test
		void shouldThrowExceptionIfCountryCodeIsNotMapped() {
			List<Country> countryCodes = getCountryCodes();
			when(generalDataService.getCountries("en")).thenReturn(countryCodes);
			assertThrows(DachserCodeNotMappedException.class, () -> countryService.mapToDachserCountryCode("XX"));
		}

		@Test
		void shouldThrowExceptionIfCountryHasNoMappedCode() {
			List<Country> countryCodes = getCountryCodes();
			countryCodes.get(0).setDachserCountryCode(null);
			when(generalDataService.getCountries("en")).thenReturn(countryCodes);
			assertThrows(DachserCodeNotMappedException.class, () -> countryService.mapToDachserCountryCode("DE"));
		}
	}

	@Nested
	class GetFavoriteCountriesForCustomer {

		@BeforeEach
		void setupCountries() {
			final WhiteListFilterConfiguration.CountriesFilter countriesFilter = new WhiteListFilterConfiguration.CountriesFilter();
			countriesFilter.setRoad(List.of("DE", "IT", "FR", "GB", "ES"));
			whiteListFilterConfiguration.setCountries(countriesFilter);
		}

		@ParameterizedTest
		@MethodSource("provideFavoriteCountryParameters")
		void shouldReturnFavoriteCountries(String language, Segment segment, String customerNumber) {
			when(generalDataService.getCountries(anyString())).thenReturn(getCountryCodes());
			when(queryService.performNativeQuery(anyString())).thenReturn(List.of("DE", "FR", "GB", "AU", "US"));
			FavoriteCountriesDto countriesWithFavorites = countryService.getCountriesForCustomer(language, segment, customerNumber);

			assertEquals(3, countriesWithFavorites.getConsigneeCountries().size());
			assertEquals("DE", countriesWithFavorites.getConsigneeCountries().get(0).getCountryCode());
			assertEquals("Germany", countriesWithFavorites.getConsigneeCountries().get(0).getLabel());
			assertEquals(3, countriesWithFavorites.getShipperCountries().size());
			assertEquals("DE", countriesWithFavorites.getShipperCountries().get(0).getCountryCode());
			assertEquals("Germany", countriesWithFavorites.getConsigneeCountries().get(0).getLabel());
			assertEquals("GB", countriesWithFavorites.getShipperCountries().get(2).getCountryCode());
		}

		private static List<Arguments> provideFavoriteCountryParameters() {
			return List.of(Arguments.of("en", Segment.ROAD, "123"), Arguments.of("en", Segment.AIR, "123"), Arguments.of("de", Segment.ROAD, "123"),
					Arguments.of("de", Segment.AIR, "123"));
		}

	}

	@Nested
	class IsPostcodeMandatory {

		@Test
		void shouldUsePostCodeMandatoryField() {
			List<Country> countryCodes = getCountryCodes();
			countryCodes.stream().filter(country -> country.getCountryCode().equals("FR")).findFirst().get().setPostcodeMandatory(false);
			when(generalDataService.getCountries("en")).thenReturn(countryCodes);

			assertFalse(countryService.isPostCodeMandatory("FR"));
			assertTrue(countryService.isPostCodeMandatory("DE"));
		}
	}

	private static List<Country> getCountryCodes() {
		return List.of(new Country("DE", "Germany", "D", true), new Country("US", "United States", null, true), new Country("AU", "Australia", null, true),
				new Country("FR", "France", "F", true), new Country("GB", "Great Britain", "GB", true));
	}

	private static List<Country> getFakeCountryCodes() {
		return List.of(new Country("DE", "Deutschland", "D", true), new Country("US", "Ämerika", null, true), new Country("AU", "Österreich", null, true),
				new Country("FR", "Oman", "F", true), new Country("GB", "Great Britain", "GB", true));
	}
}