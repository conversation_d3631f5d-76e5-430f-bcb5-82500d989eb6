package com.dachser.dfe.book.dip.converter.road.collection;

import com.dachser.dfe.book.dip.converter.DipOrderConverter;
import com.dachser.dfe.book.dip.xml.XmlConverter;
import com.dachser.dfe.book.model.jaxb.order.road.collection.CollectionOrderInformation;
import com.dachser.dfe.book.order.road.CollectionOrder;
import org.springframework.stereotype.Component;

@Component
public class CollectionDipOrderConverter extends DipOrderConverter<CollectionOrder> {

	private final CollectionOrderInformationMapper mapper;

	public CollectionDipOrderConverter(XmlConverter xmlConverter, CollectionOrderInformationMapper mapper) {
		super(xmlConverter);
		this.mapper = mapper;
	}

	@Override
	protected CollectionOrderInformation map(CollectionOrder order) {
		return mapper.map(order);
	}
}
