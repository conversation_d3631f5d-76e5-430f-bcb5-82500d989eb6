package com.dachser.dfe.book.user.platform;

import com.dachser.dfe.book.cache.CacheNames;
import com.dachser.dfe.platform.ApiClient;
import com.dachser.dfe.platform.api.PlatformV5Api;
import com.dachser.dfe.platform.model.PlatformUserV5;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.annotation.Cacheable;

@Slf4j
@ConditionalOnProperty(value = "dfe.platform.configuration.useCache", havingValue = "true")
public class CacheablePlatformV5Api extends PlatformV5Api {

	public CacheablePlatformV5Api(final ApiClient apiClient) {
		super(apiClient);
	}

	@Override
	@Cacheable(value = CacheNames.PLATFORM_USER, keyGenerator = "sidKeyGenerator")
	public PlatformUserV5 getUserProfileV5() {
		final PlatformUserV5 platformUser = super.getUserProfileV5();
		log.debug("No {} cache hit. Retrieved UserCompanyProfile for {}", CacheNames.PLATFORM_USER, platformUser.getEmail());
		return platformUser;
	}
}
