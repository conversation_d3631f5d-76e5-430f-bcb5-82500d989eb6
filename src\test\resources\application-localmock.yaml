spring:
  datasource:
    driver-class-name: org.h2.Driver
    username: sa
    password:
    #url: "jdbc:h2:./testdb/h2testdb;MODE=MSSQLServer;DB_CLOSE_ON_EXIT=FALSE;INIT=CREATE SCHEMA IF NOT EXISTS dbo\\;SET SCHEMA dbo"
    url: "jdbc:h2:mem:db1;MODE=MSSQLServer;DB_CLOSE_ON_EXIT=FALSE;INIT=CREATE SCHEMA IF NOT EXISTS dbo\\;SET SCHEMA dbo"
    # changed the database mode to regular because in MSSQL Mode it prohibits unique indexes containing null several times "For unique indexes, NULL is distinct. That means only one row with NULL in one of the columns is allowed."
    # There is a workaround in mssql but not in h2 mssql mode so we switch to regular mode
  h2:
    console:
      enabled: true
  liquibase:
    contexts: mock-data,h2
    drop-first: true


dfe:
  book:
    mock: true
    mock.product.road: true
    mock.userlib: true
    cache2k:
      enabled: false
  sharedservices:
    masterdata:
      businesspartner:
        baseURL: localhost
  road:
    masterdata:
      use-preconfigured-cache-2k: false
  platform:
    configuration:
      use-cache: false
  services:
    document:
      basePath: ./unit-test-files/

ums:
  dip:
    ediTestFlagRoad: true
    ediTestFlagAirSea: true        
  enabled: false


orderExpired:
  scheduled:
    setStatus: "-" # disable for test
    deleteOrder: "-" # disable for test
cleanUpDocuments:
  scheduled:
    deleteSentOrders: "-" # disable for test
    deleteUnlinkedOrders: "-" # disable for test
    deleteOrders: "-" # disable for test

ENV_BOOK_DB_LOGLEVEL: WARN
ENV_BOOK_APPLICATION_LOGLEVEL: WARN
ENV_USERLIBRARY_USER_SERVICE_URL: "https://keycloak-dev-dfe-keycloak.apps.ocp-dev-02.appl-02.dach041.dachser.com"
ENV_USERLIBRARY_USER_SERVICE_REALM: "dfe"
ENV_BOOK_API_DFE_SHAREDSERVICES_MASTERDATA_BUSINESSPARTNER_BASEURL: "https://dach041y.dach041.dachser.com:21167/masterdata.businesspartner.backend"
ENV_BOOK_API_DFE_SHAREDSERVICES_MASTERDATA_GEO_BASEURL: "https://dach041y.dach041.dachser.com:21175/masterdata.geo.service"
ENV_BOOK_API_DFE_AIRSEA_MASTERDATA_BASEURL: "https://dach041x.dach041.dachser.com:21397/airsea.masterdata.service"
ENV_BOOK_API_DFE_ROAD_MASTERDATA_BASEURL: "https://dach041x.dach041.dachser.com:21365/road.masterdata.order.service"
ENV_BOOK_API_DFE_ROAD_CONSINGMENT_LABEL_BASEURL: "https://dach041y.dach041.dachser.com:21957/road.consignmentlabel.service"

ENV_BOOK_UMS_FACTORY: SpringTestTopicConnectionFactory
ENV_BOOK_API_DFE_PLATFORM_BACKEND_BASEURL: "https://dfe-platform-backend-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"
ENV_BOOK_API_GENERAL_DATA_SERVER_BASEURL: "https://dfe-general-data-backend-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"
ENV_BOOK_API_PDF_EXPORT_SERVER_BASEURL: "https://dfe-pdf-export-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"
ENV_BOOK_API_DFE_EMISSIONFORECAST_BASEURL: "https://dfe-emissionforecast-backend-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"

ENV_BOOK_API_KTA_URL: foo


ENV_BOOK_OAUTH_SERVER_BASE_URL: http://localhost/not_set
