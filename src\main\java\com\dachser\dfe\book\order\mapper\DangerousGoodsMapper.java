package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.mapper.PersistentListMerger;
import com.dachser.dfe.book.model.ADRDangerousGoodDto;
import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.dfe.book.model.DangerousGoodDto;
import com.dachser.dfe.book.model.EQDangerousGoodDto;
import com.dachser.dfe.book.model.LQDangerousGoodDto;
import com.dachser.dfe.book.order.common.dangerousgood.ADRDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGoodDataItem;
import com.dachser.dfe.book.order.common.dangerousgood.EQDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.LQDangerousGood;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.SubclassExhaustiveStrategy;
import org.mapstruct.SubclassMapping;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION)
public interface DangerousGoodsMapper {

	// Single DTO -> Entity
	@SubclassMapping(source = LQDangerousGoodDto.class, target = LQDangerousGood.class)
	@SubclassMapping(source = EQDangerousGoodDto.class, target = EQDangerousGood.class)
	@SubclassMapping(source = ADRDangerousGoodDto.class, target = ADRDangerousGood.class)
	DangerousGood map(DangerousGoodDto dangerousGoodDto);

	// Single Entity -> DTO
	@InheritInverseConfiguration
	@SubclassMapping(source = LQDangerousGood.class, target = LQDangerousGoodDto.class)
	@SubclassMapping(source = EQDangerousGood.class, target = EQDangerousGoodDto.class)
	@SubclassMapping(source = ADRDangerousGood.class, target = ADRDangerousGoodDto.class)
	DangerousGoodDto map(DangerousGood dangerousGood);

	LQDangerousGood mapLQDangerousGood(LQDangerousGoodDto dangerousGood);

	@Mapping(target = "packagingKey", source = "packaging.code")
	EQDangerousGood mapEQDangerousGood(EQDangerousGoodDto dangerousGood);

	@Mapping(target = "packagingKey", source = "packaging.code")
	ADRDangerousGood mapADRDangerousGood(ADRDangerousGoodDto dangerousGood);

	// Update existing Entities
	@InheritConfiguration(name = "mapLQDangerousGood")
	LQDangerousGood updateLQDangerousGood(LQDangerousGoodDto dangerousGoodDto, @MappingTarget LQDangerousGood dangerousGood);

	@InheritConfiguration(name = "mapEQDangerousGood")
	EQDangerousGood updateEQDangerousGood(EQDangerousGoodDto dangerousGoodDto, @MappingTarget EQDangerousGood dangerousGood);

	@InheritConfiguration(name = "mapADRDangerousGood")
	ADRDangerousGood updateADRDangerousGood(ADRDangerousGoodDto dangerousGoodDto, @MappingTarget ADRDangerousGood dangerousGood);

	// Entity -> DTO
	@InheritInverseConfiguration(name = "mapLQDangerousGood")
	LQDangerousGoodDto mapLQDangerousGoodDto(LQDangerousGood dangerousGood);

	@InheritInverseConfiguration(name = "mapEQDangerousGood")
	@Mapping(target = "packaging.translationKey", source = "packagingKey", qualifiedByName = "mapPackagingKeyToTranslationKey")
	EQDangerousGoodDto mapEQDangerousGoodDto(EQDangerousGood dangerousGood);

	@InheritInverseConfiguration(name = "mapADRDangerousGood")
	@Mapping(target = "packaging.translationKey", source = "packagingKey", qualifiedByName = "mapPackagingKeyToTranslationKey")
	ADRDangerousGoodDto mapADRDangerousGoodDto(ADRDangerousGood dangerousGood);

	// Default merging for update operations
	List<DangerousGood> mapDangerousGoodsDtos(List<DangerousGoodDto> dtos);

	default List<DangerousGoodDto> mapDangerousGoods(List<DangerousGood> dangerousGoods) {
		if (dangerousGoods == null) {
			return null;
		}
		return dangerousGoods.stream().map(this::map).collect(Collectors.toList());
	}

	// UnNumberDataSet mappings
	@Mapping(source = "dgmId", target = "externalDgmId")
	DangerousGoodDataItem mapDangerousGoodDataItem(DangerousGoodDataItemDto dangerousGoodDataItemDto);

	@InheritInverseConfiguration(name = "mapDangerousGoodDataItem")
	DangerousGoodDataItemDto mapDangerousGoodDataItem(DangerousGoodDataItem dangerousGoodDataItem);

	default void updateDangerousGoodList(List<DangerousGoodDto> dtos, @MappingTarget List<DangerousGood> entities) {
		if (dtos == null || entities == null) {
			return;
		}

		// Create a merger for updating, adding and removing entities based on input DTOs
		PersistentListMerger<DangerousGood, DangerousGoodDto> listMerger = (input, existing) -> {
			// Update existing entity based on DTO subtype
			if (input instanceof ADRDangerousGoodDto adrDto && existing instanceof ADRDangerousGood adrEntity) {
				return updateADRDangerousGood(adrDto, adrEntity);
			} else if (input instanceof EQDangerousGoodDto eqDto && existing instanceof EQDangerousGood eqEntity) {
				return updateEQDangerousGood(eqDto, eqEntity);
			} else if (input instanceof LQDangerousGoodDto lqDto && existing instanceof LQDangerousGood lqEntity) {
				return updateLQDangerousGood(lqDto, lqEntity);
			}
			// Add new entity if not present
			else if (input instanceof ADRDangerousGoodDto adrDto) {
				return mapADRDangerousGood(adrDto);
			} else if (input instanceof EQDangerousGoodDto eqDto) {
				return mapEQDangerousGood(eqDto);
			} else if (input instanceof LQDangerousGoodDto lqDto) {
				return mapLQDangerousGood(lqDto);
			}
			throw new IllegalArgumentException("Unsupported DTO type: " + input.getClass());
		};

		// Unused supplier, because mappingMethod creates correct instances

		listMerger.updateList(dtos, entities, DangerousGood::getId, DangerousGoodDto::getId, () -> null);
	}

	@Named("mapPackagingKeyToTranslationKey")
	default String mapPackagingKeyToTranslationKey(String packagingKey) {
		return "generalData.packagingOptionRoad." + packagingKey;
	}

	@Named("mapADR")
	default List<ADRDangerousGoodDto> mapADR(RoadOrderLine orderLine) {
		return mapDangerousGoods(orderLine, ADRDangerousGood.class, this::mapADRDangerousGoodDto);
	}

	@Named("mapEQ")
	default List<EQDangerousGoodDto> mapEQ(RoadOrderLine orderLine) {
		return mapDangerousGoods(orderLine, EQDangerousGood.class, this::mapEQDangerousGoodDto);
	}

	@Named("mapLQ")
	default List<LQDangerousGoodDto> mapLQ(RoadOrderLine orderLine) {
		return mapDangerousGoods(orderLine, LQDangerousGood.class, this::mapLQDangerousGoodDto);
	}

	private <T extends DangerousGood, R> List<R> mapDangerousGoods(RoadOrderLine orderLine, Class<T> type, java.util.function.Function<T, R> mapper) {
		return orderLine.getDangerousGoods().stream().filter(type::isInstance).map(type::cast).map(mapper).collect(Collectors.toList());
	}

}
