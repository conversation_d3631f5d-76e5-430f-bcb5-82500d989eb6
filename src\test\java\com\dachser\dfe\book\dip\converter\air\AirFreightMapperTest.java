package com.dachser.dfe.book.dip.converter.air;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.dip.converter.shared.AirSeaGoodsDescriptionMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderContactCommunicationsMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.DateMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.StringNotEmptyConditionMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.model.jaxb.order.asl.Location;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirOrderLineHsCode;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

@ExtendWith(MockitoExtension.class)
@SpringBootTest(classes = { AirFreightMapperImpl.class, AirPickupOrderMapperImpl.class, AirDeliveryOrderMapperImpl.class, DateMapperImpl.class,
		AirSeaOrderContactCommunicationsMapperImpl.class, AirOrderLineMapperImpl.class, AirOrderReferenceMapperImpl.class, AirSeaGoodsDescriptionMapperImpl.class,
		StringNotEmptyConditionMapperImpl.class })
class AirFreightMapperTest {

	@Autowired
	AirFreightMapper airfreightMapper;

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	@Nested
	class ValidMapping {

		@Nested
		class AirOrderMapper {
			AirExportOrder order = testUtil.generateAirExportOrder();

			@Test
			void shouldMapLocations() {
				final List<Location> locations = airfreightMapper.mapLocations(order);
				assertNotNull(locations);
				assertEquals(2, locations.size());
			}

			@Test
			void shouldMapAirFreightShipment() {
				final ForwardingOrder.AirFreightShipment map = airfreightMapper.map(order);
				assertNotNull(map);
				final List<ForwardingOrder.AirFreightShipment.PickupOrder> pickupOrder = map.getPickupOrder();
				assertNotNull(pickupOrder);
				assertEquals(1, pickupOrder.size());
			}

			@Test
			void shouldSetOrderCategory() {
				final ForwardingOrder.AirFreightShipment map = airfreightMapper.map(order);
				assertNotNull(map);
				assertEquals("LCD", map.getOrderCategory());
			}

			@Test
			void shouldSetTransportMovementCategory() {
				final ForwardingOrder.AirFreightShipment map = airfreightMapper.map(order);
				assertNotNull(map);
				assertEquals("Export", map.getTransportMovement());
			}

			@Test
			void shouldMapShipmentAddress() {
				final List<ForwardingOrder.AirFreightShipment.ShipmentAddress> shipmentAddresses = airfreightMapper.customMapShipmentAddresses(order);
				assertNotNull(shipmentAddresses);
				assertEquals(6, shipmentAddresses.size());
			}

			@Test
			void shouldMapPickupAddressToOrderAddress() {
				final ForwardingOrder.AirFreightShipment map = airfreightMapper.map(order);
				assertNotNull(map.getPickupOrder());
				assertNotNull(map.getPickupOrder().getFirst());
				final ForwardingOrder.AirFreightShipment.PickupOrder.OrderAddress orderAddress = map.getPickupOrder().getFirst().getOrderAddress();
				assertNotNull(orderAddress);
				assertEquals("Example GmbH _pickup", orderAddress.getAddress().getName1());
				assertEquals(Types.AddressTypes.PICKUP, orderAddress.getTypeOfAddress());
			}

			@Test
			void shouldNotMapPickupDatesOnRequestArrangement() {
				order.setRequestArrangement(true);
				final ForwardingOrder.AirFreightShipment map = airfreightMapper.map(order);
				final List<ForwardingOrder.AirFreightShipment.PickupOrder> pickupOrder = map.getPickupOrder();
				assertNotNull(pickupOrder);
				final ForwardingOrder.AirFreightShipment.PickupOrder pickupOrder1 = pickupOrder.getFirst();
				assertNotNull(pickupOrder1);
				assertEquals(0, pickupOrder1.getPickUpDate().size());
			}

			@Test
			void shouldMapShipperAddressToOrderAddress() {
				order.setPickupAddress(null);
				final ForwardingOrder.AirFreightShipment map = airfreightMapper.map(order);
				assertNotNull(map.getPickupOrder());
				assertNotNull(map.getPickupOrder().getFirst());
				final ForwardingOrder.AirFreightShipment.PickupOrder.OrderAddress orderAddress = map.getPickupOrder().getFirst().getOrderAddress();
				assertNotNull(orderAddress);
				assertEquals("Example GmbH _shipper", orderAddress.getAddress().getName1());
			}

			@Test
			void shouldMapConsigneeAddressAlsAsConsigneeForCollectOption() {
				order.setCollectFromAirport(true);
				final ForwardingOrder.AirFreightShipment map = airfreightMapper.map(order);
				final List<ForwardingOrder.AirFreightShipment.ShipmentAddress> shipmentAddress = map.getShipmentAddress();
				assertNotNull(shipmentAddress);
				final Optional<ForwardingOrder.AirFreightShipment.ShipmentAddress> consigneeAddress = shipmentAddress.stream()
						.filter(address -> address.getTypeOfAddress().equals(Types.AddressTypes.CONSIGNEE)).findFirst();
				assertTrue(consigneeAddress.isPresent());
				assertEquals("Example GmbH _consignee", consigneeAddress.get().getAddress().getName1());
				final Optional<ForwardingOrder.AirFreightShipment.ShipmentAddress> importer = shipmentAddress.stream()
						.filter(address -> address.getTypeOfAddress().equals(Types.AddressTypes.IMPORTER)).findFirst();
				assertTrue(importer.isPresent());
			}

			@Test
			void shouldUseDeliveryAddressAsUltimateConsignee() {
				final List<ForwardingOrder.AirFreightShipment.ShipmentAddress> shipmentAddresses = airfreightMapper.customMapShipmentAddresses(order);
				assertNotNull(shipmentAddresses);
				assertTrue(shipmentAddresses.stream().anyMatch(shipmentAddress -> shipmentAddress.getTypeOfAddress().equals(Types.AddressTypes.ULTIMATE_CONSIGNEE)));
				final Optional<ForwardingOrder.AirFreightShipment.ShipmentAddress> first = shipmentAddresses.stream()
						.filter(address -> address.getTypeOfAddress().equals(Types.AddressTypes.ULTIMATE_CONSIGNEE)).findFirst();
				if (first.isPresent()) {
					final ForwardingOrder.AirFreightShipment.ShipmentAddress shipmentAddress = first.get();
					assertEquals("Example GmbH _delivery", shipmentAddress.getAddress().getName1());
				} else {
					fail("Should have an ultimate consignee address");
				}
			}

			@Test
			void shouldOmitEmptyVolume() {
				order.getOrderLines().getFirst().setVolume(BigDecimal.ZERO);
				final ForwardingOrder.AirFreightShipment map = airfreightMapper.map(order);
				final List<ForwardingOrder.AirFreightShipment.PickupOrder> pickupOrder = map.getPickupOrder();
				assertNotNull(pickupOrder);
				assertEquals(1, pickupOrder.size());
				final ForwardingOrder.AirFreightShipment.PickupOrder firstPickupOrder = pickupOrder.getFirst();
				assertNull(firstPickupOrder.getOrderPosition().getFirst().getVolume());
			}

			@Test
			void shouldSetTailLiftCollection() {
				final ForwardingOrder.AirFreightShipment map = airfreightMapper.map(order);
				final List<ForwardingOrder.AirFreightShipment.PickupOrder> pickupOrder = map.getPickupOrder();
				assertNotNull(pickupOrder);
				assertEquals(1, pickupOrder.getFirst().getLoadingInstruction().size());
				assertEquals(Types.LoadingInstruction.TLF, pickupOrder.getFirst().getLoadingInstruction().getFirst());
			}

			@Test
			void shouldOmitTailLiftIfNotSet() {
				order.setTailLiftCollection(false);
				final ForwardingOrder.AirFreightShipment map = airfreightMapper.map(order);
				final List<ForwardingOrder.AirFreightShipment.PickupOrder> pickupOrder = map.getPickupOrder();
				assertNotNull(pickupOrder);
				assertEquals(0, pickupOrder.getFirst().getLoadingInstruction().size());
			}

			@Test
			void shouldRemoveUnmappedAddressTypes() {
				final OrderFurtherAddress address = new OrderFurtherAddress();
				address.setOrder(order);
				address.setName("Should not be mapped");
				address.setAddressType("XYZ");
				order.addAddress(address);
				final ForwardingOrder.AirFreightShipment map = airfreightMapper.map(order);
				final List<ForwardingOrder.AirFreightShipment.ShipmentAddress> shipmentAddress = map.getShipmentAddress();
				assertEquals(6, shipmentAddress.size());
			}

			@Test
			void shouldHandleGoodsDescription() {
				final List<AirOrderLineHsCode> hsCodes = order.getOrderLines().getFirst().getHsCodes();
				final String goodsInput = "Goods description with a rather long text to be sure it is not cut off and still fits into the field but is split in xml.";
				hsCodes.get(0).setGoods(goodsInput);
				hsCodes.get(1).setGoods(goodsInput);
				AirOrderLineHsCode airOrderLineHsCode = new AirOrderLineHsCode();
				airOrderLineHsCode.setGoods(goodsInput);
				hsCodes.add(airOrderLineHsCode);
				hsCodes.add(airOrderLineHsCode);
				hsCodes.add(airOrderLineHsCode);
				final ForwardingOrder.AirFreightShipment map = airfreightMapper.map(order);
				final List<ForwardingOrder.AirFreightShipment.PickupOrder> pickupOrder = map.getPickupOrder();
				assertNotNull(pickupOrder);
				assertEquals(1, pickupOrder.getFirst().getOrderPosition().size());
				List<String> goodsDescription = pickupOrder.getFirst().getGoodsDescription();
				assertEquals(2, goodsDescription.size());
				final String validResult = "Goods description with a rather long text to be sure it is not cut off and still fits into the field but is split in xml. | Goods description with a rather long text to be sure it is not cut off and still fits into the field but is split in xml. | goods3 | Goods description with a rather long text to be sure it is not cut off and still fits into the field but is split in xml. | Goods description with a rather long text to be sure it is not cut off and still fits into the field but is split in xm";
				assertEquals(validResult, goodsDescription.get(0));
				assertEquals("l. | Goods description with a rather long text to be sure it is not cut off and still fits into the field but is split in xml.",
						goodsDescription.get(1));
			}
		}

	}
}