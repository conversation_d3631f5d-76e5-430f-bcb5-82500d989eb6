package com.dachser.dfe.book.order;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.common.fcl.FullContainerLoad;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaOrderLine;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.OffsetDateTime;
import java.util.List;

import static com.dachser.dfe.book.TestUtil.generateBasicOrder;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
@Nested
class OrderTest {

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	@Nested
	class RoadOrder {

		final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();

		final CollectionOrder collectionOrder = testUtil.generateCollectionOrder();

		@Nested
		class RoadForwardingOrder {
			@Test
			void shouldGetCorrectOrderType() {
				assertEquals(OrderType.ROADFORWARDINGORDER, forwardingOrder.getOrderType());
			}

			@Test
			void shouldCheckForExpiryDateWithOrderStatusExpired() {
				forwardingOrder.setStatus(OrderStatus.EXPIRED);
				forwardingOrder.setOrderExpiryDate(null);

				assertTrue(forwardingOrder.isOrderExpired());
			}

			@Test
			void shouldCheckForExpiryDateWithOrderStatusComplete() {
				forwardingOrder.setStatus(OrderStatus.COMPLETE);
				forwardingOrder.setOrderExpiryDate(OffsetDateTime.now().minusSeconds(5));

				assertTrue(forwardingOrder.isOrderExpired());
			}

			@Test
			void shouldCalculateTotalAmountPackagesWithManualNumberSSCC() {
				forwardingOrder.setManualNumberSscc(5);

				assertEquals(5, forwardingOrder.getTotalLabelCount());
			}

			@Test
			void shouldCalculateTotalAmountPackagesWithoutManualNumberSSCC() {
				forwardingOrder.setManualNumberSscc(null);

				assertEquals(4, forwardingOrder.getTotalLabelCount());
			}
		}

		@Nested
		class RoadCollectionOrder {
			@Test
			void shouldGetCorrectOrderType() {
				assertEquals(OrderType.ROADCOLLECTIONORDER, collectionOrder.getOrderType());
			}

			@Test
			void shouldCheckForExpiryDateWithOrderStatusExpired() {
				collectionOrder.setStatus(OrderStatus.EXPIRED);
				collectionOrder.setOrderExpiryDate(null);

				assertTrue(collectionOrder.isOrderExpired());
			}

			@Test
			void shouldCheckForExpiryDateWithOrderStatusComplete() {
				collectionOrder.setStatus(OrderStatus.COMPLETE);
				collectionOrder.setOrderExpiryDate(OffsetDateTime.now().minusSeconds(5));

				assertTrue(collectionOrder.isOrderExpired());
			}

			@Test
			void shouldCalculateTotalOrderCounts() {
				assertEquals(100.0, collectionOrder.getTotalOrderWeight().intValue());
				assertEquals(4.0, collectionOrder.getTotalOrderVolume());
				assertEquals(4, collectionOrder.getTotalAmountPackages());
			}

			@Test
			void shouldHandleMissingCalculationValues() {
				collectionOrder.setOrderLines(null);
				collectionOrder.setPackingPositions(null);
				assertEquals(0.0, collectionOrder.getTotalOrderWeight().intValue());
				assertEquals(0.0, collectionOrder.getTotalOrderVolume());
				assertEquals(0, collectionOrder.getTotalAmountPackages());

				collectionOrder.setPackingPositions(null);
				assertEquals(0, collectionOrder.getTotalAmountPackages());
			}
		}

	}

	@Nested
	class AslOrder {

		final AirExportOrder airExportOrder = testUtil.generateAirExportOrder();

		final SeaExportOrder seaExportOrder = testUtil.generateSeaExportOrder();

		@Test
		void shouldGetCorrectOrderType() {
			assertEquals(OrderType.AIREXPORTORDER, airExportOrder.getOrderType());
		}

		@Test
		void shouldCheckForExpiryDateWithOrderStatusExpired() {
			airExportOrder.setStatus(OrderStatus.EXPIRED);
			airExportOrder.setOrderExpiryDate(null);
			assertTrue(airExportOrder.isOrderExpired());
		}

		@Test
		void shouldCheckForExpiryDateWithOrderStatusComplete() {
			airExportOrder.setStatus(OrderStatus.COMPLETE);
			airExportOrder.setOrderExpiryDate(OffsetDateTime.now().minusSeconds(5));

			assertTrue(airExportOrder.isOrderExpired());
		}

		@Test
		void shouldCalculateTotalOrderCountsAir() {
			assertEquals(2, airExportOrder.getTotalAmountPackages());
			assertEquals(50.0, airExportOrder.getTotalOrderWeight());
			assertEquals(2.0, airExportOrder.getTotalOrderVolume());
		}

		@Test
		void shouldHandleMissingCalculationValuesAir() {
			airExportOrder.setOrderLines(null);
			assertEquals(0.0, airExportOrder.getTotalOrderWeight());
			assertEquals(0.0, airExportOrder.getTotalOrderVolume());
			assertEquals(0, airExportOrder.getTotalAmountPackages());
		}

		@Test
		void shouldCalculateTotalOrderCountsSea() {
			assertEquals(2, seaExportOrder.getTotalAmountPackages());
			assertEquals(50.0, seaExportOrder.getTotalOrderWeight());
			assertEquals(2.0, seaExportOrder.getTotalOrderVolume());
			assertEquals(0, seaExportOrder.getTotalContainerQuantity());
			assertEquals(0, seaExportOrder.getTotalVerifiedGrossMass());
		}

		@Test
		void shouldHandleMissingCalculationValuesSea() {
			seaExportOrder.setOrderLines(null);
			assertEquals(0.0, seaExportOrder.getTotalOrderWeight());
			assertEquals(0.0, seaExportOrder.getTotalOrderVolume());
			assertEquals(0, seaExportOrder.getTotalAmountPackages());
		}

		@Test
		void shouldCalculateTotalOrderCountsSeaWithFullContainerLoad() {
			seaExportOrder.setFullContainerLoad(true);

			SeaOrderLine seaOrderLine = seaExportOrder.getOrderLines().get(0);
			seaOrderLine.setQuantity(3);
			seaOrderLine.setFullContainerLoadId(1L);

			seaExportOrder.setOrderLines(null);

			FullContainerLoad fullContainerLoad = new FullContainerLoad();
			fullContainerLoad.setOrder(seaExportOrder);
			fullContainerLoad.setVerifiedGrossMass(500);
			fullContainerLoad.setOrderLines(List.of(seaOrderLine));

			// assert no FCL
			assertEquals(0, seaExportOrder.getTotalAmountPackages());
			assertEquals(0.0, seaExportOrder.getTotalOrderWeight());
			assertEquals(0.0, seaExportOrder.getTotalOrderVolume());

			// set FCL
			seaExportOrder.setFullContainerLoads(List.of(fullContainerLoad));

			// assert FCL counts
			assertEquals(3, seaExportOrder.getTotalAmountPackages());
			assertEquals(50.0, seaExportOrder.getTotalOrderWeight());
			assertEquals(1, seaExportOrder.getTotalContainerQuantity());
			assertEquals(500, seaExportOrder.getTotalVerifiedGrossMass());
		}

	}

	@ParameterizedTest
	@EnumSource(OrderType.class)
	public void testSetCustomerNumber(OrderType orderType) {
		String customerNumber = "123456";
		final Order order = generateBasicOrder(orderType);
		String expectedSegmentSuffix = switch (orderType) {
			case AIREXPORTORDER, AIRIMPORTORDER, SEAEXPORTORDER, SEAIMPORTORDER -> "ASL";
			default -> "ROAD";
		};

		order.setCustomerNumber(customerNumber);

		assertEquals(customerNumber, order.getCustomerNumber());
		assertEquals(customerNumber + expectedSegmentSuffix, order.getCustomerNumberWithSegment());
	}

	@Test
	public void testSetCustomerNumberWithNullOrderType() {
		String customerNumber = "123456";
		final Order order = generateBasicOrder(null);

		order.setCustomerNumber(customerNumber);

		assertEquals(customerNumber, order.getCustomerNumber());
		assertNull(order.getCustomerNumberWithSegment());
	}

	@Test
	public void testSetCustomerNumberWithNullCustomerNumber() {
		final Order order = generateBasicOrder(OrderType.ROADFORWARDINGORDER);
		order.setCustomerNumber(null);

		assertNull(order.getCustomerNumber());
		assertNull(order.getCustomerNumberWithSegment());
	}
}
