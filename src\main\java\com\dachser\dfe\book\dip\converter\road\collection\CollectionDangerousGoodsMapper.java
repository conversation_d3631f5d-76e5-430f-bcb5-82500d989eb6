package com.dachser.dfe.book.dip.converter.road.collection;

import com.dachser.dfe.book.dip.converter.road.DangerousGoodsMapper;
import com.dachser.dfe.book.model.jaxb.order.road.collection.ADRWeight;
import com.dachser.dfe.book.model.jaxb.order.road.collection.DangerousGoods;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface CollectionDangerousGoodsMapper extends DangerousGoodsMapper<DangerousGoods, ADRWeight> {
}
