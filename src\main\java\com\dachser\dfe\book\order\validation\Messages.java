package com.dachser.dfe.book.order.validation;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class Messages {

	public static final String INVALID_INPUT = "label.text.invalid_input";

	public static final String INVALID_EKAER = "label.text.validation_invalid_ekaer";

	public static final String INVALID_UIT = "label.text.validation_invalid_uit";

	public static final String NOT_ALLOWED_DELIVERY_OPTION = "label.text.validation_not_allowed_delivery_option";

	public static final String NOT_ALLOWED_UIT = "label.text.validation_not_allowed_uit";

	public static final String MOBILE_EMAIL_INVALID = "message.text.6357";

	public static final String MOBILE_PHONE_INVALID = "message.text.6358";

	public static final String INVALID_COLLECTION_TIME_SLOT_IN_PAST = "label.text.invalid_collection_time_past";

	public static final String INVALID_COLLECTION_TIME_SLOT_PERIOD_OF_TIME = "label.text.invalid_collection_time_period";

	public static final String INVALID_COLLECTION_TIME_SLOT_PERIOD_OF_TIME_NEGATIVE = "message.text.6687";

	public static final String INVALID_COLLECTION_TIME_SLOT_TOO_FAR_IN_FUTURE = "label.text.invalid_collection_far_in_future";

	public static final String INVALID_COLLECTION_TIME_SLOT_TO_NARROW_ON_TODAY = "label.text.invalid_collection_time_close_today";

	public static final String INVALID_COLLECTION_TIME_SLOT_MISSING = "label.text.invalid_collection_time_missing";

	public static final String FORBIDDEN_SHIPPER_AND_CONSIGNEE_COMBINATION = "label.text.embargo_failure";

	public static final String FORBIDDEN_CHANGE_QUOTE_ORDER = "label.text.forbidden_change_quote_order";

	public static final String FORBIDDEN_CHANGE_STATUS_COMPLETE = "label.text.forbidden_change_status_complete";

	public static final String GOODS_DESCRIPTION_TOO_LONG = "message.text.6661";

	public static final String INVALID_INCO_TERM = "message.text.6753";

	public static final String INVALID_AIR_PRODUCT_SELECTION = "message.text.6754";

	public static final String MISSING_AIR_PRODUCT_SELECTION = "message.text.6755";

	public static final String MISSING_DELIVERY_OPTION = "label.text.validation_missing_delivery_option";

}
