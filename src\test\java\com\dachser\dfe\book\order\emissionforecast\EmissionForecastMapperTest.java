package com.dachser.dfe.book.order.emissionforecast;

import org.junit.jupiter.api.Nested;

import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.emissionforecast.model.EFAirLocationDto;
import com.dachser.dfe.emissionforecast.model.EFCarriageTypeDto;
import com.dachser.dfe.emissionforecast.model.EFForecastRequestDto;
import com.dachser.dfe.emissionforecast.model.EFRoadLocationDto;
import com.dachser.dfe.emissionforecast.model.EFSeaLocationDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class EmissionForecastMapperTest {

	private EmissionForecastMapper mapper;

	@BeforeEach
	void setUp() {
		mapper = Mappers.getMapper(EmissionForecastMapper.class);
	}

	@Nested
	class LocationMappingTests {
	    @Test
	    void testMapToAirLocation() {
	        String code = "XYZ";
	        EFAirLocationDto dto = mapper.mapToAirLocation(code);
	        assertEquals(code, dto.getIataCode());
	    }

	    @Test
	    void testMapToSeaLocation() {
	        String port = "PORT";
	        EFSeaLocationDto dto = mapper.mapToSeaLocation(port);
	        assertEquals(port, dto.getLocode());
	    }

	    @Test
	    void testMapToRoadLocation() {
	        OrderAddress address = mock(OrderAddress.class);
	        when(address.getPostcode()).thenReturn("12345");
	        EFRoadLocationDto dto = mapper.mapToRoadLocation(address);
	        assertEquals("12345", dto.getPostCode());
	    }
	}

	@Nested
	class DoubleMappingTests {
	    @Test
	    void testMapToDouble() {
	        assertNull(mapper.mapToDouble(null));
	        assertEquals(2.5, mapper.mapToDouble(2.5), 0.0);
	        assertEquals(3.0, mapper.mapToDouble(3), 0.0);
	    }
	}

	@Nested
	class TransportMappingTests {
	    @Test
	    void testMapRoad() {
	        RoadOrder order = mock(RoadOrder.class);
	        OrderAddress ship = mock(OrderAddress.class);
	        OrderAddress cons = mock(OrderAddress.class);
	        when(order.getTotalOrderWeight()).thenReturn(10);
	        when(order.getShipperAddress()).thenReturn(ship);
	        when(order.getConsigneeAddress()).thenReturn(cons);
	        when(ship.getPostcode()).thenReturn("SHP");
	        when(cons.getPostcode()).thenReturn("CON");

	        EFForecastRequestDto dto = mapper.mapRoad(order, "EL", Segment.ROAD);
	        assertEquals(10.0, dto.getWeight(), 0.001);
	        assertEquals("SHP", dto.getDeparture().getRoadLocation().getPostCode());
	        assertEquals("CON", dto.getDestination().getRoadLocation().getPostCode());
	    }

	    @Test
	    void testMapAir() {
	        AirOrder order = mock(AirOrder.class);
	        when(order.getTotalOrderWeight()).thenReturn(5.0);
	        when(order.getFromIATA()).thenReturn("AAA");
	        when(order.getToIATA()).thenReturn("BBB");

	        EFForecastRequestDto dto = mapper.mapAir(order, "EL", Segment.AIR);
	        assertEquals(5.0, dto.getWeight(), 0.001);
	        assertEquals("AAA", dto.getDeparture().getAirLocation().getIataCode());
	        assertEquals("BBB", dto.getDestination().getAirLocation().getIataCode());
	    }

	    @Test
	    void testMapSea() {
	        SeaOrder order = mock(SeaOrder.class);
	        when(order.getTotalOrderWeight()).thenReturn(7.5);
	        when(order.getFromPort()).thenReturn("SEA");
	        when(order.getToPort()).thenReturn("CSE");

	        EFForecastRequestDto dto = mapper.mapSea(order, "EL", Segment.SEA);
	        assertEquals(7.5, dto.getWeight(), 0.001);
	        assertEquals("SEA", dto.getDeparture().getSeaLocation().getLocode());
	        assertEquals("CSE", dto.getDestination().getSeaLocation().getLocode());
	    }
	}

	@Nested
	class CarriageTypeMappingTests {
	    @Test
	    void testMapToEFCarriageTypeDto() {
	        EFCarriageTypeDto dto = mapper.mapToEFCarriageTypeDto(Segment.AIR);
	        assertNotNull(dto);
	    }
	}
}
