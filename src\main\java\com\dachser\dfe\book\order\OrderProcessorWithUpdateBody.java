package com.dachser.dfe.book.order;

import com.dachser.dfe.book.advice.AdviceService;
import com.dachser.dfe.book.dangerousgoods.DangerousGoodsTransferListService;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@Slf4j
abstract class OrderProcessorWithUpdateBody extends OrderProcessor<BasicOrderDto> {

	final OrderProcessCheckSwitchAndSave orderProcessCheckSwitchAndSaveTransaction;

	OrderProcessorWithUpdateBody(OrderRepositoryFacade orderRepositoryAccess, OrderMapper orderMapper, OrderLabelGenerator orderLabelGenerator, List<OrderSubmitter<?>> orderSubmitters,
			AdviceService adviceService, OrderProcessCheckSwitchAndSave orderProcessCheckSwitchAndSave, OrderDefaults orderDefaults, OrderLabelPrinter labelPrinter,
			DangerousGoodsTransferListService dangerousGoodsTransferListService, List<OrderPostProcessor<?>> postProcessors) {
		super(orderSubmitters, adviceService, orderRepositoryAccess, orderMapper, orderLabelGenerator, labelPrinter, dangerousGoodsTransferListService, orderDefaults,
				postProcessors);
		this.orderProcessCheckSwitchAndSaveTransaction = orderProcessCheckSwitchAndSave;
	}

	@Override
	void fillStateWithOrder(BasicOrderDto orderDto) {
		prepareOrderAndCheckPreconditions(orderDto);
		checkAndSave();
	}

	private void checkAndSave() {
		Order currentOrder = getState().getCurrentOrder();
		BasicOrderDto basicOrderDto = getState().getOrderDto();
		OrderProcessCheckSwitchAndSave.CheckSwitchAndSaveResult checkSwitchAndSaveResult = orderProcessCheckSwitchAndSaveTransaction.checkSwitchAndSave(currentOrder, basicOrderDto);
		// We need to sync result back, as an order type switch leading to a new order in process can take place during check and save
		getState().processResult.validationResult(checkSwitchAndSaveResult.validationResult());
		getState().setCurrentOrder(checkSwitchAndSaveResult.order());
		executePostSave();
	}

	private void executePostSave() {
		Optional<OrderPostProcessor<Order>> orderPostProcessor = getOrderPostProcessor(getState().getCurrentOrder());
		orderPostProcessor.ifPresent(postProcessor -> postProcessor.postSaveActions(getState().getCurrentOrder(), getState().getAction(), getState().getProcessResult()));
		getState().updateOrderInProcessResult();
	}

	abstract void prepareOrderAndCheckPreconditions(BasicOrderDto orderDto);

}
