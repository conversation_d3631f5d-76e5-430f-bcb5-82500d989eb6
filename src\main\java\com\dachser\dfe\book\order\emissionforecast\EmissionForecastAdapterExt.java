package com.dachser.dfe.book.order.emissionforecast;

import com.dachser.dfe.emissionforecast.api.ForecastApi;
import com.dachser.dfe.emissionforecast.model.EFForecastRequestDto;
import com.dachser.dfe.emissionforecast.model.EFForecastResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(value = "dfe.book.mock", havingValue = "false", matchIfMissing = true)
public class EmissionForecastAdapterExt implements EmissionForecastAdapter {

	private static final String DFE_BOOK_SERVICE = "DFE BOOK SERVICE";

	private final ForecastApi emissionForecastApi;

	@Override
	public Optional<EFForecastResponseDto> getEmissionForecast(EFForecastRequestDto requestDto) {
		try {
			return Optional.of(emissionForecastApi.calculateForecast(DFE_BOOK_SERVICE, requestDto));
		} catch (final RestClientException restClientException) {
			log.warn("EmissionForecast service not available with message: {}", restClientException.getMessage());
			return Optional.empty();
		}
	}
}
