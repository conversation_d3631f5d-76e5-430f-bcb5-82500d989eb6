package com.dachser.dfe.book.dip.converter.air;

import com.dachser.dfe.book.dip.converter.shared.AirSeaAddressMappingConfig;
import com.dachser.dfe.book.dip.converter.shared.AirSeaGoodsDescriptionMapper;
import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderContactCommunicationsMapper;
import com.dachser.dfe.book.dip.converter.shared.AirSeaPickupOrderMapper;
import com.dachser.dfe.book.dip.converter.shared.DateMapper;
import com.dachser.dfe.book.dip.converter.shared.StringNotEmptyConditionMapper;
import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.model.jaxb.order.asl.Address;
import com.dachser.dfe.book.model.jaxb.order.asl.Contact;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper(config = AirSeaAddressMappingConfig.class, componentModel = "spring", uses = { DateMapper.class, AirSeaOrderContactCommunicationsMapper.class, AirOrderLineMapper.class,
		AirOrderReferenceMapper.class, StringNotEmptyConditionMapper.class, AirSeaGoodsDescriptionMapper.class })
public interface AirPickupOrderMapper extends AirSeaPickupOrderMapper<AirOrder> {

	default List<ForwardingOrder.AirFreightShipment.PickupOrder> mapPickupOrders(AirOrder order) {
		// We need to map an delivery order in that case
		if (order.isDeliverToAirport()) {
			return List.of();
		}
		final ForwardingOrder.AirFreightShipment.PickupOrder pickupOrder = mapPickupOrder(order);
		return List.of(pickupOrder);
	}

	@Mapping(target = "loadingInstruction", source = ".", qualifiedByName = "mapLoadingInstruction")
	@Mapping(target = "orderPosition", source = "orderLines")
	@Mapping(target = "pickUpDate", source = ".")
	@Mapping(target = "goodsDescription", source = ".", qualifiedByName = "mapGoodsDescription")
	@Mapping(target = "orderAddress", source = ".", qualifiedByName = "mapPickupAddress")
	@Mapping(target = "orderReference", source = "orderReferences")
	@Mapping(target = "customsGoods", constant = Types.CustomsGoods.NON_CUSTOMS_GOODS)
	@Mapping(target = "additionalOrderInformation", source = "orderLines")
	ForwardingOrder.AirFreightShipment.PickupOrder mapPickupOrder(AirOrder order);

	@Named("mapLoadingInstruction")
	default List<String> mapLoadingInstruction(AirOrder order) {
		if (order.isTailLiftCollection()) {
			return List.of(Types.LoadingInstruction.TLF);
		}
		return List.of();
	}

	@Named("mapPickupAddress")
	@Mapping(target = "address", source = ".", qualifiedByName = "mapPickupOrShipperAddress")
	@Mapping(target = "contact", source = "orderContact")
	@Mapping(target = "typeOfAddress", constant = Types.AddressTypes.PICKUP)
	ForwardingOrder.AirFreightShipment.PickupOrder.OrderAddress mapPickupAddress(AirOrder airOrder);

	@Named("mapPickupOrShipperAddress")
	default Address mapPickupOrShipperAddress(AirOrder airOrder) {
		if (airOrder.getPickupAddress() != null) {
			return mapOrderAddress(airOrder.getPickupAddress());
		} else {
			return mapOrderAddress(airOrder.getShipperAddress());
		}
	}

	@InheritConfiguration(name = "mapAddress")
	Address mapOrderAddress(OrderAddress orderAddress);

	@InheritConfiguration(name = "mapContact")
	Contact mapOrderContact(OrderContact orderContact);

}
