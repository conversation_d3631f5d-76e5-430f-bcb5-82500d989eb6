package com.dachser.dfe.book.order.validation.road;

import com.dachser.dfe.book.model.TestMockData;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import com.dachser.dfe.book.order.validation.Messages;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.HibernateValidator;
import org.hibernate.validator.HibernateValidatorConfiguration;
import org.hibernate.validator.cfg.ConstraintMapping;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Set;
import java.util.stream.Stream;

@Slf4j
class OrderReferenceValidatorTest {

	private static HibernateValidatorConfiguration validationConfiguration;

	@BeforeAll
	public static void configure() {
		validationConfiguration = Validation.byProvider(HibernateValidator.class).configure();
		validationConfiguration.addMapping(constraintMapping());
	}

	private static ConstraintMapping constraintMapping() {
		// @formatter:off
		ConstraintMapping constraintMapping = validationConfiguration.createConstraintMapping();
		constraintMapping
		.type(RoadOrderReference.class)
		.constraintDefinition(OrderReferenceValid.class)
		.validatedBy(OrderReferenceValidatorMock.class)
		.includeExistingValidators(false);
		return constraintMapping;
		// @formatter:on
	}

	@ParameterizedTest(name = "#{index} - {0} shall be {2}")
	@MethodSource("scenarios")
	void evaluates(String scenario, RoadOrderReference roadOrderReference, boolean valid, String i18nViolationKey) {
		// given a road order reference
		Assertions.assertNotNull(roadOrderReference);
		// when validating it
		Set<ConstraintViolation<RoadOrderReference>> violations;
		try (ValidatorFactory validatorFactory = validationConfiguration.buildValidatorFactory()) {
			Validator validator = validatorFactory.getValidator();
			violations = validator.validate(roadOrderReference, CompleteOrderValidationRoadForwarding.class);
		}
		// then validation evaluates as expected
		log.debug("violations are {}", violations);
		Assertions.assertEquals(valid, violations.isEmpty());
		// and expected failure message is present
		if (i18nViolationKey != null) {
			Assertions.assertTrue(violations.stream().map(ConstraintViolation::getMessageTemplate).anyMatch(i18nViolationKey::equals));
		}
	}

	private static Stream<Arguments> scenarios() {
		// @formatter:off
		return Stream.of(
			Arguments.of("valid purchase order number", roadOrderReference(ReferenceType.PURCHASE_ORDER_NUMBER, null, "my purchase"), true, null),
			Arguments.of("valid EKAER number", roadOrderReference(ReferenceType.EKAER_NUMBER, null, TestMockData.EKAER_REFERENCE_VALUE), true, null),
			Arguments.of("too long EKAER number", roadOrderReference(ReferenceType.EKAER_NUMBER, null, TestMockData.EKAER_REFERENCE_VALUE.concat("TAIL")), false, Messages.INVALID_EKAER),
			Arguments.of("valid booking reference", roadOrderReference(ReferenceType.BOOKING_REFERENCE, null, "Amazon: my booking 1"), true, null),
			Arguments.of("invalid booking reference", roadOrderReference(ReferenceType.BOOKING_REFERENCE, null, "my booking 1 @ Amazon"), false, Messages.INVALID_INPUT),
			Arguments.of("valid UIT code", roadOrderReference(ReferenceType.IDENTIFICATION_CODE_TRANSPORT, RoadOrderReferenceSubtype.UIT, TestMockData.UIT_REFERENCE_VALUE), true, null),
			Arguments.of("invalid UIT code", roadOrderReference(ReferenceType.IDENTIFICATION_CODE_TRANSPORT, RoadOrderReferenceSubtype.UIT, TestMockData.UIT_REFERENCE_VALUE.replaceAll("[0-9]", "#")), false, Messages.INVALID_UIT)
		);
		// @formatter:on
	}

	private static RoadOrderReference roadOrderReference(ReferenceType type, RoadOrderReferenceSubtype subtype, String value) {
		// @formatter:off
		return RoadOrderReference.builder()
		.referenceType(type)
		.referenceSubtype(subtype)
		.reference(value)
		.build();
		// @formatter:on
	}

}