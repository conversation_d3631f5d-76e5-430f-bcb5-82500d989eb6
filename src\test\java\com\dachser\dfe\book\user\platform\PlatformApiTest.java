package com.dachser.dfe.book.user.platform;

import com.dachser.dfe.platform.api.PlatformV4Api;
import com.dachser.dfe.platform.api.PlatformV5Api;
import com.dachser.dfe.platform.model.PlatformAslCustomerV5;
import com.dachser.dfe.platform.model.PlatformCompanyV5;
import com.dachser.dfe.platform.model.PlatformRoadAddressTypesV5;
import com.dachser.dfe.platform.model.PlatformRoadCustomerV5;
import com.dachser.dfe.platform.model.PlatformRoadOrderOptionsV5;
import com.dachser.dfe.platform.model.PlatformUserV5;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PlatformApiTest {

	@Mock
	PlatformV4Api platformV4Api;

	@Mock
	PlatformV5Api platformV5Api;

	@InjectMocks
	PlatformApi platformApi;

	@BeforeEach
	void setUp() {
		when(platformV5Api.getUserProfileV5()).thenReturn(buildPlatformUser());
	}

	@Test
	void shouldReturnPlatformUser() {
		PlatformUserV5 platformUser = platformApi.getPlatformUser();
		assertEquals("John", platformUser.getFirstName());
		assertEquals("Doe", platformUser.getLastName());
		assertEquals("John Doe", platformUser.getName());
	}

	// setup methods

	public static PlatformUserV5 buildPlatformUser() {
		List<PlatformAslCustomerV5> aslCustomer = List.of(new PlatformAslCustomerV5().id(UUID.randomUUID()).name("Asl Customer"));

		List<PlatformRoadCustomerV5> roadCustomer = List.of(
				new PlatformRoadCustomerV5().businessLine(PlatformRoadCustomerV5.BusinessLineEnum.FL).id(UUID.randomUUID()).name("Road Customer").bookBranchNumber(6)
						.bookCollectingElBranchNumber(6).bookCollectingFlBranchNumber(6).bookForwardingFlBranchNumber(6).bookForwardingElBranchNumber(6)
						.addressTypes(new PlatformRoadAddressTypesV5().coverAddress(false).deconsolidatorAddress(true))
						.orderOptions(new PlatformRoadOrderOptionsV5().manualNumberSscc(true)).number("9876543"));

		PlatformCompanyV5 company = new PlatformCompanyV5().id(UUID.randomUUID()).name("Example Company").customersASL(aslCustomer).customersROAD(roadCustomer);

		return new PlatformUserV5().id(UUID.randomUUID()).firstName("John").lastName("Doe").name("John Doe").email("<EMAIL>").company(company);
	}
}
