package com.dachser.dfe.book.quote;

import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.hscode.HsCodeService;
import com.dachser.dfe.book.model.AirExportQuoteInformationDto;
import com.dachser.dfe.book.model.AirImportQuoteInformationDto;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.BasicQuoteInformationDto;
import com.dachser.dfe.book.model.CollectionOptionDto;
import com.dachser.dfe.book.model.RoadCollectionQuoteInformationDto;
import com.dachser.dfe.book.model.RoadForwardingQuoteInformationDto;
import com.dachser.dfe.book.model.SourceOfOrder;
import com.dachser.dfe.book.model.options.CollectionOptions;
import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.mapper.*;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaImportOrder;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.book.product.air.AirProductService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.io.IOException;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = { OrderTypeMapperImpl.class, QuoteMapperImpl.class, OrderLineMapperImpl.class, OrderLineHsCodeMapperImpl.class, OrderReferenceMapperImpl.class,
		OrderAddressMapperImpl.class, DeliveryOptionMapperImpl.class, QuotePackingPositionMapperImpl.class, DangerousGoodsMapperImpl.class})
public class QuoteMapperTest implements ResourceLoadingTest {

	@Autowired
	private QuoteMapper quoteMapper;

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	@Spy
	private OrderLineMapper orderLineMapper;

	@Spy
	private OrderLineHsCodeMapper orderLineHsCodeMapper;

	@Spy
	private OrderAddressMapper orderAddressMapper;

	@Spy
	private OrderReferenceMapper orderReferenceMapper;

	@Spy
	private OrderTypeMapper orderTypeMapper;

	@MockBean
	private HsCodeService hsCodeService;

	@MockBean
	private AirProductService airProductService;

	@Nested
	class RoadQuoteInfo {

		final String forwardingExamplePayload = "orders/quote/road-quote-information-forwarding.json";

		final String collectionExamplePayload = "orders/quote/road-quote-information-collection.json";

		@Nested
		class ToEntity {

			@ParameterizedTest
			@ValueSource(strings = { collectionExamplePayload, forwardingExamplePayload })
			void mapFullRoadQuoteInfo(String jsonPath) throws IOException {
				final BasicQuoteInformationDto quoteInformationDto = createJsonQuoteInformation(jsonPath);
				final RoadQuoteInformation mapped = (RoadQuoteInformation) quoteMapper.mapQuoteInfo(quoteInformationDto);

				if (Objects.equals(jsonPath, collectionExamplePayload)) {
					assertEquals(OrderType.ROADCOLLECTIONORDER, mapped.getOrderType());
				} else {
					assertEquals(OrderType.ROADFORWARDINGORDER, mapped.getOrderType());
				}

				assertRequiredFieldsNotNull(mapped);
				assertValidAddressInformation(mapped);
				assertNotNull(mapped.getCollectionFrom());
				assertNotNull(mapped.getCollectionTo());
				assertNotNull(mapped.getGoodsValue());
				assertNotNull(mapped.getGoodsCurrency());
				assertNotNull(mapped.getProduct());
			}

			@Test
			void mapFullRoadCollectionQuoteInfo() throws IOException {
				final RoadCollectionQuoteInformationDto quoteInformationDto = (RoadCollectionQuoteInformationDto) createJsonQuoteInformation(collectionExamplePayload);
				quoteInformationDto.setCollectionOption(CollectionOptionDto.BOOKING);
				final RoadQuoteInformation mapped = (RoadQuoteInformation) quoteMapper.mapQuoteInfo(quoteInformationDto);

				assertEquals(OrderType.ROADCOLLECTIONORDER, mapped.getOrderType());
				assertEquals(CollectionOptions.BOOKING, mapped.getCollectionOption());

				assertRequiredFieldsNotNull(mapped);
				assertValidAddressInformation(mapped);
				assertNotNull(mapped.getCollectionFrom());
				assertNotNull(mapped.getCollectionTo());
				assertNotNull(mapped.getGoodsValue());
				assertNotNull(mapped.getGoodsCurrency());
				assertNotNull(mapped.getProduct());
			}

			@Test
			void mapCustomRoadQuoteInfo() throws IOException {
				final OffsetDateTime expiryDate = OffsetDateTime.now().plusDays(1);

				final RoadForwardingQuoteInformationDto quoteInformationDto = (RoadForwardingQuoteInformationDto) createJsonQuoteInformation(forwardingExamplePayload);
				quoteInformationDto.setQuoteReference("ePricing");

				quoteInformationDto.setQuoteExpiryDate(expiryDate);
				final RoadQuoteInformation mapped = (RoadQuoteInformation) quoteMapper.mapQuoteInfo(quoteInformationDto);

				assertRequiredFieldsNotNull(mapped);
				assertValidAddressInformation(mapped);
				assertEquals("ePricing", mapped.getQuoteReference());
				assertEquals(expiryDate, mapped.getQuoteExpiryDate());
				assertEquals(DeliveryOptions.AS, mapped.getDeliveryOption());
				assertEquals(2.0, mapped.getPalletLocations().doubleValue());
				assertNotNull(mapped.getCollectionFrom());
				assertNotNull(mapped.getCollectionTo());
				assertNotNull(mapped.getGoodsValue());
				assertNotNull(mapped.getGoodsCurrency());
				assertNotNull(mapped.getQuoteReference());
				assertNotNull(mapped.getProduct());
				List<QuoteOrderLine> orderLineItems = mapped.getOrderLineItems();
				assertNotNull(orderLineItems);
				assertFalse(orderLineItems.isEmpty());
				assertNotNull(orderLineItems.get(0).getGoodsGroupCode());
			}
		}

		@Nested
		class ToOrder {

			@ParameterizedTest
			@ValueSource(strings = { forwardingExamplePayload, collectionExamplePayload })
			void mapToOrder(String jsonPath) throws IOException {
				BasicQuoteInformationDto quoteInformationDto = createJsonQuoteInformation(jsonPath);
				Order order = quoteMapper.mapToOrder(quoteInformationDto);

				if (Objects.equals(jsonPath, forwardingExamplePayload)) {
					assertEquals(OrderType.ROADFORWARDINGORDER, order.getOrderType());
				} else {
					assertEquals(OrderType.ROADCOLLECTIONORDER, order.getOrderType());
				}
				assertRequiredOrderFieldsNotNull(order);
			}

			@Test
			void mapDeliveryOptionsForNullInput() throws IOException {
				RoadForwardingQuoteInformationDto quoteInformationDto = (RoadForwardingQuoteInformationDto) createJsonQuoteInformation(forwardingExamplePayload);
				quoteInformationDto.setDeliveryOption(null);

				Order mapped = quoteMapper.mapToOrder(quoteInformationDto);
				assertInstanceOf(RoadOrder.class, mapped);
				assertEquals(DeliveryOptions.NO, ((RoadOrder) mapped).getQuoteInformation().getDeliveryOption());
			}
		}

	}

	@Nested
	class AirQuoteInfo {

		final String airExportExamplePayload = "orders/quote/air-quote-information-export.json";

		final String airImportExamplePayload = "orders/quote/air-quote-information-import.json";

		@Nested
		class ToEntity {

			@ParameterizedTest
			@ValueSource(strings = { airExportExamplePayload, airImportExamplePayload })
			void mapFullAirQuoteInfo(String jsonPath) throws IOException {
				final BasicQuoteInformationDto quoteInformationDto = createJsonQuoteInformation(jsonPath);
				final AirQuoteInformation mapped = (AirQuoteInformation) quoteMapper.mapQuoteInfo(quoteInformationDto);

				if (Objects.equals(jsonPath, airExportExamplePayload)) {
					assertEquals(OrderType.AIREXPORTORDER, mapped.getOrderType());
				} else {
					assertEquals(OrderType.AIRIMPORTORDER, mapped.getOrderType());
				}

				assertRequiredFieldsNotNull(mapped);
				assertValidAddressInformation(mapped);
				assertNotNull(mapped.getCollectionFrom());
				assertNotNull(mapped.getCollectionTo());
				assertNotNull(mapped.getGoodsValue());
				assertNotNull(mapped.getGoodsCurrency());
				assertNotNull(mapped.getQuoteReference());
				assertNotNull(mapped.getProduct());
			}
		}

		@Nested
		class ToOrder {

			@ParameterizedTest
			@ValueSource(strings = { airExportExamplePayload, airImportExamplePayload })
			void mapToOrder(String jsonPath) throws IOException {
				AirProductDto airProduct = new AirProductDto();
				airProduct.setCode("1");
				airProduct.setActive(true);
				airProduct.setDescription("Air Product");
				when(airProductService.getAirProductByProductCode(anyString())).thenReturn(Optional.of(airProduct));

				BasicQuoteInformationDto quoteInformationDto = createJsonQuoteInformation(jsonPath);

				if (Objects.equals(jsonPath, airExportExamplePayload)) {
					((AirExportQuoteInformationDto) quoteInformationDto).setQuoteExpiryDate(OffsetDateTime.now().plusDays(1));
				} else {
					((AirImportQuoteInformationDto) quoteInformationDto).setQuoteExpiryDate(OffsetDateTime.now().plusDays(1));
				}

				AirOrder order = (AirOrder) quoteMapper.mapToOrder(quoteInformationDto);

				if (Objects.equals(jsonPath, airExportExamplePayload)) {
					assertEquals(OrderType.AIREXPORTORDER, order.getOrderType());
				} else {
					assertEquals(OrderType.AIRIMPORTORDER, order.getOrderType());
				}
				assertRequiredOrderFieldsNotNull(order);
				assertNotNull(order.getOrderExpiryDate());
				assertFalse(order.getProductName().isBlank());
			}

			@Test
			void mapToOrderWithInvalidProductCode() throws IOException {
				BasicQuoteInformationDto quoteInformationDto = createJsonQuoteInformation(airExportExamplePayload);
				when(airProductService.getAirProductByProductCode(anyString())).thenReturn(Optional.empty());

				AirOrder order = (AirOrder) quoteMapper.mapToOrder(quoteInformationDto);
				assertNull(order.getProductName());
			}

			@Test
			void mapToOrderWithNullProductCode() throws IOException {
				BasicQuoteInformationDto quoteInformationDto = createJsonQuoteInformation(airExportExamplePayload);
				((AirExportQuoteInformationDto) quoteInformationDto).setProduct(null);
				when(airProductService.getAirProductByProductCode(anyString())).thenReturn(Optional.empty());

				AirOrder order = (AirOrder) quoteMapper.mapToOrder(quoteInformationDto);
				assertNull(order.getProductName());
			}
		}
	}

	@Nested
	class SeaQuoteInfo {

		final String seaExportExamplePayload = "orders/quote/sea-quote-information-export.json";

		final String seaImportExamplePayload = "orders/quote/sea-quote-information-import.json";

		@Nested
		class ToEntity {

			@ParameterizedTest
			@ValueSource(strings = { seaExportExamplePayload, seaImportExamplePayload })
			void mapFullSeaQuoteInfo(String jsonPath) throws IOException {
				final BasicQuoteInformationDto quoteInformationDto = createJsonQuoteInformation(jsonPath);
				final SeaQuoteInformation mapped = (SeaQuoteInformation) quoteMapper.mapQuoteInfo(quoteInformationDto);

				if (Objects.equals(jsonPath, seaExportExamplePayload)) {
					assertEquals(OrderType.SEAEXPORTORDER, mapped.getOrderType());
				} else {
					assertEquals(OrderType.SEAIMPORTORDER, mapped.getOrderType());
				}

				assertRequiredFieldsNotNull(mapped);
				assertValidAddressInformation(mapped);
				assertNotNull(mapped.getCollectionFrom());
				assertNotNull(mapped.getCollectionTo());
				assertNotNull(mapped.getGoodsValue());
				assertNotNull(mapped.getGoodsCurrency());
				assertNotNull(mapped.getQuoteReference());

			}

		}

		@Nested
		class ToOrder {

			@ParameterizedTest
			@ValueSource(strings = { seaExportExamplePayload, seaImportExamplePayload })
			void mapToOrder(String jsonPath) throws IOException {
				BasicQuoteInformationDto quoteInformationDto = createJsonQuoteInformation(jsonPath);
				Order order = quoteMapper.mapToOrder(quoteInformationDto);

				if (Objects.equals(jsonPath, seaExportExamplePayload)) {
					assertEquals(OrderType.SEAEXPORTORDER, order.getOrderType());
				} else {
					assertEquals(OrderType.SEAIMPORTORDER, order.getOrderType());
				}
				assertRequiredOrderFieldsNotNull(order);
			}
		}
	}

	@Nested
	class AirOrderToQuoteInfo {

		@ParameterizedTest
		@ValueSource(classes = { AirExportOrder.class, AirImportOrder.class })
		void toQuoteInfo(Class<? extends AirOrder> targetClass) {

			final AirOrder order;

			if (Objects.equals(targetClass.getClass().getName(), AirExportOrder.class.getName())) {
				order = testUtil.generateAirExportOrder();
			} else {
				order = testUtil.generateAirImportOrder();
			}

			assertNotNull(order);
			assertNull(order.getQuoteInformation());
			quoteMapper.buildQuoteInformationForOrder(order);
			assertNotNull(order.getQuoteInformation());

			if (order instanceof AirExportOrder) {
				assertEquals(OrderType.AIREXPORTORDER, order.getQuoteInformation().getOrderType());
			} else {
				assertEquals(OrderType.AIRIMPORTORDER, order.getQuoteInformation().getOrderType());
			}
		}

		@ParameterizedTest
		@ValueSource(classes = { AirExportOrder.class, AirImportOrder.class })
		void mapFields(Class<? extends AirOrder> targetClass) {

			final AirOrder order;

			if (Objects.equals(targetClass.getClass().getName(), AirExportOrder.class.getName())) {
				order = testUtil.generateAirExportOrder();
			} else {
				order = testUtil.generateAirImportOrder();
			}

			assertNotNull(order);

			order.setOrderExpiryDate(OffsetDateTime.now().plusDays(1));

			AirQuoteInformation mapped = quoteMapper.mapAirQuoteInfoFromOrder(order);
			assertNotNull(mapped);
			assertEquals(order.getOrderExpiryDate(), mapped.getQuoteExpiryDate());
			assertEquals(order.getCustomerNumber(), mapped.getCustomerNumber());
			assertEquals(order.getOrderType(), mapped.getOrderType());
			assertEquals(order.getCollectionDate(), mapped.getCollectionDate());
			assertEquals(order.getIncoTerm(), mapped.getTermCode());
			assertNotNull(mapped.getShipperAddress());
			assertNotNull(mapped.getConsigneeAddress());
			assertEquals(order.getOrderLines().size(), mapped.getOrderLineItems().size());
		}

	}

	@Nested
	class SeaOrderToQuoteInfo {

		@ParameterizedTest
		@ValueSource(classes = { SeaExportOrder.class, SeaImportOrder.class })
		void toQuoteInfo(Class<? extends SeaOrder> targetClass) {

			final SeaOrder order;

			if (Objects.equals(targetClass.getClass().getName(), SeaExportOrder.class.getName())) {
				order = testUtil.generateSeaExportOrder();
			} else {
				order = testUtil.generateSeaImportOrder();
			}

			assertNotNull(order);
			assertNull(order.getQuoteInformation());
			quoteMapper.buildQuoteInformationForOrder(order);
			assertNotNull(order.getQuoteInformation());

			if (order instanceof SeaExportOrder) {
				assertEquals(OrderType.SEAEXPORTORDER, order.getQuoteInformation().getOrderType());
			} else {
				assertEquals(OrderType.SEAIMPORTORDER, order.getQuoteInformation().getOrderType());
			}
		}

		@ParameterizedTest
		@ValueSource(classes = { SeaExportOrder.class, SeaImportOrder.class })
		void mapFields(Class<? extends SeaOrder> targetClass) {

			final SeaOrder order;

			if (Objects.equals(targetClass.getClass().getName(), SeaExportOrder.class.getName())) {
				order = testUtil.generateSeaExportOrder();
			} else {
				order = testUtil.generateSeaImportOrder();
			}

			assertNotNull(order);

			order.setOrderExpiryDate(OffsetDateTime.now().plusDays(1));

			SeaQuoteInformation mapped = quoteMapper.mapSeaQuoteInfoFromOrder(order);
			assertNotNull(mapped);
			assertEquals(order.getOrderExpiryDate(), mapped.getQuoteExpiryDate());
			assertEquals(order.getCustomerNumber(), mapped.getCustomerNumber());
			assertEquals(order.getOrderType(), mapped.getOrderType());
			assertEquals(order.getCollectionDate(), mapped.getCollectionDate());
			assertEquals(order.getIncoTerm(), mapped.getTermCode());
			assertNotNull(mapped.getShipperAddress());
			assertNotNull(mapped.getConsigneeAddress());
			assertEquals(order.getOrderLines().size(), mapped.getOrderLineItems().size());
		}

	}

	@Nested
	class RoadOrderToQuoteInfo {

		@Test
		void toQuoteInfo() {
			final RoadOrder order = testUtil.generateForwardingOrder();

			assertNull(order.getQuoteInformation());
			quoteMapper.buildQuoteInformationForOrder(order);
			assertNotNull(order.getQuoteInformation());

			assertEquals(OrderType.ROADFORWARDINGORDER, order.getQuoteInformation().getOrderType());
		}

		@Test
		void mapFields() {
			final ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();

			forwardingOrder.setOrderExpiryDate(OffsetDateTime.now().plusDays(1));
			forwardingOrder.setFrostProtection(true);

			RoadQuoteInformation mapped = quoteMapper.mapRoadQuoteInfoFromOrder(forwardingOrder);
			assertNotNull(mapped);
			assertEquals(forwardingOrder.getOrderExpiryDate(), mapped.getQuoteExpiryDate());
			assertEquals(forwardingOrder.getCustomerNumber(), mapped.getCustomerNumber());
			assertEquals(forwardingOrder.getOrderType(), mapped.getOrderType());
			assertEquals(forwardingOrder.getCollectionDate(), mapped.getCollectionDate());
			assertEquals(forwardingOrder.getFreightTerm(), mapped.getTermCode());
			assertNotNull(mapped.getShipperAddress());
			assertNotNull(mapped.getConsigneeAddress());
			assertEquals(forwardingOrder.getOrderLines().size(), mapped.getOrderLineItems().size());
			assertTrue(mapped.getTailLift());
			assertTrue(mapped.getSelfCollector());
			assertTrue(mapped.getFrostProtected());
			assertEquals(forwardingOrder.getPackingPositions().size(), mapped.getPackingPositions().size());
		}
	}

	@Nested
	class EdgeCases {
		@Test
		void ensureNullSafeFillCollections() {
			assertDoesNotThrow(() -> quoteMapper.fillNullCollections(null, null));
		}

		@Test
		void mapWithNoFrom() throws IOException {
			AirExportQuoteInformationDto airQuoteInformationDto = (AirExportQuoteInformationDto) createJsonQuoteInformation("orders/quote/air-quote-information-export.json");
			airQuoteInformationDto.setFrom(null);
			AirExportOrder mapped = quoteMapper.mapAirExportOrder(airQuoteInformationDto);
			assertNotNull(mapped.getCollectionFrom());
			assertEquals(OffsetDateTime.of(2999, 11, 15, 8, 0, 0, 0, ZoneOffset.UTC), mapped.getCollectionFrom());
		}

		@Test
		void mapWithNoTo() throws IOException {
			AirExportQuoteInformationDto airQuoteInformationDto = (AirExportQuoteInformationDto) createJsonQuoteInformation("orders/quote/air-quote-information-export.json");
			airQuoteInformationDto.setTo(null);
			AirExportOrder mapped = quoteMapper.mapAirExportOrder(airQuoteInformationDto);
			assertNotNull(mapped.getCollectionFrom());
			assertEquals(OffsetDateTime.of(2999, 11, 15, 16, 0, 0, 0, ZoneOffset.UTC), mapped.getCollectionTo());
		}
	}

	private void assertRequiredOrderFieldsNotNull(Order mapped) {
		assertEquals(SourceOfOrder.QUOTE, mapped.getDatasource());
		assertNotNull(mapped.getQuoteInformation());
		assertNotNull(mapped.getConsigneeAddress());
		assertNotNull(mapped.getShipperAddress());
		assertNotNull(mapped.getCollectionDate());
	}

	private void assertValidAddressInformation(QuoteInformation quoteInformation) {
		assertNotNull(quoteInformation.getShipperAddress());
		assertNotNull(quoteInformation.getConsigneeAddress());

		if (quoteInformation instanceof AirQuoteInformation airQuoteInformation) {
			assertNotNull(airQuoteInformation.getPrincipalAddress());
		}
	}

	private void assertRequiredFieldsNotNull(QuoteInformation quoteInformation) {
		assertNotNull(quoteInformation.getQuoteRequestId());
		assertNotNull(quoteInformation.getCustomerNumber());
		assertNotNull(quoteInformation.getOrderType());
		assertNotNull(quoteInformation.getCollectionDate());
		assertNotNull(quoteInformation.getTermCode());
		if (quoteInformation instanceof AirQuoteInformation) {
			assertNotNull(quoteInformation.getQuoteExpiryDate());
		}
		if (quoteInformation instanceof RoadQuoteInformation roadQuoteInformation && StringUtils.isNotBlank(roadQuoteInformation.getQuoteReference())) {
			assertNotNull(roadQuoteInformation.getQuoteExpiryDate());
		}
	}

	private BasicQuoteInformationDto createJsonQuoteInformation(String s) throws IOException {
		return loadResourceAndConvert(s, new TypeReference<>() {
		});
	}

}
