package com.dachser.dfe.book.document;

import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.model.DocumentCategoryDto;
import com.dachser.dfe.book.model.DocumentCategoryTypeDto;
import com.dachser.dfe.book.model.DocumentGroupDto;
import com.dachser.dfe.book.model.DocumentInfoDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentSortingService {

	private final static String DOCUMENT_CLASS_UNKNOWN_LABEL = "unknown";

	private final Translator translator;

	public List<DocumentGroupDto> sortDocumentResponse(List<DocumentInfoDto> documentInfos) {
		List<DocumentInfoDto> infoOrder = new ArrayList<>();
		List<DocumentInfoDto> infoCustom = new ArrayList<>();
		List<DocumentInfoDto> infoCollectionOrder = new ArrayList<>();
		List<DocumentInfoDto> infoDangerousGood = new ArrayList<>();

		documentInfos.forEach(info -> {
			info.setDocumentClass(translator.toLocale(Objects.requireNonNullElse(info.getDocumentClass(), DOCUMENT_CLASS_UNKNOWN_LABEL)));
			switch (info.getDocumentCategoryEnum()) {
				case CUSTOMS -> infoCustom.add(info);
				case ORDER -> infoOrder.add(info);
				case COLLECTION_ORDER -> infoCollectionOrder.add(info);
			case DANGEROUS_GOOD -> infoDangerousGood.add(info);
			case UNKNOWN -> log.warn("Document Category {} not implemented yet.", info.getDocumentCategory());
			}
		});

		return createResultSet(infoCollectionOrder, infoCustom, infoOrder, infoDangerousGood);
	}

	private List<DocumentGroupDto> createResultSet(List<DocumentInfoDto> infoCollectionOrder, List<DocumentInfoDto> infoCustom, List<DocumentInfoDto> infoOrder,
			List<DocumentInfoDto> infoDangerousGood) {
		List<DocumentGroupDto> resultSet = new ArrayList<>();

		if (!infoCollectionOrder.isEmpty()) {
			resultSet.add(createGroup(DocumentCategory.COLLECTION_ORDER, infoCollectionOrder));
		}

		if (!infoCustom.isEmpty()) {
			resultSet.add(createGroup(DocumentCategory.CUSTOMS, infoCustom));
		}

		if (!infoOrder.isEmpty()) {
			resultSet.add(createGroup(DocumentCategory.ORDER, infoOrder));
		}

		if (!infoDangerousGood.isEmpty()) {
			resultSet.add(createGroup(DocumentCategory.DANGEROUS_GOOD, infoDangerousGood));
		}

		return resultSet;
	}

	private DocumentGroupDto createGroup(DocumentCategory type, List<DocumentInfoDto> infos) {
		DocumentGroupDto group = new DocumentGroupDto();
		DocumentCategoryDto category = new DocumentCategoryDto();
		category.setType(DocumentCategoryTypeDto.fromValue(type.name()));
		category.setDescription(translator.toLocale(type.getLabel()));
		group.setDocumentCategory(category);
		group.setDocumentInfo(sortDocumentInfo(infos));
		return group;
	}

	private List<DocumentInfoDto> sortDocumentInfo(List<DocumentInfoDto> documentInfos) {
		return documentInfos.stream().sorted(Comparator.comparing(DocumentInfoDto::getDocumentClass).thenComparing(DocumentInfoDto::getDocumentName)).toList();
	}

	protected List<DocumentInfoDto> sortDocumentInfoById(List<DocumentInfoDto> documentInfos) {
		return documentInfos.stream().sorted(Comparator.comparing(DocumentInfoDto::getDocumentId)).toList();
	}
}
