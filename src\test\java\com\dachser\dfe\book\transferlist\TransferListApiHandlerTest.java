package com.dachser.dfe.book.transferlist;

import com.dachser.dfe.book.BaseOpenApiTest;
import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.model.DocumentDownloadDto;
import com.dachser.dfe.book.model.FilterDto;
import com.dachser.dfe.book.model.FreightTermDto;
import com.dachser.dfe.book.model.KeysDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderLineDetailDto;
import com.dachser.dfe.book.model.QueryObjectDto;
import com.dachser.dfe.book.model.SortingDto;
import com.dachser.dfe.book.model.TransferListAddressDto;
import com.dachser.dfe.book.model.TransferListAddressTypeDto;
import com.dachser.dfe.book.model.TransferListFilterDto;
import com.dachser.dfe.book.model.TransferListPdfItemDto;
import com.dachser.dfe.book.model.TransferListPdfQueryObjectDto;
import com.dachser.dfe.book.model.TransferListQueryObjectDto;
import com.dachser.dfe.book.model.TransferListResponseDto;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderRepositoryFacade;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.overview.CachedOrderOverviewService;
import com.dachser.dfe.book.transferlist.entity.VTransferList;
import com.dachser.dfe.book.transferlist.mapper.TransferListPdfMapper;
import com.dachser.dfe.book.transferlist.pdf.PdfService;
import com.dachser.dfe.book.transferlist.repository.VTransferListRepository;
import com.dachser.dfe.book.transferlist.service.PackingPositionService;
import com.dachser.dfe.book.transferlist.service.RoadOrderLineService;
import com.dachser.dfe.book.transferlist.supervisingbranch.BranchService;
import com.dachser.dfe.book.transferlist.trackablepackingaid.TrackablePackingAidService;
import com.dachser.dfe.road.masterdata.model.RMDTrackablePackingAidResponseDTO;
import io.restassured.common.mapper.TypeRef;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import io.restassured.module.mockmvc.specification.MockMvcRequestSpecification;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.test.context.support.WithMockUser;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import static com.dachser.dfe.book.TestUtil.generatePackingPositionDto;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.when;

public class TransferListApiHandlerTest extends BaseOpenApiTest {

	@Autowired
	protected VTransferListRepository vTransferListRepository;

	@Autowired
	protected TestUtil testUtil;

	@MockBean
	protected PdfService pdfService;

	@MockBean
	protected TransferListPdfMapper transferListPdfMapper;

	@MockBean
	private OrderRepositoryFacade orderRepositoryFacade;

	@MockBean
	private BranchService branchService;

	@MockBean
	private CachedOrderOverviewService cachedOrderOverviewService;

	@MockBean
	private RoadOrderLineService roadOrderLineService;

	@MockBean
	private PackingPositionService packingPositionService;

	@MockBean
	private CountryService countryService;

	@MockBean
	TrackablePackingAidService trackablePackingAidService;

	@Nested
	class GetTransferList {
		@Nested
		class WithAccessToken {
			@Nested
			class ValidRequest {
				private void testEmptyTransferList(String endpoint) {
					QueryObjectDto queryObject = new QueryObjectDto();
					FilterDto filter = new FilterDto();
					filter.setKey(KeysDto.TRANSFER_LIST_ADDRESS);
					filter.setValue("");
					queryObject.setSort(new SortingDto());
					queryObject.setFilter(List.of(filter));

					MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
					final MockMvcResponse response = request.post(endpoint);
					assertThat(response.statusCode()).isEqualTo(200);
					ArrayList<HashMap<String, List<String>>> content = response.getBody().jsonPath().get("content");
					assertThat(content).isEmpty();
				}

				@Test
				void shouldReturnEmptyTransferListWhenFilterIsEmpty() {
					testEmptyTransferList("v2/transferlist");
				}

				private void testTransferListEndpoint(String endpoint) {
					TransferListQueryObjectDto queryObject= new TransferListQueryObjectDto();
					queryObject.setAddressHash("6D8EC4E80767201A87AD80A6BD6292765771281CE2AB9D0EBEAF0FABCE7B3B6F");
					queryObject.setPickupDate(LocalDate.parse("2024-05-02"));

					MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
					final MockMvcResponse response = request.post(endpoint);
					assertThat(response.statusCode()).isEqualTo(200);
					TransferListResponseDto transferListResponse = response.getBody().as(TransferListResponseDto.class);
					assertThat(transferListResponse.getContent()).hasSize(2);
				}

				@Test
				void shouldReturnTransferListWhenFilterIsValid() {
					testTransferListEndpoint("v2/transferlist");
				}

			}

		}
	}

	@Nested
	class FilterRequests {

		@Test
		void testFiltering() {
			final String addressHash = "43A98AEAEEF936A4D5072CF7B35FF9D4BFF67A419A868C97FCE1B97910B0BF40";
			final String pickupDate = "2024-05-02";
			TransferListQueryObjectDto queryObject = new TransferListQueryObjectDto();
			queryObject.setAddressHash(addressHash);
			queryObject.setPickupDate(LocalDate.parse(pickupDate));
			MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
			final MockMvcResponse response = request.post("v2/transferlist");
			assertThat(response.statusCode()).isEqualTo(200);
			final TransferListResponseDto content = response.getBody().as(new TypeRef<>() {
			});
			assertThat(content.getTotalElements()).isEqualTo(1);
			assertThat(content.getContent().get(0).getShipmentNumber()).isEqualTo("4010183804");
		}

		@Test
		void testFilteringAddressWithEmptyValueShouldReturnEmptyResult() {
			final String addressHash = "";
			TransferListQueryObjectDto queryObject = new TransferListQueryObjectDto();
			queryObject.setAddressHash(addressHash);
			MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
			final MockMvcResponse response = request.post("v2/transferlist");
			assertThat(response.statusCode()).isEqualTo(200);
			ArrayList<HashMap<String, List<String>>> content = response.getBody().jsonPath().get("content");
			assertThat(content).isEmpty();
		}

		@Test
		void testFilteringDateWithEmptyValueShouldReturnEmptyResult() {
			TransferListQueryObjectDto queryObject = new TransferListQueryObjectDto();
			queryObject.setPickupDate(null);
			MockMvcRequestSpecification request = givenWriteRequest().body(queryObject);
			final MockMvcResponse response = request.post("v2/transferlist");
			assertThat(response.statusCode()).isEqualTo(200);
			ArrayList<HashMap<String, List<String>>> content = response.getBody().jsonPath().get("content");
			assertThat(content).isEmpty();
		}
	}

	@Nested
	class GetTransferListFilter {
		@Nested
		@WithMockUser
		class WithAccessToken {
			@Nested
			class ValidRequest {

				private void testGroupAndSortFilterItems(String endpoint) {
					final MockMvcResponse response = givenRequest().get(endpoint);
					assertThat(response.statusCode()).isEqualTo(200);
					final List<TransferListFilterDto> filterItems = response.getBody().as(new TypeRef<>() {
					});

					assertFilterItem(filterItems.get(0), TransferListAddressTypeDto.PRINCIPAL, 3, List.of("2024-05-02", "2024-05-06", "2024-05-07"),
							"20D802B33F4DDA5D178C73413031865B4E3EF5561403FD4D8050F875B3F12090");
					assertFilterItem(filterItems.get(1), TransferListAddressTypeDto.PRINCIPAL, 2, List.of("2024-05-01", "2024-05-02"),
							"DBE04416E4839F3F379028FCE43D45704B7EB4E0AC2E6153DCB6307D09B9F36F");
					assertFilterItem(filterItems.get(2), TransferListAddressTypeDto.LOADING_POINT, 2, List.of("2024-05-01", "2024-05-02"),
							"F2CD50D327A50507B4636C69974B4CF9EF5D6ECE6E81DB30272898D2808C3C34");
					assertFilterItem(filterItems.get(3), TransferListAddressTypeDto.LOADING_POINT, 2, List.of("2024-05-01", "2024-05-02"),
							"6D8EC4E80767201A87AD80A6BD6292765771281CE2AB9D0EBEAF0FABCE7B3B6F");
					assertFilterItem(filterItems.get(4), TransferListAddressTypeDto.PRINCIPAL, 1, List.of("2024-05-02"),
							"CA2BD9562EC937D8CF7E70298E41BC10E58E3D56C95F8DA0EB82D953BDCD2D5B");
					assertFilterItem(filterItems.get(5), TransferListAddressTypeDto.LOADING_POINT, 1, List.of("2024-05-02"),
							"43A98AEAEEF936A4D5072CF7B35FF9D4BFF67A419A868C97FCE1B97910B0BF40");
					assertFilterItem(filterItems.get(6), TransferListAddressTypeDto.LOADING_POINT, 1, List.of("2024-05-02"),
							"8711F06FB9604C8CAE9AD329413877EB5BFC9BE479DE28CCE08708ED9E130213");
				}

				@Test
				void shouldGroupAndSortFilterItems() {
					testGroupAndSortFilterItems("v1/transferlist/filter");
				}

				private void assertFilterItem(TransferListFilterDto actual, TransferListAddressTypeDto expectedAddressType, int expectedBookedOrders, List<String> expectedDates,
						String expectedAddressHash) {
					assertThat(actual.getAddressType()).isEqualTo(expectedAddressType);
					assertThat(actual.getBookedOrders()).isEqualTo(expectedBookedOrders);
					assertThat(actual.getAddressHash()).isEqualTo(expectedAddressHash);
					assertFilterDates(actual.getDates());
				}

				private void assertFilterDates(List<LocalDate> dates) {
					DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
					String today = LocalDate.now().format(formatter);
					List<String> dateStrings = dates.stream().map(date -> date.format(formatter)).toList();

					assertThat(dateStrings).isSortedAccordingTo(String::compareTo).contains(today);
				}
			}
		}
	}

	@Nested
	class GetTransferListPdf {
		@Nested
		class WithAccessToken {
			@Nested
			class ValidRequest {

				private void testReturnPdf(String endpoint) {
					final String transferListDate = "2024-05-02";
					final String transferListAddressHash = "43A98AEAEEF936A4D5072CF7B35FF9D4BFF67A419A868C97FCE1B97910B0BF40";
					TransferListPdfQueryObjectDto pdfQueryObject = new TransferListPdfQueryObjectDto();
					TransferListQueryObjectDto queryObject = new TransferListQueryObjectDto();
					pdfQueryObject.setQueryObject(queryObject);
					pdfQueryObject.setDateFormat("yyyy-MM-dd");
					pdfQueryObject.setPrintDateTime("2023-09-27, 02:45 PM");
					queryObject.setAddressHash(transferListAddressHash);
					queryObject.setPickupDate(LocalDate.parse(transferListDate));
					DocumentDownloadDto expectedPdfDocument = createDocumentDownloadDto();
					TransferListAddressDto expectedAddress = createTransferListAddressDto();
					when(pdfService.createPdf(nullable(Object.class), nullable(String.class), nullable(String.class))).thenReturn(expectedPdfDocument);
					when(transferListPdfMapper.mapFromAddress(any(VTransferList.class))).thenReturn(expectedAddress);
					TransferListPdfItemDto expectedPdfItem = createTransferListPdfItemDto(2L);
					when(transferListPdfMapper.mapTransferListPdfItem(any(VTransferList.class), anyString(), any())).thenReturn(expectedPdfItem);
					Order order = testUtil.generateForwardingOrderWithDangerousGoods();
					order.setBranchId(111);
					when(orderRepositoryFacade.loadOrderById(anyLong())).thenReturn(order);
					when(branchService.getBranchAddress(anyInt())).thenReturn(Optional.of(expectedAddress));
					when(cachedOrderOverviewService.getProductNameOrError(any(), any())).thenReturn("dummyProductName");
					when(roadOrderLineService.getRoadOrderLines(anyLong())).thenReturn(new ArrayList<>());
					when(packingPositionService.getRoadPackingPositions(any())).thenReturn(new ArrayList<>());
					when(countryService.mapToDachserCountryCode(anyString())).thenReturn("001");
					RMDTrackablePackingAidResponseDTO responseDTO = createRMDTrackablePackingAidResponseDTO();
					when(trackablePackingAidService.categorizeTransferList(anyString(), anyString(), any(), any(), any())).thenReturn(responseDTO);

					MockMvcRequestSpecification request = givenWriteRequest();
					request.body(pdfQueryObject);
					final MockMvcResponse response = request.post(endpoint);
					assertThat(response.statusCode()).isEqualTo(200);
					final DocumentDownloadDto content = response.getBody().as(new TypeRef<>() {});
					assertThat(content.getDocumentName()).isEqualTo(expectedPdfDocument.getDocumentName());
					assertThat(content.getFileType()).isEqualTo(expectedPdfDocument.getFileType());
					assertThat(content.getContent()).isEqualTo(expectedPdfDocument.getContent());
				}

				@Test
				void shouldReturnPdf() {
					testReturnPdf("v2/transferlist/pdf");
				}

				private DocumentDownloadDto createDocumentDownloadDto() {
					DocumentDownloadDto documentDownload = new DocumentDownloadDto();
					documentDownload.setDocumentName("dummyDocumentName");
					documentDownload.setFileType(DocumentDownloadDto.FileTypeEnum.PDF);
					documentDownload.setContent("dummyContent");
					return documentDownload;
				}

				private TransferListAddressDto createTransferListAddressDto() {
					TransferListAddressDto address = new TransferListAddressDto();
					address.setId(12345L);
					address.setName("Dummy Name");
					address.setName2("Dummy Name2");
					address.setName3("Dummy Name3");
					address.setStreet("Dummy Street");
					address.setStreet2("Dummy Street2");
					address.setPostcode("12345");
					address.setCity("Dummy City");
					address.setCountryCode("DE");
					return address;
				}

				public TransferListPdfItemDto createTransferListPdfItemDto(Long orderId) {
					TransferListPdfItemDto pdfItem = new TransferListPdfItemDto();
					pdfItem.setCustomerNumber("dummyCustomerNumber");
					pdfItem.setOrderId(orderId);
					pdfItem.setOrderNumber("dummyOrderNumber");
					pdfItem.setShipmentNumber("dummyShipmentNumber");

					TransferListAddressDto address = new TransferListAddressDto();
					address.setId(12345L);
					address.setName("Dummy Name");
					address.setName2("Dummy Name2");
					address.setName3("Dummy Name3");
					address.setStreet("Dummy Street");
					address.setStreet2("Dummy Street2");
					address.setPostcode("12345");
					address.setCity("Dummy City");
					address.setCountryCode("DE");
					pdfItem.setConsigneeAddress(address);

					OptionDto product = new OptionDto();
					product.setCode("dummyCode");
					product.setDescription("dummyDescription");
					pdfItem.setProduct(product);

					pdfItem.setQuantity(100L);

					List<OrderLineDetailDto> orderLineItems = new ArrayList<>();
					OrderLineDetailDto orderLineDetail = new OrderLineDetailDto();
					orderLineDetail.setQuantity(50);
					orderLineDetail.setWeight(10);
					orderLineDetail.setVolume(5.0);
					orderLineItems.add(orderLineDetail);
					pdfItem.setOrderLineItems(orderLineItems);

					pdfItem.setPackingPositions(List.of(generatePackingPositionDto(1, TestUtil.generateRoadOrderLineDto())));

					pdfItem.setDeliveryDate("2022-10-01T00:00:00.000Z");

					FreightTermDto freightTerm = new FreightTermDto();
					freightTerm.setDachserTermKey("dummyDachserTermKey");
					freightTerm.setIncoTermKey("dummyIncoTermKey");
					freightTerm.setHeadline("dummyHeadline");
					pdfItem.setFreightTerm(freightTerm);

					return pdfItem;
				}

				private RMDTrackablePackingAidResponseDTO createRMDTrackablePackingAidResponseDTO() {
					RMDTrackablePackingAidResponseDTO responseDTO = new RMDTrackablePackingAidResponseDTO();
					responseDTO.setTrackablePackingAids(List.of("dummyTrackableAid1", "dummyTrackableAid2"));
					responseDTO.setNonTrackablePackingAids(List.of("dummyNonTrackableAid1", "dummyNonTrackableAid2"));
					return responseDTO;
				}

			}
		}
	}

}
