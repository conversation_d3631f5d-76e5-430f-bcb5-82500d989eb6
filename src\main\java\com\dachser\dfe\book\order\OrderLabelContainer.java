package com.dachser.dfe.book.order;

import com.dachser.dfe.book.order.road.ForwardingOrder;
import jakarta.xml.bind.DatatypeConverter;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class OrderLabelContainer {

	private List<byte[]> rawShipmentLabels = new ArrayList<>();

	private List<byte[]> rawConsignmentLabels = new ArrayList<>();

	private List<Long> orderIds = new ArrayList<>();

	private String transformedBase64;

	private byte[] transformedLabels;

	private boolean locked = false;

	private ForwardingOrder forwardingOrder;

	public void addShipmentLabel(byte[] data, Long orderId) {
		if (this.isLocked()) {
			throw new IllegalStateException("Cannot add shipment label to locked container");
		}
		rawShipmentLabels.add(data);
		orderIds.add(orderId);

	}

	public void addConsignmentLabel(byte[] data) {
		if (this.isLocked()) {
			throw new IllegalStateException("Cannot add consignment label to locked container");
		}
		rawConsignmentLabels.add(data);
	}

	public OrderLabelContainer merge(OrderLabelContainer orderLabelContainer) {
		if (orderLabelContainer.isLocked() || this.isLocked()) {
			throw new IllegalStateException("Cannot merge locked containers");
		}
		if (!CollectionUtils.isEmpty(orderLabelContainer.getRawShipmentLabels())) {
			rawShipmentLabels.addAll(orderLabelContainer.getRawShipmentLabels());
			orderIds.addAll(orderLabelContainer.getOrderIds());
		}
		if (!CollectionUtils.isEmpty(orderLabelContainer.getRawConsignmentLabels())) {
			rawConsignmentLabels.addAll(orderLabelContainer.getRawConsignmentLabels());
		}
		// memory saving
		orderLabelContainer.setRawShipmentLabels(null);
		orderLabelContainer.setRawConsignmentLabels(null);
		forwardingOrder = orderLabelContainer.getForwardingOrder();
		return this;
	}

	public void lockWithTransformed(byte[] transformed) {
		this.transformedLabels = transformed;
		this.transformedBase64 = DatatypeConverter.printBase64Binary(transformed);
		locked = true;
		this.rawShipmentLabels = null;
		this.rawConsignmentLabels = null;
	}

}
