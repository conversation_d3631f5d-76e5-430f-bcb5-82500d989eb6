package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.document.DocumentRepositoryFacade;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.order.OrderRepositoryFacade;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.generator.OrderGenerator;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaImportOrder;
import com.dachser.dfe.book.service.ShipmentNumberService;
import com.dachser.dfe.book.user.UserContextService;
import com.dachser.dfe.book.user.UserServiceMock;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import static com.dachser.dfe.book.order.generator.OrderGenerator.generateOrderAddress;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

@ExtendWith(MockitoExtension.class)
public class OrderTypeConversionMapperTest {

	@InjectMocks
	OrderTypeConversionMapperImpl orderTypeConversionMapper;

	@Mock
	DocumentRepositoryFacade documentRepositoryFacade;

	@Mock
	OrderRepositoryFacade orderRepositoryFacade;

	@Mock
	ShipmentNumberService shipmentNumberService;

	@Spy
	UserContextService userContextService = new UserServiceMock();

	@Spy
	private OrderGenerator orderGenerator = new OrderGenerator(userContextService);

	@InjectMocks
	private TestUtil testUtil;

	@BeforeEach
	void setup() throws IOException {
		Mockito.when(shipmentNumberService.getShipmentNumber()).thenReturn(new Random().nextLong());
	}

	@Test
	void shouldConvertToAirImportOrder() {
		AirExportOrder airExportOrder = testUtil.generateAirExportOrder();
		airExportOrder.setOrderId(1L);
		airExportOrder.getShipperAddress().setName("shipper");
		airExportOrder.getConsigneeAddress().setName("consignee");
		airExportOrder.setProductCode(1);
		airExportOrder.setProductName("Targo Fix");
		airExportOrder.setPickupAddress(OrderGenerator.generateOrderAddress("_pickup", "DE", true, null, true, "tax1234"));
		airExportOrder.setPrincipalAddress(OrderGenerator.generateOrderAddress("_principal", "DE", true, null, true, "tax1234"));
		airExportOrder.setOrderExpiryDate(OffsetDateTime.now());

		List<Long> documentList = new ArrayList<>();
		documentList.add(1L);
		documentList.add(2L);
		airExportOrder.setDocumentIds(documentList);

		AirImportOrder importOrder = orderTypeConversionMapper.convertToAirImportOrder(airExportOrder, documentRepositoryFacade, shipmentNumberService);

		assertNull(importOrder.getOrderId());
		assertNotNull(importOrder.getBranchId());
		assertNotNull(importOrder.getCollectionDate());
		assertNotNull(importOrder.getCollectionFrom());
		assertNotNull(importOrder.getCollectionTo());
		assertEquals("shipper", importOrder.getConsigneeAddress().getName());
		assertEquals("consignee", importOrder.getShipperAddress().getName());
		assertNotNull(importOrder.getCustomerNumber());
		assertNotNull(importOrder.getCustomerNumberWithSegment());
		assertNotNull(importOrder.getDatasource());
		assertNotNull(importOrder.getDeliveryAddress());
		assertNotNull(importOrder.getFromIATA());
		assertNotNull(importOrder.getToIATA());
		assertNotNull(importOrder.getGoodsValue());
		assertNotNull(importOrder.getIncoTerm());
		assertNotNull(importOrder.getProductCode());
		assertNotNull(importOrder.getProductName());
		assertNotNull(importOrder.getShipmentNumber());
		assertNotEquals(importOrder.getShipmentNumber(), airExportOrder.getShipmentNumber());
		assertNull(importOrder.getShipmentTransferId());
		assertNotNull(importOrder.getShipperReference());
		assertEquals(OrderStatus.DRAFT, importOrder.getStatus());
		assertNotNull(importOrder.getPickupAddress());
		assertNotNull(importOrder.getPrincipalAddress());
		assertNotNull(importOrder.getOrderExpiryDate());
		assertNotNull(importOrder.getOrderLines());
		assertEquals(importOrder, importOrder.getOrderLines().get(0).getOrder());
		assertNotNull(importOrder.getOrderReferences());
		assertEquals(importOrder, importOrder.getOrderReferences().get(0).getOrder());
		assertNotNull(importOrder.getOrderTexts());
		assertEquals(importOrder, importOrder.getOrderTexts().get(0).getOrder());
		assertNotNull(importOrder.getDocumentIds());
	}

	@Test
	void shouldConvertToAirExportOrder() {
		AirImportOrder airImportOrder = testUtil.generateAirImportOrder();
		airImportOrder.setOrderId(1L);
		airImportOrder.getShipperAddress().setName("shipper");
		airImportOrder.getConsigneeAddress().setName("consignee");
		airImportOrder.setProductCode(1);
		airImportOrder.setProductName("Targo Fix");
		airImportOrder.setPickupAddress(OrderGenerator.generateOrderAddress("_pickup", "DE", true, null, true, "tax1234"));
		airImportOrder.setPrincipalAddress(OrderGenerator.generateOrderAddress("_principal", "DE", true, null, true, "tax1234"));
		airImportOrder.setOrderExpiryDate(OffsetDateTime.now());

		List<Long> documentList = new ArrayList<>();
		documentList.add(1L);
		documentList.add(2L);
		airImportOrder.setDocumentIds(documentList);

		AirExportOrder exportOrder = orderTypeConversionMapper.convertToAirExportOrder(airImportOrder, documentRepositoryFacade, shipmentNumberService);

		assertNull(exportOrder.getOrderId());
		assertNotNull(exportOrder.getBranchId());
		assertNotNull(exportOrder.getCollectionDate());
		assertNotNull(exportOrder.getCollectionFrom());
		assertNotNull(exportOrder.getCollectionTo());
		assertEquals("shipper", exportOrder.getConsigneeAddress().getName());
		assertEquals("consignee", exportOrder.getShipperAddress().getName());
		assertNotNull(exportOrder.getCustomerNumber());
		assertNotNull(exportOrder.getCustomerNumberWithSegment());
		assertNotNull(exportOrder.getDatasource());
		assertNotNull(exportOrder.getDeliveryAddress());
		assertNotNull(exportOrder.getFromIATA());
		assertNotNull(exportOrder.getToIATA());
		assertNotNull(exportOrder.getGoodsValue());
		assertNotNull(exportOrder.getIncoTerm());
		assertNotNull(exportOrder.getProductCode());
		assertNotNull(exportOrder.getProductName());
		assertNotNull(exportOrder.getShipmentNumber());
		assertNotEquals(exportOrder.getShipmentNumber(), airImportOrder.getShipmentNumber());
		assertNull(exportOrder.getShipmentTransferId());
		assertNotNull(exportOrder.getShipperReference());
		assertEquals(OrderStatus.DRAFT, exportOrder.getStatus());
		assertNotNull(exportOrder.getPickupAddress());
		assertNotNull(exportOrder.getPrincipalAddress());
		assertNotNull(exportOrder.getOrderExpiryDate());
		assertNotNull(exportOrder.getOrderLines());
		assertEquals(exportOrder, exportOrder.getOrderLines().get(0).getOrder());
		assertNotNull(exportOrder.getOrderReferences());
		assertEquals(exportOrder, exportOrder.getOrderReferences().get(0).getOrder());
		assertNotNull(exportOrder.getOrderTexts());
		assertEquals(exportOrder, exportOrder.getOrderTexts().get(0).getOrder());
		assertNotNull(exportOrder.getDocumentIds());
	}

	@Test
	void shouldConvertToSeaImportOrder() {
		SeaExportOrder seaExportOrder = testUtil.generateSeaExportOrder();
		seaExportOrder.setOrderId(1L);
		seaExportOrder.getShipperAddress().setName("shipper");
		seaExportOrder.getConsigneeAddress().setName("consignee");
		seaExportOrder.setPickupAddress(OrderGenerator.generateOrderAddress("_pickup", "DE", true, null, true, "tax1234"));
		seaExportOrder.setPrincipalAddress(OrderGenerator.generateOrderAddress("_principal", "DE", true, null, true, "tax1234"));
		seaExportOrder.setOrderExpiryDate(OffsetDateTime.now());

		List<Long> documentList = new ArrayList<>();
		documentList.add(1L);
		documentList.add(2L);
		seaExportOrder.setDocumentIds(documentList);

		SeaImportOrder importOrder = orderTypeConversionMapper.convertToSeaImportOrder(seaExportOrder, documentRepositoryFacade, shipmentNumberService);

		assertNull(importOrder.getOrderId());
		assertNotNull(importOrder.getBranchId());
		assertNotNull(importOrder.getCollectionDate());
		assertNotNull(importOrder.getCollectionFrom());
		assertNotNull(importOrder.getCollectionTo());
		assertEquals("shipper", importOrder.getConsigneeAddress().getName());
		assertEquals("consignee", importOrder.getShipperAddress().getName());
		assertNotNull(importOrder.getCustomerNumber());
		assertNotNull(importOrder.getCustomerNumberWithSegment());
		assertNotNull(importOrder.getDatasource());
		assertNotNull(importOrder.getDeliveryAddress());
		assertNotNull(importOrder.getFromPort());
		assertNotNull(importOrder.getToPort());
		assertNotNull(importOrder.getGoodsValue());
		assertNotNull(importOrder.getIncoTerm());
		assertNotNull(importOrder.getShipmentNumber());
		assertNotEquals(importOrder.getShipmentNumber(), seaExportOrder.getShipmentNumber());
		assertNull(importOrder.getShipmentTransferId());
		assertNotNull(importOrder.getShipperReference());
		assertEquals(OrderStatus.DRAFT, importOrder.getStatus());
		assertNotNull(importOrder.getPickupAddress());
		assertNotNull(importOrder.getPrincipalAddress());
		assertNotNull(importOrder.getOrderExpiryDate());
		assertNotNull(importOrder.getOrderLines());
		assertEquals(importOrder, importOrder.getOrderLines().get(0).getOrder());
		assertEquals(3, importOrder.getOrderLines().get(0).getHsCodes().size());
		assertNotNull(importOrder.getOrderReferences());
		assertEquals(importOrder, importOrder.getOrderReferences().get(0).getOrder());
		assertNotNull(importOrder.getOrderTexts());
		assertEquals(importOrder, importOrder.getOrderTexts().get(0).getOrder());
		assertNotNull(importOrder.getDocumentIds());
	}

	@Test
	void shouldConvertToSeaExportOrder() {
		SeaImportOrder seaImportOrder = testUtil.generateSeaImportOrder();
		seaImportOrder.setOrderId(1L);
		seaImportOrder.getShipperAddress().setName("shipper");
		seaImportOrder.getConsigneeAddress().setName("consignee");
		seaImportOrder.setPickupAddress(OrderGenerator.generateOrderAddress("_pickup", "DE", true, null, true, "tax1234"));
		seaImportOrder.setPrincipalAddress(OrderGenerator.generateOrderAddress("_principal", "DE", true, null, true, "tax1234"));
		seaImportOrder.setOrderExpiryDate(OffsetDateTime.now());

		List<Long> documentList = new ArrayList<>();
		documentList.add(1L);
		documentList.add(2L);
		seaImportOrder.setDocumentIds(documentList);

		SeaExportOrder exportOrder = orderTypeConversionMapper.convertToSeaExportOrder(seaImportOrder, documentRepositoryFacade, shipmentNumberService);

		assertNull(exportOrder.getOrderId());
		assertNotNull(exportOrder.getBranchId());
		assertNotNull(exportOrder.getCollectionDate());
		assertNotNull(exportOrder.getCollectionFrom());
		assertNotNull(exportOrder.getCollectionTo());
		assertEquals("shipper", exportOrder.getConsigneeAddress().getName());
		assertEquals("consignee", exportOrder.getShipperAddress().getName());
		assertNotNull(exportOrder.getCustomerNumber());
		assertNotNull(exportOrder.getCustomerNumberWithSegment());
		assertNotNull(exportOrder.getDatasource());
		assertNotNull(exportOrder.getDeliveryAddress());
		assertNotNull(exportOrder.getFromPort());
		assertNotNull(exportOrder.getToPort());
		assertNotNull(exportOrder.getGoodsValue());
		assertNotNull(exportOrder.getIncoTerm());
		assertNotNull(exportOrder.getShipmentNumber());
		assertNotEquals(exportOrder.getShipmentNumber(), seaImportOrder.getShipmentNumber());
		assertNull(exportOrder.getShipmentTransferId());
		assertNotNull(exportOrder.getShipperReference());
		assertEquals(OrderStatus.DRAFT, exportOrder.getStatus());
		assertNotNull(exportOrder.getPickupAddress());
		assertNotNull(exportOrder.getPrincipalAddress());
		assertNotNull(exportOrder.getOrderExpiryDate());
		assertNotNull(exportOrder.getOrderLines());
		assertEquals(exportOrder, exportOrder.getOrderLines().get(0).getOrder());
		assertNotNull(exportOrder.getOrderReferences());
		assertEquals(exportOrder, exportOrder.getOrderReferences().get(0).getOrder());
		assertNotNull(exportOrder.getOrderTexts());
		assertEquals(exportOrder, exportOrder.getOrderTexts().get(0).getOrder());
		assertNotNull(exportOrder.getDocumentIds());
	}

	@Test
	void shouldConvertToForwardingOrder() {
		CollectionOrder collectionOrder = testUtil.generateCollectionOrder();
		collectionOrder.setOrderId(1L);
		OrderAddress shipperAddress = generateOrderAddress("_shipper", "DE", true, null);
		collectionOrder.setShipperAddress(shipperAddress);
		collectionOrder.getShipperAddress().setName("shipper");
		collectionOrder.getConsigneeAddress().setName("consignee");
		collectionOrder.setPrincipalAddress(OrderGenerator.generateOrderAddress("_principal", "DE", true, null, true, "tax1234"));
		collectionOrder.setOrderExpiryDate(OffsetDateTime.now());

		List<Long> documentList = new ArrayList<>();
		documentList.add(1L);
		documentList.add(2L);
		collectionOrder.setDocumentIds(documentList);

		ForwardingOrder forwardingOrder = orderTypeConversionMapper.convertToRoadForwardingOrder(collectionOrder, documentRepositoryFacade, shipmentNumberService);

		assertNull(forwardingOrder.getOrderId());
		assertNotNull(forwardingOrder.getBranchId());
		assertNotNull(forwardingOrder.getCollectionDate());
		assertNotNull(forwardingOrder.getCollectionFrom());
		assertNotNull(forwardingOrder.getCollectionTo());
		assertEquals("shipper", forwardingOrder.getConsigneeAddress().getName());
		assertEquals("consignee", forwardingOrder.getShipperAddress().getName());
		assertNotNull(forwardingOrder.getCustomerNumber());
		assertNotNull(forwardingOrder.getCustomerNumberWithSegment());
		assertNotNull(forwardingOrder.getDatasource());
		assertNotNull(forwardingOrder.getGoodsValue());
		assertNotNull(forwardingOrder.getFreightTerm());
		assertNotNull(forwardingOrder.getShipmentNumber());
		assertNotEquals(forwardingOrder.getShipmentNumber(), collectionOrder.getShipmentNumber());
		assertNull(forwardingOrder.getShipmentTransferId());
		assertEquals(OrderStatus.DRAFT, forwardingOrder.getStatus());
		assertNotNull(forwardingOrder.getPrincipalAddress());
		assertNotNull(forwardingOrder.getOrderExpiryDate());
		assertNotNull(forwardingOrder.getOrderLines());
		assertEquals(forwardingOrder, forwardingOrder.getOrderLines().get(0).getOrder());
		assertNotNull(forwardingOrder.getOrderReferences());
		assertEquals(forwardingOrder, forwardingOrder.getOrderReferences().get(0).getOrder());
		assertNotNull(forwardingOrder.getOrderTexts());
		assertEquals(forwardingOrder, forwardingOrder.getOrderTexts().get(0).getOrder());
		assertNotNull(forwardingOrder.getDocumentIds());
	}

	@Test
	void shouldConvertToCollectionOrder() {
		ForwardingOrder forwardingOrder = testUtil.generateForwardingOrder();
		forwardingOrder.setOrderId(1L);
		forwardingOrder.getShipperAddress().setName("shipper");
		forwardingOrder.getConsigneeAddress().setName("consignee");
		forwardingOrder.setPrincipalAddress(OrderGenerator.generateOrderAddress("_principal", "DE", true, null, true, "tax1234"));
		forwardingOrder.setOrderExpiryDate(OffsetDateTime.now());

		List<Long> documentList = new ArrayList<>();
		documentList.add(1L);
		documentList.add(2L);
		forwardingOrder.setDocumentIds(documentList);

		CollectionOrder collectionOrder = orderTypeConversionMapper.convertToRoadCollectionOrder(forwardingOrder, documentRepositoryFacade, shipmentNumberService);

		assertNull(collectionOrder.getOrderId());
		assertNotNull(collectionOrder.getBranchId());
		assertNotNull(collectionOrder.getCollectionDate());
		assertNotNull(collectionOrder.getCollectionFrom());
		assertNotNull(collectionOrder.getCollectionTo());
		assertEquals("shipper", collectionOrder.getConsigneeAddress().getName());
		assertEquals("consignee", collectionOrder.getShipperAddress().getName());
		assertNotNull(collectionOrder.getCustomerNumber());
		assertNotNull(collectionOrder.getCustomerNumberWithSegment());
		assertNotNull(collectionOrder.getDatasource());
		assertNotNull(collectionOrder.getGoodsValue());
		assertNotNull(collectionOrder.getFreightTerm());
		assertNotNull(collectionOrder.getShipmentNumber());
		assertNotEquals(collectionOrder.getShipmentNumber(), forwardingOrder.getShipmentNumber());
		assertNull(collectionOrder.getShipmentTransferId());
		assertEquals(OrderStatus.DRAFT, collectionOrder.getStatus());
		assertNotNull(collectionOrder.getPrincipalAddress());
		assertNotNull(collectionOrder.getOrderExpiryDate());
		assertNotNull(collectionOrder.getOrderLines());
		assertEquals(collectionOrder, collectionOrder.getOrderLines().get(0).getOrder());
		assertNotNull(collectionOrder.getOrderReferences());
		assertEquals(collectionOrder, collectionOrder.getOrderReferences().get(0).getOrder());
		assertNotNull(collectionOrder.getOrderTexts());
		assertEquals(collectionOrder, collectionOrder.getOrderTexts().get(0).getOrder());
		assertNotNull(collectionOrder.getDocumentIds());
	}
}
