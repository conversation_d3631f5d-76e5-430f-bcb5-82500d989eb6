package com.dachser.dfe.book;

import com.dachser.dfe.platform.security.SecurityConstants;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.time.Clock;
import java.time.Instant;
import java.time.temporal.ChronoUnit;

import static org.mockito.ArgumentMatchers.any;

/**
 * The {@link Instant#now(Clock)} is mocked to return one minute before
 * ({@link #MOCKED_TOKEN_EXPIRATION_TIME_EPOCH_SECONDS_MINUS_ONE_MINUTE}) the token expires, so it is always valid.
 * <p>
 * If the mocked token changes:
 * <ul>
 *     <li>consider to update the value of the {@link #MOCKED_TOKEN_EXPIRATION_TIME_EPOCH_SECONDS} constant</li>
 *     <li>consider to update WireMock stub providing the JWK set in wiremock-stubs/jwk-set.json</li>
 * </ul>
 */
public class MockConstants {
	/**
	 * Access token bearing a common book roleset the role {@link SecurityConstants#ROLE_ACTIVE_COMPANY}.
	 * "realm_access": {
	 *     "roles": [
	 *       "cmp-active-account",
	 *       "offline_access",
	 *       "uma_authorization",
	 *       "default-roles-dfe",
	 *       "company-book"
	 *     ]
	 *   },
	 */
	public static final String ACTIVE_COMPANY_ACCESS_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJwTS16cVFyVFRsU2laWHpOZ0MtRjllcWNfdUtWb20wV3Y1eGZxTjU3WXVJIn0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ASoHH6r61fe91Dn6ht880Q1WURsczXADj33wV4bjLbw-3BrNjGCCOkfpZ1ofbqT2VHWT_FdIqUtryKtgoRhicH82JgKUsIEjve2eXCj8JpeNWLuiV3h_PFm8Ld7OMZ74-KhnMI3oQ1Oci-c6tndTCZE1hqb62uoBWcYuZXIzgMcFEkkcEAErZdw6LwyFWwOKsrXg-fiBsHME9LgYjVZ4po1Rr8JaSKLfZxoWesKx_MW3BCJ79EZwLD2lhBbbQbPGrBYZWoxjyWj6K0dveIpf3HLNBq0c6aPIe9Ect27naOy1og0ZsBiU-i6NBn6nvrT2DmT65PQbpgPsBpGzrK9uIA";

	/**
	 * "realm_access": {
	 *     "roles": [
	 *       "active-company",
	 *       "company-trace",
	 *       "offline_access",
	 *       "uma_authorization",
	 *       "default-roles-dfe"
	 *     ]
	 *   },
	 */
	public static final String ACTIVE_COMPANY_TT_ACCESS_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJwTS16cVFyVFRsU2laWHpOZ0MtRjllcWNfdUtWb20wV3Y1eGZxTjU3WXVJIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kZ2Pw8Xd6khmbSLayjzvPE3LxRb1T39C5VFc87noN9DKxqL0SPMn21t2IXaSASnYflaOEoQ33BydD_3B1KGQHIcMlqxn8UY0tHNWC1jw-AJLwJ7GdLba2YHoBX4_Si7XrTGGXiCdfaMbxH2_hIT5e2nthdkR8YFWsR73rYy0y2ZEIyH4V9HPMjd4ert_YqQpkp6SmETLXqnDcfqzAzln8x3SsKXSg4EMdJ18LI3ppTBRTHlPJxZKhO6lWO1L8f5VDyGTdzbW49z2otU0hoHAWJPVtb6Q56MQx05s59goJbdGz51PXNqAeCJ502a-UJVmCOZpv8UZuW3aY5E34z5ixg";

	/**
	 * Access token without any role - the user is considered as inactive because of the missing {@link SecurityConstants#ROLE_ACTIVE_COMPANY}
	 */
	public static final String INACTIVE_USER_ACCESS_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJwTS16cVFyVFRsU2laWHpOZ0MtRjllcWNfdUtWb20wV3Y1eGZxTjU3WXVJIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.A4OeE9QaUlW4vCNEQzJKSpItn8ucp-wt1Qt9TKk9JodeuIhga3WvI4-jBaw4Gv0AJnAM5Fm0IrdXLvpFHqjn6r1PGosWu1G-FyXS2kxrMkktRkoDmhWQfqzymGOXv7xOqJ05-VuO95x3dB3LojLwzpceX_o6txecIgrNYkGOQ1aq-LbqXR29R20GY9DdsYvV6TsRxMi2CWxZnQRaM85S0k8hYxfT5NWVr0DGilRsdYni3vvbCX5ECqT14SswBMITf4e0Q1u1Lt5Jsa8r6cpHG7Uf1afRFC-w4ZHdB39CaczmSPmwFe3rKrswYXeIL-Mx9wEWru1TF0s64k5PBfxTRA";

	/**
	 * This is the expiration time of the mocked token in seconds since epoch.
	 * <p>
	 * This is the <code>exp</code> claim of the token in {@link #ACTIVE_COMPANY_ACCESS_TOKEN}.
	 * <p
	 * <b>UPDATE THIS CONSTANT WHEN CHANGING THE MOCKED TOKEN</b>
	 */
	public static final int MOCKED_TOKEN_EXPIRATION_TIME_EPOCH_SECONDS = **********;

	/**
	 * One minute before {@link #MOCKED_TOKEN_EXPIRATION_TIME_EPOCH_SECONDS}.
	 */
	public static final Instant MOCKED_TOKEN_EXPIRATION_TIME_EPOCH_SECONDS_MINUS_ONE_MINUTE = Instant.ofEpochSecond(MOCKED_TOKEN_EXPIRATION_TIME_EPOCH_SECONDS)
			.minus(1, ChronoUnit.MINUTES);

	public static MockedStatic<Instant> buildMockedInstant() {
		// mock the current time to be one minute before the expiration time of the token
		final MockedStatic<Instant> instantMockedStatic = Mockito.mockStatic(Instant.class, Mockito.CALLS_REAL_METHODS);
		instantMockedStatic.when(() -> Instant.now(any(Clock.class))).thenReturn(MOCKED_TOKEN_EXPIRATION_TIME_EPOCH_SECONDS_MINUS_ONE_MINUTE);
		return instantMockedStatic;
	}
}
