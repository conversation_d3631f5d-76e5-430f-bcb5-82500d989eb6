package com.dachser.dfe.book.customer;

import org.springframework.security.access.prepost.PreAuthorize;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@PreAuthorize("@customerNumberSecurityService.isCustomerNumberValid(#customerNumber, #customerSegment, #orderType)")
public @interface CustomerNumberValid {

}
