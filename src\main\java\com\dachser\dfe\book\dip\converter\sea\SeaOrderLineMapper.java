package com.dachser.dfe.book.dip.converter.sea;

import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderLineMapper;
import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderLineMapperConfig;
import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.model.jaxb.order.asl.AdditionalOrderInformation;
import com.dachser.dfe.book.model.jaxb.order.asl.OrderPosition;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.book.order.sea.SeaOrderLine;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, config = AirSeaOrderLineMapperConfig.class, uses = { SeaContainerMapper.class })
public interface SeaOrderLineMapper extends AirSeaOrderLineMapper {

	@InheritConfiguration
	@Mapping(target = "grossWeight", source = "weight")
	@Mapping(target = "containerID", source = "fullContainerLoad.sortingPosition", qualifiedByName = "mapSortingPosition")
	OrderPosition mapOrderPosition(SeaOrderLine orderLine);

	@Mapping(target = "typeOfInformation", constant = Types.AdditionalOrderInformation.MARKS_AND_NUMBERS)
	@Mapping(target = "value", source = "markAndNumbers")
	AdditionalOrderInformation mapMarksAndNumbers(SeaOrderLine orderLine);

	List<AdditionalOrderInformation> mapMarksAndNumbers(List<SeaOrderLine> orderLines);

	@AfterMapping
	default void removeEmptyMarksAndNumbers(@MappingTarget List<AdditionalOrderInformation> additionalOrderInformationList) {
		additionalOrderInformationList.removeIf(info -> StringUtils.isEmpty(info.getValue()));
	}

	List<OrderPosition> mapOrderPositionsSea(List<SeaOrderLine> orderLines);

	default List<OrderPosition> mapOrderPositionsSea(SeaOrder seaOrder) {
		return mapOrderPositionsSea(seaOrder.getAllOrderLinesOrFCLOrderLines());
	}

}
