package com.dachser.dfe.book.order.orderapihandler;

import com.dachser.dfe.book.BaseOpenApiTest;
import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.document.Document;
import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.document.FileStorageCleanup;
import com.dachser.dfe.book.model.ADRDangerousGoodDto;
import com.dachser.dfe.book.model.AirExportOrderDto;
import com.dachser.dfe.book.model.AirImportOrderDto;
import com.dachser.dfe.book.model.AirOrderLineDto;
import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.dfe.book.model.DocumentResponseDto;
import com.dachser.dfe.book.model.EQDangerousGoodDto;
import com.dachser.dfe.book.model.FullContainerLoadDto;
import com.dachser.dfe.book.model.LQDangerousGoodDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderAddressDto;
import com.dachser.dfe.book.model.OrderContactDataDto;
import com.dachser.dfe.book.model.OrderProcessResultDto;
import com.dachser.dfe.book.model.OrderReferenceTypeDto;
import com.dachser.dfe.book.model.OrderSaveActionDto;
import com.dachser.dfe.book.model.OrderStatusDto;
import com.dachser.dfe.book.model.OrderValidationResultDto;
import com.dachser.dfe.book.model.PackingPositionDto;
import com.dachser.dfe.book.model.PrintLabelStartPosition;
import com.dachser.dfe.book.model.RoadCollectionOrderDto;
import com.dachser.dfe.book.model.RoadForwardingOrderDto;
import com.dachser.dfe.book.model.RoadOrderLineDto;
import com.dachser.dfe.book.model.RoadOrderReferenceDto;
import com.dachser.dfe.book.model.SeaExportOrderDto;
import com.dachser.dfe.book.model.SeaOrderLineDto;
import com.dachser.dfe.book.model.ValidationResultEntryDto;
import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.OrderApiTestUtil;
import com.dachser.dfe.book.order.OrderRepositoryFacade;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.pdf.ShipmentLabelPdfTransformer;
import com.dachser.dfe.book.user.UserServiceMock;
import com.fasterxml.jackson.core.type.TypeReference;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import io.restassured.module.mockmvc.specification.MockMvcRequestSpecification;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static com.dachser.dfe.book.order.OrderApiTestUtil.CUSTOMER_NUMBER_ROAD;
import static com.dachser.dfe.book.order.OrderApiTestUtil.createContactData;
import static com.dachser.dfe.book.order.OrderApiTestUtil.parseOrderProcessResult;
import static com.dachser.dfe.book.order.OrderApiTestUtil.parseResponse;
import static com.dachser.dfe.book.order.OrderApiTestUtil.syncIds;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;

class CreateNewOrderTest extends BaseOpenApiTest implements ResourceLoadingTest {

	@Autowired
	DocumentService documentService;

	@Autowired
	FileStorageCleanup fileStorageCleanup;

	@MockBean
	ShipmentLabelPdfTransformer shipmentLabelPdfTransformer;

	@Autowired
	OrderRepositoryFacade orderRepositoryFacade;

	@BeforeEach
	public void setUp() {
		MockitoAnnotations.openMocks(this);
	}

	@AfterEach
	void tearDown() {
		fileStorageCleanup.deleteAllFoldersInBasePath();
	}

	@Nested
	class DraftOrder {
		@Nested
		class ValidRequests {
			@Test
			void shouldCreateNewCollectionOrder() throws IOException {
				final RoadCollectionOrderDto order = loadResourceAndConvert("orders/new-collection-order.json", new TypeReference<>() {
				});
				order.setDeliveryOption(DeliveryOptions.NO.getKey());
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ROAD);
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));

				assertEquals(200, response.statusCode());
				final RoadCollectionOrderDto collectionOrder = parseOrderProcessResult(response, RoadCollectionOrderDto.class);
				assertNotNull(collectionOrder.getConsigneeAddress());
				assertEquals(0, collectionOrder.getFurtherAddresses().size());
				assertEquals("Consignee Adresse", collectionOrder.getConsigneeAddress().getName());
				assertEquals("Shipper Adresse", collectionOrder.getShipperAddress().getName());
				assertEquals("testOrder", collectionOrder.getOrderNumber());
				assertEquals(OrderStatusDto.DRAFT, collectionOrder.getStatus());

				assertNotNull(collectionOrder.getPrincipalAddress());
				assertEquals("Dachser Name 1", collectionOrder.getPrincipalAddress().getName());
			}

			@Test
			void shouldCreateNewCollectionOrderWithoutAddresses() throws IOException {
				final RoadCollectionOrderDto order = loadResourceAndConvert("orders/new-collection-order_no_addresses.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ROAD);
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final RoadCollectionOrderDto collectionOrder = parseOrderProcessResult(response, RoadCollectionOrderDto.class);
				assertNull(collectionOrder.getConsigneeAddress());
				assertEquals(0, collectionOrder.getFurtherAddresses().size());
				assertEquals(OrderStatusDto.DRAFT, collectionOrder.getStatus());
			}

			@Test
			void shouldCreateNewForwardingOrderAsDraft() throws IOException {
				final RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ROAD);
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final RoadForwardingOrderDto forwardingOrder = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
				assertNull(forwardingOrder.getConsigneeAddress());
				assertEquals("Fast-Forward", forwardingOrder.getTransportName());
				assertEquals(1, forwardingOrder.getFurtherAddresses().size());
				assertEquals(OrderStatusDto.DRAFT, forwardingOrder.getStatus());
				assertTrue(forwardingOrder.getSelfCollection());
				assertEquals("testOrder", forwardingOrder.getOrderNumber());
				assertEquals(1.2, forwardingOrder.getOrderLineItems().get(0).getLoadingMeter());
			}

			@Test
			void shouldCalculateTotalOrderWeight() throws IOException {
				final RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ROAD);
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final RoadForwardingOrderDto forwardingOrder = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
				assertEquals(OrderStatusDto.DRAFT, forwardingOrder.getStatus());
				assertEquals(50, forwardingOrder.getTotalOrderWeight());
			}

			@Test
			void shouldConvertOrderReferences() throws IOException {
				final RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ROAD);
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final RoadForwardingOrderDto forwardingOrder = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
				assertEquals(OrderStatusDto.DRAFT, forwardingOrder.getStatus());
				final List<RoadOrderReferenceDto> orderReferences = forwardingOrder.getReferences();
				assertEquals(2, orderReferences.size());
				assertEquals(OrderReferenceTypeDto.PURCHASE_ORDER_NUMBER, orderReferences.get(1).getReferenceType());
			}

			@Test
			void shouldCreateNewForwardingOrderFullDataset() throws IOException {

				final RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order-full.json", new TypeReference<>() {
				});
				order.setLastModified(null);
				RoadOrderReferenceDto ref = new RoadOrderReferenceDto();
				ref.setReferenceValue("testOrder");
				ref.setReferenceType(OrderReferenceTypeDto.ORDER_NUMBER);
				order.addReferencesItem(ref);
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ROAD);
				order.setCreatedAt(null);

				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final RoadForwardingOrderDto forwardingOrder = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
				assertEquals(DeliveryOptions.AP.getKey(), forwardingOrder.getDeliveryOption());
				assertEquals("G", forwardingOrder.getProduct());

				assertNotNull(forwardingOrder.getOrderId());
				syncIds(order, forwardingOrder);
				order.setStatus(forwardingOrder.getStatus());
				order.setOrderStatus(forwardingOrder.getOrderStatus());
				order.setDivision(forwardingOrder.getDivision());
				order.setTotalOrderWeight(3);
				order.setTotalOrderVolume(0.179);
				order.getReferences().get(0).setReferenceType(OrderReferenceTypeDto.PURCHASE_ORDER_NUMBER);
				order.getReferences().get(0).setReferenceValue("4711");
				order.setPrincipalAddress(forwardingOrder.getPrincipalAddress());
				order.setCustomsGoods(Boolean.FALSE);

				// this needs to be reset. Its generated at random / on creation and cannot be matched with a static json
				forwardingOrder.setShipmentNumber(null);
				forwardingOrder.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ROAD);
				forwardingOrder.setLastModified(null);
				forwardingOrder.setCreatedAt(null);
				forwardingOrder.setTotalAmountPackages(null);
				forwardingOrder.setTotalLabelCount(null);

				assertEquals(order, forwardingOrder);
			}

			@Test
			void shouldCreateNewRoadOrderWithPackingPositions() throws IOException {

				final RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order-full.json", new TypeReference<>() {
				});
				order.setLastModified(null);
				RoadOrderReferenceDto ref = new RoadOrderReferenceDto();
				ref.setReferenceValue("testOrder");
				ref.setReferenceType(OrderReferenceTypeDto.ORDER_NUMBER);
				order.addReferencesItem(ref);
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ROAD);
				order.setCreatedAt(null);
				RoadOrderLineDto roadOrderLine = createOrderLine("packing", 0);
				PackingPositionDto packingPosition = new PackingPositionDto();
				packingPosition.setQuantity(2);
				packingPosition.setPackagingType(new OptionDto().code("1"));
				packingPosition.addLinesItem(roadOrderLine);
				order.addPackingPositionsItem(packingPosition);

				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final RoadForwardingOrderDto forwardingOrder = parseOrderProcessResult(response, RoadForwardingOrderDto.class);

				assertNotNull(forwardingOrder.getPackingPositions());
				assertEquals(1, forwardingOrder.getPackingPositions().size());
				assertEquals(2, forwardingOrder.getPackingPositions().get(0).getQuantity());
				assertEquals("1", forwardingOrder.getPackingPositions().get(0).getPackagingType().getCode());
				assertEquals(1, forwardingOrder.getPackingPositions().get(0).getLines().size());
				assertEquals("packing", forwardingOrder.getPackingPositions().get(0).getLines().get(0).getContent());
			}

			@Test
			void shouldCreateNewRoadOrderWithDangerousGoods() throws IOException {

				final RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order-full.json", new TypeReference<>() {
				});
				order.setLastModified(null);
				RoadOrderReferenceDto ref = new RoadOrderReferenceDto();
				ref.setReferenceValue("testOrder");
				ref.setReferenceType(OrderReferenceTypeDto.ORDER_NUMBER);
				order.addReferencesItem(ref);
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ROAD);
				order.setCreatedAt(null);
				RoadOrderLineDto roadOrderLine = createOrderLine("dangerous", 0);

				// EQDangerousGood
				EQDangerousGoodDto eqDangerousGood = new EQDangerousGoodDto();
				eqDangerousGood.setPackaging(new OptionDto().code("1"));
				eqDangerousGood.setNoOfPackages(2);
				eqDangerousGood.setSortingPosition(0);
				roadOrderLine.addDangerousGoodsItem(eqDangerousGood);

				// LQDangerousGood
				LQDangerousGoodDto lqDangerousGood = new LQDangerousGoodDto();
				lqDangerousGood.setGrossMass(BigDecimal.TEN);
				lqDangerousGood.setNos("TEST N.O.S.");
				lqDangerousGood.setSortingPosition(0);
				lqDangerousGood.setDangerousGoodDataItem(createDangerousGoodDataItem("1111"));
				roadOrderLine.addDangerousGoodsItem(lqDangerousGood);

				// ADRDangerousGood
				ADRDangerousGoodDto adrDangerousGoodDto1 = new ADRDangerousGoodDto();
				adrDangerousGoodDto1.setNos("TEST N.O.S.");
				adrDangerousGoodDto1.setSortingPosition(0);
				adrDangerousGoodDto1.setPackaging(new OptionDto().code("1"));
				adrDangerousGoodDto1.setEnvironmentallyHazardous(true);
				adrDangerousGoodDto1.setGrossMass(BigDecimal.TEN);
				adrDangerousGoodDto1.setNoOfPackages(2);
				adrDangerousGoodDto1.setDangerousGoodDataItem(createDangerousGoodDataItem("1112"));
				roadOrderLine.addDangerousGoodsItem(adrDangerousGoodDto1);

				order.addOrderLineItemsItem(roadOrderLine);

				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final RoadForwardingOrderDto forwardingOrder = parseOrderProcessResult(response, RoadForwardingOrderDto.class);

				assertNotNull(forwardingOrder.getOrderLineItems());
				assertEquals(2, forwardingOrder.getOrderLineItems().size());

				// EQDangerousGood
				EQDangerousGoodDto eqDangerousGoodDto = (EQDangerousGoodDto) forwardingOrder.getOrderLineItems().get(1).getDangerousGoods().get(0);
				assertEquals("1", eqDangerousGoodDto.getPackaging().getCode());
				assertEquals(2, eqDangerousGoodDto.getNoOfPackages());
				assertEquals(0, eqDangerousGoodDto.getSortingPosition());

				// LQDangerousGood
				LQDangerousGoodDto lqDangerousGoodDto = (LQDangerousGoodDto) forwardingOrder.getOrderLineItems().get(1).getDangerousGoods().get(1);
				assertEquals(BigDecimal.valueOf(10), lqDangerousGoodDto.getGrossMass());
				assertEquals("TEST N.O.S.", lqDangerousGoodDto.getNos());
				assertEquals(0, lqDangerousGoodDto.getSortingPosition());
				assertEquals("1111", lqDangerousGoodDto.getDangerousGoodDataItem().getUnNumber());

				// ADRDangerousGood
				ADRDangerousGoodDto adrDangerousGoodDto = (ADRDangerousGoodDto) forwardingOrder.getOrderLineItems().get(1).getDangerousGoods().get(2);
				assertEquals("1", adrDangerousGoodDto.getPackaging().getCode());
				assertEquals(2, adrDangerousGoodDto.getNoOfPackages());
				assertEquals(0, adrDangerousGoodDto.getSortingPosition());
				assertEquals("TEST N.O.S.", adrDangerousGoodDto.getNos());
				assertTrue(adrDangerousGoodDto.getEnvironmentallyHazardous());
				assertEquals(BigDecimal.valueOf(10), adrDangerousGoodDto.getGrossMass());
				assertEquals("1112", adrDangerousGoodDto.getDangerousGoodDataItem().getUnNumber());
				assertEquals("E", adrDangerousGoodDto.getDangerousGoodDataItem().getTunnelCode());
				assertEquals("1", adrDangerousGoodDto.getDangerousGoodDataItem().getTransportCategory());

			}

			@Test
			void shouldCreateNewForwardingOrderFullDatasetWithoutPrincipalContactForRoadOrders() throws IOException {
				final RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order-full.json", new TypeReference<>() {
				});
				order.setLastModified(null);
				RoadOrderReferenceDto ref = new RoadOrderReferenceDto();
				ref.setReferenceValue("testOrder");
				ref.setReferenceType(OrderReferenceTypeDto.ORDER_NUMBER);
				order.addReferencesItem(ref);
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ROAD);
				MockMvcRequestSpecification request = givenWriteRequest().body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final RoadForwardingOrderDto forwardingOrder = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
				assertEquals(DeliveryOptions.AP.getKey(), forwardingOrder.getDeliveryOption());
				assertEquals("G", forwardingOrder.getProduct());

				assertNotNull(forwardingOrder.getOrderId());
				syncIds(order, forwardingOrder);
				order.setStatus(forwardingOrder.getStatus());
				order.setOrderStatus(forwardingOrder.getOrderStatus());
				order.setDivision(forwardingOrder.getDivision());
				order.setTotalOrderWeight(3);
				order.setTotalOrderVolume(0.179);
				order.getReferences().get(0).setReferenceType(OrderReferenceTypeDto.PURCHASE_ORDER_NUMBER);
				order.getReferences().get(0).setReferenceValue("4711");
				order.setPrincipalAddress(forwardingOrder.getPrincipalAddress());
				order.setCustomsGoods(Boolean.FALSE);

				// this needs to be reset. Its generated at random / on creation and cannot be matched with a static json
				forwardingOrder.setShipmentNumber(null);
				forwardingOrder.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ROAD);
				forwardingOrder.setLastModified(null);
				forwardingOrder.setCreatedAt(null);
				forwardingOrder.setTotalAmountPackages(null);
				forwardingOrder.setTotalLabelCount(null);

				assertEquals(order, forwardingOrder);
				assertNull(forwardingOrder.getPrincipalAddress().getContact());
			}

			@Test
			void shouldCreateNewForwardingOrderIncludingContactDataOfAddress() throws IOException {
				final RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ROAD);
				order.getFurtherAddresses().get(0).setContact(createContactData("_address_1"));
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final RoadForwardingOrderDto forwardingOrder = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
				assertNull(forwardingOrder.getConsigneeAddress());
				assertEquals("Fast-Forward", forwardingOrder.getTransportName());
				assertEquals(1, forwardingOrder.getFurtherAddresses().size());
				final OrderContactDataDto contact = forwardingOrder.getFurtherAddresses().get(0).getContact();
				assertNotNull(contact);
				assertEquals("name_address_1", contact.getName());
				assertEquals("email", contact.getEmail());
				assertTrue(forwardingOrder.getSelfCollection());
			}

			@Test
			void shouldCreateNewAirExportOrder() throws IOException {
				final AirExportOrderDto order = loadResourceAndConvert("orders/new-air-export-order.json", new TypeReference<>() {
				});
				order.setCollectFromAirport(true);
				order.setDeliverToAirport(true);
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final AirExportOrderDto airOrder = parseOrderProcessResult(response, AirExportOrderDto.class);
				assertEquals("FRA", airOrder.getFromIATA().getCode());
				assertNotNull(airOrder.getToIATA());
				assertNotNull(airOrder.getPickupAddress());
				assertNotNull(airOrder.getDeliveryAddress());
				assertNull(airOrder.getProduct());
				assertEquals(2, airOrder.getOrderLineItems().size());

				airOrder.getOrderLineItems().forEach(l -> {
					assertNotNull(l.getHsCodes());
					assertEquals("Test", l.getMarkAndNumbers());
				});
				final AirOrderLineDto airOrderLineDto = airOrder.getOrderLineItems().get(0);
				assertEquals(3.5, airOrderLineDto.getVolume());

				assertTrue(airOrder.getCollectFromAirport());
				assertTrue(airOrder.getDeliverToAirport());
				assertFalse(airOrder.getClonedOrder());
			}

			@Test
			void shouldCreateNewAirExportWithoutConsigneePostcodeOrder() throws IOException {
				final AirExportOrderDto order = loadResourceAndConvert("orders/new-air-export-order.json", new TypeReference<>() {
				});
				order.setCollectFromAirport(true);
				order.setDeliverToAirport(true);
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				order.getConsigneeAddress().setPostcode(null);
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final AirExportOrderDto airOrder = parseOrderProcessResult(response, AirExportOrderDto.class);
				assertEquals("FRA", airOrder.getFromIATA().getCode());
				assertNotNull(airOrder.getToIATA());
				assertNotNull(airOrder.getPickupAddress());
				assertNotNull(airOrder.getDeliveryAddress());
				assertNull(airOrder.getProduct());
				assertEquals(2, airOrder.getOrderLineItems().size());

				airOrder.getOrderLineItems().forEach(l -> {
					assertNotNull(l.getHsCodes());
					assertEquals("Test", l.getMarkAndNumbers());
				});
				final AirOrderLineDto airOrderLineDto = airOrder.getOrderLineItems().get(0);
				assertEquals(3.5, airOrderLineDto.getVolume());

				assertTrue(airOrder.getCollectFromAirport());
				assertTrue(airOrder.getDeliverToAirport());
				assertNull(airOrder.getConsigneeAddress().getPostcode());
			}

			@Test
			void shouldCreateNewAirImportOrder() throws IOException {
				final AirImportOrderDto order = loadResourceAndConvert("orders/new-air-import-order.json", new TypeReference<>() {
				});
				order.setCollectFromAirport(true);
				order.setDeliverToAirport(true);
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final AirImportOrderDto airOrder = parseOrderProcessResult(response, AirImportOrderDto.class);
				assertEquals("FRA", airOrder.getFromIATA().getCode());
				assertNotNull(airOrder.getToIATA());
				assertNotNull(airOrder.getPickupAddress());
				assertNotNull(airOrder.getDeliveryAddress());
				assertNull(airOrder.getProduct());
				assertEquals(2, airOrder.getOrderLineItems().size());

				airOrder.getOrderLineItems().forEach(l -> {
					assertNotNull(l.getHsCodes());
					assertEquals("Test", l.getMarkAndNumbers());
				});
				final AirOrderLineDto airOrderLineDto = airOrder.getOrderLineItems().get(0);
				assertEquals(3.5, airOrderLineDto.getVolume());

				assertTrue(airOrder.getCollectFromAirport());
				assertTrue(airOrder.getDeliverToAirport());

				assertNotNull(airOrder.getPrincipalAddress());
				assertEquals("Dachser ASL", airOrder.getPrincipalAddress().getName());
			}

			@Test
			void shouldCreateNewSeaOrder() throws IOException {
				final SeaExportOrderDto order = loadResourceAndConvert("orders/new-sea-order.json", new TypeReference<>() {
				});
				order.setCollectFromPort(true);
				order.setDeliverToPort(true);
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final SeaExportOrderDto seaOrder = parseOrderProcessResult(response, SeaExportOrderDto.class);
				assertEquals("DEHAM", seaOrder.getFromPort().getCode());
				assertNotNull(seaOrder.getToPort());
				assertNotNull(seaOrder.getPickupAddress());
				assertNotNull(seaOrder.getDeliveryAddress());
				assertEquals(2, seaOrder.getOrderLineItems().size());

				seaOrder.getOrderLineItems().forEach(l -> {
					assertNotNull(l.getHsCodes());
					assertEquals("Test", l.getMarkAndNumbers());
				});
				final SeaOrderLineDto seaOrderLineDto = seaOrder.getOrderLineItems().get(0);
				assertEquals(3.5, seaOrderLineDto.getVolume());

				assertTrue(seaOrder.getCollectFromPort());
				assertTrue(seaOrder.getDeliverToPort());
				assertEquals("555", seaOrder.getShipperAddress().getIndividualId());
				assertEquals("666", seaOrder.getConsigneeAddress().getIndividualId());

				assertNotNull(seaOrder.getPrincipalAddress());
				assertEquals("Dachser ASL", seaOrder.getPrincipalAddress().getName());
			}

			@Test
			void shouldCreateNewSeaOrderWithFullContainerLoad() throws IOException {
				final SeaExportOrderDto order = loadResourceAndConvert("orders/new-sea-order.json", new TypeReference<>() {
				});
				FullContainerLoadDto fullContainerLoad = new FullContainerLoadDto().lines(order.getOrderLineItems()).containerNumber("ABCU1234567")
						.containerType(new OptionDto().code("40")).sortingPosition(1).verifiedGrossMass(111L);

				order.addFullContainerLoadsItem(fullContainerLoad);
				order.setFullContainerLoad(true);
				order.setCollectFromPort(true);
				order.setDeliverToPort(true);
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				order.setOrderLineItems(new ArrayList<>());
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final SeaExportOrderDto seaOrder = parseOrderProcessResult(response, SeaExportOrderDto.class);
				assertEquals("DEHAM", seaOrder.getFromPort().getCode());
				assertNotNull(seaOrder.getToPort());
				assertNotNull(seaOrder.getPickupAddress());
				assertNotNull(seaOrder.getDeliveryAddress());
				assertEquals(0, seaOrder.getOrderLineItems().size());
				assertEquals(1, seaOrder.getFullContainerLoads().size());
				assertEquals("ABCU1234567", seaOrder.getFullContainerLoads().get(0).getContainerNumber());
				assertEquals("40", seaOrder.getFullContainerLoads().get(0).getContainerType().getCode());
				assertEquals(1, seaOrder.getFullContainerLoads().get(0).getSortingPosition());
			}

			@Test
			void shouldCreateNewSeaOrderWithLimitedData() {
				SeaExportOrderDto order = new SeaExportOrderDto();
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final SeaExportOrderDto seaOrder = parseOrderProcessResult(response, SeaExportOrderDto.class);
				assertNotNull(seaOrder.getShipmentNumber());

				assertEquals(OrderStatusDto.DRAFT, seaOrder.getStatus());
			}

			@Test
			void shouldStoreVolumeCorrectlyInNewAirOrder() throws IOException {
				final AirExportOrderDto order = loadResourceAndConvert("orders/new-air-export-order.json", new TypeReference<>() {
				});
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ASL);
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final AirExportOrderDto airOrder = parseOrderProcessResult(response, AirExportOrderDto.class);

				final AirExportOrderDto loadedOrder = (AirExportOrderDto) OrderApiTestUtil.getOrder(airOrder.getOrderId());
				assertEquals(2, loadedOrder.getOrderLineItems().size());

				final AirOrderLineDto airOrderLineDto = loadedOrder.getOrderLineItems().get(0);
				assertEquals(3.5, airOrderLineDto.getVolume());
			}

			@Test
			void shouldAddDocumentsToOrder() throws IOException {
				final DocumentResponseDto documentResponseDto = documentService.uploadDocument(CUSTOMER_NUMBER_ROAD, null, "123", 1, "valid.pdf", MediaType.APPLICATION_PDF,
						new MockMultipartFile("file", "orig", null, "test".getBytes()));
				final RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order.json", new TypeReference<>() {
				});
				order.setDocumentIds(List.of(documentResponseDto.getDocumentId()));
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ROAD);
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final RoadForwardingOrderDto roadForwardingOrderDto = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
				final List<Document> assignedDocuments = documentService.getDocumentsOfOrder(roadForwardingOrderDto.getOrderId());
				assertEquals(1, assignedDocuments.size());

			}

			@Test
			void shouldOverrideCustomerNumberOfDocuments() throws IOException {
				final DocumentResponseDto documentResponseDto = documentService.uploadDocument(CUSTOMER_NUMBER_ROAD, null, "123", 1, "valid.pdf", MediaType.APPLICATION_PDF,
						new MockMultipartFile("file", "orig", null, "test".getBytes()));
				final RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order.json", new TypeReference<>() {
				});
				order.setDocumentIds(List.of(documentResponseDto.getDocumentId()));
				order.setCustomerNumber(UserServiceMock.VALID_CUST_NO_ROAD_2);
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final RoadForwardingOrderDto roadForwardingOrderDto = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
				final List<Document> assignedDocuments = documentService.getDocumentsOfOrder(roadForwardingOrderDto.getOrderId());
				assertEquals(1, assignedDocuments.size());
				assertEquals(UserServiceMock.VALID_CUST_NO_ROAD_2, roadForwardingOrderDto.getCustomerNumber());
				assertEquals(UserServiceMock.VALID_CUST_NO_ROAD_2, assignedDocuments.get(0).getCustomerNumber());

			}

			@Test
			void shouldSetPrincipalAddressOnSave() throws IOException {
				AirExportOrderDto validDraftAirExportOrder = OrderApiTestUtil.createValidDraftAirExportOrder(documentService);
				OrderAddressDto principalAddress = validDraftAirExportOrder.getPrincipalAddress();
				assertNotNull(principalAddress);
				OrderContactDataDto contact = principalAddress.getContact();
				assertNotNull(contact);
				assertEquals("Dachser ASL", principalAddress.getName());
				assertEquals("firstName lastName", contact.getName());
			}

			@Test
			void shouldCreateOrderWhereAtLeastOneWeightIsSetInOrderLines() throws IOException {
				final RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
				});
				order.getOrderLineItems().get(1).setWeight(null);
				order.getOrderLineItems().get(2).setWeight(null);
				order.setCustomerNumber(OrderApiTestUtil.CUSTOMER_NUMBER_ROAD);

				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(200, response.statusCode());
				final RoadForwardingOrderDto forwardingOrder = parseOrderProcessResult(response, RoadForwardingOrderDto.class);
				assertEquals(OrderStatusDto.DRAFT, forwardingOrder.getStatus());
				assertEquals(10, forwardingOrder.getTotalOrderWeight());
			}

		}

		@Nested
		class InValidRequests {
			@Test
			void shouldRejectTooLongInput() throws IOException {
				RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert("orders/new-forwarding-order.json", new TypeReference<>() {
				});
				forwardingOrder.setDeliveryOption("TOO_LONG");
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(forwardingOrder);
				final MockMvcResponse response = request.put(buildUrl("/orders/"));
				assertEquals(400, response.getStatusCode());
				OrderProcessResultDto processResult = response.getBody().as(OrderProcessResultDto.class);
				OrderValidationResultDto validationResult = processResult.getValidationResult();
				assertFalse(validationResult.getValid());
				List<ValidationResultEntryDto> results = validationResult.getResults();
				assertEquals(1, results.size());
				ValidationResultEntryDto resultEntry = results.get(0);
				assertEquals("deliveryOption", resultEntry.getField());
				assertEquals("size must be between 0 and 2", resultEntry.getDescription());
			}

			@Test
			void shouldNotCreateNewRoadOrderWithPackingPositionsIfItIsNotAllowedForUser() throws IOException {
				final RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order-full.json", new TypeReference<>() {
				});
				order.setLastModified(null);
				RoadOrderReferenceDto ref = new RoadOrderReferenceDto();
				ref.setReferenceValue("testOrder");
				ref.setReferenceType(OrderReferenceTypeDto.ORDER_NUMBER);
				order.addReferencesItem(ref);
				order.setCustomerNumber("00000002");
				order.setCreatedAt(null);
				RoadOrderLineDto roadOrderLine = createOrderLine("packing", 0);
				PackingPositionDto packingPosition = new PackingPositionDto();
				packingPosition.setQuantity(2);
				packingPosition.setPackagingType(new OptionDto().code("1"));
				packingPosition.addLinesItem(roadOrderLine);
				order.addPackingPositionsItem(packingPosition);

				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(order);

				final MockMvcResponse response = givenRequest().spec(request).put(buildUrl("/orders/"));
				assertEquals(400, response.statusCode());
			}

			@Test
			void shouldRejectInvalidCustomerNumber() throws IOException {
				RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert("orders/new-forwarding-order.json", new TypeReference<>() {
				});
				forwardingOrder.setCustomerNumber("12312310");
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(forwardingOrder);
				final MockMvcResponse response = request.put(buildUrl("/orders/"));
				assertEquals(404, response.getStatusCode());
			}

			@ParameterizedTest
			@ValueSource(doubles = { -0.01, 99.99, 100.0 })
			void shouldRejectOutOfRangeLoadingMeter(Double loadingMeter) throws IOException {
				RoadForwardingOrderDto forwardingOrder = loadResourceAndConvert("orders/new-forwarding-order.json", new TypeReference<>() {
				});
				forwardingOrder.setCustomerNumber(VALID_CUST_NO_1);
				forwardingOrder.getOrderLineItems().get(0).setLoadingMeter(loadingMeter);
				MockMvcRequestSpecification request = givenRequest().postProcessors(csrf()).header("Content-Type", "application/json").body(forwardingOrder);
				final MockMvcResponse response = request.put(buildUrl("/orders/"));
				assertEquals(400, response.getStatusCode());
				OrderProcessResultDto orderProcessResult = parseResponse(response, OrderProcessResultDto.class);
				OrderValidationResultDto validationResult = orderProcessResult.getValidationResult();
				assertFalse(validationResult.getValid());
				assertEquals("orderLineItems[0].loadingMeter", validationResult.getResults().get(0).getField());
			}

		}
	}

	@Nested
	class BookOrder {
		@Test
		void shouldCreateNewRoadOrderAndMoveDocs() throws IOException {
			when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any(PrintLabelStartPosition.class))).thenReturn("mockPdf".getBytes());
			final DocumentResponseDto documentResponseDto = documentService.uploadDocument(CUSTOMER_NUMBER_ROAD, null, "123", 1, "valid.pdf", MediaType.APPLICATION_PDF,
					new MockMultipartFile("file", "orig", null, "test".getBytes()));
			RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
			});
			order.setDocumentIds(List.of(documentResponseDto.getDocumentId()));
			order = OrderApiTestUtil.createNewOrder(order, null, UserServiceMock.VALID_CUST_NO_ROAD_2, OrderSaveActionDto.SUBMIT);

			assertEquals(OrderStatusDto.SENT, order.getStatus());
			final List<Document> assignedDocuments = documentService.getDocumentsOfOrder(order.getOrderId());
			assertEquals(1, assignedDocuments.size());
			assertEquals(UserServiceMock.VALID_CUST_NO_ROAD_2, order.getCustomerNumber());
			assertEquals(UserServiceMock.VALID_CUST_NO_ROAD_2, assignedDocuments.get(0).getCustomerNumber());
			verify(goodsGroupService, times(1)).hasCustomerGoodsGroups(anyString(), anyInt(), anyInt());
			verify(goodsGroupService, times(1)).findInvalidGoodsGroupInOrderLines(anyList(), anyBoolean());

		}

		@Test
		void shouldCreateNewRoadOrderIncludingFixDate() throws IOException {
			RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
			});
			// Targo onsite fix
			order.setProduct("U");
			order.setFixDate(LocalDate.now().plusDays(5));
			order.setDeliveryOption(DeliveryOptions.AP.getKey());
			order.setDeliveryContact(new OrderContactDataDto().name("Test").email("<EMAIL>"));

			order = OrderApiTestUtil.createNewOrder(order, null, UserServiceMock.VALID_CUST_NO_ROAD, OrderSaveActionDto.VALIDATE);
			assertEquals(OrderStatusDto.LABEL_PENDING, order.getStatus());
			assertNotNull(order.getFixDate());
		}

		@Test
		void shouldCreateNewRoadOrderWithPackingPositionsAndNoOrderLine() throws IOException {
			RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
			});
			order.setLastModified(null);
			RoadOrderReferenceDto ref = new RoadOrderReferenceDto();
			ref.setReferenceValue("testOrder");
			ref.setReferenceType(OrderReferenceTypeDto.ORDER_NUMBER);
			order.addReferencesItem(ref);
			order.setCustomerNumber("00000001");
			order.setCreatedAt(null);
			RoadOrderLineDto roadOrderLine = createOrderLine("packing", 0);
			PackingPositionDto packingPosition = new PackingPositionDto();
			packingPosition.setQuantity(2);
			packingPosition.setPackagingType(new OptionDto().code("1").description("testing"));
			packingPosition.addLinesItem(roadOrderLine);
			order.addPackingPositionsItem(packingPosition);
			order.setOrderLineItems(new ArrayList<>());

			order = OrderApiTestUtil.createNewOrder(order, null, UserServiceMock.VALID_CUST_NO_ROAD, OrderSaveActionDto.VALIDATE);

			assertEquals(OrderStatusDto.LABEL_PENDING, order.getStatus());
			assertEquals(UserServiceMock.VALID_CUST_NO_ROAD, order.getCustomerNumber());
			assertEquals(0, order.getOrderLineItems().size());
			assertEquals(1, order.getPackingPositions().size());
		}

		@Test
		void shouldCreateNewRoadOrderWithDangerousGood() throws IOException {
			RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
			});
			order.setLastModified(null);
			RoadOrderReferenceDto ref = new RoadOrderReferenceDto();
			ref.setReferenceValue("testOrder");
			ref.setReferenceType(OrderReferenceTypeDto.ORDER_NUMBER);
			order.addReferencesItem(ref);
			order.setCustomerNumber("00000001");
			order.setCreatedAt(null);
			RoadOrderLineDto roadOrderLine = createOrderLine("danger", 0);

			// EQDangerousGood
			EQDangerousGoodDto eqDangerousGood = new EQDangerousGoodDto();
			eqDangerousGood.setPackaging(new OptionDto().code("1"));
			eqDangerousGood.setNoOfPackages(2);
			eqDangerousGood.setSortingPosition(0);
			roadOrderLine.addDangerousGoodsItem(eqDangerousGood);

			// LQDangerousGood
			LQDangerousGoodDto lqDangerousGood = new LQDangerousGoodDto();
			lqDangerousGood.setGrossMass(BigDecimal.TEN);
			lqDangerousGood.setNos("TEST N.O.S.");
			lqDangerousGood.setSortingPosition(1);
			lqDangerousGood.setDangerousGoodDataItem(createDangerousGoodDataItem("1111"));
			roadOrderLine.addDangerousGoodsItem(lqDangerousGood);

			// ADRDangerousGood
			ADRDangerousGoodDto adrDangerousGoodDto1 = new ADRDangerousGoodDto();
			adrDangerousGoodDto1.setNos("TEST N.O.S.");
			adrDangerousGoodDto1.setSortingPosition(2);
			adrDangerousGoodDto1.setPackaging(new OptionDto().code("1"));
			adrDangerousGoodDto1.setEnvironmentallyHazardous(true);
			adrDangerousGoodDto1.setGrossMass(BigDecimal.TEN);
			adrDangerousGoodDto1.setNoOfPackages(2);
			adrDangerousGoodDto1.setDangerousGoodDataItem(createDangerousGoodDataItem("1112"));
			roadOrderLine.addDangerousGoodsItem(adrDangerousGoodDto1);

			order.addOrderLineItemsItem(roadOrderLine);

			order = OrderApiTestUtil.createNewOrder(order, null, UserServiceMock.VALID_CUST_NO_ROAD, OrderSaveActionDto.VALIDATE);

			assertEquals(OrderStatusDto.LABEL_PENDING, order.getStatus());
			assertEquals(UserServiceMock.VALID_CUST_NO_ROAD, order.getCustomerNumber());
			assertEquals(4, order.getOrderLineItems().size());

			// EQDangerousGood
			EQDangerousGoodDto eqDangerousGoodDto = (EQDangerousGoodDto) order.getOrderLineItems().get(3).getDangerousGoods().get(0);
			assertEquals("1", eqDangerousGoodDto.getPackaging().getCode());
			assertEquals(2, eqDangerousGoodDto.getNoOfPackages());
			assertEquals(0, eqDangerousGoodDto.getSortingPosition());

			// LQDangerousGood
			LQDangerousGoodDto lqDangerousGoodDto = (LQDangerousGoodDto) order.getOrderLineItems().get(3).getDangerousGoods().get(1);
			assertEquals(BigDecimal.valueOf(10), lqDangerousGoodDto.getGrossMass());
			assertEquals("TEST N.O.S.", lqDangerousGoodDto.getNos());
			assertEquals(1, lqDangerousGoodDto.getSortingPosition());
			assertEquals("1111", lqDangerousGoodDto.getDangerousGoodDataItem().getUnNumber());

			// ADRDangerousGood
			ADRDangerousGoodDto adrDangerousGoodDto = (ADRDangerousGoodDto) order.getOrderLineItems().get(3).getDangerousGoods().get(2);
			assertEquals("1", adrDangerousGoodDto.getPackaging().getCode());
			assertEquals(2, adrDangerousGoodDto.getNoOfPackages());
			assertEquals(2, adrDangerousGoodDto.getSortingPosition());
			assertEquals("TEST N.O.S.", adrDangerousGoodDto.getNos());
			assertTrue(adrDangerousGoodDto.getEnvironmentallyHazardous());
			assertEquals(BigDecimal.valueOf(10), adrDangerousGoodDto.getGrossMass());
			assertEquals("1112", adrDangerousGoodDto.getDangerousGoodDataItem().getUnNumber());
		}

		@Test
		void shouldNotValidateGoodsGroupWhenCreatingCollectionOrder() throws IOException {
			when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any(PrintLabelStartPosition.class))).thenReturn("mockPdf".getBytes());
			final DocumentResponseDto documentResponseDto = documentService.uploadDocument(CUSTOMER_NUMBER_ROAD, null, "123", 1, "valid.pdf", MediaType.APPLICATION_PDF,
					new MockMultipartFile("file", "orig", null, "test".getBytes()));
			RoadCollectionOrderDto order = loadResourceAndConvert("orders/new-collection-order-complete-valid.json", new TypeReference<>() {
			});
			order.setDocumentIds(List.of(documentResponseDto.getDocumentId()));
			order.setFixDate(null);
			order = OrderApiTestUtil.createNewOrder(order, null, UserServiceMock.VALID_CUST_NO_ROAD_2, OrderSaveActionDto.VALIDATE);

			assertEquals(OrderStatusDto.COMPLETE, order.getStatus());
			final List<Document> assignedDocuments = documentService.getDocumentsOfOrder(order.getOrderId());
			assertEquals(1, assignedDocuments.size());
			assertEquals(UserServiceMock.VALID_CUST_NO_ROAD_2, order.getCustomerNumber());
			assertEquals(UserServiceMock.VALID_CUST_NO_ROAD_2, assignedDocuments.get(0).getCustomerNumber());
			verify(goodsGroupService, times(0)).hasCustomerGoodsGroups(anyString(), anyInt(), anyInt());
			verify(goodsGroupService, times(0)).findInvalidGoodsGroupInOrderLines(anyList(), anyBoolean());

		}

		@Test
		void sendAvisOnSubmitBookDirectly() throws IOException {
			when(shipmentLabelPdfTransformer.mergeSeveralOntoPage(any(), any(PrintLabelStartPosition.class))).thenReturn("mockPdf".getBytes());
			RoadForwardingOrderDto order = loadResourceAndConvert("orders/new-forwarding-order-complete-valid.json", new TypeReference<>() {
			});
			order = OrderApiTestUtil.createNewOrder(order, null, UserServiceMock.VALID_CUST_NO_ROAD_2, OrderSaveActionDto.SUBMIT);
			ForwardingOrder orderById = (ForwardingOrder) orderRepositoryFacade.loadOrderById(order.getOrderId());
			assertEquals(OrderStatusDto.SENT, order.getStatus());
			assertNotNull(orderById.getAdviceSent());

		}
	}

	@Nested
	class CloneOrder {
		@Test
		void shouldCloneOrder() throws IOException {
			AirExportOrderDto order = loadResourceAndConvert("orders/new-air-export-order.json", new TypeReference<>() {
			});
			order.setCustomerNumber(UserServiceMock.VALID_CUST_NO_ASL);
			order = OrderApiTestUtil.createNewOrder(order, null, UserServiceMock.VALID_CUST_NO_ASL, null);
			MockMvcRequestSpecification mockMvcRequestSpecification = givenWriteRequest();
			MockMvcResponse mockMvcResponse = mockMvcRequestSpecification.post("/orders/{id}/clone", String.valueOf(order.getOrderId()));
			assertEquals(200, mockMvcResponse.statusCode());
			AirExportOrderDto airExportOrderDto = parseOrderProcessResult(mockMvcResponse, AirExportOrderDto.class);
			assertEquals(OrderStatusDto.DRAFT, airExportOrderDto.getStatus());
			assertNotEquals(order.getOrderId(), airExportOrderDto.getOrderId());
			assertNotEquals(order.getShipmentNumber(), airExportOrderDto.getShipmentNumber());
			assertTrue(airExportOrderDto.getClonedOrder());
		}
	}

	private RoadOrderLineDto createOrderLine(final String name, final Integer number) {
		return new RoadOrderLineDto().number(number).content(name).quantity(1).length(100).width(100).height(100).volume(102.0d)
				.packaging(new OptionDto().code("I").description("Package"));
	}

	private DangerousGoodDataItemDto createDangerousGoodDataItem(String unNumber) {
		return new DangerousGoodDataItemDto().unNumber(unNumber).description("Test").classificationCode("1").packingGroup("II").mainDanger("1").nosRequired(true)
				.subsidiaryHazardOne("1").subsidiaryHazardTwo("2").subsidiaryHazardThree("3").tunnelCode("E").transportCategory("1");

	}

}
