package com.dachser.dfe.book.overview;

import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.document.DocumentType;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.product.air.AirProductService;
import com.dachser.dfe.book.product.road.RoadProductsService;
import com.dachser.dfe.book.user.BusinessDomainProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.dachser.dfe.book.cache.CacheNames.OVERVIEW_AIR_PRODUCT_NAMES;
import static com.dachser.dfe.book.cache.CacheNames.OVERVIEW_DOCUMENT_TYPES;
import static com.dachser.dfe.book.cache.CacheNames.OVERVIEW_PRODUCT_NAMES;
import static org.springframework.util.StringUtils.hasText;

@Service
@Slf4j
@RequiredArgsConstructor
public class CachedOrderOverviewService {
	private static final String ERROR = "ERROR";

	private final RoadProductsService roadProductsService;

	private final AirProductService airProductService;

	private final BusinessDomainProvider businessDomainProvider;

	private final DocumentService documentService;

	@Cacheable(value = OVERVIEW_DOCUMENT_TYPES, key = "#docTypeId", condition = "#docTypeId!=null")
	public Optional<DocumentType> loadDocumentTypeById(String docTypeId) {
		if (hasText(docTypeId)) {
			return documentService.findDocumentTypeById(docTypeId);
		}
		return Optional.empty();
	}

	@Cacheable(cacheNames = OVERVIEW_PRODUCT_NAMES, key = "#productCode + '_' + #division", condition = "#productCode != null && #division != null")
	public String getProductNameOrError(String productCode, Division division) {
		if (hasText(productCode)) {
			final Optional<DeliveryProductDto> product = roadProductsService.getProductByKey(productCode, division);
			if (product.isPresent()) {
				return product.get().getDescription();
			} else {
				log.warn("No Product found with code {} and divisionKey {}.", productCode, division.getKey());
				return ERROR;
			}
		}
		return null;
	}

	@Cacheable(cacheNames = OVERVIEW_AIR_PRODUCT_NAMES, key = "#productCode", condition = "#productCode!=null")
	public String getAirProductNameOrError(String productCode) {
		if (hasText(productCode)) {
			try {
				Optional<AirProductDto> airProduct = airProductService.getAirProductByProductCode(productCode);
				if (airProduct.isPresent()) {
					return airProduct.get().getDescription();
				} else {
					log.warn("No AirProduct found with code {}.", productCode);
					return ERROR;
				}
			} catch (NumberFormatException e) {
				log.error("Invalid AirProduct code {}.", productCode);
				return ERROR;
			}
		}
		return null;
	}

}
