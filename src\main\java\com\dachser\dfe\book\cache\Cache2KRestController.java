package com.dachser.dfe.book.cache;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.cache2k.core.HeapCache;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Profile({ "dev", "dev-memory", "local", "local-mssql", "default" })
@Slf4j
public class Cache2KRestController {

	private final CacheManager cacheManager;

	@GetMapping("/mgmt/caches/detail/{cacheName}")
	public ResponseEntity<String> getCacheStatistics(@PathVariable String cacheName) {
		final Cache cache = cacheManager.getCache(cacheName);
		if (cache != null) {
			final HeapCache<?, ?> nativeCache = (HeapCache) cache.getNativeCache();
			return ResponseEntity.ok(nativeCache.toString());
		} else {
			log.info("Didn't find cache {} within cacheManager {}", cacheName, cacheManager);
			return ResponseEntity.notFound().build();
		}
	}

	@PutMapping("/mgmt/caches/clear/{cacheName}")
	@Operation(security = { @SecurityRequirement(name = "dachserOAuth", scopes = {}) })
	public ResponseEntity<String> clearCache(@PathVariable String cacheName) {
		final Cache cache = cacheManager.getCache(cacheName);
		if (cache != null) {
			final HeapCache<?, ?> nativeCache = (HeapCache) cache.getNativeCache();
			nativeCache.clearLocalCache();
			return ResponseEntity.ok(nativeCache.toString());
		} else {
			log.info("Didn't find cache {} within cacheManager {}", cacheName, cacheManager);
			return ResponseEntity.notFound().build();
		}
	}

}
