package com.dachser.dfe.book;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.reactive.server.WebTestClient;

import java.time.Instant;

import static com.dachser.dfe.book.MockConstants.buildMockedInstant;

@Tag("IntegrationTest")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles({ "apitest", "localmock" })
@AutoConfigureMockMvc
@AutoConfigureWireMock(port = 0, stubs = "classpath:/wiremock-stubs")
public abstract class BaseWebClientTest {

	@Autowired
	protected WebTestClient client;

	protected MockedStatic<Instant> instantMock;

	@BeforeEach
	public void setup() {
		instantMock = buildMockedInstant();
	}

	@AfterEach
	protected void cleanup() {
		instantMock.close();
	}

}