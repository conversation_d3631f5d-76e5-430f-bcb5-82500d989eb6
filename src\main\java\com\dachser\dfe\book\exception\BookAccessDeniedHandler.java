package com.dachser.dfe.book.exception;

import com.dachser.dfe.book.model.ForbiddenProblemDto;
import com.dachser.dfe.platform.security.model.DfePrincipal;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Optional;

/**
 * DFE Platform authentication error handler, translating authentication exceptions in {@link com.dachser.dfe.book.model.ForbiddenProblemDto}
 * responses with {@link org.springframework.http.HttpStatus#UNAUTHORIZED} response code.
 *
 * <AUTHOR>
 * @since 20.04.2023
 */
@Slf4j
@Component
public class BookAccessDeniedHandler implements AccessDeniedHandler {

	public static final String BOOK_API_BASE_PATH = "/book";

	private final ObjectMapper objectMapper;

	public BookAccessDeniedHandler(final ObjectMapper objectMapper) {
		this.objectMapper = objectMapper;
	}

	@Override
	public void handle(final HttpServletRequest request, final HttpServletResponse response, final AccessDeniedException accessDeniedException) throws IOException {
		final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		final String requestURI = request.getRequestURI();


		if (requestURI.contains(BOOK_API_BASE_PATH)) {
			final String apiPath = requestURI.replaceFirst(".*" + BOOK_API_BASE_PATH, "");

			if (authentication != null && authentication.getPrincipal() != null && authentication.getPrincipal() instanceof DfePrincipal dfePrincipal) {
				Optional<String> emailOpt = dfePrincipal.getEmail();
				if (emailOpt.isPresent()) {
					log.error("Access Denied Exception: {} for path: {} for user: {} ", accessDeniedException.getMessage(), apiPath, emailOpt.get());
				} else {
					log.error("Access Denied Exception: {} for path: {}", accessDeniedException.getMessage(), apiPath);
				}
			} else {
				log.error("Access Denied Exception: {} for path: {} for user with invalid token", accessDeniedException.getMessage(), apiPath);
			}
		} else {
			log.error("Access Denied Exception: {} for path: {}", accessDeniedException.getMessage(), requestURI);
		}

		final ForbiddenProblemDto forbiddenProblemDto = new ForbiddenProblemDto();
		ProblemUtils.populateProblemData(forbiddenProblemDto, BookErrorId.ERR_KC_02, ProblemUtils.PROBLEM_URI_PREFIX + "csrf:").detail(BookErrorId.ERR_KC_02.getI18nDetailsLabel());
		response.setStatus(forbiddenProblemDto.getStatus());
		response.setContentType(MediaType.APPLICATION_PROBLEM_JSON_VALUE);
		objectMapper.writerFor(ForbiddenProblemDto.class).writeValue(response.getWriter(), forbiddenProblemDto);
	}
}
