package com.dachser.dfe.book.transferlist.service;

import com.dachser.dfe.book.model.OrderLineDetailDto;
import com.dachser.dfe.book.order.common.orderline.RoadOrderLineRepository;
import com.dachser.dfe.book.order.mapper.OrderLineMapper;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class RoadOrderLineServiceImpl implements RoadOrderLineService {
	private final RoadOrderLineRepository roadOrderLineRepository;

	private final OrderLineMapper orderLineMapper;

	@Override
	public List<OrderLineDetailDto> getRoadOrderLines(Long orderId) {
		List<RoadOrderLine> orderLines = roadOrderLineRepository.findAllByOrder_OrderIdAndPackingPositionIdIsNull(orderId);
		return orderLineMapper.mapOrderLineDetailDtos(orderLines);
	}

}
