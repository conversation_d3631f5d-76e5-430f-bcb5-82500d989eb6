package com.dachser.dfe.book.order.validation.sea;

import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.order.sea.SeaOrder;
import com.dachser.dfe.book.order.sea.SeaOrderLine;
import com.dachser.dfe.book.order.sea.SeaOrderLineHsCode;
import com.dachser.dfe.book.order.sea.SeaOrderReference;
import com.dachser.dfe.book.order.validation.Messages;
import com.dachser.dfe.book.order.validation.PayloadProvidingValidator;
import com.dachser.dfe.book.order.validation.common.AirSeaOrderConstraintValidator;
import com.dachser.dfe.book.term.IncoTermService;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
@Slf4j
final class SeaOrderConstraintValidator extends AirSeaOrderConstraintValidator<SeaOrderReference, SeaOrder>
		implements ConstraintValidator<ValidSeaOrder, SeaOrder>, PayloadProvidingValidator {

	private final Translator translator;

	private static final String ORDER_LINES = "orderLines";

	@Autowired
	public SeaOrderConstraintValidator(Translator translator, IncoTermService incoTermService) {
		super(translator, incoTermService);
		this.translator = translator;
	}

	@Override
	public boolean isValid(SeaOrder order, ConstraintValidatorContext context) {
		context.disableDefaultConstraintViolation();
		AtomicBoolean anyInvalid = new AtomicBoolean(false);

		if (!order.isFullContainerLoad()) {
			if (order.getOrderLines().isEmpty()) {
				context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode(ORDER_LINES).addConstraintViolation();
				anyInvalid.set(true);
			}

			checkNotNullOrderLineConstraintsForLCL(order.getOrderLines(), context);
		} else if (!order.getOrderLines().isEmpty()) {
			// FCL order should not have order lines not placed inside a container
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode(ORDER_LINES).addConstraintViolation();
			anyInvalid.set(true);
		}

		if (!isOrderValid(order, context)) {
			anyInvalid.set(true);
		}

		return !anyInvalid.get();
	}

	private void checkNotNullOrderLineConstraintsForLCL(List<SeaOrderLine> orderLines, ConstraintValidatorContext context) {
		for (int orderLineIndex = 0; orderLineIndex < orderLines.size(); orderLineIndex++) {
			SeaOrderLine orderLine = orderLines.get(orderLineIndex);

			checkFieldNotNull(orderLine.getQuantity(), context, orderLineIndex, "quantity");
			checkFieldNotNull(orderLine.getPackagingType(), context, orderLineIndex, "packaging");
			checkFieldNotNull(orderLine.getLength(), context, orderLineIndex, "length");
			checkFieldNotNull(orderLine.getWidth(), context, orderLineIndex, "width");
			checkFieldNotNull(orderLine.getHeight(), context, orderLineIndex, "height");
			checkFieldNotNull(orderLine.getVolume(), context, orderLineIndex, "volume");

			for (int indexHsCodes = 0; indexHsCodes < orderLine.getHsCodes().size(); indexHsCodes++) {
				SeaOrderLineHsCode hsCode = orderLine.getHsCodes().get(indexHsCodes);
				checkFieldNotNull(hsCode.getGoods(), context, orderLineIndex, "hsCodes", indexHsCodes, "good");
			}
		}
	}

	private void checkFieldNotNull(Object field, ConstraintValidatorContext context, int orderLineIndex, String fieldName) {
		if (field == null) {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode(ORDER_LINES).addPropertyNode(null).inIterable()
					.atIndex(orderLineIndex).addPropertyNode(fieldName).addConstraintViolation();
		}
	}

	private void checkFieldNotNull(Object field, ConstraintValidatorContext context, int orderLineIndex, String parentField, int nestedIndex, String fieldName) {
		if (field == null) {
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode(ORDER_LINES).addPropertyNode(null).inIterable()
					.atIndex(orderLineIndex).addPropertyNode(parentField).addPropertyNode(null).inIterable().atIndex(nestedIndex).addPropertyNode(fieldName)
					.addConstraintViolation();
		}
	}

	protected String getCombinedOrderLineGoods(SeaOrder order) {
		return order.getCombinedOrderLineGoods();
	}

	@Override
	protected boolean hasInvalidIncoTerm(SeaOrder order) {
		return hasInvalidIncoTerm(order.getIncoTerm());
	}

}
