package com.dachser.dfe.book.user;

import com.dachser.dfe.book.exception.generic.NotAuthenticatedException;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.CustomerDto;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.model.TestMockData;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.user.model.Address;
import com.dachser.dfe.book.user.model.AslCustomer;
import com.dachser.dfe.book.user.model.OrderOptions;
import com.dachser.dfe.book.user.model.RoadCustomer;
import com.dachser.dfe.book.user.model.User;
import com.dachser.dfe.book.user.model.UserPreferences;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Component
@Profile("!integrationtest")
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "true")
public class UserServiceMock implements UserContextService, PermissionService {

	public static final int T_FORWARD_BRANCH_ID = 132;

	private static final int BUSINESS_DOMAIN = TestMockData.BUSINESS_DOMAIN;

	public static final int BOOK_BRANCH_ID = 10;

	public static final String VALID_CUST_NO_ROAD = "00000001";

	public static final String VALID_CUST_NO_ROAD_2 = "00000002";

	public static final String VALID_CUST_NO_ROAD_AT_ILO_PRINT = "00000003";

	public static final String VALID_CUST_NO_ASL = "00000100";

	public static final String VALID_CUST_NO_ASL_2 = "00000200";

	public static final String VALID_CUST_NO_ASL_3 = "00000300";

	public static final String INVALID_CUST_NO = "10000200";

	public static final List<String> ALL_ASSOCIATED_CUSTOMER_NUMBERS = List.of("5000");

	public static final List<String> ALL_ASSOCIATED_CUSTOMER_NUMBERS_WITH_SEGMENT = List.of("00005000ROAD", "00005000ASL");

	@Override
	public List<String> getAllBookPermittedCustomerIds() {
		return ALL_ASSOCIATED_CUSTOMER_NUMBERS;
	}

	@Override
	public List<String> getAllBookPermittedCustomerIdsWithSegment() {
		return ALL_ASSOCIATED_CUSTOMER_NUMBERS_WITH_SEGMENT;
	}

	@Override
	public String getCurrentUserId() {
		final SecurityContext context = SecurityContextHolder.getContext();
		if (context != null) {
			final Authentication authentication = context.getAuthentication();
			if (authentication != null) {
				return authentication.getName();
			}
		}
		return UUID.randomUUID().toString();
	}

	@Override
	public String getCurrentUserIdIfAvailable() {
		return getCurrentUserId();
	}

	@Override
	public List<CustomerDto> getCustomers(Segment segment) {
		if (Segment.ROAD == segment) {
			return getRoadCustomers();
		} else {
			return getAslCustomers();
		}
	}

	@Override
	public Optional<RoadCustomer> getCustomerInformationRoad(String customerNumber) {
		Optional<TestMockData.Customer> first = Arrays.stream(TestMockData.Customer.values()).filter(customer -> customer.getCustomerNumber().equals(customerNumber)).findFirst();
		final UUID randomUUID = UUID.randomUUID();
		if (first.isPresent()) {
			final TestMockData.Customer customer = first.get();
			OrderOptions orderOptions = buildOrderOptions(customer.isOrderOptionsEnabled());
			Address address = Address.builder().name(customer.getName()).name1(customer.getName() + " Name 1").name2(customer.getName() + " Name 2")
					.name3(customer.getName() + " Name 3").street(customer.getStreet()).city(customer.getCity()).country("Deutschland").countryCode(customer.getCountry())
					.postcode(customer.getPostcode()).gln(customer.getGln()).build();
			return Optional.ofNullable(
					RoadCustomer.builder().id(randomUUID).name("Company 1").customerNumber(customer.getCustomerNumber()).division(Division.T).orderOptions(orderOptions)
							.branchId(T_FORWARD_BRANCH_ID).address(address).build());
		}
		return Optional.empty();
	}

	@Override
	public Optional<AslCustomer> getCustomerInformationAsl(String customerNumber) {
		Optional<TestMockData.CustomerAsl> first = Arrays.stream(TestMockData.CustomerAsl.values()).filter(customer -> customer.getCustomerNumber().equals(customerNumber))
				.findFirst();
		final UUID randomUUID = UUID.randomUUID();
		if (first.isPresent()) {
			final TestMockData.CustomerAsl customer = first.get();
			OrderOptions orderOptions = buildOrderOptions(customer.isOrderOptionsEnabled());
			Address address = Address.builder().name(customer.getName()).name1(customer.getName()).name2("").name3("").street(customer.getStreet()).city(customer.getCity())
					.country("Deutschland").countryCode(customer.getCountry()).postcode(customer.getPostcode()).gln(customer.getGln()).build();
			return Optional.ofNullable(AslCustomer.builder().id(randomUUID).name("Company ASL").customerNumber(customer.getCustomerNumber()).airProducts(customer.isAirProducts())
					.orderOptions(orderOptions).address(address).build());
		}
		return Optional.empty();
	}

	private List<CustomerDto> getRoadCustomers() {
		return Arrays.stream(TestMockData.Customer.values())
				.map(customer -> new CustomerDto().customerNumber(customer.getCustomerNumber()).label(customer.getLabel()).cashOnDelivery(customer.hasCashOnDelivery())).toList();

	}

	private List<CustomerDto> getAslCustomers() {
		return Arrays.stream(TestMockData.CustomerAsl.values()).map(customer -> new CustomerDto().customerNumber(customer.getCustomerNumber()).label(customer.getLabel())).toList();
	}

	@Override
	public int getBranchId(String customerNumber, OrderType orderType) {
		return switch (orderType) {
			case AIRIMPORTORDER -> BOOK_BRANCH_ID;
			case AIREXPORTORDER -> 9;
			case SEAEXPORTORDER -> 8;
			case SEAIMPORTORDER -> 7;
			case ROADCOLLECTIONORDER -> T_FORWARD_BRANCH_ID;
			default -> T_FORWARD_BRANCH_ID;
		};
	}

	public static OrderOptions buildOrderOptions(boolean orderOptionsEnabled) {
		return OrderOptions.builder().commonInstructions(orderOptionsEnabled).coverAddress(orderOptionsEnabled).customsAgent(orderOptionsEnabled)
				.deconsolidatorAddress(orderOptionsEnabled).deliveryInstructions(orderOptionsEnabled).differingInvoiceAddress(orderOptionsEnabled).importer(orderOptionsEnabled)
				.finalDeliveryAddress(orderOptionsEnabled).frostProtection(orderOptionsEnabled).goodsDescriptions(orderOptionsEnabled).invoiceText(orderOptionsEnabled)
				.manualNumberSscc(orderOptionsEnabled).neutralization(orderOptionsEnabled).notifyAddress(orderOptionsEnabled).otherInformations(orderOptionsEnabled)
				.palletLocation(orderOptionsEnabled).pickupAddress(orderOptionsEnabled).selfCollection(orderOptionsEnabled).consigneeExportCertification(orderOptionsEnabled)
				.orderNumberMandatory(orderOptionsEnabled).build();
	}

	@Override
	public Integer getBusinessDomain() {
		return BUSINESS_DOMAIN;
	}

	@NotNull
	@Override
	public Division getFirstFoundDivision(String customerNumber, Segment segment) {
		if (Segment.ROAD.equals(segment)) {
			Optional<Division> division = getCustomerInformationRoad(customerNumber).map(RoadCustomer::getDivision).stream().findFirst();
			return division.orElse(Division.T);
		} else {
			return Division.T;
		}
	}

	@Override
	public boolean isCustomerAllowedToSetManualSscc(String customerNumber) {
		Optional<RoadCustomer> roadCustomer = getCustomerInformationRoad(customerNumber);
		return roadCustomer.map(RoadCustomer::getOrderOptions).map(OrderOptions::getManualNumberSscc).orElse(false);
	}

	@Override
	public boolean isCustomerAllowedToViewAirProducts(String customerNumber, Segment segment) {
		if (Segment.AIR == segment) {
			return getCustomerInformationAsl(customerNumber).map(AslCustomer::getAirProducts).orElse(false);
		} else {
			return false;
		}
	}

	@Override
	public boolean isCustomerAllowedToViewPackingAidPosition(String customerNumber) {
		return customerNumber.equals(VALID_CUST_NO_ROAD);
	}

	@Override
	public boolean isCustomerAllowedToViewDangerousGoods(String customerNumber) {
		return customerNumber.equals(VALID_CUST_NO_ROAD);
	}

	@Override
	public boolean isCustomerAllowedToPrintConsignmentLabels(String customerNumber) {
		return customerNumber.equals(VALID_CUST_NO_ROAD_AT_ILO_PRINT);
	}

	@Override
	public boolean isCustomerNumberValid(String customerNumber, Segment segment) {
		if (Segment.ROAD == segment) {
			return Arrays.stream(TestMockData.Customer.values()).anyMatch(customer -> customer.getCustomerNumber().equals(customerNumber));
		} else {
			return Arrays.stream(TestMockData.CustomerAsl.values()).anyMatch(customer -> customer.getCustomerNumber().equals(customerNumber));
		}
	}

	@Override
	public Optional<OrderOptions> getOrderOptions(String customerNumber, Segment segment) {
		if (Segment.ROAD == segment) {
			return getCustomerInformationRoad(customerNumber).map(RoadCustomer::getOrderOptions);
		} else {
			return getCustomerInformationAsl(customerNumber).map(AslCustomer::getOrderOptions);
		}
	}

	@Override
	public Optional<Address> getCustomerAddress(String customerNumber, Segment segment) {
		if (Segment.ROAD == segment) {
			return getCustomerInformationRoad(customerNumber).map(RoadCustomer::getAddress);
		} else {
			return getCustomerInformationAsl(customerNumber).map(AslCustomer::getAddress);
		}
	}

	@Override
	public User getCurrentUser() throws NotAuthenticatedException {
		return new User(UUID.fromString(getCurrentUserId()), "Username", "email", "firstName", "lastName", "true", "phone", null);
	}

	@Override
	public UserPreferences getUserPreferences() {
		return new UserPreferences("de", "phoneNumber", "mobileNumber", "dinA4printerPreset", 123, 345);
	}
}
