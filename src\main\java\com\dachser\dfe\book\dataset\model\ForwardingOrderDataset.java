package com.dachser.dfe.book.dataset.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;

import java.util.ArrayList;
import java.util.List;

@Getter
@NoArgsConstructor
public class ForwardingOrderDataset {

	@NonNull
	private final List<CountryDataset> countries = new ArrayList<>();

	@NonNull
	private final List<ProductDataset> products = new ArrayList<>();

	@NonNull
	private final List<IncoTermDataset> incoTerms = new ArrayList<>();
}
