package com.dachser.dfe.book.masterdata.airsea;

import com.dachser.dfe.airsea.masterdata.api.AirportApi;
import com.dachser.dfe.airsea.masterdata.api.AirportRoutingApi;
import com.dachser.dfe.airsea.masterdata.api.EmbargoApi;
import com.dachser.dfe.airsea.masterdata.api.SeaportApi;
import com.dachser.dfe.airsea.masterdata.api.SeaportRoutingApi;
import com.dachser.dfe.airsea.masterdata.model.ASMDPageAirport;
import com.dachser.dfe.airsea.masterdata.model.ASMDPageAirportRouting;
import com.dachser.dfe.airsea.masterdata.model.ASMDPageSeaport;
import com.dachser.dfe.airsea.masterdata.model.ASMDPageSeaportRouting;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AirSeaMasterDataApiWrapper {

	private final AirportApi airportApi;

	private final AirportRoutingApi airportRoutingApi;

	private final EmbargoApi embargoApi;

	private final SeaportApi seaportApi;

	private final SeaportRoutingApi seaportRoutingApi;

	public ASMDPageAirport findAllAirports(int pageIndex, int airportSize, List<String> sort) {
		return airportApi.findAllAirports(pageIndex, airportSize, sort);
	}

	public ASMDPageAirportRouting findAirportRoutingsForCountryAndPostcode(String countryCode, String postalCode, Integer page, Integer size, List<String> sort) {
		ResponseEntity<ASMDPageAirportRouting> airportRoutingsForCountryAndPostcodeWithHttpInfo = airportRoutingApi.findAirportRoutingsForCountryAndPostcodeWithHttpInfo(
				countryCode, postalCode, page, size, sort);

		if (airportRoutingsForCountryAndPostcodeWithHttpInfo.getStatusCode().equals(HttpStatusCode.valueOf(204))) {
			log.warn("204 No Content - AirportRoutingApi : No airport routing found for country code {} and postal code {}", countryCode, postalCode);
		}

		return airportRoutingsForCountryAndPostcodeWithHttpInfo.getBody();
	}

	public boolean findEmbargo(String fromCountryCode, String toCountryCode) {
		return embargoApi.findEmbargo(fromCountryCode, toCountryCode);
	}

	public ASMDPageSeaport getAllSeaports(int pageIndex, int seaportSize, List<String> sort) {
		return seaportApi.getAllSeaports(pageIndex, seaportSize, sort);
	}

	public ASMDPageSeaportRouting findSeaportRoutingsForCountryAndPostcode(String countryCode, String postalCode, Integer page, Integer size, List<String> sort) {
		ResponseEntity<ASMDPageSeaportRouting> seaportRoutingsByPostcodeAndCountryWithHttpInfo = seaportRoutingApi.findSeaportRoutingsByPostcodeAndCountryWithHttpInfo(countryCode,
				postalCode, page, size, sort);

		if (seaportRoutingsByPostcodeAndCountryWithHttpInfo.getStatusCode().equals(HttpStatusCode.valueOf(204))) {
			log.warn("204 No Content - SeaportRoutingApi : No seaport routing found for country code {} and postal code {}", countryCode, postalCode);
		}

		return seaportRoutingsByPostcodeAndCountryWithHttpInfo.getBody();
	}
}
