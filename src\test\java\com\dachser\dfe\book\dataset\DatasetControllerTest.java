package com.dachser.dfe.book.dataset;

import com.dachser.dfe.book.BaseOpenApiTest;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

import static org.junit.jupiter.api.Assertions.assertEquals;

class DatasetControllerTest extends BaseOpenApiTest {

	@SpyBean
	DatasetService datasetService;

	@Test
	void shouldReturn200ForwardingOrderDataset() {
		final String shipperCountryCode = "DE";
		final String shipperPostalCode = "12345";
		final String consigneeCountryCode = "FR";
		final String consigneePostalCode = "54321";

		final MockMvcResponse response = givenRequest()
				.queryParam("shipperCountryCode", shipperCountryCode)
				.queryParam("shipperPostalCode", shipperPostalCode)
				.queryParam("consigneeCountryCode", consigneeCountryCode)
				.queryParam("consigneePostalCode", consigneePostalCode)
				.get(buildUrl("/dataset/forwarding-order"));

		assertEquals(200, response.statusCode());
	}


}