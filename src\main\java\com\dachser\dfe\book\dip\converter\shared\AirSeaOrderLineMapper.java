package com.dachser.dfe.book.dip.converter.shared;

import com.dachser.dfe.book.model.jaxb.order.asl.Dimension;
import com.dachser.dfe.book.model.jaxb.order.asl.GrossWeight;
import com.dachser.dfe.book.model.jaxb.order.asl.Volume;
import com.dachser.dfe.book.order.air.AirOrderLineHsCode;
import com.dachser.dfe.book.order.common.orderline.OrderLine;
import com.dachser.dfe.book.order.sea.SeaOrderLineHsCode;
import org.mapstruct.Condition;
import org.mapstruct.Mapping;

import java.math.BigDecimal;

public interface AirSeaOrderLineMapper {

	@Mapping(target = "dimensionUnit", constant = Types.DimensionUnit.CMT)
	@Mapping(target = "length", source = "length", conditionExpression = "java(orderLine.getLength() > 0)")
	@Mapping(target = "width", source = "width", conditionExpression = "java(orderLine.getWidth() > 0)")
	@Mapping(target = "height", source = "height", conditionExpression = "java(orderLine.getHeight() > 0)")
	Dimension mapDimension(OrderLine orderLine);

	@Mapping(target = "value", source = ".")
	@Mapping(target = "weightUnit", constant = Types.WeightUnit.KGM)
	GrossWeight mapWeight(BigDecimal weight);

	@Mapping(target = "value", source = ".")
	@Mapping(target = "volumeUnit", constant = Types.VolumeUnits.MTQ)
	Volume mapVolume(BigDecimal volume);

	@Condition
	default boolean isBigDecimalValid(BigDecimal value) {
		return value != null && value.doubleValue() > 0;
	}

	default String mapHsCode(AirOrderLineHsCode hsCode) {
		if (hsCode == null) {
			return null;
		}
		return hsCode.getHsCode();
	}

	default String mapHsCode(SeaOrderLineHsCode hsCode) {
		if (hsCode == null) {
			return null;
		}
		return hsCode.getHsCode();
	}
}
