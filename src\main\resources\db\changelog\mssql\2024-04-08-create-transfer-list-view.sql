-- liquibase formatted sql

-- changeset ristw:2024-04-08-create-v-transfer-list-view
-- validCheckSum: 1:any

DROP VIEW IF EXISTS v_transfer_list_principal;

CREATE VIEW v_transfer_list_principal WITH SCHEMABINDING AS
select
    ob.customer_number                                                                    as customer_number,
    ob.order_id                                                                           as order_id,
    CAST(case when ob.collection_from is null then ob.collection_date
              else ob.collection_from
        end AS date)                                                                      as pick_up_date_from,
    CAST(ob.shipment_number AS varchar)                                                   as shipment_number,
    orr.division                                                                          as division,
    orr.product                                                                           as product,
    orr.order_number                                                                      as order_number,
    orr.fix_date                                                                          as delivery_date,
    ob.consignee_address_id                                                               as consignee_address,
    'PRINCIPAL'                                                                           as address_type,
    ob.principal_address_id                                                               as principal_address,
    null                                                                                  as lp_address,
    convert(Varchar(64), hashbytes('SHA2_256', concat(
            coalesce(padr.name, ''),'|',
            coalesce(padr.name2, ''),'|',
            coalesce(padr.name3, ''),'|',
            coalesce(padr.street, ''),'|',
            coalesce(padr.street2, ''),'|',
            coalesce(padr.postcode, ''),'|',
            coalesce(padr.city, ''),'|',
            coalesce(padr.country_code, ''))),2)                                          as address_hash,
    orr.freight_term                                                                      as freight_term
from [${default-schema}].order_road_forward orf
         inner join [${default-schema}].order_base ob on orf.order_id = ob.order_id
         inner join [${default-schema}].order_road orr on orf.order_id = orr.order_id
         inner join [${default-schema}].order_address padr on ob.principal_address_id = padr.order_address_id
where ob.status = 'SENT';

CREATE UNIQUE CLUSTERED INDEX cidx_v_transfer_list_principal
    ON v_transfer_list_principal(customer_number, order_id, shipment_number);
CREATE INDEX idx_v_transfer_list_pick_up_date_from ON v_transfer_list_principal(pick_up_date_from);
CREATE INDEX idx_v_transfer_list_pr_address_type ON v_transfer_list_principal(address_type);
CREATE INDEX idx_v_transfer_list_pr_address_hash ON v_transfer_list_principal(address_hash);

DROP VIEW IF EXISTS v_transfer_list_lp;

CREATE VIEW v_transfer_list_lp WITH SCHEMABINDING AS
select
    ob.customer_number                                                                     as customer_number,
    orf.order_id                                                                           as order_id,
    CAST(case when ob.collection_from is null then ob.collection_date
              else ob.collection_from
        end AS date)                                                                       as pick_up_date_from,
    CAST(ob.shipment_number AS varchar)                                                    as shipment_number,
    orr.division                                                                           as division,
    orr.product                                                                            as product,
    orr.order_number                                                                       as order_number,
    orr.fix_date                                                                           as delivery_date,
    ob.consignee_address_id                                                                as consignee_address,
    'LOADING_POINT'                                                                        as address_type,
    null                                                                                   as principal_address,
    fadr.order_further_address_id                                                          as lp_address,
    convert(Varchar(64), hashbytes('SHA2_256', concat(
            coalesce(fadr.name, ''),'|',
            coalesce(fadr.name2, ''),'|',
            coalesce(fadr.name3, ''),'|',
            coalesce(fadr.street, ''),'|',
            coalesce(fadr.street2, ''),'|',
            coalesce(fadr.postcode, ''),'|',
            coalesce(fadr.city, ''),'|',
            coalesce(fadr.country_code, ''))),2)                                           as address_hash,
    orr.freight_term                                                                       as freight_term
from [${default-schema}].order_road_forward orf
         inner join [${default-schema}].order_base ob on orf.order_id = ob.order_id
         inner join [${default-schema}].order_road orr on orf.order_id = orr.order_id
         inner join [${default-schema}].order_further_address fadr
                    on ob.order_id = fadr.order_id and fadr.address_type = 'LP' and coalesce(fadr.name, '') != ' '
where ob.status = 'SENT';

CREATE UNIQUE CLUSTERED INDEX cidx_v_transfer_list_lp
    ON v_transfer_list_lp(customer_number, order_id, shipment_number);
CREATE INDEX idx_v_transfer_list_pick_up_date_from ON v_transfer_list_lp(pick_up_date_from);
CREATE INDEX idx_v_transfer_list_lp_address_type ON v_transfer_list_lp(address_type);
CREATE INDEX idx_v_transfer_list_lp_address_hash ON v_transfer_list_lp(address_hash);

DROP VIEW IF EXISTS v_transfer_list;

CREATE VIEW v_transfer_list AS
select * from (select *
               from v_transfer_list_principal WITH (NOEXPAND)
               union all
               select *
               from v_transfer_list_lp WITH (NOEXPAND)) as [vtlp*vtll*]
where (pick_up_date_from is null and pick_up_date_from >= DATEADD(DAY, -30, GETDATE()) and pick_up_date_from <= DATEADD(DAY, 14, GETDATE()))
   or (pick_up_date_from is not null and pick_up_date_from >= DATEADD(DAY, -30, GETDATE()) and pick_up_date_from <= DATEADD(DAY, 14, GETDATE()))
