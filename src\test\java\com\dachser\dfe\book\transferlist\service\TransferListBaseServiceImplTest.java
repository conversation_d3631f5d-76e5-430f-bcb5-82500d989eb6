package com.dachser.dfe.book.transferlist.service;

import com.dachser.dfe.book.jpa.OrderSsccRepository;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderLineDetailDto;
import com.dachser.dfe.book.model.PackingPositionDto;
import com.dachser.dfe.book.model.QueryObjectDto;
import com.dachser.dfe.book.model.RoadOrderLineDto;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.model.TransferListQueryObjectDto;
import com.dachser.dfe.book.order.common.orderline.RoadOrderLineRepository;
import com.dachser.dfe.book.order.mapper.OrderLineMapper;
import com.dachser.dfe.book.order.mapper.PackingPositionMapper;
import com.dachser.dfe.book.overview.CachedOrderOverviewService;
import com.dachser.dfe.book.packaging.PackagingOptionsService;
import com.dachser.dfe.book.product.road.RoadProductsService;
import com.dachser.dfe.book.transferlist.entity.VTransferList;
import com.dachser.dfe.book.transferlist.mapper.TransferListFilterMapper;
import com.dachser.dfe.book.transferlist.mapper.TransferListFilterMapperImpl;
import com.dachser.dfe.book.transferlist.mapper.TransferListMapperImpl;
import com.dachser.dfe.book.transferlist.model.TransferListAddressType;
import com.dachser.dfe.book.transferlist.repository.PackingPositionRepository;
import com.dachser.dfe.book.transferlist.repository.TransferListFilterRepository;
import com.dachser.dfe.book.transferlist.repository.VTransferListRepository;
import com.dachser.dfe.book.user.UserContextService;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.List;

import static com.dachser.dfe.book.TestUtil.generatePackingPosition;
import static com.dachser.dfe.book.TestUtil.generateRoadOrderLine;
import static com.dachser.dfe.book.TestUtil.generateRoadOrderLineDto;
import static com.dachser.dfe.book.TestUtil.generateVTransferList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

class TransferListServiceBaseConcreteImpl extends TransferListBaseServiceImpl {
	public TransferListServiceBaseConcreteImpl(VTransferListRepository transferListRepository, UserContextService userContextService, CachedOrderOverviewService cachedOrderOverviewService,
			RoadOrderLineService roadOrderLineService, PackingPositionService packingPositionService,PackagingOptionsService packagingOptionsService, OrderSsccRepository orderSsccRepository) {
		super(transferListRepository, userContextService, cachedOrderOverviewService, roadOrderLineService, packingPositionService, packagingOptionsService, orderSsccRepository);
	}
}

@ExtendWith(MockitoExtension.class)
class TransferListBaseServiceImplTest {

	@InjectMocks
	private TransferListServiceBaseConcreteImpl sut;

	@Mock
	private UserContextService userContextService;

	@Mock
	private TransferListFilterRepository transferListFilterRepository;

	@Mock
	private VTransferListRepository vTransferListRepository;

	@Mock
	private RoadOrderLineRepository roadOrderLineRepository;

	@Mock
	private PackingPositionRepository packingPositionRepository;

	@Mock
	private RoadOrderLineService roadOrderLineService;

	@Mock
	private PackingPositionService packingPositionService;

	@Mock
	private RoadProductsService productsService;

	@Mock
	private CachedOrderOverviewService cachedOrderOverviewService;

	@Mock
	private PackagingOptionsService packagingOptionsService;

	@Mock
	private OrderSsccRepository orderSsccRepository;

	@Spy
	private TransferListFilterMapper filterMapper = new TransferListFilterMapperImpl();

	@Spy
	private TransferListMapperImpl transferListMapper;

	@Mock
	private OrderLineMapper orderLineMapper;

	@Mock
	private PackingPositionMapper packingPositionMapper;

	@Captor
	private ArgumentCaptor<QueryObjectDto> queryObjectCaptor;

	@Nested
	class FetchTransferListParamValidation {
		@Test
		void fetchTransferList_shouldReturnEmptyList_whenQueryObjectIsNull() {
			List<VTransferList> result = sut.fetchTransferList(null);

			assertThat(result).isEmpty();
			verifyNoInteractions(vTransferListRepository);
		}

		@Test
		void fetchTransferList_shouldReturnEmptyList_whenPickupDateIsNull() {
			TransferListQueryObjectDto queryObject = new TransferListQueryObjectDto();
			queryObject.setAddressHash("someHash");

			List<VTransferList> result = sut.fetchTransferList(queryObject);

			assertThat(result).isEmpty();
			verifyNoInteractions(vTransferListRepository);
		}

		@Test
		void fetchTransferList_shouldReturnEmptyList_whenAddressHashIsNull() {
			TransferListQueryObjectDto queryObject = new TransferListQueryObjectDto();
			queryObject.setPickupDate(LocalDate.now());

			List<VTransferList> result = sut.fetchTransferList(queryObject);

			assertThat(result).isEmpty();
			verifyNoInteractions(vTransferListRepository);
		}
	}

	@Test
	void getRoadOrderLinesIsEmpty() {
		when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(null);

		List<OrderLineDetailDto> orderLines = sut.getRoadOrderLines(1L);

		assertThat(orderLines).isEmpty();
	}

	@Test
	void getRoadOrderLines() {
		List<OrderLineDetailDto> orderLineDetailDtos = orderLineMapper.mapOrderLineDetailDtos(List.of(generateRoadOrderLine(), generateRoadOrderLine()));
		when(roadOrderLineService.getRoadOrderLines(any())).thenReturn(orderLineDetailDtos);

		List<OrderLineDetailDto> orderLines = sut.getRoadOrderLines(1L);

		assertThat(orderLines).isEqualTo(orderLineDetailDtos);
	}

	@Test
	void getPackingPositionsIsEmpty() {
		when(packingPositionService.getRoadPackingPositions(any())).thenReturn(null);

		List<PackingPositionDto> packingPositions = sut.getPackingPositions(1L);

		assertThat(packingPositions).isEmpty();
	}

	@Test
	void getPackingPositions() {
		List<PackingPositionDto> packingPositionDtos = packingPositionMapper.mapPackingPositionsDtos(
				List.of(generatePackingPosition(1, generateRoadOrderLine("EU", "Euroflachpalette", 1, 1L))));
		when(packingPositionService.getRoadPackingPositions(any())).thenReturn(packingPositionDtos);

		List<PackingPositionDto> packingPositions = sut.getPackingPositions(1L);

		assertThat(packingPositions).isEqualTo(packingPositionDtos);
	}

	@Test
	void mapProduct() {
		VTransferList vTransferList = generateVTransferList(TransferListAddressType.PRINCIPAL, null, null, null);
		DeliveryProductDto product = new DeliveryProductDto().code(vTransferList.getProduct()).description("description");
		when(cachedOrderOverviewService.getProductNameOrError(anyString(), any(Division.class))).thenReturn(product.getDescription());
		sut.mapProductName(vTransferList);

		assertThat(vTransferList.getProduct()).isEqualTo(product.getCode());
		assertThat(vTransferList.getProductName()).isEqualTo(product.getDescription());
	}

	@Test
	void mapProductNotFound() {
		VTransferList vTransferList = generateVTransferList(TransferListAddressType.PRINCIPAL, null, null, null);
		DeliveryProductDto product = new DeliveryProductDto().code(vTransferList.getProduct()).description("description");
		when(cachedOrderOverviewService.getProductNameOrError(anyString(), any(Division.class))).thenReturn("ERROR");
		sut.mapProductName(vTransferList);

		assertThat(vTransferList.getProduct()).isEqualTo(product.getCode());
		assertThat(vTransferList.getProductName()).isEqualTo("ERROR");
	}

	@Test
	void mapProductNull() {
		VTransferList vTransferList = generateVTransferList(TransferListAddressType.PRINCIPAL, null, null, null);
		vTransferList.setProduct(null);
		DeliveryProductDto product = new DeliveryProductDto().code(vTransferList.getProduct()).description("description");
		when(cachedOrderOverviewService.getProductNameOrError(any(), any(Division.class))).thenReturn(null);
		sut.mapProductName(vTransferList);

		assertThat(vTransferList.getProduct()).isEqualTo(product.getCode());
		assertThat(vTransferList.getProductName()).isNull();
	}

	@Test
	void testLookupPackagingDescriptionFound() {
		String packagingAid = "pack1";
		String expectedDescription = "description1";
		OptionDto option = new OptionDto();
		option.setCode(packagingAid);
		option.setDescription(expectedDescription);

		when(packagingOptionsService.getPackagingOptions(any(Segment.class))).thenReturn(List.of(option));

		String description = sut.lookupPackagingDescription(packagingAid);

		assertThat(description).isEqualTo(expectedDescription);
	}

	@Test
	void testLookupPackagingDescriptionNotFoundReturnsOriginalCode() {
		String packagingAid = "pack1";

		when(packagingOptionsService.getPackagingOptions(any(Segment.class))).thenReturn(List.of());

		String description = sut.lookupPackagingDescription(packagingAid);

		assertThat(description).isEqualTo(packagingAid);
	}

	@Test
	void getTotalLabelCount() {
		List<VTransferList> transferList = List.of(generateVTransferList(TransferListAddressType.PRINCIPAL, null, null, null),
				generateVTransferList(TransferListAddressType.PRINCIPAL, null, null, null));
		List<Long> orderIds = transferList.stream().map(VTransferList::getOrderId).toList();
		when(orderSsccRepository.countOrderSsccsByOrderIds(orderIds)).thenReturn(5);

		int labelCount = sut.getTotalLabelCount(transferList);

		assertThat(labelCount).isEqualTo(5);
		verify(orderSsccRepository, times(1)).countOrderSsccsByOrderIds(orderIds);
	}

	@Test
	void getLabelCount() {
		Long orderId = 12345L;
		when(orderSsccRepository.countOrderSsccsByOrderIds(List.of(orderId))).thenReturn(3);

		int labelCount = sut.getLabelCount(orderId);

		assertThat(labelCount).isEqualTo(3);
		verify(orderSsccRepository, times(1)).countOrderSsccsByOrderIds(List.of(orderId));
	}

	@Test
	void mapPackingPosition_shouldSetDescriptions() {
		OptionDto packagingType = new OptionDto();
		packagingType.setCode("pack1");

		OptionDto packaging = new OptionDto();
		packaging.setCode("pack2");

		PackingPositionDto packingPosition = new PackingPositionDto();
		packingPosition.setPackagingType(packagingType);
		RoadOrderLineDto generateRoadOrderLineDto = generateRoadOrderLineDto();
		packingPosition.setLines(List.of(generateRoadOrderLineDto));

		List<PackingPositionDto> packingPositions = List.of(packingPosition);

		List<OptionDto> packagingOptions = List.of(new OptionDto().code("pack1").description("Description 1"), new OptionDto().code("pack2").description("Description 2"));

		when(packagingOptionsService.getPackagingOptions(any(Segment.class))).thenReturn(packagingOptions);

		sut.mapPackingPosition(packingPositions);

		assertThat(packingPositions.get(0).getPackagingType().getDescription()).isEqualTo(packagingOptions.get(0).getDescription());
		assertThat(packingPositions.get(0).getLines().get(0).getPackaging().getDescription()).isEqualTo(generateRoadOrderLineDto.getPackaging().getDescription());
	}

	@Test
	void mapPackingPosition_shouldHandleMissingDescriptions() {
		OptionDto packagingType = new OptionDto();
		packagingType.setCode("pack1");

		OptionDto packaging = new OptionDto();
		packaging.setCode("pack2");

		PackingPositionDto packingPosition = new PackingPositionDto();
		packingPosition.setPackagingType(packagingType);
		RoadOrderLineDto generateRoadOrderLineDto = generateRoadOrderLineDto();
		packingPosition.setLines(List.of(generateRoadOrderLineDto));

		List<PackingPositionDto> packingPositions = List.of(packingPosition);

		when(packagingOptionsService.getPackagingOptions(any(Segment.class))).thenReturn(List.of());

		sut.mapPackingPosition(packingPositions);

		assertThat(packingPositions.get(0).getPackagingType().getDescription()).isEqualTo(packagingType.getCode());
		assertThat(packingPositions.get(0).getLines().get(0).getPackaging().getDescription()).isEqualTo(generateRoadOrderLineDto.getPackaging().getCode());
	}
}