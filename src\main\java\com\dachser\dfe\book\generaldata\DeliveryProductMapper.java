package com.dachser.dfe.book.generaldata;

import com.dachser.dfe.book.mapper.custom.TranslationMapper;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.generaldata.model.GDAirProductDto;
import com.dachser.dfe.generaldata.model.GDDeliveryProductDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, uses = TranslationMapper.class)
public interface DeliveryProductMapper {

	DeliveryProductDto map(GDDeliveryProductDto gdDeliveryProductDto);

	List<DeliveryProductDto> mapList(List<GDDeliveryProductDto> gdDeliveryProductDtos);

	@Mapping(target = "hint", source = "hint", qualifiedByName = TranslationMapper.TRANSLATED_LABEL_QUALIFIER)
	AirProductDto map(GDAirProductDto gdAirProductDto);

	List<AirProductDto> mapAirProductList(List<GDAirProductDto> gdAirProductDtos);

	DeliveryProductDto mapAirProductToDeliveryProduct(AirProductDto gdAirProductDto);

	List<DeliveryProductDto> mapAirProductListToDeliveryProductList(List<AirProductDto> gdAirProductDtos);
}
