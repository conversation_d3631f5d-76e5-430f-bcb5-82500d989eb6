package com.dachser.dfe.book.service.forwardingdomain;

import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.exception.ErrorIdConstraintValidatorBaseException;
import com.dachser.dfe.road.masterdata.api.ForwardingDomainControllerApi;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestClientException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ForwardingDomainAdapterExtTest {

	@InjectMocks
	private ForwardingDomainAdapterExt forwardingDomainAdapter;

	@Mock
	private ForwardingDomainControllerApi forwardingDomainApi;

	@Mock
	private CountryService countryService;

	@Test
	void shouldCallApi() {
		// given
		int businessDomain = 1;
		int branchId = 1;
		String consigneeCountryCode = "DE";
		String shipperCountryCode = "DE";
		String mappedCountryCode = "D";

		// when
		when(countryService.mapToDachserCountryCode("DE")).thenReturn(mappedCountryCode);
		forwardingDomainAdapter.getForwardingDomain(businessDomain, branchId, consigneeCountryCode, shipperCountryCode);

		// then
		verify(forwardingDomainApi).getForwardingDomain(businessDomain, branchId, mappedCountryCode, mappedCountryCode);
	}

	@Test
	void shouldReturnErrorWhenApiFails() {
		// given
		int businessDomain = 1;
		int branchId = 1;
		String consigneeCountryCode = "DE";
		String shipperCountryCode = "DE";
		String mappedCountry = "D";

		// when
		when(forwardingDomainApi.getForwardingDomain(businessDomain, branchId, mappedCountry, mappedCountry)).thenThrow(new RestClientException("API failed"));
		when(countryService.mapToDachserCountryCode("DE")).thenReturn(mappedCountry);

		// then
		assertEquals(ForwardingDomainAdapter.ERROR_MESSAGE, (assertThrows(ErrorIdConstraintValidatorBaseException.class,
				() -> forwardingDomainAdapter.getForwardingDomain(businessDomain, branchId, consigneeCountryCode, shipperCountryCode))).getMessage());
	}

}
