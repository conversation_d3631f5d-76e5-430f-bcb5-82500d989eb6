package com.dachser.dfe.book.masterdata.road;

import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.road.masterdata.api.AdressTypeDescriptionControllerApi;
import com.dachser.dfe.road.masterdata.api.BranchDataControllerApi;
import com.dachser.dfe.road.masterdata.api.CountryControllerApi;
import com.dachser.dfe.road.masterdata.api.CustomerOrderGroupControllerApi;
import com.dachser.dfe.road.masterdata.api.ForwardingDomainControllerApi;
import com.dachser.dfe.road.masterdata.api.ProductGroupControllerApi;
import com.dachser.dfe.road.masterdata.api.ProductNameControllerApi;
import com.dachser.dfe.road.masterdata.api.ProductPilotControllerApi;
import com.dachser.dfe.road.masterdata.api.RelationLabelprintingControllerApi;
import com.dachser.dfe.road.masterdata.api.ShipperMasterdataControllerApi;
import com.dachser.dfe.road.masterdata.api.TrackablePackingAidControllerApi;
import com.dachser.dfe.road.masterdata.api.ValidateFixedDateControllerApi;
import com.dachser.dfe.road.masterdata.api.ValidateProductControllerApi;
import com.dachser.dfe.road.masterdata.api.ValidateTermControllerApi;
import com.dachser.dfe.road.masterdata.api.ValidateThirdCountryControllerApi;
import com.dachser.dfe.road.masterdata.model.RMDAdressTypeDescriptionDTO;
import com.dachser.dfe.road.masterdata.model.RMDBranchDataDTO;
import com.dachser.dfe.road.masterdata.model.RMDCountryDTO;
import com.dachser.dfe.road.masterdata.model.RMDCustomerOrderGroupDTO;
import com.dachser.dfe.road.masterdata.model.RMDProductGroupDTO;
import com.dachser.dfe.road.masterdata.model.RMDProductNameDTO;
import com.dachser.dfe.road.masterdata.model.RMDProductPilotRequestDTO;
import com.dachser.dfe.road.masterdata.model.RMDProductPilotResponseDTO;
import com.dachser.dfe.road.masterdata.model.RMDRelationLabelprintingDTO;
import com.dachser.dfe.road.masterdata.model.RMDShipperMasterdataDTO;
import com.dachser.dfe.road.masterdata.model.RMDTrackablePackingAidRequestDTO;
import com.dachser.dfe.road.masterdata.model.RMDTrackablePackingAidResponseDTO;
import com.dachser.dfe.road.masterdata.model.RMDValidateFixedDateDTO;
import com.dachser.dfe.road.masterdata.model.RMDValidateProductDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Locale;

@Slf4j
@Service
@RequiredArgsConstructor
public class RoadMasterDataApiWrapper {

	private final ProductNameControllerApi productNameControllerApi;

	private final AdressTypeDescriptionControllerApi adressTypeDescriptionControllerApi;

	private final CustomerOrderGroupControllerApi customerOrderGroupControllerApi;

	private final ProductGroupControllerApi productGroupControllerApi;

	private final ValidateProductControllerApi validateProductControllerApi;

	private final ValidateTermControllerApi validateTermControllerApi;

	private final ValidateFixedDateControllerApi validateFixedDateControllerApi;

	private final ValidateThirdCountryControllerApi validateThirdCountryControllerApi;

	private final BranchDataControllerApi branchDataControllerApi;

	private final RelationLabelprintingControllerApi relationLabelprintingControllerApi;

	private final TrackablePackingAidControllerApi trackablePackingAidControllerApi;

	private final ProductPilotControllerApi productPilotControllerApi;

	private final ShipperMasterdataControllerApi shipperControllerApi;

	private final ForwardingDomainControllerApi forwardingDomainControllerApi;

	private final CountryControllerApi countryControllerApi;

	private final GeneralDataService generalDataService;

	public RMDValidateFixedDateDTO validateFixedDate(final RMDValidateFixedDateDTO fixedDateDTO) {
		return validateFixedDateControllerApi.validateFixedDate1(fixedDateDTO);
	}

	public RMDBranchDataDTO getBranchData(final Integer businessDomain, final Integer branchId) {
		return branchDataControllerApi.getBranchData(businessDomain, branchId);
	}

	public Boolean validateFreightTerm(final int businessDomain, final String freightTerm, final String forwardingDomain) {
		return validateTermControllerApi.validateFixedDate2(businessDomain, freightTerm, forwardingDomain);
	}

	public List<RMDAdressTypeDescriptionDTO> getAddressTypeDescription(final Locale locale, final int businessDomain) {
		return adressTypeDescriptionControllerApi.getAdressTypeDescription(generalDataService.getMasterdataLanguageForLocale(locale), businessDomain);
	}

	public List<RMDProductNameDTO> getProductName(final Locale locale, final String divisionKey, final int businessDomain) {
		return productNameControllerApi.getProductName(generalDataService.getMasterdataLanguageForLocale(locale), divisionKey, businessDomain);
	}

	public RMDValidateProductDTO validateProduct(final RMDValidateProductDTO validateProductDTO) {
		return validateProductControllerApi.validateFixedDate(validateProductDTO);
	}

	public List<RMDProductGroupDTO> getProductGroup(final int branchId, final Long customerNumber, final int businessDomain) {
		return productGroupControllerApi.getProductGroup(branchId, customerNumber, businessDomain);
	}

	public List<RMDCustomerOrderGroupDTO> getCustomerGroup(final int businessDomain, final Long customerNumber, final int branchId, final String divisionId,
			final String conditionGroup, final String forwardingDomain) {
		return customerOrderGroupControllerApi.getCustomerGroup1(businessDomain, customerNumber, branchId, divisionId, conditionGroup, forwardingDomain);
	}

	public Boolean validateCustomerOrderGroup(final int businessDomain, final Long customerFreightPayer, final Integer branchId, final String conditionGroup,
			final String forwardingDomain, final String key, final String orderGroup) {
		return customerOrderGroupControllerApi.validateCustomerOrderGroup(businessDomain, customerFreightPayer, branchId, conditionGroup, forwardingDomain, key, orderGroup);
	}

	public Boolean isThirdCountry(final int businessDomain, final String fromCountryCode, final String fromPostCode, final String toCountryCode, final String toPostCode,
			final LocalDate now) {
		return validateThirdCountryControllerApi.isThirdCountry(businessDomain, fromCountryCode, fromPostCode, toCountryCode, toPostCode, now);
	}

	public RMDRelationLabelprintingDTO retrieveRelation(final RMDRelationLabelprintingDTO relationLabelPrintingDTO) {
		return relationLabelprintingControllerApi.retrieveRelation(relationLabelPrintingDTO);
	}

	public RMDTrackablePackingAidResponseDTO categorizeTransferList(final RMDTrackablePackingAidRequestDTO trackablePackingAidRequestDTO) {
		return trackablePackingAidControllerApi.categorizeTransferList(trackablePackingAidRequestDTO);
	}

	public RMDProductPilotResponseDTO retrieveProducts(final RMDProductPilotRequestDTO rmDProductPilotRequestDTO) {
		return productPilotControllerApi.retrieveProducts(rmDProductPilotRequestDTO);
	}

	public List<RMDShipperMasterdataDTO> getShipperMasterdata(final Long shipperNumber, final Integer businessDomain) {
		return shipperControllerApi.getShipperMasterdata(shipperNumber, businessDomain);
	}

	public String getForwardingDomain(final int businessDomain, final int branchId, final String shipperCountryCode, final String consigneeCountryCode) {
		return forwardingDomainControllerApi.getForwardingDomain(businessDomain, branchId, consigneeCountryCode, shipperCountryCode);
	}

	public List<RMDCountryDTO> getCountries() {
		return countryControllerApi.getCountries();
	}
}
