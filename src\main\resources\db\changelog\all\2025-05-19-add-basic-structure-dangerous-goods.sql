-- liquibase formatted sql

-- changeset mvogt:2025-05-19-add-basic-structure-dangerous-goods.sql

-- DANGEROUS_GOOD
CREATE TABLE order_dangerous_good
(
    id                  bigint identity
        constraint pk_dangerous_good
            primary key,
    sorting_position    int,
    dangerous_good_type tinyint not null,
    road_order_line_id  BIGINT
);


-- UN NUMBER DATA SET
CREATE TABLE order_dangerous_good_data_item
(
    id                      bigint identity
        constraint pk_un_number_data_set
            primary key,
    un_number               nvarchar(4),
    description             nvarchar(255),
    packing_group           nvarchar(25) not null,
    main_danger             nvarchar(255),
    nos_required            bit not null default 0,
    classification_code     nvarchar(3),
    subsidiary_hazard_one   int,
    subsidiary_hazard_two   int,
    subsidiary_hazard_three int
);

-- UN NUMBER DANGEROUS GOOD
CREATE TABLE order_adr_dangerous_good
(
    id bigint
        constraint pk_un_number_dangerous_good
            primary key
        constraint fk_un_number_dangerous_good_id
            references order_dangerous_good,
    dangerous_good_data_item_id bigint,
    nos                       nvarchar(255),
    no_of_packages            int,
    packaging_key             nvarchar(25),
    gross_mass                DECIMAL(8, 3),
    environmentally_hazardous bit not null default 0
);

ALTER TABLE order_adr_dangerous_good
    ADD CONSTRAINT fk_adr_dangerous_good_data_set
        FOREIGN KEY (dangerous_good_data_item_id)
            REFERENCES order_dangerous_good_data_item (id)
            ON DELETE CASCADE;

-- LQ DANGEROUS GOOD
CREATE TABLE order_lq_dangerous_good
(
    id bigint
        constraint pk_lq_dangerous_good
            primary key
        constraint fk_lq_dangerous_good_id
            references order_dangerous_good,
    dangerous_good_data_item_id bigint,
    nos                   nvarchar(255),
    gross_mass            DECIMAL(8, 3)
);

ALTER TABLE order_lq_dangerous_good
    ADD CONSTRAINT fk_lq_dangerous_good_data_set
        FOREIGN KEY (dangerous_good_data_item_id)
            REFERENCES order_dangerous_good_data_item (id)
            ON DELETE CASCADE;

-- EQ DANGEROUS GOOD
CREATE TABLE order_eq_dangerous_good
(
    id bigint
        constraint pk_eq_dangerous_good
            primary key
        constraint fk_eq_dangerous_good_id
            references order_dangerous_good,
    no_of_packages        int,
    packaging_key         nvarchar(25)
);
