package com.dachser.dfe.book.order.road;

import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.order.road.validation.ValidRoadOrderLine;
import com.dachser.dfe.book.order.validation.CompleteOrderValidation;
import com.dachser.dfe.book.order.validation.common.ValidationGroup;
import com.dachser.dfe.book.order.validation.road.CompleteOrderValidationRoadForwarding;
import com.dachser.dfe.book.order.validation.road.EkaerValid;
import com.dachser.dfe.book.order.validation.road.OrderGroupValid;
import com.dachser.dfe.book.order.validation.road.ProductValid;
import com.dachser.dfe.book.order.validation.road.UitValid;
import com.dachser.dfe.book.order.validation.road.ValidRoadCollectionTimeSlot;
import com.dachser.dfe.book.order.validation.road.ValidRoadOrder;
import com.dachser.dfe.book.order.validation.road.delivery.ValidDelivery;
import com.dachser.dfe.book.quote.RoadQuoteInformation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.SQLRestriction;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "order_road")
@EkaerValid(groups = CompleteOrderValidation.class)
@UitValid(groups = CompleteOrderValidation.class)
@ProductValid(groups = CompleteOrderValidation.class)
@OrderGroupValid(groups = CompleteOrderValidation.class)
@ValidRoadOrder(groups = CompleteOrderValidation.class)
@ValidRoadCollectionTimeSlot(groups = CompleteOrderValidation.class)
@ValidRoadOrderLine(groups = CompleteOrderValidation.class)
@ValidDelivery(groups = CompleteOrderValidation.class)
@ValidationGroup(validationGroupClass = CompleteOrderValidation.class)
public class RoadOrder extends Order {

	@Enumerated(EnumType.STRING)
	@NotNull
	private Division division;

	@Size(max = 1)
	@NotNull(groups = CompleteOrderValidation.class, message = "{label.text.invalid_input}")
	private String product;

	private LocalDate fixDate;

	private Boolean tailLiftDelivery;

	private Boolean labelPrinted;

	private Integer shipmentInformationHash;

	private Boolean transferlistPrinted;

	private Boolean ekaerNotRequired = Boolean.FALSE;

	@Enumerated(EnumType.STRING)
	private DeliveryOptions deliveryOption;

	@Size(max = 1)
	private String orderGroup;

	private Boolean frostProtection;

	@Size(max = 3)
	@NotNull(groups = CompleteOrderValidationRoadForwarding.class, message = "{label.text.invalid_input}")
	private String freightTerm;

	@ToString.Exclude
	@OneToMany(mappedBy = "order", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@Valid
	@OrderBy("number")
	@SQLRestriction("packing_position_id IS NULL")
	private List<RoadOrderLine> orderLines;

	@ToString.Exclude
	@OneToMany(mappedBy = "order", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@Valid
	private List<RoadOrderReference> orderReferences;

	@OneToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "delivery_contact_id")
	private OrderContact deliveryContact;

	@ToString.Exclude
	@OneToMany(mappedBy = "order", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@OrderBy("id")
	@Size(max = 999, groups = CompleteOrderValidation.class, message = "{label.text.invalid_input}")
	@Valid
	private List<PackingPosition> packingPositions;

	@Size(max = 35)
	private String orderNumber;

	private boolean customsGoods = false;

	public void addOrderReference(RoadOrderReference roadOrderReference) {
		if (orderReferences == null) {
			orderReferences = new ArrayList<>();
		}
		orderReferences.add(roadOrderReference);
		roadOrderReference.setOrder(this);
	}

	public void addOrderLine(RoadOrderLine orderLine) {
		if (orderLines == null) {
			orderLines = new ArrayList<>();
		}
		orderLines.add(orderLine);
		orderLine.setOrder(this);
	}

	public Integer getTotalOrderWeight() {
		int totalOrderWeight = 0;
		if (orderLines != null) {
			totalOrderWeight += orderLines.stream().filter(RoadOrderLine::isNotAssignedToPackingPosition).mapToInt(RoadOrderLine::getWeightForCount).sum();
		}
		if (packingPositions != null) {
			totalOrderWeight += packingPositions.stream().map(PackingPosition::getOrderLines).filter(Objects::nonNull).flatMap(List::stream)
					.mapToInt(RoadOrderLine::getWeightForCount).sum();
		}
		return totalOrderWeight;
	}

	public double getTotalOrderVolume() {
		double totalOrderVolume = 0.0;
		if (orderLines != null) {
			totalOrderVolume += orderLines.stream().filter(RoadOrderLine::isNotAssignedToPackingPosition).map(RoadOrderLine::getVolume).filter(Objects::nonNull)
					.mapToDouble(BigDecimal::doubleValue).sum();
		}
		if (packingPositions != null) {
			totalOrderVolume += packingPositions.stream().map(PackingPosition::getOrderLines).filter(Objects::nonNull).flatMap(List::stream).map(RoadOrderLine::getVolume)
					.filter(Objects::nonNull).mapToDouble(BigDecimal::doubleValue).sum();
		}
		return totalOrderVolume;
	}

	public int getTotalAmountPackages() {
		int totalAmountPackages = 0;
		if (orderLines != null) {
			totalAmountPackages += orderLines.stream().filter(RoadOrderLine::isNotAssignedToPackingPosition).mapToInt(RoadOrderLine::getQuantity).sum();
		}
		if (packingPositions != null) {
			totalAmountPackages += packingPositions.stream().map(PackingPosition::getQuantity).mapToInt(Integer::intValue).sum();
		}

		return totalAmountPackages;
	}

	@Transient
	public boolean isDangerousGoodsOrder() {
		return orderLines != null && orderLines.stream().anyMatch(RoadOrderLine::hasDangerousGoods);
	}

	@Override
	public OrderType getOrderType() {
		return OrderType.ROADFORWARDINGORDER;
	}

	// Provide a getter for the quote information of a road order - this is absolutely necessary for mapper to determine types correctly and build json correctly in the following serialization
	@Override
	public RoadQuoteInformation getQuoteInformation() {
		return (RoadQuoteInformation) super.getQuoteInformation();
	}

	public boolean isEPricingQuote() {
		return this.getOrderReferences().stream().anyMatch(orderReference -> orderReference.getReferenceType().equals(ReferenceType.DAILY_PRICE_REFERENCE));
	}

	/**
	 * Check if the order has a valid expiry date set from quote.
	 * <p>
	 * Case 1: ePricingQuote == true and expiryDate != null > return true (valid Quote)
	 * Case 2: ePricingQuote == true and expiryDate == null > return false (invalid Quote)
	 * <p>
	 * Case 3: ePricingQuote == false and expiryDate == null > return true (valid Quote)
	 * Case 4: ePricingQuote == false and expiryDate != null > return false (invalid Quote)
	 *
	 * @return true if the quote expiry date is invalid, false otherwise
	 */
	public boolean hasValidExpiryDateFromQuote() {
		return isEPricingQuote() == (getOrderExpiryDate() != null);
	}
}
