package com.dachser.dfe.book.order.validation.road.delivery;

import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.validation.Messages;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@RequiredArgsConstructor
public class DeliveryContactValidator implements ConstraintValidator<DeliveryContactValid, RoadOrder> {

	private static final String DELIVERY_CONTACT = "deliveryContact";

	private final Translator translator;

	@Override
	public boolean isValid(RoadOrder roadOrder, ConstraintValidatorContext context) {
		context.disableDefaultConstraintViolation();
		if (roadOrder.getDeliveryOption() != DeliveryOptions.NO && roadOrder.getDeliveryOption() != null) {
			boolean result = validateBasicInput(roadOrder, context);
			result &= validateBasedOnOption(roadOrder, context);
			return result;
		}
		return true;
	}

	private boolean validateBasedOnOption(RoadOrder roadOrder, ConstraintValidatorContext context) {
		final OrderContact orderContact = getOrderContactNullSafe(roadOrder);
		if (orderContact == null) {
			return false;
		}
		if (DeliveryOptions.AP.equals(roadOrder.getDeliveryOption()) || DeliveryOptions.AS.equals(roadOrder.getDeliveryOption())) {
			boolean emailPresent = StringUtils.isNotEmpty(orderContact.getEmail());
			boolean mobilePresent = StringUtils.isNotEmpty(orderContact.getMobile());
			if (emailPresent || mobilePresent) {
				return true;
			} else {
				// "email and mobile is not present, one of both needs to be set"
				context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.MOBILE_EMAIL_INVALID)).addPropertyNode(DELIVERY_CONTACT).addPropertyNode("email")
						.addConstraintViolation();
			}
		} else if (DeliveryOptions.AT.equals(roadOrder.getDeliveryOption()) || DeliveryOptions.AC.equals(roadOrder.getDeliveryOption())) {
			boolean phonePresent = StringUtils.isNotEmpty(orderContact.getTelephone());
			boolean mobilePresent = StringUtils.isNotEmpty(orderContact.getMobile());
			if (phonePresent || mobilePresent) {
				return true;
			} else {
				// "phone and mobile is not present, one of both needs to be set"
				context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.MOBILE_PHONE_INVALID)).addPropertyNode(DELIVERY_CONTACT).addPropertyNode("phone")
						.addConstraintViolation();
			}
		}
		return false;
	}

	private boolean validateBasicInput(RoadOrder roadOrder, ConstraintValidatorContext context) {
		final OrderContact orderContact = getOrderContactNullSafe(roadOrder);
		if (orderContact == null) {
			// contact is missing though required as soon as a delivery option is set
			context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode(DELIVERY_CONTACT).addConstraintViolation();
			return false;
		} else {
			final boolean empty = StringUtils.isEmpty(orderContact.getName());
			if (empty) {
				// contact name is missing though required as soon as a delivery option is set
				context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT)).addPropertyNode(DELIVERY_CONTACT).addPropertyNode("name")
						.addConstraintViolation();
			}
			return !empty;
		}
	}

	private static OrderContact getOrderContactNullSafe(RoadOrder roadOrder) {
		return roadOrder.getDeliveryContact() != null ? roadOrder.getDeliveryContact() : null;
	}
}
