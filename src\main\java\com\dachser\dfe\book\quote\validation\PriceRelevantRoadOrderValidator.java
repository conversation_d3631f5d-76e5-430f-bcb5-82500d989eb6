package com.dachser.dfe.book.quote.validation;

import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.quote.RoadQuoteInformation;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
@Getter
abstract class PriceRelevantRoadOrderValidator<O extends RoadOrder> implements PriceRelevantFieldsValidator<RoadQuoteInformation, O> {

	protected static final String PRINCIPAL_SELECTION = "principalSelection";

	private static final String DIVISION = "division";

	private static final String FREIGHT_TERM = "freightTerm";

	private static final String PRODUCT = "product";

	private static final String ORDER_REFERENCE = "orderReference";

	private static final String TAILLIFT = "tailLift";

	private static final String FROSTPROTECT = "frostProtected";

	private static final String DELIVERY_OPTION = "deliveryOption";

	private final PriceRelevantOrderLineFieldsValidator<RoadOrderLine> orderLineValidator;

	private final PriceRelevantPackingPositionFieldsValidator<PackingPosition> packingPositionValidator;

	private final PriceRelevantAddressFieldsValidator addressValidator;

	abstract List<PriceRelevantChange> getOrderSpecificChanges(RoadQuoteInformation quoteInformation, O order);

	@Override
	public List<PriceRelevantChange> getPriceRelevantChanges(RoadQuoteInformation quoteInformation, O order) {
		List<PriceRelevantChange> priceRelevantChanges = new ArrayList<>();

		//@formatter:off
		List<Pair<String, Boolean>> roadFields = new ArrayList<>(List.of(
				Pair.of(FREIGHT_TERM, Objects.equals(quoteInformation.getTermCode(), order.getFreightTerm())),
				Pair.of(DIVISION, Objects.equals(quoteInformation.getDivision(), order.getDivision())),
				Pair.of(PRODUCT, Objects.equals(quoteInformation.getProduct(), String.valueOf(order.getProduct()))),
				Pair.of(TAILLIFT, Objects.equals(quoteInformation.getTailLift(), order.getTailLiftDelivery())),
				Pair.of(FROSTPROTECT, Objects.equals(quoteInformation.getFrostProtected(), order.getFrostProtection())),
				Pair.of(DELIVERY_OPTION, Objects.equals(quoteInformation.getDeliveryOption(), order.getDeliveryOption())
						|| (quoteInformation.getDeliveryOption() == null && Objects.equals(order.getDeliveryOption(), DeliveryOptions.NO))
						|| (order.getDeliveryOption() == null && Objects.equals(quoteInformation.getDeliveryOption(), DeliveryOptions.NO))),
				Pair.of(ORDER_REFERENCE, StringUtils.isBlank(quoteInformation.getQuoteReference())
						|| (order.getOrderReferences().stream().anyMatch(ref -> Objects.equals(ref.getReferenceType(), ReferenceType.DAILY_PRICE_REFERENCE)
							&& Objects.equals(ref.getReference(), quoteInformation.getQuoteReference())))))
		);
		//@formatter:on

		priceRelevantChanges.addAll(buildPriceRelevantChangesFromFields(roadFields));

		priceRelevantChanges.addAll(orderLineValidator.getPriceRelevantChanges(quoteInformation.getOrderLineItems(), order.getOrderLines()));

		priceRelevantChanges.addAll(packingPositionValidator.getPriceRelevantChanges(quoteInformation.getPackingPositions(), order.getPackingPositions()));

		return priceRelevantChanges;
	}
}
