-- liquibase formatted sql

-- changeset joergw:2024-03-27-shippers-reference_mssql_2
-- validCheckSum: 9:81e637529b16e3b0c849d33ff091abd7

BEGIN
    declare @constraint_name nvarchar(128)
    select @constraint_name = name FROM SYS.default_constraints WHERE parent_object_id = (SELECT object_id FROM SYS.COLUMNS WHERE object_id = OBJECT_ID('[${default-schema}].[order_sea]') and name = 'unloading') AND parent_column_id = (SELECT column_id FROM SYS.COLUMNS WHERE object_id = OBJECT_ID('[${default-schema}].[order_sea]') and name = 'unloading')
    exec('ALTER TABLE order_sea DROP CONSTRAINT IF EXISTS ' + @constraint_name)
END

