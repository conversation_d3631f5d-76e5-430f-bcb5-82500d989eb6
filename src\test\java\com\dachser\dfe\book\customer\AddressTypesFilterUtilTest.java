package com.dachser.dfe.book.customer;

import com.dachser.dfe.book.model.FurtherAddressType;
import com.dachser.dfe.book.user.model.OrderOptions;
import com.dachser.dfe.book.user.model.RoadCustomer;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertTrue;

class AddressTypesFilterUtilTest {

	@Nested
	class GetFilterForRoadCustomer {
		@Test
		void allFurtherAddressOptionsAvailable() {
			final OrderOptions orderOptions = new OrderOptions();
			orderOptions.setCoverAddress(true);
			orderOptions.setDeconsolidatorAddress(true);
			orderOptions.setDifferingInvoiceAddress(true);
			orderOptions.setFinalDeliveryAddress(true);
			orderOptions.setConsigneeExportCertification(true);

			final RoadCustomer roadCustomer = new RoadCustomer();
			roadCustomer.setOrderOptions(orderOptions);
			final List<String> filterForRoadCustomer = AddressTypesFilterUtil.getFilterForRoadCustomer(Optional.of(roadCustomer));

			assertTrue(filterForRoadCustomer.isEmpty());
		}

		@Test
		void noFurtherAddressOptionsAvailable() {
			final OrderOptions orderOptions = new OrderOptions();
			orderOptions.setCoverAddress(false);
			orderOptions.setDeconsolidatorAddress(false);
			orderOptions.setDifferingInvoiceAddress(false);
			orderOptions.setFinalDeliveryAddress(false);
			orderOptions.setConsigneeExportCertification(false);

			final RoadCustomer roadCustomer = new RoadCustomer();
			roadCustomer.setOrderOptions(orderOptions);
			final List<String> filterForRoadCustomer = AddressTypesFilterUtil.getFilterForRoadCustomer(Optional.of(roadCustomer));

			Arrays.stream(FurtherAddressType.values()).filter(furtherAddressType -> !furtherAddressType.equals(FurtherAddressType.IMPORTER))
					.forEach(furtherAddressType -> assertTrue(filterForRoadCustomer.contains(furtherAddressType.getKey())));
		}

		@Test
		void roadCustomerOptionalIsEmpty() {
			final List<String> filterForRoadCustomer = AddressTypesFilterUtil.getFilterForRoadCustomer(Optional.empty());

			assertTrue(filterForRoadCustomer.isEmpty());
		}
	}
}