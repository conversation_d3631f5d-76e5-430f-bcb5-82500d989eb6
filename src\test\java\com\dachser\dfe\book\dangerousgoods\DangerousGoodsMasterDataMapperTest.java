package com.dachser.dfe.book.dangerousgoods;

import com.dachser.dfe.book.ResourceLoadingTest;
import com.dachser.dfe.book.model.DangerousGoodDataItemDto;
import com.dachser.masterdata.thirdparty.model.MDTDGItemADR;
import com.dachser.masterdata.thirdparty.model.MDTDangerousGoodInformation;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class DangerousGoodsMasterDataMapperTest implements ResourceLoadingTest {

	@InjectMocks
	DangerousGoodsMasterDataMapperImpl unMapper;

	@Test
	void shouldMapRoadAllPropertiesOf1791() {
		// given
		MDTDangerousGoodInformation mdtdgItemGeneralDGItemADR = buildADRItem1791();
		DangerousGoodDataItemDto expected = buildExpected1791();
		// when
		DangerousGoodDataItemDto mapped = unMapper.mapRoad(mdtdgItemGeneralDGItemADR);

		// then
		Assertions.assertEquals(expected, mapped);
	}

	@Test
	void shouldMapRoadAllPropertiesOf2988() {
		// given
		MDTDangerousGoodInformation mdtdgItemGeneralDGItemADR = buildADRItem2988();
		DangerousGoodDataItemDto expected = buildExpected2988();
		// when
		DangerousGoodDataItemDto mapped = unMapper.mapRoad(mdtdgItemGeneralDGItemADR);

		// then
		Assertions.assertEquals(expected, mapped);
	}

	@Test
	void shouldMapRoadAllPropertiesOf1714() {
		// given
		MDTDangerousGoodInformation mdtdgItemGeneralDGItemADR = buildADRItem1714();
		DangerousGoodDataItemDto expected = buildExpected1714();
		// when
		DangerousGoodDataItemDto mapped = unMapper.mapRoad(mdtdgItemGeneralDGItemADR);

		// then
		Assertions.assertEquals(expected, mapped);
	}


	private DangerousGoodDataItemDto buildExpected1791() {
		DangerousGoodDataItemDto unNumberDataSetDto = new DangerousGoodDataItemDto();
		unNumberDataSetDto.setUnNumber("1791");
		unNumberDataSetDto.setMainDanger("8");
		unNumberDataSetDto.setPackingGroup("II");
		unNumberDataSetDto.setDescription("HYPOCHLORITLÖSUNG");
		unNumberDataSetDto.setClassificationCode("C9");
		unNumberDataSetDto.setTransportCategory("2");
		unNumberDataSetDto.setTunnelCode("E");
		unNumberDataSetDto.nosRequired(false);
		unNumberDataSetDto.setDgmId("12");
		unNumberDataSetDto.setTransportCategory("2");
		unNumberDataSetDto.setTunnelCode("E");
		return unNumberDataSetDto;
	}

	private MDTDangerousGoodInformation buildADRItem1791() {
		MDTDGItemADR mdtdgItemGeneralDGItemADR = new MDTDGItemADR();
		mdtdgItemGeneralDGItemADR.setUnNumber("1791");
		mdtdgItemGeneralDGItemADR.setDgClass("8");
		mdtdgItemGeneralDGItemADR.setSubrisk(null);
		mdtdgItemGeneralDGItemADR.setSpecProv("521");
		mdtdgItemGeneralDGItemADR.setPackingGroup("II");
		mdtdgItemGeneralDGItemADR.setPsn("HYPOCHLORITLÖSUNG");
		mdtdgItemGeneralDGItemADR.setKgl("2");
		mdtdgItemGeneralDGItemADR.setHighConsequenceBulk(null);
		mdtdgItemGeneralDGItemADR.setHighConsequencePackages(null);
		mdtdgItemGeneralDGItemADR.setHighConsequenceTank(null);
		mdtdgItemGeneralDGItemADR.setNeedTechChemName(false);
		mdtdgItemGeneralDGItemADR.setLimitedQuantity("1 l");
		mdtdgItemGeneralDGItemADR.setMaxQuantity1136("333");
		mdtdgItemGeneralDGItemADR.setClassificationCode("C9");
		mdtdgItemGeneralDGItemADR.setTunnelCode("E");
		mdtdgItemGeneralDGItemADR.setLimitedQuantityAllowed(true);
		mdtdgItemGeneralDGItemADR.setExceptedQuantityAllowed(true);
		mdtdgItemGeneralDGItemADR.setTransportCategory("2");
		return new MDTDangerousGoodInformation().dgmId("12").dgItemADR(mdtdgItemGeneralDGItemADR);
	}

	private DangerousGoodDataItemDto buildExpected2988() {
		DangerousGoodDataItemDto unNumberDataSetDto = new DangerousGoodDataItemDto();
		unNumberDataSetDto.setUnNumber("2988");
		unNumberDataSetDto.setMainDanger("4.3");
		unNumberDataSetDto.setSubsidiaryHazardOne("3");
		unNumberDataSetDto.setSubsidiaryHazardTwo("8");
		unNumberDataSetDto.setSubsidiaryHazardThree("8");
		unNumberDataSetDto.setDescription("CHLORSILANE, MIT WASSER REAGIEREND, ENTZUENDBAR, AETZEND, N.A.G.");
		unNumberDataSetDto.setPackingGroup("I");
		unNumberDataSetDto.setClassificationCode("WFC");
		unNumberDataSetDto.nosRequired(false);
		unNumberDataSetDto.setDgmId("123");
		unNumberDataSetDto.setTransportCategory("0");
		unNumberDataSetDto.setTunnelCode("B/E");
		return unNumberDataSetDto;
	}

	private MDTDangerousGoodInformation buildADRItem2988() {
		MDTDGItemADR mdtdgItemGeneralDGItemADR = new MDTDGItemADR();
		mdtdgItemGeneralDGItemADR.setUnNumber("2988");
		mdtdgItemGeneralDGItemADR.setDgClass("4.3");
		mdtdgItemGeneralDGItemADR.setSubrisk("(3, 8, 8)");
		mdtdgItemGeneralDGItemADR.setSpecProv("549");
		mdtdgItemGeneralDGItemADR.setPackingGroup("I");
		mdtdgItemGeneralDGItemADR.setPsn("CHLORSILANE, MIT WASSER REAGIEREND, ENTZUENDBAR, AETZEND, N.A.G.");
		mdtdgItemGeneralDGItemADR.setKgl("2");
		mdtdgItemGeneralDGItemADR.setHighConsequenceBulk(null);
		mdtdgItemGeneralDGItemADR.setHighConsequencePackages(null);
		mdtdgItemGeneralDGItemADR.setHighConsequenceTank("3000");
		mdtdgItemGeneralDGItemADR.setNeedTechChemName(false);
		mdtdgItemGeneralDGItemADR.setLimitedQuantity("0");
		mdtdgItemGeneralDGItemADR.setMaxQuantity1136("0");
		mdtdgItemGeneralDGItemADR.setClassificationCode("WFC");
		mdtdgItemGeneralDGItemADR.setTunnelCode("B/E");
		mdtdgItemGeneralDGItemADR.setLimitedQuantityAllowed(false);
		mdtdgItemGeneralDGItemADR.setExceptedQuantityAllowed(false);
		mdtdgItemGeneralDGItemADR.setTransportCategory("0");
		return new MDTDangerousGoodInformation().dgmId("123").dgItemADR(mdtdgItemGeneralDGItemADR);

	}

	private MDTDangerousGoodInformation buildADRItem1714() {
		MDTDGItemADR mdtdgItemGeneralDGItemADR = new MDTDGItemADR();
		mdtdgItemGeneralDGItemADR.setUnNumber("1714");
		mdtdgItemGeneralDGItemADR.setDgClass("4.3");
		mdtdgItemGeneralDGItemADR.setSubrisk("(6.1)");
		mdtdgItemGeneralDGItemADR.setSpecProv(null);
		mdtdgItemGeneralDGItemADR.setPackingGroup("I");
		mdtdgItemGeneralDGItemADR.setPsn("ZINKPHOSPHID");
		mdtdgItemGeneralDGItemADR.setKgl("1");
		mdtdgItemGeneralDGItemADR.setHighConsequenceBulk(null);
		mdtdgItemGeneralDGItemADR.setHighConsequencePackages(null);
		mdtdgItemGeneralDGItemADR.setHighConsequenceTank("3000");
		mdtdgItemGeneralDGItemADR.setNeedTechChemName(false);
		mdtdgItemGeneralDGItemADR.setLimitedQuantity("0");
		mdtdgItemGeneralDGItemADR.setMaxQuantity1136("20");
		mdtdgItemGeneralDGItemADR.setClassificationCode("WT2");
		mdtdgItemGeneralDGItemADR.setTunnelCode("E");
		mdtdgItemGeneralDGItemADR.setLimitedQuantityAllowed(false);
		mdtdgItemGeneralDGItemADR.setExceptedQuantityAllowed(false);
		mdtdgItemGeneralDGItemADR.setTransportCategory("1");
		return new MDTDangerousGoodInformation().dgmId("124").dgItemADR(mdtdgItemGeneralDGItemADR);

	}

	private DangerousGoodDataItemDto buildExpected1714() {
		DangerousGoodDataItemDto unNumberDataSetDto = new DangerousGoodDataItemDto();
		unNumberDataSetDto.setUnNumber("1714");
		unNumberDataSetDto.setMainDanger("4.3");
		unNumberDataSetDto.setSubsidiaryHazardOne("6.1");
		unNumberDataSetDto.setDescription("ZINKPHOSPHID");
		unNumberDataSetDto.setPackingGroup("I");
		unNumberDataSetDto.setClassificationCode("WT2");
		unNumberDataSetDto.nosRequired(false);
		unNumberDataSetDto.setDgmId("124");
		unNumberDataSetDto.setTransportCategory("1");
		unNumberDataSetDto.setTunnelCode("E");
		return unNumberDataSetDto;
	}

}