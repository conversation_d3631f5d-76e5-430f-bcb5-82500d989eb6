package com.dachser.dfe.book.service.freightterm;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.config.term.TermSorter;
import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ErrorIdExternalServiceNotAvailableException;
import com.dachser.dfe.book.generaldata.ContainerTypeMapperImpl;
import com.dachser.dfe.book.generaldata.GeneralDataAdapterMock;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.generaldata.term.TermMapperImpl;
import com.dachser.dfe.book.model.FreightTermDto;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.sea.BaseFullContainerLoadRepository;
import com.dachser.dfe.book.service.forwardingdomain.ForwardingDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@SpringBootTest(properties = "dfe.book.mock=true", classes = { FreightTermService.class, FreightTermAdapter.class, ForwardingDomainService.class, GeneralDataService.class,
		GeneralDataAdapterMock.class, ContainerTypeMapperImpl.class, TermMapperImpl.class, TermSorter.class })
public class FreightTermServiceTest {

	@SpyBean
	private FreightTermService freightTermService;

	@SpyBean
	private FreightTermAdapterMock freightTermAdapter;

	@MockBean
	private ForwardingDomainService forwardingDomainService;

	@MockBean
	private WhiteListFilterConfiguration whiteListFilterConfiguration;

	@MockBean
	private BaseFullContainerLoadRepository repository;

	@BeforeEach
	void setup() {
		when(forwardingDomainService.getForwardingDomain(anyInt(), anyInt(), any(), any())).thenReturn("1");
		//		when(repository.findByDachserKeyInSortedByTermIdAsc(anyList())).thenReturn(generateFreightTerms());
		//		when(repository.findOneByDachserKey("011")).thenReturn(new FreightTerm(1L, "011", "EXW", "Ex Works"));
		when(whiteListFilterConfiguration.getFreightTermsFilter()).thenReturn(getFreightTermsFilter());
		when(whiteListFilterConfiguration.getTerms()).thenReturn(getTermFilter());
	}

	@Nested
	class GetValidFreightTermOptions {

		@Test
		void shouldReturnValidFreightTermOptionList() {
			final List<FreightTermDto> serviceResponse = freightTermService.getAllValidFreightTermOptions();

			assertNotNull(serviceResponse);
			assertFalse(serviceResponse.isEmpty());
			assertEquals(6, serviceResponse.size());
			FreightTermDto firstFreightTerm = serviceResponse.get(0);
			assertEquals("011", firstFreightTerm.getDachserTermKey());
			assertEquals("EXW", firstFreightTerm.getIncoTermKey());
			assertEquals("Ex Works", firstFreightTerm.getHeadline());
			assertEquals("031", serviceResponse.get(1).getDachserTermKey());
		}

		@Test
		void shouldGetFreightTermByDachserKey() {
			final FreightTermDto serviceResponse = freightTermService.getFreightTermByDachserKey("011");

			assertNotNull(serviceResponse);
			assertEquals("011", serviceResponse.getDachserTermKey());
			assertEquals("EXW", serviceResponse.getIncoTermKey());
			assertEquals("Ex Works", serviceResponse.getHeadline());
		}

		@Test
		void shouldReturnNullOnKeyNotFound() {
			final FreightTermDto serviceResponse = freightTermService.getFreightTermByDachserKey("111");

			assertNull(serviceResponse);
		}
	}

	@Nested
	class ValidateFreightTerm {

		@Test
		void shouldReturnTrueForValidFreightTerm() {
			final ForwardingOrder order = TestUtil.createInstanceNonSpring().generateForwardingOrder();

			final boolean serviceResponse = freightTermService.validateFreightTermForOrder(order, 1);

			assertTrue(serviceResponse);
		}

		@Test
		void shouldReturnFalseForInvalidFreightTerm() {
			final ForwardingOrder order = TestUtil.createInstanceNonSpring().generateForwardingOrder();
			order.setFreightTerm("INVALID");
			final boolean serviceResponse = freightTermService.validateFreightTermForOrder(order, 1);

			assertFalse(serviceResponse);
		}

		@Test
		void shouldReturnFalseForMissingFreightTerm() {
			final ForwardingOrder order = TestUtil.createInstanceNonSpring().generateForwardingOrder();
			order.setFreightTerm(null);
			final boolean serviceResponse = freightTermService.validateFreightTermForOrder(order, 1);

			assertFalse(serviceResponse);
		}

		@Test
		void shouldReturnFalseForInvalidForwardingDomain() {
			final ForwardingOrder order = TestUtil.createInstanceNonSpring().generateForwardingOrder();
			when(forwardingDomainService.getForwardingDomain(anyInt(), anyInt(), any(), any())).thenReturn(null);
			final boolean serviceResponse = freightTermService.validateFreightTermForOrder(order, 1);

			assertFalse(serviceResponse);
		}

		@Test
		void shouldReturnFalseWhenShipperAddressIsMissing() {
			final ForwardingOrder order = TestUtil.createInstanceNonSpring().generateForwardingOrder();
			order.setShipperAddress(null);
			final boolean serviceResponse = freightTermService.validateFreightTermForOrder(order, 1);

			assertFalse(serviceResponse);
		}

		@Test
		void shouldReturnFalseWhenConsigneeAddressIsMissing() {
			final ForwardingOrder order = TestUtil.createInstanceNonSpring().generateForwardingOrder();
			order.setConsigneeAddress(null);
			final boolean serviceResponse = freightTermService.validateFreightTermForOrder(order, 1);

			assertFalse(serviceResponse);
		}

		@Test
		void shouldReturnFalseWhenForwardingDomainCouldNotBeFetched() {
			when(forwardingDomainService.getForwardingDomain(anyInt(), anyInt(), any(), any())).thenThrow(
					new ErrorIdExternalServiceNotAvailableException(BookErrorId.ERR_OG_01, ""));
			final ForwardingOrder order = TestUtil.createInstanceNonSpring().generateForwardingOrder();
			final boolean serviceResponse = freightTermService.validateFreightTermForOrder(order, -1);

			assertFalse(serviceResponse);
		}

		@Test
		void shouldReturnFalseWhenFreightTermAdapterFails() {
			final ForwardingOrder order = TestUtil.createInstanceNonSpring().generateForwardingOrder();
			when(forwardingDomainService.getForwardingDomain(anyInt(), anyInt(), any(), any())).thenReturn("1");
			final boolean serviceResponse = freightTermService.validateFreightTermForOrder(order, -1);

			assertFalse(serviceResponse);
		}
	}

	private WhiteListFilterConfiguration.FreightTermsFilter getFreightTermsFilter() {
		WhiteListFilterConfiguration.FreightTermsFilter freightTermsFilter = new WhiteListFilterConfiguration.FreightTermsFilter();
		freightTermsFilter.setRoad(List.of("011", "031", "081", "082", "083", "084"));
		return freightTermsFilter;
	}

	private WhiteListFilterConfiguration.TermFilter getTermFilter() {
		WhiteListFilterConfiguration.TermFilter termFilter = new WhiteListFilterConfiguration.TermFilter();
		termFilter.setFreight(List.of("011", "031", "081", "082", "083", "084"));
		termFilter.setInco(List.of());
		return termFilter;
	}
}
