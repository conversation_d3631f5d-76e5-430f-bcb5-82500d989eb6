package com.dachser.dfe.book.order.validation.road.delivery;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import org.hibernate.validator.constraints.CompositionType;
import org.hibernate.validator.constraints.ConstraintComposition;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({ ElementType.TYPE, ElementType.ANNOTATION_TYPE })
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = { })
@ConstraintComposition(CompositionType.AND)
@DeliveryDateValid
@DeliveryOptionValid
@DeliveryContactValid
public @interface ValidDelivery {

	String message() default "{com.dachser.dfe.book.validation.road.ValidDelivery.message}";

	Class<?>[] groups() default {};

	Class<? extends Payload>[] payload() default {};

}
