package com.dachser.dfe.book.order.clone;

import com.dachser.dfe.book.model.RoadForwardingOrderDto;
import com.dachser.dfe.book.order.DraftOrderProcessor;
import com.dachser.dfe.book.order.OrderRepositoryFacade;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ForwardingOrderCloner extends OrderCloner<ForwardingOrder, RoadForwardingOrderDto> {

	@Autowired
	public ForwardingOrderCloner(DraftOrderProcessor draftOrderProcessor, OrderMapper orderMapper, OrderRepositoryFacade orderRepositoryFacade) {
		super(draftOrderProcessor, orderMapper, orderRepositoryFacade);
	}

	@Override
	void beforeClone(RoadForwardingOrderDto order) {
		order.setReferences(null);
		order.getOrderLineItems().forEach(this::clearRoadOrderLineItemIds);
		order.getPackingPositions().forEach(this::clearPackingPositionIds);
	}

	@Override
	void afterClone(ForwardingOrder clonedOrder) {
		clonedOrder.setAdviceSent(null);
	}


}
