package com.dachser.dfe.book.quote;

import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.options.CollectionOptions;
import com.dachser.dfe.book.model.options.DeliveryOptions;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;

@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "quote_information_road")
@Valid
public class RoadQuoteInformation extends QuoteInformation {

	@Column(updatable = false)
	private OffsetDateTime quoteExpiryDate;

	@NotNull
	@Size(max = 1, min = 1)
	@Column(updatable = false, length = 1)
	private String product;

	@Enumerated(EnumType.STRING)
	@NotNull
	@Column(updatable = false, length = 1)
	private Division division;

	@Column(name = "tail_lift", updatable = false)
	private Boolean tailLift;

	@Column(name = "frost_protected", updatable = false)
	private Boolean frostProtected;

	@Column(name = "self_collector", updatable = false)
	private Boolean selfCollector;

	@DecimalMin("0.01")
	@DecimalMax("99.99")
	private BigDecimal palletLocations;

	@Enumerated(EnumType.STRING)
	private DeliveryOptions deliveryOption;

	@Enumerated(EnumType.STRING)
	private CollectionOptions collectionOption;

	@OneToMany(mappedBy = "quoteInformation", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@Valid
	private List<QuotePackingPosition> packingPositions;

	private boolean cashOnDelivery;

	@DecimalMin("0.01")
	@DecimalMax("99999.99")
	// is used for the cash on delivery amount in EUR, currently no other currency is supported, so it is implicitly EUR
	private BigDecimal cashOnDeliveryAmount;
}
