package com.dachser.dfe.book.advice;

import com.dachser.dfe.book.exception.AdviceSubmitFailedException;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.service.Interruptible;
import com.dachser.dfe.legacy.advice.bean.AdvisedOrder;
import com.dachser.dfe.legacy.advice.bean.DfeLegacyAdviceExecutionCode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
@ComponentScan(basePackages = "com.dachser.dfe.legacy.advice.web.client.configuration")
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "false", matchIfMissing = true)
@Interruptible
class AdviceAdapterExt implements AdviceAdapter {

	private final AdvisedOrderMapper mapper;

	// This is required for the JSON serialization of the advised order to use the correct date format
	private final ObjectMapper dtoObjectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

	private final com.dachser.dfe.legacy.advice.service.api.AdviceService adviceService;

	@Override
	public boolean submitAdvice(ForwardingOrder order, String legacyStatus) throws AdviceSubmitFailedException {
		AdvisedOrder advisedOrder = mapper.map(order);
		advisedOrder.setStatusCode(legacyStatus);
		try {
			DfeLegacyAdviceExecutionCode serviceResponse = adviceService.pushAdvice(advisedOrder);
			final boolean wasAdviceAccepted = serviceResponse != DfeLegacyAdviceExecutionCode.REJECTED;
			if (wasAdviceAccepted) {
				log.info("Advice data submitted for order {} with status {}", order.getOrderId(), serviceResponse.name());
			} else {
				log.error("Advice data rejected for order {}", order.getOrderId());
			}
			return wasAdviceAccepted;
		} catch (Exception e) {
			throw new AdviceSubmitFailedException("External service error", e);
		}
	}
}
