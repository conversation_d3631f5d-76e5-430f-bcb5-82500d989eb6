package com.dachser.dfe.book.mapper;

import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.jpa.entity.VOrderOverview;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.order.air.AirOrderReference;
import com.dachser.dfe.book.order.common.AirSeaOrderReferenceType;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import com.dachser.dfe.book.order.sea.SeaOrderReference;
import com.dachser.dfe.trackandtrace.model.TTASLOrder;
import com.dachser.dfe.trackandtrace.model.TTOrderReference;
import com.dachser.dfe.trackandtrace.model.TTOrderType;
import com.dachser.dfe.trackandtrace.model.TTRoadOrder;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Stream;

import static com.dachser.dfe.book.user.UserServiceMock.VALID_CUST_NO_ASL;
import static com.dachser.dfe.book.user.UserServiceMock.VALID_CUST_NO_ROAD;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
@SpringBootTest(classes = { OrderOverviewMapperImpl.class })
public class OrderOverviewMapperTest {

	OffsetDateTime DEFAULT_DATE = OffsetDateTime.of(1970, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC);

	@Autowired
	private OrderOverviewMapper mapper;

	@TestInstance(TestInstance.Lifecycle.PER_CLASS)
	@Nested
    class ValidMapping {

		@ParameterizedTest
		@MethodSource("provideRoadOrderMappingMethods")
        void mapRoad(Function<VOrderOverview, TTRoadOrder> mappingFunction) {
            VOrderOverview overview = generateBasicOverview();
            overview.setOrderType(1);
            setCustomerNumber(overview, VALID_CUST_NO_ROAD);
			TTRoadOrder road = mappingFunction.apply(overview);
            Assertions.assertEquals(overview.getValueOfGoods(), road.getGoodsValue());
            // Further Addresses, directDelivery, division, typeOfGoods not in view - comes with v2
			assertThat(road.getFavouriteStatus()).isEqualTo(overview.getFavouriteStatus());
			assertThat(road.getPickupDateTime().getFrom()).isEqualTo(overview.getPickUpDateFrom() != null ? overview.getPickUpDateFrom() : null);
			assertThat(road.getPickupDateTime().getTo()).isEqualTo(overview.getPickUpDateTo() != null ? overview.getPickUpDateTo() : null);
			OrderAddress collectionOrderDto = overview.getCollectionAddress();
			assertThat(collectionOrderDto.getName2()).isEqualTo("name2");
			OrderAddress consigneeAddressDto = overview.getCollectionAddress();
			assertThat(consigneeAddressDto.getName2()).isEqualTo("name2");
			assertThat(road.getWeight()).isEqualTo(overview.getWeight());
			assertThat(road.getContainsDangerousGoods()).isEqualTo(overview.getContainsDangerousGoods());
			assertThat(road.getProduct().getCode()).isEqualTo(overview.getProduct());
			assertThat(road.getProduct().getDescription()).isEqualTo(overview.getProductName());
			assertThat(road.getQuantity()).isEqualTo(overview.getQuantity());
			assertThat(road.getTransport()).isEqualTo(overview.getTransport());
			assertThat(road.getOrderSent()).isEqualTo(overview.getOrderSent());
			assertThat(road.getPickupDateTime().getType().getCode()).isEqualTo(overview.getPickUpDateType());
			assertThat(road.getGoodsCurrency()).isEqualTo(overview.getGoodsCurrency());
			assertThat(road.getShipmentNumber()).isEqualTo(overview.getShipmentNumber());
			assertThat(road.getReferences().get(0).getLabel()).isNull();
			assertThat(road.getDivision()).isEqualTo("T");
			assertThat(road.getExpirationTime()).isEqualTo(overview.getQuoteExpirationTime());
			OffsetDateTime overviewLastModified = mapper.mapLastModified(overview.getLastModified());
			assertThat(road.getLastModified()).isEqualTo(overviewLastModified);
        }

		@Test
		void mapAirImport() {
			VOrderOverview overview = generateBasicOverview();
			overview.setPortDeparture("FRA");
			overview.setPortDestination("MUC");
			setCustomerNumber(overview, VALID_CUST_NO_ASL);
			TTASLOrder asl = mapper.mapASL(overview);
			// port of loading is now removed - will be added with DFE3-51
			Assertions.assertEquals(overview.getFavouriteStatus(), asl.getFavouriteStatus());
			Assertions.assertEquals(overview.getPickUpDateFrom() != null ? overview.getPickUpDateFrom() : null, asl.getPickupDateTime().getFrom());
			Assertions.assertEquals(overview.getPickUpDateTo() != null ? overview.getPickUpDateTo() : null, asl.getPickupDateTime().getTo());
			Assertions.assertEquals(overview.getRequestArrangement(), asl.getPickupDateTime().getRequestArrangement());
			OrderAddress collectionOrderDto = overview.getCollectionAddress();
			Assertions.assertEquals("name2", collectionOrderDto.getName2());
			OrderAddress consigneeAddressDto = overview.getCollectionAddress();
			Assertions.assertEquals("name2", consigneeAddressDto.getName2());
			Assertions.assertEquals(overview.getWeight(), asl.getWeight());
			Assertions.assertEquals(overview.getContainsDangerousGoods(), asl.getContainsDangerousGoods());
			Assertions.assertEquals(overview.getProduct(), asl.getProduct().getCode());
			Assertions.assertEquals(overview.getProductName(), asl.getProduct().getDescription());
			Assertions.assertEquals(overview.getQuantity(), asl.getQuantity());
			Assertions.assertEquals(overview.getTransport(), asl.getTransport());
			Assertions.assertEquals(overview.getOrderSent(), asl.getOrderSent());
			Assertions.assertEquals(overview.getPickUpDateType(), asl.getPickupDateTime().getType().getCode());
			Assertions.assertEquals("T", asl.getDivision());
			Assertions.assertEquals(overview.getQuoteExpirationTime(), asl.getExpirationTime());
			Assertions.assertNotNull(asl.getPortInfo());
			Assertions.assertEquals(overview.getPortDeparture(), asl.getPortInfo().getFrom().getCode());
			Assertions.assertEquals(overview.getPortDestination(), asl.getPortInfo().getTo().getCode());
			OffsetDateTime overviewLastModified = mapper.mapLastModified(overview.getLastModified());
			Assertions.assertEquals(overviewLastModified, asl.getLastModified());
		}

		@ParameterizedTest
		@MethodSource("provideRoadOrderMappingMethods")
		void mapToRoadShouldHandleAllNullValues(Function<VOrderOverview, TTRoadOrder> mappingFunction) {
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderType(1);
			setCustomerNumber(overview, VALID_CUST_NO_ROAD);
			try {
				mappingFunction.apply(overview);
			} catch (Exception e) {
				Assertions.fail("Mapping is not null save. Cause: " + e.getMessage());
			}

		}

		@Test
		void mapToASLShouldHandleAllNullValuesASL() {
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderType(4);
			setCustomerNumber(overview, VALID_CUST_NO_ASL);
			try {
				mapper.mapASL(overview);
			} catch (Exception e) {
				Assertions.fail("Mapping is not null save. Cause: " + e.getMessage());
			}

		}

		@ParameterizedTest
		@MethodSource("provideRoadOrderMappingMethods")
		void mapToASLShouldHandleAllNullValuesRoad(Function<VOrderOverview, TTRoadOrder> mappingFunction) {
			VOrderOverview overview = new VOrderOverview();
			overview.setOrderType(2);
			setCustomerNumber(overview, VALID_CUST_NO_ROAD);
			try {
				mappingFunction.apply(overview);
			} catch (Exception e) {
				Assertions.fail("Mapping is not null save. Cause: " + e.getMessage());
			}
		}

		@Test
		void canConvertReferencesToList() {
			testReferenceMapping("A1,A2,A3", 3);
		}

		@Test
		void referenceDoesNotFailWithEmptyString() {
			testReferenceMapping("", 0);
		}

		@Test
		void canFilterEmptyRefStrings() {
			testReferenceMapping("A1,A2,A3,,", 3);
		}

		@Test
		void shouldMapReferencesRoad() {
			// given
			List<RoadOrderReference> references = generateReferences(ReferenceType.EKAER_NUMBER, ReferenceType.PURCHASE_ORDER_NUMBER,  ReferenceType.ORDER_NUMBER, ReferenceType.BOOKING_REFERENCE, ReferenceType.IDENTIFICATION_CODE_TRANSPORT);
			// when
			List<TTOrderReference> result = mapper.mapReferencesRoad(references);
			// then
			final RoadOrderReference ekaerIn = references.stream().filter(r -> r.getReferenceType().equals(ReferenceType.EKAER_NUMBER)).findFirst().orElse(null);
			final RoadOrderReference ponIn = references.stream().filter(r -> r.getReferenceType().equals(ReferenceType.PURCHASE_ORDER_NUMBER)).findFirst().orElse(null);
			final RoadOrderReference onIn = references.stream().filter(r -> r.getReferenceType().equals(ReferenceType.ORDER_NUMBER)).findFirst().orElse(null);
			final RoadOrderReference brIn = references.stream().filter(r -> r.getReferenceType().equals(ReferenceType.BOOKING_REFERENCE)).findFirst().orElse(null);
			final RoadOrderReference transportCodeIn = references.stream().filter(r -> r.getReferenceType().equals(ReferenceType.IDENTIFICATION_CODE_TRANSPORT)).findFirst().orElse(null);
			final TTOrderReference ekaerOut = result.stream().filter(r -> r.getReferenceCode().equals(TTOrderReference.ReferenceCodeEnum.EKAER_NUMBER)).findFirst()
					.orElse(null);
			final TTOrderReference ponOut = result.stream().filter(r -> r.getReferenceCode().equals(TTOrderReference.ReferenceCodeEnum.PURCHASE_ORDER_NUMBER)).findFirst()
					.orElse(null);
			final TTOrderReference onOut = result.stream().filter(r -> r.getReferenceCode().equals(TTOrderReference.ReferenceCodeEnum.ORDER_NUMBER)).findFirst()
					.orElse(null);
			final TTOrderReference brOut = result.stream().filter(r -> r.getReferenceCode().equals(TTOrderReference.ReferenceCodeEnum.BOOKING_REFERENCE_LABEL)).findFirst()
					.orElse(null);
			final TTOrderReference transportCodeOut = result.stream().filter(r -> r.getReferenceCode().equals(TTOrderReference.ReferenceCodeEnum.IDENTIFICATION_CODE_TRANSPORT)).findFirst()
					.orElse(null);
			assertThat(result).hasSize(5);
			assertThat(ekaerOut).isNotNull();
			assertThat(ekaerOut.getReferenceCode().getValue()).isEqualTo(ReferenceType.EKAER_NUMBER.getDfeValue());
			assertThat(ekaerOut.getLabel()).isNull();
			assertThat(ekaerIn).isNotNull();
			assertThat(ponOut).isNotNull();
			assertThat(ekaerOut.getValue()).isEqualTo(List.of(ekaerIn.getReference()));
			assertThat(ponOut.getReferenceCode().getValue()).isEqualTo(ReferenceType.PURCHASE_ORDER_NUMBER.getDfeValue());
			assertThat(ponOut.getLabel()).isNull();
			assertThat(ponIn).isNotNull();
			assertThat(ponOut.getValue()).isEqualTo(List.of(ponIn.getReference()));
			assertThat(onOut).isNotNull();
			assertThat(onOut.getReferenceCode().getValue()).isEqualTo(ReferenceType.ORDER_NUMBER.getDfeValue());
			assertThat(onOut.getLabel()).isNull();
			assertThat(onIn).isNotNull();
			assertThat(onOut.getValue()).isEqualTo(List.of(onIn.getReference()));
			assertThat(brIn).isNotNull();
			assertThat(brOut.getValue()).isEqualTo(List.of(brIn.getReference()));
			assertThat(brOut.getLabel()).isNull();
			assertThat(transportCodeOut.getValue()).isEqualTo(List.of(transportCodeIn.getReference()));
			assertThat(transportCodeOut.getLabel()).isNull();
		}

		@Test
		void shouldNotMapForbiddenRoadReferences() {
			List<RoadOrderReference> forbiddenReferences = generateReferences(ReferenceType.DAILY_PRICE_REFERENCE);

			List<TTOrderReference> result = mapper.mapReferencesRoad(forbiddenReferences);

			assertThat(result).hasSize(1);
			assertThat(result.getFirst()).isNull();
		}

		@Test
		void shouldReturnNullWhenRoadOrderReferenceIsNull() {
			RoadOrderReference roadOrderReference = null;

			TTOrderReference result = mapper.mapReferenceRoad(roadOrderReference);

			assertThat(result).isNull();
		}

		@Test
		void shouldMapValuesWithoutPrefixWhenReferenceSubtypeIsNull() {
			RoadOrderReference roadOrderReference = new RoadOrderReference();
			roadOrderReference.setReferenceType(ReferenceType.ORDER_NUMBER);
			roadOrderReference.setReference("A1");

			TTOrderReference result = mapper.mapReferenceRoad(roadOrderReference);

			assertThat(result).isNotNull();
			assertThat(result.getReferenceCode()).isEqualTo(TTOrderReference.ReferenceCodeEnum.ORDER_NUMBER);
			assertThat(result.getValue()).containsExactly("A1");
		}

		@Test
		void shouldMapValuesWithPrefixWhenReferenceSubtypeIsPresent() {
			RoadOrderReference roadOrderReference = new RoadOrderReference();
			roadOrderReference.setReferenceType(ReferenceType.IDENTIFICATION_CODE_TRANSPORT);
			roadOrderReference.setReference("A1");
			roadOrderReference.setReferenceSubtype(RoadOrderReferenceSubtype.UIT);

			TTOrderReference result = mapper.mapReferenceRoad(roadOrderReference);

			assertThat(result).isNotNull();
			assertThat(result.getReferenceCode()).isEqualTo(TTOrderReference.ReferenceCodeEnum.IDENTIFICATION_CODE_TRANSPORT);
			assertThat(result.getValue()).containsExactly("UIT: A1");
		}

		@Test
		void shouldMapReferencesAir() {
			// given
			List<AirOrderReference> references = generateReferencesAir(AirSeaOrderReferenceType.QUOTATION_REFERENCE, AirSeaOrderReferenceType.INVOICE_NUMBER, AirSeaOrderReferenceType.SHIPPERS_REFERENCE);
			// when
			List<TTOrderReference> result = mapper.mapReferencesAir(references);
			// then
			final AirOrderReference quotationReference = references.stream().filter(r -> r.getReferenceType().equals(AirSeaOrderReferenceType.QUOTATION_REFERENCE)).findFirst()
					.orElse(null);
			final AirOrderReference invoiceNumber = references.stream().filter(r -> r.getReferenceType().equals(AirSeaOrderReferenceType.INVOICE_NUMBER)).findFirst().orElse(null);
			final TTOrderReference quotationReferenceJson = result.stream().filter(r -> r.getReferenceCodeASL().equals(TTOrderReference.ReferenceCodeASLEnum.QUOTATION_REFERENCE))
					.findFirst()
														.orElse(null);
			final TTOrderReference invoiceNumberJson = result.stream().filter(r -> r.getReferenceCodeASL().equals(TTOrderReference.ReferenceCodeASLEnum.INVOICE_NUMBER)).findFirst()
														.orElse(null);
			final TTOrderReference shippersReferenceJson = result.stream().filter(r -> r.getReferenceCodeASL().equals(TTOrderReference.ReferenceCodeASLEnum.SHIPPERS_REFERENCE))
					.findFirst()
					.orElse(null);
			assertThat(result).hasSize(3);
			assertThat(quotationReferenceJson).isNotNull();
			assertThat(quotationReferenceJson.getLabel()).isNull();
			assertThat(quotationReference).isNotNull();
			assertThat(invoiceNumberJson).isNotNull();
			assertThat(invoiceNumberJson.getLabel()).isNull();
			assertThat(shippersReferenceJson).isNotNull();
			assertThat(shippersReferenceJson.getLabel()).isNull();
			assertThat(quotationReferenceJson.getValue()).isEqualTo(List.of(quotationReference.getReferenceValue()));
			assertThat(quotationReferenceJson.getReferenceCodeASL()).isEqualTo(TTOrderReference.ReferenceCodeASLEnum.QUOTATION_REFERENCE);
			assertThat(invoiceNumberJson.getReferenceCodeASL()).isEqualTo(TTOrderReference.ReferenceCodeASLEnum.INVOICE_NUMBER);
			assertThat(shippersReferenceJson.getReferenceCodeASL()).isEqualTo(TTOrderReference.ReferenceCodeASLEnum.SHIPPERS_REFERENCE);
			assertThat(invoiceNumber).isNotNull();
			assertThat(invoiceNumberJson.getValue()).isEqualTo(List.of(invoiceNumber.getReferenceValue()));
		}

		@ParameterizedTest
		@ValueSource(ints = { 5,1 })
		void shouldMapReferencesASL(int orderType) {
			VOrderOverview overview = generateBasicOverview();
			overview.setOrderType(orderType);
			// given
			if (Segment.AIR.equals(OrderType.getById(orderType).getSegment())){
				overview.setAllReferencesAir(generateReferencesAir(AirSeaOrderReferenceType.INVOICE_NUMBER, AirSeaOrderReferenceType.CONSIGNEE_REFERENCE_NUMBER, AirSeaOrderReferenceType.SHIPPERS_REFERENCE));
			} else {
				overview.setAllReferencesSea(generateReferencesSea(AirSeaOrderReferenceType.INVOICE_NUMBER, AirSeaOrderReferenceType.CONSIGNEE_REFERENCE_NUMBER, AirSeaOrderReferenceType.SHIPPERS_REFERENCE));
			}
			// when
			List<TTOrderReference> result = mapper.mapReferencesASL(overview);
			// then
			Assertions.assertEquals(3, result.size());
			assertThat(result.get(0).getLabel()).isNull();
			assertThat(result.get(0).getReferenceCodeASL()).isEqualTo(TTOrderReference.ReferenceCodeASLEnum.INVOICE_NUMBER);
			assertThat(result.get(1).getLabel()).isNull();
			assertThat(result.get(1).getReferenceCodeASL()).isEqualTo(TTOrderReference.ReferenceCodeASLEnum.CONSIGNEE_REFERENCE_NUMBER);
			assertThat(result.get(2).getLabel()).isNull();
			assertThat(result.get(2).getReferenceCodeASL()).isEqualTo(TTOrderReference.ReferenceCodeASLEnum.SHIPPERS_REFERENCE);
		}

		@Test
		void shouldFilterAndAggregateReferencesRoad() {
			// given
			List<TTOrderReference> references = generateReferenceDtos(TTOrderType.ROAD, "purchase_order_number", "order_number", "ekaer_number", "purchase_order_number", null);
			final TTRoadOrder order = new TTRoadOrder();
			order.setOrderType(TTOrderType.ROAD);
			order.setReferences(references);
			// when
			mapper.aggregateReferences(order);
			// then
			Assertions.assertEquals(3, order.getReferences().size());
			final TTOrderReference orderNumber = order.getReferences().stream().filter(r -> r.getReferenceCode().equals(TTOrderReference.ReferenceCodeEnum.ORDER_NUMBER))
					.findFirst().orElse(null);
			final TTOrderReference ekaer = order.getReferences().stream().filter(r -> r.getReferenceCode().equals(TTOrderReference.ReferenceCodeEnum.EKAER_NUMBER)).findFirst()
					.orElse(null);
			final TTOrderReference pon = order.getReferences().stream().filter(r -> r.getReferenceCode().equals(TTOrderReference.ReferenceCodeEnum.PURCHASE_ORDER_NUMBER))
					.findFirst().orElse(null);
			assertThat(orderNumber.getReferenceCode()).isEqualTo(TTOrderReference.ReferenceCodeEnum.ORDER_NUMBER);
			assertThat(ekaer.getReferenceCode()).isEqualTo(TTOrderReference.ReferenceCodeEnum.EKAER_NUMBER);
			assertThat(pon.getReferenceCode()).isEqualTo(TTOrderReference.ReferenceCodeEnum.PURCHASE_ORDER_NUMBER);
			assertThat(pon.getValue()).hasSize(2);
			assertThat(order.getReferences().get(0).getReferenceCode()).isEqualTo(TTOrderReference.ReferenceCodeEnum.ORDER_NUMBER);
		}

		@Test
		void shouldFilterAndAggregateReferencesAsl() {
			// given
			List<TTOrderReference> references = generateReferenceDtos(TTOrderType.AIR, "marks_and_numbers", "shippers_reference", "purchase_order_number", "shippers_reference",
					"purchase_order_number", null);
			final TTASLOrder order = new TTASLOrder();
			order.setOrderType(TTOrderType.AIR);
			order.setReferences(references);
			// when
			mapper.aggregateReferences(order);
			// then
			Assertions.assertEquals(3, order.getReferences().size());
			final TTOrderReference shippersReference = order.getReferences().stream()
					.filter(r -> r.getReferenceCodeASL().equals(TTOrderReference.ReferenceCodeASLEnum.SHIPPERS_REFERENCE))
					.findFirst().orElse(null);
			final TTOrderReference marksAndNumbers = order.getReferences().stream()
					.filter(r -> r.getReferenceCodeASL().equals(TTOrderReference.ReferenceCodeASLEnum.MARKS_AND_NUMBERS)).findFirst()
					.orElse(null);
			final TTOrderReference pon = order.getReferences().stream().filter(r -> r.getReferenceCodeASL().equals(TTOrderReference.ReferenceCodeASLEnum.PURCHASE_ORDER_NUMBER))
					.findFirst().orElse(null);
			assertThat(shippersReference.getReferenceCodeASL()).isEqualTo(TTOrderReference.ReferenceCodeASLEnum.SHIPPERS_REFERENCE);
			assertThat(marksAndNumbers.getReferenceCodeASL()).isEqualTo(TTOrderReference.ReferenceCodeASLEnum.MARKS_AND_NUMBERS);
			assertThat(pon.getReferenceCodeASL()).isEqualTo(TTOrderReference.ReferenceCodeASLEnum.PURCHASE_ORDER_NUMBER);
			assertThat(pon.getValue()).hasSize(2);
			assertThat(shippersReference.getValue()).hasSize(2);
			assertThat(order.getReferences().get(0).getReferenceCodeASL()).isEqualTo(TTOrderReference.ReferenceCodeASLEnum.SHIPPERS_REFERENCE);
		}

		@ParameterizedTest
		@MethodSource("provideRoadOrderMappingMethods")
		void testCollectionTimeMapping(Function<VOrderOverview, TTRoadOrder> mappingFunction) {
			VOrderOverview overview = new VOrderOverview();
			overview.setNoTimeInformationAvailable(true);
			overview.setCustomerNumber(VALID_CUST_NO_ROAD);

			TTRoadOrder order = mappingFunction.apply(overview);

			Assertions.assertEquals(DEFAULT_DATE, order.getPickupDateTime().getTo());
			Assertions.assertEquals(DEFAULT_DATE, order.getPickupDateTime().getFrom());
			Assertions.assertTrue(order.getPickupDateTime().getNoTimeInformationAvailable());
		}

		@Test
		void testMapLastModifiedShouldRemoveNanoSeconds() {
			OffsetDateTime original = OffsetDateTime.of(2022, 1, 1, 0, 0, 0, 123456789, ZoneOffset.UTC);

			OffsetDateTime result = mapper.mapLastModified(original);

			assertThat(result.getNano()).isZero();
		}

		@Test
		void testMapLastModifiedShouldReturnNull() {
			OffsetDateTime result = mapper.mapLastModified(null);

			assertThat(result).isNull();
		}

		private void testReferenceMapping(String input, Integer assertion) {
			List<String> references = mapper.mapStringToList(input);
			Assertions.assertEquals(assertion, references.size());
		}

		private Stream<Function<VOrderOverview, TTRoadOrder>> provideRoadOrderMappingMethods() {
			return Stream.of(
					mapper::mapRoadForward,
					mapper::mapRoadCollection
			);
		}
	}

	private static void setCustomerNumber(VOrderOverview overview, String customerNumber) {
		overview.setCustomerNumber(customerNumber);
	}

	private VOrderOverview generateBasicOverview() {
		VOrderOverview overview = new VOrderOverview();
		overview.setOrderId(1L);
		overview.setOrderType(1);
		overview.setFavouriteStatus(true);
		overview.setPickUpDateFrom(OffsetDateTime.of(LocalDateTime.now(), ZoneOffset.UTC));
		overview.setPickUpDateTo(OffsetDateTime.of(LocalDateTime.now().plusWeeks(1L), ZoneOffset.UTC));
		overview.setOrderSent(OffsetDateTime.of(LocalDateTime.now(), ZoneOffset.UTC));
		overview.setContainsDangerousGoods(false);
		overview.setValueOfGoods(10.0);
		final OrderAddress collectionAddress = generateAddress("collectionAddress");
		final OrderAddress consigneeAddress = generateAddress("consigneeAddress");
		overview.setShipperName(collectionAddress.getName());
		overview.setShipperName2(collectionAddress.getName2());
		overview.setShipperName3(collectionAddress.getName3());
		overview.setShipperCity(collectionAddress.getCity());
		overview.setShipperCountry(collectionAddress.getCountryCode());
		overview.setShipperPostcode(collectionAddress.getPostcode());
		overview.setConsigneeName(consigneeAddress.getName());
		overview.setConsigneeName2(consigneeAddress.getName2());
		overview.setConsigneeName3(consigneeAddress.getName3());
		overview.setConsigneeCity(consigneeAddress.getCity());
		overview.setConsigneeCountry(consigneeAddress.getCountryCode());
		overview.setConsigneePostcode(consigneeAddress.getPostcode());
		overview.setOrderReferences("ref1");
		overview.setAllReferencesRoad(generateReferences(ReferenceType.EKAER_NUMBER));
		overview.setTransport("");
		overview.setProduct("product_code");
		overview.setProductName("product_name");
		overview.setPrincipalAddress(1);
		overview.setIncludedDocuments("w");
		overview.setFlightNumberOrVesselName("f1");
		overview.setTermCode("1df");
		overview.setPortDeparture("er");
		overview.setWeight(1.0);
		overview.setCustomerNumber(VALID_CUST_NO_ROAD);
		overview.setPickUpDateType("not before");
		overview.setShipmentNumber("1");
		overview.setDivision(Division.T);
		overview.setQuoteExpirationTime(OffsetDateTime.of(LocalDateTime.now(), ZoneOffset.UTC));
		overview.setRequestArrangement(true);
		overview.setLastModified(OffsetDateTime.of(LocalDateTime.now(), ZoneOffset.UTC));
		return overview;
	}

	private OrderAddress generateAddress(String suffix) {
		final OrderAddress orderAddressDto = new OrderAddress();
		orderAddressDto.setName("name_" + suffix);
		orderAddressDto.setName2("name2");
		orderAddressDto.setName3("name3");
		orderAddressDto.setCity("city");
		orderAddressDto.setPostcode("postcode");
		orderAddressDto.setCountryCode("DE");
		return orderAddressDto;
	}

	private List<RoadOrderReference> generateReferences(ReferenceType... values) {
		return Arrays.stream(values).map(type -> {
			RoadOrderReference ref = new RoadOrderReference();
			ref.setReferenceType(type);
			ref.setReference("1");
			return ref;
		}).toList();
	}

	private List<AirOrderReference> generateReferencesAir(AirSeaOrderReferenceType... values) {
		return Arrays.stream(values).map(type -> {
			AirOrderReference ref = new AirOrderReference();
			ref.setReferenceType(type);
			ref.setReferenceValue("1");
			return ref;
		}).toList();
	}

	private List<SeaOrderReference> generateReferencesSea(AirSeaOrderReferenceType... values) {
		return Arrays.stream(values).map(type -> {
			SeaOrderReference ref = new SeaOrderReference();
			ref.setReferenceType(type);
			ref.setReferenceValue("1");
			return ref;
		}).toList();
	}

	private List<TTOrderReference> generateReferenceDtos(TTOrderType orderType, String... values) {
		return Arrays.stream(values).map(type -> {
			TTOrderReference ref = new TTOrderReference();
			switch (orderType) {
			case ROAD -> ref.setReferenceCode(type != null ? TTOrderReference.ReferenceCodeEnum.fromValue(type) : null);
			case AIR, SEA -> ref.setReferenceCodeASL(type != null ? TTOrderReference.ReferenceCodeASLEnum.fromValue(type) : null);
			}
			ref.setValue(type != null ? List.of("101") : null);
			return ref;
		}).toList();
	}
}
