server:
  port: 9000
spring:
  datasource:
    driver-class-name: org.h2.Driver
    username: sa
    password:
    #url: "jdbc:h2:./testdb/h2testdb;MODE=MSSQLServer;DB_CLOSE_ON_EXIT=FALSE;INIT=CREATE SCHEMA IF NOT EXISTS dbo\\;SET SCHEMA dbo"
    url: "jdbc:h2:mem:db1;MODE=MSSQLServer;DB_CLOSE_ON_EXIT=FALSE;INIT=CREATE SCHEMA IF NOT EXISTS dbo\\;SET SCHEMA dbo"
  liquibase:
    contexts: mock-data, h2
  jpa:
    properties:
      hibernate:
        # as long as we use h2 in MSSQL mode we need to use the SQLServerDialect
        dialect: org.hibernate.dialect.H2Dialect
    show-sql: false
  security:
    oauth2:
      resource-server:
        dachserOAuth:
          jwk-set-uri: http://localhost:${wiremock.server.port}/keycloack-mock/certs
#  jpa:
#    show-sql: true

oAuth:
  server-base-url: "http://localhost/not_set"

ENV_BOOK_DB_LOGLEVEL: WARN
ENV_BOOK_APPLICATION_LOGLEVEL: WARN
ENV_BOOK_TRIAL_MODE_ROAD: true
ENV_BOOK_TRIAL_MODE_AIR_SEA: true
ENV_BOOK_UMS_FACTORY: SpringTestTopicConnectionFactory

dfe:
  road:
    collectionOrder:
      enabled: true
    masterdata:
      use-preconfigured-cache-2k: false
  services:
    document:
      basePath: ./unit-test-files/
  platform:
    configuration:
      baseURL: "https://dfe-platform-backend-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"
      use-cache: false
    security:
      jwk-set-uri: ${spring.security.oauth2.resource-server.dachserOAuth.jwk-set-uri}
      any-required-roles: [company-book, groups-book]
      required-authority: ROLE_active-company
      public-endpoints: [ "/v1/air-products", "/delivery-products", "/countries", "/freight-terms", "/inco-terms" ]
  general-data:
    configuration:
      baseURL: "https://dfe-general-data-backend-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"
  emissionforecast:
    configuration:
      baseURL: "https://dfe-emissionforecast-backend-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"
  pdf-export:
    configuration:
      baseURL: "https://dfe-pdf-export-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com"


ums:
  dip:
    ediTestFlagRoad: true
    ediTestFlagAirSea: true
  enabled: false