package com.dachser.dfe.book.dip.xml;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.PosixFilePermissions;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;

@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "false", matchIfMissing = true)
public class XmlFileStorage implements XmlFileStorageProvider {

	private static final String XML = ".xml";

	private static final String PERMIT_ALL = "rwxrwxrwx";

	@Value("${dfe.services.order.basePath}")
	private String basePath;

	public void saveXml(@NonNull String xmlContent, @NonNull Long shipmentNumber) throws IOException {
		Path path = buildPath(shipmentNumber);
		Files.write(path, xmlContent.getBytes());
		updateFilePermissions(path);
		log.info("XML file saved at {}.", path);
	}

	public String getXml(Long shipmentNumber) {
		String order = "";
		Path path = buildPath(shipmentNumber);
		try {
			order = new String(Files.readAllBytes(path));
		} catch (IOException e) {
			log.error("Reading of XML file with shipmentNumber {} failed.", shipmentNumber);
		}
		return order;
	}

	public void cleanUpXmlFiles(int ageThresholdToDelete) {
		File uploadFolder = Paths.get(basePath).toFile();
		File[] files = uploadFolder.listFiles();

		if (files != null) {
			/* @formatter:off */
			Arrays.stream(files)
					// we proceed only with files older than x days
					.filter(file -> {
						Instant lastUsed;
						Instant now = Instant.now();
						Duration ageLimit = Duration.ofDays(ageThresholdToDelete);
						try {
							lastUsed = Files.getLastModifiedTime(file.toPath()).toInstant();
						} catch (IOException e) {
							log.error("Cleanup XML file {} failed. Cannot get last modified time.", file.getName());
							lastUsed = Instant.now(); //fallback so that the file is not deleted
						}
						return lastUsed.isBefore(now.minus(ageLimit));})
					// we only proceed with xml files
					.filter(file -> file.getName().endsWith(XML))
					//all files that match the criteria above will be deleted
					.forEach(file -> {
						try {
							Files.delete(file.toPath());
							log.info("Deleted XML file {}", file.getName());
						} catch (IOException e) {
							log.error("Deletion for XML file {} failed", file.getName());
						}
					});
			/* @formatter:on */
		}
	}

	private void createBasePathIfMissing() throws IOException {
		if (!Files.exists(Paths.get(basePath))) {
			Path uploadFolder = Files.createDirectories(Paths.get(basePath));
			updateFilePermissions(uploadFolder);
		}
	}

	private Path buildPath(Long shipmentNumber) {
		try {
			createBasePathIfMissing();
		} catch (IOException e) {
			log.error("Creation of missing base path {} failed.", basePath, e);
		}
		return Paths.get(basePath + shipmentNumber + XML);
	}

	/**
	 * the upload is done by a book-backend pod different from the pod handling the deletion later. <br>
	 * To avoid access denied errors, the files must be given specific permissions.
	 */
	private void updateFilePermissions(Path filePath) {
		try {
			Files.setPosixFilePermissions(filePath, PosixFilePermissions.fromString(PERMIT_ALL));
		} catch (UnsupportedOperationException | IOException e) {
			// in case of testing this can fail and should have no effect
			log.error("Failed to update permissions for XML file {}", filePath, e);
		}
	}

}
