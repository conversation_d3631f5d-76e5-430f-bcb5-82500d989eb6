package com.dachser.dfe.book.order;

import com.dachser.dfe.book.document.DocumentRepositoryFacade;
import com.dachser.dfe.book.exception.OrderTypeSwitchException;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.dachser.dfe.book.order.mapper.OrderTypeConversionMapper;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaImportOrder;
import com.dachser.dfe.book.service.ShipmentNumberService;
import jakarta.annotation.PostConstruct;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;
import java.util.function.Predicate;

@Service
@AllArgsConstructor
@Slf4j
public class OrderTypeSwitchService {

	private final OrderRepositoryFacade orderRepositoryFacade;

	private final OrderTypeConversionMapper orderTypeConversionMapper;

	private final DocumentRepositoryFacade documentRepositoryFacade;

	private final ShipmentNumberService shipmentNumberService;

	private final OrderMapper orderMapper;

	private List<OrderConversionStrategy> conversionStrategies;


	@PostConstruct
	public void init() {
		conversionStrategies = List.of(
				// Road-Orders
				new OrderConversionStrategy(CollectionOrder.class::isInstance, OrderType.ROADFORWARDINGORDER,
						order -> orderTypeConversionMapper.convertToRoadForwardingOrder((CollectionOrder) order, documentRepositoryFacade, shipmentNumberService)),
				new OrderConversionStrategy(ForwardingOrder.class::isInstance, OrderType.ROADCOLLECTIONORDER,
						order -> orderTypeConversionMapper.convertToRoadCollectionOrder((ForwardingOrder) order, documentRepositoryFacade, shipmentNumberService)),
				// Air-Orders
				new OrderConversionStrategy(AirImportOrder.class::isInstance, OrderType.AIREXPORTORDER,
						order -> orderTypeConversionMapper.convertToAirExportOrder((AirImportOrder) order, documentRepositoryFacade, shipmentNumberService)),
				new OrderConversionStrategy(AirExportOrder.class::isInstance, OrderType.AIRIMPORTORDER,
						order -> orderTypeConversionMapper.convertToAirImportOrder((AirExportOrder) order, documentRepositoryFacade, shipmentNumberService)),
				// Sea-Orders
				new OrderConversionStrategy(SeaImportOrder.class::isInstance, OrderType.SEAEXPORTORDER,
						order -> orderTypeConversionMapper.convertToSeaExportOrder((SeaImportOrder) order, documentRepositoryFacade, shipmentNumberService)),
				new OrderConversionStrategy(SeaExportOrder.class::isInstance, OrderType.SEAIMPORTORDER,
						order -> orderTypeConversionMapper.convertToSeaImportOrder((SeaExportOrder) order, documentRepositoryFacade, shipmentNumberService)));
	}

	@Transactional(value = Transactional.TxType.MANDATORY)
	public Order switchOrderType(Order existingOrder, BasicOrderDto inputOrder) {
		OrderType targetOrderType = OrderType.getByName(inputOrder.getOrderType());
		if (targetOrderType != existingOrder.getOrderType()) {
			Order switchedOrder = orderRepositoryFacade.loadOrderById(switchOrderType(existingOrder, targetOrderType));
			// Because we created a new clone of the order the input is no longer meant to be applied for the old id
			inputOrder.setOrderId(switchedOrder.getOrderId());
			orderMapper.update(inputOrder, switchedOrder);
			return switchedOrder;
		}
		return existingOrder;
	}

	@Transactional(value = Transactional.TxType.MANDATORY)
	public Long switchOrderType(Order existingOrder, OrderType targetOrderType) {
		for (OrderConversionStrategy strategy : conversionStrategies) {
			if (strategy.supports(existingOrder, targetOrderType)) {
				Order targetOrder = strategy.convert(existingOrder);
				Order newOrder = orderRepositoryFacade.save(targetOrder);
				// Save the existing order as it is now in state deleted and has no more entities attached
				orderRepositoryFacade.save(existingOrder);
				log.debug("Switched OrderType from {} to {} for old Order with id {} creating new with {}", existingOrder.getOrderType(), targetOrderType, existingOrder.getOrderId(), newOrder.getOrderId());
				return newOrder.getOrderId();
			}
		}
		throw new OrderTypeSwitchException("Unable to switch OrderType to " + targetOrderType.getName());
	}

	private record OrderConversionStrategy(Predicate<Order> predicate, OrderType targetOrderType, Function<Order, Order> conversionFunction) {

		public boolean supports(Order order, OrderType targetOrderType) {
			return this.targetOrderType.equals(targetOrderType) && predicate.test(order);
		}

		public Order convert(Order order) {
			return conversionFunction.apply(order);
		}
	}
}
