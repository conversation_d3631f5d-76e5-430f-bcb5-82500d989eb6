# DFE Book Backend

Implementation based on dfe-book-api specification which provides a Spring REST generated server frame, defining all endpoints and transfer objects.
Further implementations can be performed without direct modification of generated frame classes.

## Development

Requires a built dfe-book-api jar as maven dependency. Per default latest stable version is provided as maven dependency. If you want try a snapshot version e.g. when editing API
Definition in parallel change dependency to current snapshot version, and make sure to rebuild it before starting the microservice.

*Please be aware that the usage of dachser artifactory is required as maven repository in order to build the project. https://artifactory.dach041.dachser.com/ This may be achieved
by using a customized local maven settings file*

Example File

    <settings>
        <servers>
        </servers>
        <mirrors>
            <mirror>
                <id>maven-all</id>
                <name>maven-all</name>
                <url>https://artifactory.dach041.dachser.com:443/artifactory/bint-maven-dfe-all/</url>
                <mirrorOf>*</mirrorOf>
            </mirror>
        </mirrors>
        <profiles>
            <profile>
                <id>artifactory</id>
                <repositories>
                    <repository>
                        <id>maven-all</id>
                        <name>maven-all</name>
                        <url>https://artifactory.dach041.dachser.com:443/artifactory/bint-maven-dfe-all/</url>
                    </repository>
                </repositories>
            </profile>
        </profiles>
        <activeProfiles>
            <activeProfile>artifactory</activeProfile>
        </activeProfiles>
    </settings>

### Swagger

Swagger UI is deployed as well under http://localhost:8090/swagger-ui/index.html

**Note:** *This is a representation of annotated services, not the openapi.yaml so there might be some differences e.g. name of the schemas*

### Spring Profiles

Within the *application-local.yaml* there are different profiles configured for local development.


> **local**
>
> Standard local development environment (Targetsystems DEV, H2 DB, DIP disabled by default):

> **local-mssql**
>
> Standard local development with mssql (docker on localhost:1433) instead of H2.
> This is specifically useful to check compatibility of liquibase changes before cluster deployment:

> **advanced-debug**
>
> Additional profile for advanced DB debugging and testing purposes:

**Note:** *It is required to configure groups on top level (application.yaml). Feature is used to activate multiple profiles at once.*

### MSSQL

There is a docker configuration provided to be used locally:

    src/main/docker/local-mssql/README.md

### H2

We are using h2 database in mode REGULAR because in MSSQL Mode prohibits unique indexes containing null values several times.
See http://www.h2database.com/html/features.html#compatibility.
We use such an unique index on ```order_base.shipment_number```.

### Liquibase

We are using Liquibase https://docs.liquibase.com/home.html for database versioning. Every change to database structure has to done via changlog under
```/src/main/resources/db/changelog```  in the according subdirectory of either ```all```, ```h2```, ```mssql```.
Each changeset has to be registered in ```/src/main/resources/db/changelog/db.changelog-master.yaml```, earlier attempts to work with includeAll did lead to problems in sequence of
execution over different directories.
We are using ```.sql``` files for changesets, because we as team decided it ti be easier to understand.

Further we use liquibase to bootstrap test / mock data into h2 in momory database. This can be used either for

- local development
- during execution of unit tests
- for environments without dedicated database source

So in case of adaptions to database, it is required to evaluate changes against h2 and mssql.
For database specific changes we use contexts of ```h2``` and ```mssql``` as well es separate folders. Set context in master file application.properties are already prepared for
adding these contexts depending on used database.

### Mapstruct

*Note that changes in data objects require a rebuild of mapstruct mappers, so a `mvn clean install` is necessary if dataobjects have changed*

## Releases

No manual action is required for release. Everything is done by semantic release and gitlab pipelines.
As a consequence, every commit must follow the rules of semantic commit messages.
https://github.com/angular/angular/blob/main/CONTRIBUTING.md#-commit-message-format

Please visit https://dil-itd.atlassian.net/wiki/spaces/10172/pages/38866876/Release for further information about the release process.

### DEV Releases (alpha)

The alpha branch is our development playground. No reviews happen on alpha.
You can reset the alpha branch to the head of any other stable branch by running the following command:

    git checkout alpha
    git reset --hard <branch>
    git push -f

Use this to reset the alpha branch during sprints only when needed.

Note: *You need to delete the alpha tags of the current iteration BEFORE resetting alpha*

### TEST Releases (develop)

Can be disabled setting BETA_RELEASES_ACTIVE to false in CI/CD pipeline

### PROD Releases (master)

The master branch is our production branch.
Once the release pipeline is finished, pull requests to develop and alpha are created automatically.
They need to be merged manually after each release to start a new iteration cycle.
Semantic release will handle the versioning and tagging.

## Authentication

Currently, connected to keycloak dev instance, login required for most of the API endpoints.
Keycloak can be configured via env settings.

## Health / status information

Spring actuator is used to provide status information about the running application

- health endpoint http://localhost:8090/mgmt/health
- info endpoint http://localhost:8090/mgmt/info

Info endpoint contains information in json format

    {
        "git": {
            "branch": "feature/DFE-466",
            "commit": {
                "id": "fada320",
                "time": "2022-09-07T07:04:36Z"
            }
        },
        "build": {
            "artifact": "dfe-book-backend",
            "name": "DFE book backend",
            "time": "2022-09-07T13:38:58.388Z",
            "version": "0.0.1-SNAPSHOT",
            "group": "com.dachser.dfe.book"
        },
        "api": {
            "name": "DFE book API",
            "version": "0.0.22"
        }
    }

## Connected Systems

Up-to-date list of all connected systems and architecture by dfe book backend:
https://dil-itd.atlassian.net/wiki/spaces/10172/pages/38869209/3.+System+Scope+and+Context+-+Platform#*******.-Book%5BhardBreak%5D

### EDI

XML Messages are used to export data to edi. For that purpose we use a jaxb auto generated model structure based on a provided xsd file. Generation of classes is integrated in
maven process. Mapstruct Mapper is used to convert Order entities into their according xsd DTOs.

## Logging

Default logging is logback integrated in spring boot. But log4j can be used as well using ENV Variable LOG4J_CONFIGURATION_FILE to pass a log4j 2 configuration file.

## Secrets

### Running project locally with Vault

https://dil-itd.atlassian.net/wiki/spaces/10172/pages/38867459/Vault+Integartion+-+Spring+Projects#Run-Local

Tip: *You can store a .vault-token locally as this is the default behavior of the application.
The token then needs to be renewed centrally once it expires.*

### Property Mapped Secrets

| Vault Engine | Vault Namespace | Vault Path       | Secret Path                | Secret Key                           |
|--------------|-----------------|------------------|----------------------------|--------------------------------------|
| kv2_dfe      | DFE             | dfe-book-backend | spring.datasource.username | dfe-book-backend.datasource.username |
| kv2_dfe      | DFE             | dfe-book-backend | spring.datasource.password | dfe-book-backend.datasource.password |

### Auto Configured Secrets

| Vault Engine             | Vault Namespace | Vault Path                                     | Secret Path                                                        |
|--------------------------|-----------------|------------------------------------------------|--------------------------------------------------------------------|
| kv2_dfe                  | DFE             | dfe-legacy-advice-service                      | dfeLegacyAdvice.username / password                                |
| kv2_dfe                  | DFE             | dfe-general-data                               | com.dachser.dfe.general-data.configuration.username / password     |
| kv2_dfe                  | DFE             | dfe-emissionforecast-backend                   | com.dachser.dfe.emissionforecast.configuration.username / password |
| kv2_road                 | DFE             | dfe-road-masterdata                            | dfe.road.masterdata.username / password                            |
| kv2_road                 | DFE             | dfe-road-consignment-evaluation                | dfe.road.consignment-evaluation.username / password                |
| kv2_road                 | DFE             | road-ohs-pub-service                           | road.ohs.pub.service.username / password                           |
| kv2_airsea               | DFE             | dfe-airsea-masterdata                          | dfe.airsea.masterdata.username / password                          | 
| kv2_shared_service       | DFE             | dfe-shared-services-masterdata-geo             | dfe.sharedservices.masterdata.geo.username / password              | 
| kv2_shared_service       | DFE             | dfe-shared-services-masterdata-businesspartner | dfe.sharedservices.masterdata.businesspartner.username / password  | 
| kv2_shared_service       | DFE             | dfe-common-pub-archive-service                 | common.pub.archive.service.username / password                     | 
| kv2_shared_service       | DFE             | kta                                            | kta.apiKey                                                         | 
| kv2_business_integration | DFE             | ums-provider                                   | ums.provider.principal / ums.provider.credentials                  |
| kv2_business_integration | DFE             | bi-common-security-service-pri                 | bi.common.security.pri.username / password                         |
| kv2_business_integration | DFE             | bi-common-print-service-pri                    | bi.common.print.pri.username  / password                           |
| kv2_business_integration | DFE             | bi-common-shipment-service-pri                 | bi.common.shipment.pri.username / password                         |
| kv2_business_integration | DFE             | bi-common-validation-service-pri               | bi.common.validation.pri.username / password                       |
| kv2_business_integration | DFE             | elogistics-order-pub-service                   | elogistics.order.pub.service.username / password                   |

