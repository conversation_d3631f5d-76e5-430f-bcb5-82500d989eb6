package com.dachser.dfe.book.dip.converter.sea;

import com.dachser.dfe.book.dip.converter.shared.AirSeaDocumentHeaderMapper;
import com.dachser.dfe.book.dip.converter.shared.DateMapper;
import com.dachser.dfe.book.order.sea.SeaExportOrder;
import com.dachser.dfe.book.order.sea.SeaImportOrder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.SubclassExhaustiveStrategy;

@Mapper(uses = { SeaFreightMapper.class, DateMapper.class, AirSeaDocumentHeaderMapper.class }, componentModel = "spring", subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION)
@Named("SeaOrderMapper")
public interface SeaOrderMapper {

	@Mapping(target = "documentHeader", source = ".")
	@Mapping(target = "seaFreightShipment", source = ".")
	com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder map(SeaExportOrder order);

	@Mapping(target = "documentHeader", source = ".")
	@Mapping(target = "seaFreightShipment", source = ".")
	com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder map(SeaImportOrder order);
}
