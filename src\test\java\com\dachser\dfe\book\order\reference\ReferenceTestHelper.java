package com.dachser.dfe.book.order.reference;

import com.dachser.dfe.book.model.jaxb.order.road.collection.AdditionalReference;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.codehaus.plexus.util.StringUtils;

import java.util.List;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public abstract class ReferenceTestHelper {

	public static RoadOrderReference roadOrderReference(ReferenceType type, RoadOrderReferenceSubtype subtype, String value) {
		RoadOrderReference reference = new RoadOrderReference();
		reference.setReferenceType(type);
		reference.setReferenceSubtype(subtype);
		reference.setReference(value);
		return reference;
	}

	public static String stringify(List<AdditionalReference> references) {
		return "[".concat(StringUtils.join(references.stream().map(ReferenceTestHelper::stringify).toArray(), ", ")).concat("]");
	}

	public static String stringify(AdditionalReference reference) {
		return ReflectionToStringBuilder.toString(reference, ToStringStyle.SHORT_PREFIX_STYLE, true, false, true, AdditionalReference.class);
	}

}