package com.dachser.dfe.book.cache;

import org.cache2k.extra.spring.SpringCache2kCacheManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.Map;

@EnableCaching
@Configuration
@ConditionalOnProperty(prefix = "dfe.book.cache2k", name = "enabled", havingValue = "true", matchIfMissing = true)
public class Cache2KConfiguration {

	@Value("${dfe.book.cache2k.expiry.hours:#{8}}")
	private int defaultCacheExpiryHours;

	@Value("#{${dfe.book.cache2k.expiry.custom}}")
	private Map<String, Integer> customCacheExpiryMinutes;

	private static SpringCache2kCacheManager INSTANCE;

	@Bean
	@ConditionalOnMissingBean
	public SpringCache2kCacheManager cacheManager() {
		// Really bad workaround, but Cache2k internally uses a static instance, so there may occur differences between reused instance of internal cache and recreated spring cache bean
		// springcache2k uses a names2cache which might differ to instances contained in native cache holder. First noticed during tests where spring beans are often recreated while jvm
		// and instances remain over several tests. Problem leads to exception -> cache already configured, because names2cache does not contain the cache, but instance in background
		// has a cache for that name, could be seen as a bug of cache2k in my opinion as name2cache should be updated when creating a new spring cache manager bean.

		if (INSTANCE == null || INSTANCE.getNativeCacheManager().isClosed()) {
			INSTANCE = new SpringCache2kCacheManager("cache2k-" + hashCode()).defaultSetup(builder -> builder.expireAfterWrite(Duration.ofHours(defaultCacheExpiryHours)));

			customCacheExpiryMinutes.forEach(
					(cacheName, expiryInMinutes) -> INSTANCE.addCache(cacheName, cache2kBuilder -> cache2kBuilder.expireAfterWrite(Duration.ofMinutes(expiryInMinutes))));

			// Create caches with the default settings if missing
			CacheNames.getValues().forEach(INSTANCE::getCache);
		}
		return INSTANCE;
	}

	@Bean("sidKeyGenerator")
	public KeyGenerator sidKeyGenerator() {
		return new SIDKeyGenerator();
	}

}
