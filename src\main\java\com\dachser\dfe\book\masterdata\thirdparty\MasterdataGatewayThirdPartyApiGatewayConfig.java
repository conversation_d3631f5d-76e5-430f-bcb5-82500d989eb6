package com.dachser.dfe.book.masterdata.thirdparty;

import com.dachser.dfe.book.masterdata.MasterdataGatewayConfig;
import com.dachser.dfe.masterdata.thirdparty.ApiClient;
import com.dachser.dfe.masterdata.thirdparty.api.DangerousGoodsApiApi;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
@Getter
@Setter
class MasterdataGatewayThirdPartyApiGatewayConfig {

	private final MasterdataGatewayConfig masterdataGatewayConfig;

	private final RestTemplate bookRestTemplateNoAuth;

	@Autowired
	public MasterdataGatewayThirdPartyApiGatewayConfig(@Qualifier("bookRestTemplateNoAuth") RestTemplate bookRestTemplateNoAuth, MasterdataGatewayConfig masterdataGatewayConfig) {
		this.bookRestTemplateNoAuth = bookRestTemplateNoAuth;
		this.masterdataGatewayConfig = masterdataGatewayConfig;
	}

	@Bean
	public DangerousGoodsApiApi dangerousGoodsApiApi() {
		return new DangerousGoodsApiApi(thirdPartyApiClient());
	}

	private ApiClient thirdPartyApiClient() {
		final ApiClient apiClient = new ApiClient(bookRestTemplateNoAuth);
		apiClient.setBasePath(masterdataGatewayConfig.getBaseURL());
		apiClient.setUsername(masterdataGatewayConfig.getUsername());
		apiClient.setPassword(masterdataGatewayConfig.getPassword());
		return apiClient;
	}
}
