package com.dachser.dfe.book.order;

import com.dachser.dfe.book.advice.AdviceService;
import com.dachser.dfe.book.dangerousgoods.DangerousGoodsTransferListService;
import com.dachser.dfe.book.document.DocumentService;
import com.dachser.dfe.book.exception.ErrorIdOrderExpiredException;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.OrderSaveActionDto;
import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.model.OrderValidationResultDto;
import com.dachser.dfe.book.model.PrintLabelStartPositionDto;
import com.dachser.dfe.book.model.RoadForwardingOrderDto;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.validation.StatusCompleteOrderValidator;
import com.dachser.dfe.book.quote.validation.QuoteOrderUpdateValidator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderUpdateProcessorUnitTest {

	@Mock
	List<OrderSubmitter<?>> orderSubmitters;

	@Mock
	AdviceService adviceService;

	@Mock
	OrderRepositoryFacade orderRepository;

	@Mock
	OrderMapper orderMapper;

	@Mock
	OrderLabelGenerator orderLabelGenerator;

	@Mock
	OrderLabelPrinter orderLabelPrinter;

	@Mock
	OrderProcessCheckSwitchAndSave orderProcessCheckSwitchAndSaveTransaction;

	@Mock
	QuoteOrderUpdateValidator quoteOrderUpdateValidator;

	@Mock
	StatusCompleteOrderValidator statusCompleteOrderValidator;

	@Mock
	DangerousGoodsTransferListService dangerousGoodsTransferListService;

	@Mock
	DocumentService documentService;

	@InjectMocks
	OrderUpdateProcessor orderUpdateProcessor;

	@ParameterizedTest
	@CsvSource({ "VALIDATE", "PRINT_LABELS", "SUBMIT" })
	void rejectValidateOnExpiredOrder(OrderSaveActionDto action) {
		Long orderId = 4711L;
		BasicOrderDto basicOrder = new BasicOrderDto();
		basicOrder.setOrderId(1L);
		RoadOrder roadOrder = new RoadOrder();
		roadOrder.setOrderId(orderId);
		roadOrder.setStatus(OrderStatus.DRAFT);

		roadOrder.setOrderExpiryDate(OffsetDateTime.now().minusSeconds(1));
		
		roadOrder.setCustomerNumber("0");
		when(orderRepository.loadOrderById(1L)).thenReturn(roadOrder);

		ErrorIdOrderExpiredException orderExpiredException = assertThrows(ErrorIdOrderExpiredException.class, () -> orderUpdateProcessor.process(basicOrder, action, null));
		assertThat(orderExpiredException.getMessage()).isEqualTo("Order with id %d is expired. Action %s cannot be performed".formatted(orderId, action.name()));
	}
	
	@ParameterizedTest
	@CsvSource({ "VALIDATE", "PRINT_LABELS", "SUBMIT" })
	void rejectValidateOnExpiredOrderByStatus(OrderSaveActionDto action) {
		Long orderId = 4711L;
		BasicOrderDto basicOrder = new BasicOrderDto();
		basicOrder.setOrderId(1L);
		RoadOrder roadOrder = new RoadOrder();
		roadOrder.setOrderId(orderId);
		roadOrder.setStatus(OrderStatus.EXPIRED);		
	
		roadOrder.setOrderExpiryDate(OffsetDateTime.now().minusHours(1));
		roadOrder.setCustomerNumber("0");
		when(orderRepository.loadOrderById(1L)).thenReturn(roadOrder);

		ErrorIdOrderExpiredException orderExpiredException = assertThrows(ErrorIdOrderExpiredException.class, () -> orderUpdateProcessor.process(basicOrder, action, null));
		assertThat(orderExpiredException.getMessage()).isEqualTo("Order with id %d is expired. Action %s cannot be performed".formatted(orderId, action.name()));
	}

	@ParameterizedTest
	@EnumSource(PrintLabelStartPositionDto.class)
	@NullSource
	void allPrinterStartPositionValuesWork(PrintLabelStartPositionDto startPosition) {
		BasicOrderDto orderDto = new RoadForwardingOrderDto();
		orderDto.setOrderId(1L);
		RoadOrder roadOrder = new ForwardingOrder();
		roadOrder.setStatus(OrderStatus.DRAFT);
		when(orderRepository.loadOrderById(1L)).thenReturn(roadOrder);
		when(orderLabelGenerator.generateLabels(any())).thenReturn(new OrderLabelContainer());
		when(orderProcessCheckSwitchAndSaveTransaction.checkSwitchAndSave(roadOrder, orderDto)).thenReturn(new OrderProcessCheckSwitchAndSave.CheckSwitchAndSaveResult(roadOrder, new OrderValidationResultDto()));

		orderUpdateProcessor.process(orderDto, OrderSaveActionDto.PRINT_LABELS, startPosition);
		verify(orderLabelGenerator).generateLabels(any());
	}

	@Test
	void shouldInvokeForwardingOrderProcessorOnUpdateForwardingOrder() {
		ForwardingOrderPostProcessor forwardingOrderPostProcessor = Mockito.spy(new ForwardingOrderPostProcessor(adviceService, documentService));
		// Construct it manually to ensure all is setup correctly, there were runtime errors in the past
		OrderUpdateProcessor updateProcessor = new OrderUpdateProcessor(orderRepository, orderMapper, orderLabelGenerator, new ArrayList<>(), adviceService,
				orderProcessCheckSwitchAndSaveTransaction, quoteOrderUpdateValidator, statusCompleteOrderValidator, Mockito.mock(OrderDefaults.class), orderLabelPrinter,
				List.of(forwardingOrderPostProcessor), dangerousGoodsTransferListService);

		BasicOrderDto orderDto = new RoadForwardingOrderDto();
		orderDto.setOrderId(1L);
		ForwardingOrder roadOrder = new ForwardingOrder();
		roadOrder.setStatus(OrderStatus.DRAFT);
		when(orderRepository.loadOrderById(1L)).thenReturn(roadOrder);
		when(orderLabelGenerator.generateLabels(any())).thenReturn(new OrderLabelContainer());
		when(orderProcessCheckSwitchAndSaveTransaction.checkSwitchAndSave(roadOrder, orderDto)).thenReturn(new OrderProcessCheckSwitchAndSave.CheckSwitchAndSaveResult(roadOrder, new OrderValidationResultDto()));
		updateProcessor.process(orderDto, OrderSaveActionDto.PRINT_LABELS, PrintLabelStartPositionDto.BOTTOM_LEFT);
		verify(forwardingOrderPostProcessor).postSaveActions(any(), any(), any());
	}

}