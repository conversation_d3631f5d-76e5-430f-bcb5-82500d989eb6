# [2.9.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/compare/2.8.0...2.9.0) (2025-06-27)


### Bug Fixes

* DFE-4464 Adjust configuration after some local dry runs ([2f5cedf](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/2f5cedf5ae68cc1b5ffb61d7420f7dc41065dca4))
* DFE-4464 Cleanup unwanted markup ([4f73cb2](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/4f73cb2ea50bbd7769f9470cd22b442cd08429cc))
* DFE-4464 Switch renovate bot scheduling syntax to CRON syntax ([82eaa09](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/82eaa0948d287f8b5208926866e58c75700f28c6))
* DFE-4501 Introduce validation for Dangerous goods order texts ([63f703e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/63f703e9408b63a74677c087f4df793bf7bbad33))
* DFE-4511: fix of ADRFlag ([c588435](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c588435bd7e36e552da64c9b640ed9e067433143))
* DFE-4511: fix of ADRFlag ([bd8295a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/bd8295a07ba0948a2335c8d012fc2f3c7fc37b7e))
* DFE-4541 environmentally_hazardous can be null ([3b197c7](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3b197c7d750cc2a86d0c0a6b330d9fc2741ec51d))
* DFE-4560 - Integrate API for un number ([b607f7c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/b607f7cf3fb3ee6d7b51cb391f237a0f6afa5eb0))
* DFE-4586 Get consolidator relation from road masterdata for shipment labels ([e065256](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/e065256e3f6bef8d13ccb597bd9a9daf8cd0db74))


### Features

* Add dangerous goods to order options ([c51ed10](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c51ed10d14c0d25de82282e9a9c51ebc2aee51d7))
* DFE-4277 Introduce DangerousGoods to RoadOrderLines ([eb9bd85](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/eb9bd854525c9d05f6137bfcf54dac03d983f463))
* DFE-4511 edi export dangerous goods ([da414ce](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/da414cebaaa822db8b039bf47092b61aa9a58323))
* DFE-4541 - Dangerous Goods for TransferList ([144e3fe](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/144e3fef4bf63615df7c739b4e63c0f2b7674a21))
* DFE-4541 Dont make nos required for ADR ([b3d9f5b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/b3d9f5b6f0a52addb2b5b770201241153bec601f))
* DFE-4541 Introduce dangerous goods to transfer list ([5145b54](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/5145b544ce33a049353dc26718c0b2bb7d2ee3a7))
* DFE-4550 un number search ([094011b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/094011bbba17bf4703f80672ff3ae0de51f3a54d))
* DFE-4551 Introduce document types for dangerous goods ([79f2e00](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/79f2e0023a735daefb90b201123674f1d4561b09))
* DFE-4553 Map dangerous goods to legacy road advices ([cb2fc7f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/cb2fc7fe0798ff889ffa3f9bfb7fa47f2d53ad9b))
* DFE-4557 - Add dangerous goods packaging options ([d3d71f7](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/d3d71f72d74e2ee2590465387aebe012e0a31f12))
* DFE-4557 Feature/ dangerous goods packaging options ([e2aa471](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/e2aa471faa607a6dcea377997f124ffd0623e6de))
* DFE-4560 further adaptions to datamodel ([9d8492d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/9d8492d2897100af7536196d9263e8f3eab14ba6))
* DFE-4560 further adaptions to datamodel ([406e522](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/406e5227a3bdd1aa4e49064230f9150c524a8258))
* DFE-4560 further adaptions to datamodel ([efce966](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/efce966493a782f04574ec3f17c2fdedbbc5b4b9))
* DFE2-2232 New EP for packaging options ([70f17d0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/70f17d0d6eeafa47dfe6b24383966029f445de43))
* implemented the new EP allFurtherAddresses ([23b8a9b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/23b8a9b66f2cea79efa5c5c0e57e7fec6a42ce00))
* implemented the new EP for packaging options ([5ce6789](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/5ce6789390c2255cde6c4a2897a8b8b4859314fb))

# [2.8.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/compare/2.7.0...2.8.0) (2025-06-05)


### Bug Fixes

* DFE-4540 correct handling of postcode validation ([d3b10fd](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/d3b10fd5415e5ce219f0f003dea95f2a533a6688))
* DFE-4540 correct handling of postcode validation ([2c96ccf](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/2c96ccffbbab9b279ad4d3b11d28f13a150161cf))
* DFE-4540 correct handling of postcode validation ([25cfa2e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/25cfa2e72ebb76db18ff08b2c86f41f0f0c25db6))
* DFE-4555 Set forwarding order to order label container when merging ([a262986](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/a262986a7d07b8cff03b9a3db0314c9310825170))
* DFE-4590 - Contacts for delivery options were not mapped to advice ([eeb9010](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/eeb9010de607d0efb10be4b95dd319c6b821d989))


### Features

* DFE-4415 cash on delivery ([4f0f13d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/4f0f13d0a8009655f61b851f38854c2efc67e182))
* DFE-4415 cash on delivery ([67dd118](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/67dd118c3aa3b41b8ccf15d8f258b634e481a544))
* DFE-4415 cash on delivery ([6a7105a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/6a7105acc7ba6d89393d46caf87cc25f7181eea7))
* DFE-4415 cash on delivery ([f7ce867](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/f7ce8675a1ca122a3cf1448c85883ea35fa1b9c5))
* DFE-4415 cash on delivery ([ab70c75](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/ab70c75fbaddbe7bb40d26e56f13bfd47348fb7f))
* DFE-4415 cash on delivery ([188dc8f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/188dc8fa7c78a847154f12e9e44824b89ab22649))
* DFE-4415 cash on delivery ([c0702cf](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c0702cf96307b68080a5822fd3c76f79b6f88f4b))
* DFE-4415 cash on delivery ([3b1ccfd](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3b1ccfdcf5c9a8aaf729bff7260a98e74e47a8e4))
* DFE-4415 cash on delivery ([22b47d2](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/22b47d21be6740f89e733cc2e033974375959999))
* DFE-4540 postcode validation migration to kong masterdata api gateway ([3beec18](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3beec1881ad570692732d7cdee63a5973f8b14fb))

# [2.7.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/compare/2.6.0...2.7.0) (2025-05-21)


### Bug Fixes

* DFE-4342 marks and numbers should be added to additional order information as well for air and sea ([95ec455](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/95ec45511cab7a8e5469c48e8b4f4b9f2c999fd9))
* DFE-4342 marks and numbers should be added to additional order information as well for air and sea ([46894b2](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/46894b28c9921b01414453240239d38bebfc2fee))
* DFE-4342 marks and numbers should be added to additional order... ([3da941e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3da941ea0dc299af750ffa2058fc8a9f5b73a9e3))
* DFE-4529 dip conversion for fcl weight, use orderline sum instead of verified gross weight ([3723e88](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3723e88896ae1d6598548ded205757a2275549ea))
* DFE-4529 dip conversion for fcl weight, use orderline sum instead of verified gross weight ([57fb8b7](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/57fb8b73b5ddc6e589131052e66455079c18235f))
* DFE-4529 nullpointer if volume was not available was fixed as volume is not required on sea ([bc98c29](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/bc98c292fa6d73535cd833e62b25fcb05863957f))
* DFE-4539 Reorder order lines total weight and quantity for sea orders into order overview ([3615c4f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3615c4f4ef0c1cb59b63391da576704bc443ae0b))
* fixing tests on develop ([1f3d397](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/1f3d3973287c95ee5da24ca0533539a9de281658))


### Features

* DFE-3290 Introduce emission forecast calculation to book ([eb9a07d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/eb9a07d6f9f40a7f388345bf0ca11388793c0ffe))
* DFE-3290 Reduce retryLimit to 9 - Does not trigger calculation for existing orders on PROD ([731f473](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/731f47350364104b04792aa0332f5a6e6302651f))
* DFE-4311 - Provide a road forwarding order debug endpoint for dev and test ([c20e6c9](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c20e6c95372acbcb79e81c905d2b75da1f0632f2))
* DFE-4320 - Use Masterdata Languages from GeneralData service ([a0fc812](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/a0fc81229c6fa8731cb14f0aa22644d3f43ac4a3))
* DFE-4350 Adjust length of contact name ([b1421cc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/b1421cc611e7f021a2acfea347b81e57b7b48ef5))
* DFE-4350 Adjust length of order address and order contact attributes ([e188b29](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/e188b29c7d768434f9a5475264c6ef467acd80cf))
* DFE-4350 Raise API version ([e1c6747](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/e1c6747971ce11c309b9961ca3aec41d329c3335))
* DFE-4473 - Handle missing book branch ([10621ed](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/10621ed15054e1add002352d90463c6ea9ec0516))
* DFE-4514 use cmp-active-account instead of active-company ([c610efd](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c610efd0b43260afc164aa6f4d81dceb0da97c0e))
* DFE-4527 - add collection option to Q2B ROAD coll orders ([f60e8d8](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/f60e8d84ea8b0c57ae786f0b8c32a3ca1b784d37))
* DFE-4529 ([5f348c5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/5f348c5edbdedda6b57a802f98bf4b8ddece9c43))
* DFE3-2520 remove overview references prefixes ("label") ([59df626](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/59df6267765409c273e047b44ac71578058982f2))
* DFE3-3314 include IDENTIFICATION_CODE_TRANSPORT reference in overview ([358d98a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/358d98a85bc38372b8ba6d42523d706e53c565c0))
* DFE3-3314 overview translate reference label "uit_code" in backend ([d1a711d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/d1a711d365038ad4d486dee872b24d8996a1cbe7))
* DFE3-3314 prefix overview reference values with subType ("UIT:") ([e198a13](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/e198a13585d6a6a82118b1d9b689048e812d7dee))

# [2.6.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/compare/2.5.0...2.6.0) (2025-04-22)


### Bug Fixes

* DFE-1726 DFE-4363 Append delivery options rules for targo on-site fix ([505f749](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/505f749a4d6904c39512992ee6229aebd94a991c))
* DFE-3555 adding WA and RO to whitelist as they are used in the quote to book maaping of packaging types ([d0980e2](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/d0980e20eedd88cb960ee9c53baf235098144aeb))
* DFE-3555 limit packaging types to current available on prod ([30b60a8](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/30b60a87950851034deadfbaac04c19c7bf5fb0a))
* DFE-3555 refactor packaging options ([b6f5993](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/b6f59934dae47e4c07b71c1b498d95dde57b62a2))
* DFE-4263 dip conversion for fcl sea export ([3046173](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3046173f323c2f40d958be032d653654a6be21c0))
* DFE-4263 dip conversion for fcl sea export ([3899341](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/38993410caa523f7251bf04e247f99eec5b7b242))
* DFE-4263 dip conversion for fcl sea export ([92272d5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/92272d503736f71aa4e218fd925e22a0c38db585))
* DFE-4285 Code review. ([ac864fe](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/ac864fe8e56ce8a393aaa86049e4e61f10b2e138))
* DFE-4285 Fixed blocking the order submit for Irish principal. ([e03e051](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/e03e0517d027932a5daf249978a129ca3aa33afb))
* DFE-4348 forwarding controller now uses mapped country codes to dachser country code ([9bfed38](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/9bfed381796a3774044e2a13ab23305f00331a5a))
* DFE-4353 Fix Liquibase MD5 checksum for SQL Server on Docker ([c76a7ad](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c76a7ad32bc2d8596ed451f3df9ed997be8336d1))
* DFE-4353 Fix local SQL Server setup ([1925048](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/192504885d4bb52c5d159087592b6affb7a2dfb8))
* DFE-4422 adding a profile to local for enabling h2-console ([9390e1d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/9390e1ddc5944842b2f227c49e642fbc9ffefce6))
* DFE-4422 adding a profile to local for enabling h2-console ([44aec1d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/44aec1d57fbf54f096b94547ec7b009911bfe260))
* DFE-4461 Fix Hikari HTTP security ([b54b492](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/b54b4923041599d57371c800c0c321762ec142be))
* DFE3-3404 division was missing from the cache ([5e71709](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/5e7170941a55ca560730d021767300cfddecf6b5))
* fixing-develop ([a33dc48](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/a33dc488bf1a3271081166381354128112d895ec))
* fixing-develop ([22bf940](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/22bf9405c418945c9c138346f29d1363145b6b54))


### Features

* DFE-1202 - Q2B for ePricing Collection Orders ([c276c2b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c276c2b535153247017ea23445a26fc2f3ff99d3))
* DFE-1726 DFE-4363 Validate possible delivery options for road products ([0569d59](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/0569d59c6cc13b3ebe8b664933b3558969a30f5c))
* DFE-3444 inco terms refactoring ([b4b871b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/b4b871b47aff74502b9211d72b9c5089495aa915))
* DFE-3444 inco terms refactoring ([b9413c2](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/b9413c27dff2808c9db7c3c987cd3a7d59b56a7d))
* DFE-3444 inco terms refactoring ([0005709](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/00057095f1fd2715a0f73e3ae46676639054d903))
* DFE-3444 inco terms refactoring ([e0efd32](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/e0efd32208e9c6aacd8d4301bee69adff42fc360))
* DFE-3445 refactoring of delivery products ([5b78610](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/5b78610e7338c409bd679b3ceead0962e416fc92))
* DFE-3555 refactor packaging options ([be0b38a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/be0b38a9d6bbfd710f37d70331a2850e467a1523))
* DFE-3555 refactor packaging options ([e017e0e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/e017e0e8dc52f585785977703e72a842f4251f71))
* DFE-3555 refactor packaging options ([1399037](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/13990379e0677a7efc98fde014c08c6f5b71c4ca))
* DFE-3555 refactor packaging options ([b6dcde5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/b6dcde5eb188d856674d4e8742de11947885d770))
* DFE-3555 refactor packaging options ([54dac78](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/54dac78625e77cab517ee6d3aeee1c97385fb25e))
* DFE-4078 Introduce mime type to docuements ([97abf2d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/97abf2db8788ee077f702014d0b9b49720824795))
* DFE-4078 Introduce mimeType ([102d1dd](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/102d1ddd6149a65cb878b6cdc4abd4995cefcaaf))
* DFE-4232 - Calculations for order overview ([e7d83f0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/e7d83f0aca973eac5fc7a03cc158450be23a670d))
* DFE-4380 Register Hikari CP pool JMX monitoring bean for Spring local profile ([0c5832a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/0c5832a6c544b4e4ccd8a8f45a50d0a538c2d022))
* DFE-4381 Dont map shockSensitive and stackable for FCL ([44c57d9](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/44c57d90e2ba068696aeda23a80dab86e7668a6a))
* DFE-4411 - raise log level for external ILO label service ([c80d867](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c80d8676a110f15589e015f1803d64ab45962e88))
* DFE-4461 Publish Hikari pool metrics over HTTP ([882ed18](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/882ed188cab5d29b6a7ba79609de6213271f158b))
* DFE3-3099 remove filter/sort dependencies in transfer list API ([a3c8164](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/a3c8164355c844cda5449fdf34f16ada86ab7251))

# [2.5.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/compare/2.4.1...2.5.0) (2025-04-02)


### Bug Fixes

* DFE-4249 Adjust regex to allow empty strings and raise api version ([889bc6d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/889bc6de6c72222721d8a22c2d26e5d4447ec1cb))
* DFE-4250 - Avoid sending xml for same shipment multiple times ([f29a07c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/f29a07c34803763dd6a7ed534ce32bf3e56f3e21))
* DFE-4387 - slow customer list endpoint ([ef905bf](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/ef905bf33abb683b9afcc7d2e9e67ee673887bcc))
* Resolve update/delete issues with full container loads ([855c02d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/855c02df5a9adf49d83a19e997a2c83fa6bc8c22))


### Features

* DFE-4232 - Missing calculations for OrderOverview ([b184aee](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/b184aee716af7b206702309b3b8e5495e4344245))
* DFE-4263 ([4e00919](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/4e0091983bf6806b6c7bd6b55a2acbfb18cc5ea0))
* DFE-4263 ([58e036b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/58e036b35ee7309f960aeabaea01dbb4bacb854a))
* DFE-4263 ([b465390](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/b465390d4e2a5ad454f0d3221bbb43c8f61128dd))
* DFE-4263 ([a5e260c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/a5e260c44684c64e32637d77e6a4dbc76510e311))
* DFE-4263 dip conversion for fcl sea export ([2ce61ba](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/2ce61bafc8f969110d3a205f18ce2f752def5529))
* DFE-4263 dip conversion for fcl sea export ([3d8d70d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3d8d70d1aed9af6dc2690bffc47f1a76baded978))
* DFE-4263 dip conversion for fcl sea export ([f8a357b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/f8a357b2daee7d8eddfbc632284849db953c7edb))
* DFE-4263 dip conversion for fcl sea export ([f64bc20](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/f64bc203358cf9adcd0fade50bf3aeb16b4b8acf))
* DFE-4263 fcl sea dip export ([69d0b49](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/69d0b49a01654feb66332f13b0bf0070f7823fe0))
* DFE-4271 Introduce NatureOfGoods service to fetch last 5 used goods of the user ([335c4b3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/335c4b35bb0a97ff89eea48987e665bec374bdbf))
* DFE-4338 ([1d2178e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/1d2178e273123f4826d1179f43331a918cd6cbc5))
* DFE-4349 order type switch ([b175195](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/b1751956fe3af3bd334ecda6888b1bc1cdddd9af))
* DFE-4376 Restore old behavior of sending collection option and delivery option contact data to edi ([be046d3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/be046d3d3a0eaa58ba3cc220d2f3d62bb2f8245f))
* DFE3-2115 handle transferlist database not available error ([158e91c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/158e91cb74f0bf8960e378f473ae29c21a1eb640))
* Merge branch 'develop' into 'master' ([ecd41ca](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/ecd41cad1b40bc58b9855b86efb5f8d7e6447c62))


### Reverts

* Revert "Revert "chore: update java version to 21"" ([1dfd46c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/1dfd46ca701465aaa2cfe5541f7406c885cf986f))

## [2.4.1](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/compare/2.4.0...2.4.1) (2025-03-19)


### Bug Fixes

* DFE-4376 Restore old behavior of sending collection option and delivery option contact data to edi ([7d6af69](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/7d6af6955bbad7b0cf31042713647e3dbd275cb6))

# [2.4.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/compare/2.3.1...2.4.0) (2025-03-14)


### Bug Fixes

*  DFE-4082 - packing aid positions ([3418e52](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3418e524765123d5b0e76b14c304ba4c035b2d5b))
* DFE-4038 - Hide unsupported references on order overview ([d59bad5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/d59bad5007cf9c393b5330cf53a26d5fb37d6b5d))
* DFE-4038 Support both lower/upper case for road reference subtype prefix ([138ebb9](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/138ebb9846e193078c11c63daa8613dcf2194019))
* DFE-4082 - packing aid positions ([4899199](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/48991992daa1e96ada03d091d07707ada0cc933e))
* DFE-4128 Validate Min 1 orderLine when Sea LCL order ([3ec7194](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3ec719462b8c8ffeca0b86063d22629192520dd0))
* DFE-4337 Use MERGE instead of ALL for cascadeType of packing positions ([6c19db6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/6c19db60e8de17d899085b55f3294ca86355bb6a))
* DFE-4347 Fix XML DIP mapping for UIT codes ([662544a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/662544a9ac41ac234cda41a6b2247ea03f21f763))
* DFE-4352 - Adjusted packingPosition weight calculation for advised mapping ([b84c65c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/b84c65c82463cca5e7e895bba7c4c7ffeb55d367))
* DFE-4352 - fixed update of packingPositions ([09c2d2f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/09c2d2f81a90bf506964030d1525916ad8b2bc5b))
* DFE-4354 Enable collection order in BE ([294eb38](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/294eb383aaa196cc4a1fe70a4d46412b5f248f50))
* Extend advice information with packing aid position data ([5566e4a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/5566e4a8511114b869edb1446af3bca3ed417828))


### Features

* DFE-3446 refactor to use countries from generaldata and not directly from masterdata ([2700bc5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/2700bc55dcdc18ab2afbb43c5385a4d16f43c7f1))
* DFE-4038 Check allowed countries for UIT road reference 337 ([9cec823](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/9cec8234007ca6a2808ceacaacb99b09cf7a9dbc))
* DFE-4038 Define road reference subtype at storage level ([1dd3a9f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/1dd3a9f293281f4ca5f459075ca266d664d07d85))
* DFE-4038 Evaluate if an order is shipping from/to a specific country ([bb1b184](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/bb1b1841756ca301923d11df07d4bdafa5ab397c))
* DFE-4038 Map road references 077 and 337 between OAS and JPA ([45f21fa](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/45f21fa263bd6b602235021218a8fb15e55614a8))
* DFE-4038 Validate format for road references 077 and 337 ([4a5ad74](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/4a5ad749204a17f85fa18a1d92f9a38c2eaed1c6))
* DFE-4249 provide container type endpoint ([be813e5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/be813e54df636977c5dc188c34760343139ebede))
* DFE-4340 container types whitelist ([3bc29de](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3bc29de93173dd4bbea62dec9592f78ec589886e))
* DFE-4340 container types whitelist ([e8207cf](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/e8207cfd410ef059305af2857bbe896184da9927))
* DFE-4347 Support UIT road references for DIP mapping and XML serialization ([768dcda](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/768dcda9da631d306ba8dcdf68dde9c49638ce06))
* DFE3-3032 map packing positions for transfer list details ([7a83cc7](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/7a83cc7d1a4effe5b8ba40013463fb642a9b94ec))
* DFE3-3282 overview map road collection order ([6a96d6f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/6a96d6f079e391eaf0fd9dab65338d99a5de8971))
* Merge branch 'feature/DFE-4347-dip-support-for-booking-and-uit-references' into 'develop' ([1ab9eb6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/1ab9eb65f85acc2dc3a1e26d004e83a39cd93a50))
* Merge branch 'feature/DFE-4347-dip-support-for-booking-and-uit-references' into 'develop' ([84df659](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/84df6599cdfdde4afa93a9bd4ca8ae9c57186e74))

## [2.3.1](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/compare/2.3.0...2.3.1) (2025-03-03)


### Bug Fixes

* DFE-4337 Use MERGE instead of ALL for cascadeType of packing positions ([63f6c03](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/63f6c03ddf7081383ca3dcb53c3735736325b501))

# [2.3.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/compare/2.2.1...2.3.0) (2025-02-27)


### Bug Fixes

* DFE-4235 Fix collection interpreter options i18n behavior ([cab3cef](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/cab3cefe83e33cee56407bdc6122ea0d12c004cc))
* DFE-4235 Repair collection interpreter options identifying key ([0484184](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/0484184e92ab11a348692327ba4dddf5e77ea4a5))
* DFE-4245 Fix i18n key change for SQL Server ([717b789](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/717b78943e37432d9407a982524616a2c320c6c6))
* DFE-4253 Fix consignee address mapping of road collection orders for DIP ([aa999d9](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/aa999d936adab1dd5d44fcf5c5c4d3fe6e50fcdc))
* DFE-4253 Fix consignor address mapping of road collection orders for DIP ([928101c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/928101c4dd760f84ed1e035a33ae84f93da859c2))
* DFE-4284 - Changed Weight type from Double to BigDecimal for precision ([c470885](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c470885848330e1c6d606a4c3dd2eba51d419d8f))
* DFE-4284 Introduce validation for decimal places in weight - remove custom annotation NullableMin and replace it with Min and DecimalMin ([b7e3092](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/b7e30926cd6fedf4164d119ae42430bcc39bc317))
* DFE-4287 - using decimal for weight column in QuoteOrderLine ([e8429d8](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/e8429d854ad1b2c6c88c5d6aa01e5ca4d319d378))
* DFE3-3257 change query to be mssql/h2 compatible ([ddadbb4](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/ddadbb4f38a8b3cb56b489fc91c6d40d237d4756))
* DFE3-3257 need different custom queries for H2 and MSSQL ([f183d6a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/f183d6aac42003a08e15d1db8f556df60a1af10e))
* DFE3-3257 remove/fix STRING_AGG (8000 char limit issue) ([1933eb3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/1933eb3e5bc1bd6e4ed4f3bd0f59c6266e1ee238))


### Features

* DFE-4090 switch order types ([f20b475](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/f20b47501382ae0115737130d5be4be19af62723))
* DFE-4128 Adapt order cloner process for container loads ([2927211](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/29272118f6d2d83c019267d9b46350f88dabd8bf))
* DFE-4128 Add boolean fullContainerLoad to sea order ([95d4f8f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/95d4f8f3149032ebfa6e1277a977b8728fd6af5b))
* DFE-4128 Add new XSD for ASL - correct packagename ([bebd1dc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/bebd1dcad7582ba2693c820b9db7d3e554de278f))
* DFE-4128 Added OrderBy for SeaOrder -> FullContainerLoad and FullContainerLoad -> OrderLines ([f49e4ae](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/f49e4ae2882134911e9293507bd625e0c4c8a623))
* DFE-4128 Introduce FCL to book structure ([5e92a95](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/5e92a95ca74b6297c5564368669a0507e89892f9))
* DFE-4128 Make AfterMapping for SEA more readable ([7835918](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/78359186b8500d665af90a253f1d583b7e6048bc))
* DFE-4128 Make NotNull constraint violation conditional for LCL in OrderLine ([9dbb198](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/9dbb19841e9bde093d31ad956e29505b14f7ad4c))
* DFE-4128 Map referenced right and introduce some update tests ([8e83c9e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/8e83c9e974139f23fd0beafb15eaf0a4c5cf97e4))
* DFE-4128 Remove container size and add sorting position to full container load ([37a0267](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/37a0267040bd554a567878423a37a2d12fd94262))
* DFE-4128 Update book api version ([ef4bc86](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/ef4bc862a5eebc8a89e0e3495bbc700309ee04e7))
* DFE-4167 different test config approach ([44bfb57](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/44bfb5720e04c3d5d03f8191c75928235820ffc9))
* DFE-4167 different test config approach ([0cf0575](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/0cf057594afd1425124d8bb95336c7871a283c75))
* DFE-4167 different test config approach ([c7303e8](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c7303e88ccc222e614b29b158a3c12dd14d77e19))
* DFE-4167 different test config approach ([0cc179c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/0cc179c28e580a534c4792c2e7eb6c59a167b461))
* DFE-4256 Implement logic for originalTerm Mapper of collection order ([fa2f7de](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/fa2f7de5a14c8c724c7e2579ecc87c572f1d12cd))
* DFE3-2783 add PackingPositionService ([800fcc5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/800fcc513afb9785a2fb6ae813ef118b75e44e1e))
* DFE3-2783 change mapper to OrderLineMapperImpl ([7b6db3f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/7b6db3fa0fcaee9529cbd9f33247f114ec223714))
* DFE3-2783 changed query to get all orderLines with packingPositionId is null ([0f7950d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/0f7950dedd830e4593e7a5b0115b9d9c7502d602))
* DFE3-2783 create PackingPositionRepository ([7d09b06](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/7d09b066550fe63d7bb7846be989f72942cabcf9))
* DFE3-2783 create PackingPositionService ([6acaa15](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/6acaa1528fe87463e274e91fa0b049a94455227f))
* DFE3-2783 extend getTotalWeight and getTotalVolume ([a343a0a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/a343a0aa1cb9bdd5da10e4da6c8ee666e7a68d11))
* DFE3-2783 extend packingAids logic ([d63e4c3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/d63e4c35c82b7379769c051baf38076046a0fee1))
* DFE3-2783 extend query to check IsPackingPositionIdNull + refactor method names ([a080bed](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/a080bed81dc2859d9b5eeabfc3d3ac15dea13b0f))
* DFE3-2783 extend TransferListApiHandlerTest ([a3eac24](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/a3eac24a64e627a95e03d0ad46af01dae54d73ef))
* DFE3-2783 fixed imports ([c4c2694](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c4c26946b5c8c37d0806b452dd344d076558d58e))
* DFE3-2783 fixed query ([62dff75](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/62dff75d554c33185b9bd4aabf2276fe0c61e7a0))
* DFE3-2783 removed unused logging annotation ([e35e192](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/e35e192d203c5434d9610c350b36a6dfe3966cc9))
* DFE3-2783 set Description for all JsonPackingPosition elements ([f631e3c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/f631e3c68a470b3e364894eac19b7c6976c3f6ae))
* DFE3-2783 TestUtil - extend generateRoadOrderLine with packingPositionId parameter ([24e2041](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/24e204103a3ff5cc893468db6b5f8cf28bdb70b6))
* DFE3-2783 use dfe.book-api 7.4.0-rc.2 ([56c0499](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/56c0499278e46935b0b95f7189b4ca81aebef99e))
* DFE3-2939 add /overview/terms service ([d9b6c36](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/d9b6c36edeee04559423c9a7c209fa1afd0e06d6))
* Merge develop into master ([7ac9792](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/7ac979278114f6a7215be781ad1be6447b395a4a))

## [2.2.1](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/compare/2.2.0...2.2.1) (2025-02-17)


### Bug Fixes

* DFE-4284 Introduce validation for decimal places in weight - remove custom annotation NullableMin and replace it with Min and DecimalMin ([206556c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/206556cf01dcb10c96bfe45fbd83b727627147ae))

# [2.2.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/compare/2.1.0...2.2.0) (2025-02-13)


### Bug Fixes

* DFE-1233 - Fill BaseEntity fields for OrderXml on creation ([a2bb692](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/a2bb692347a32b51818e6cec5e8911617f0421f5))
* DFE-1233 - Fix scheduled publishing for future forwarding orders ([5ba1eb8](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/5ba1eb86623c78a65f0c26eb85d6fef71446696c))
* DFE-1233 - fixed tests ([5a6b34c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/5a6b34c1749fa147b83158b4f7a14abea23bb7de))
* DFE-1233 - when the defined basepath is missing the application now creates it to store the xml files instead of failing the submission ([6567184](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/656718436c7903403d61104d67886d48149ceed9))
* DFE-3901 - Consignment Label ([22092f8](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/22092f82ef0a642726d19dbce16e9c05c5315682))
* DFE-3901 - Consignment Label ([0753b8f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/0753b8fa6ae3facea97b6ab428db9c92c5f14d9b))
* DFE-3961 - add missing db update script ([5c95d7a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/5c95d7a3310d6f49fca99dcee005b3e252a5b04c))
* DFE-4033 Check for reference value duplication not for type ([4e7de25](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/4e7de2500e48318967b453227f1a3603db543e68))
* DFE-4077 delivery or pickup address q2b locked after switch ([a6f6a64](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/a6f6a641a53575064da8a3b00ca0401b836e2506))
* DFE-4077 log cleanup, stacktrace shortened in tests ([c2ded4c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c2ded4c960727b0066d0ed05faa652cfb5430587))
* DFE-4119 null check for pickupaddress, deliveryaddress ([8e32bf6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/8e32bf6783d6d812b2db535f823b9f3172f58aab))
* DFE-4169 Fix order overview having an SQL field sequence inversion ([a7c8678](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/a7c8678fc1abf175098339c234c8536b588b99e2))
* DFE-4172 Use fixdate for deliveryDate not collectionDate ([064a35d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/064a35d08fb7d61000391641026b2f8d1753e8ed))
* DFE-4179 ROAD orders are savable without orderlines when customer is allowed to create packing positions ([8a8054b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/8a8054b74a3a1c980f4681c1118de1c808c6bed0))
* DFE-4207 address update without id mapping ([3d6f2ec](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3d6f2ece325698ebba791f1195010d3f689112e4))
* DFE-4214 Accept null mapping for weight in advises order mapper ([bb78918](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/bb78918e0cf717362a906b578e353767fe3f5c49))
* DFE-4214 Add null checks for weight ([c637298](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c63729813952a38e9d1b60802f8cb30525c05954))
* DFE-4214 Delete NOT NULL constraint from quote_order_line weight column ([6272705](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/6272705849d3edfe9f6d6c7c47eba07986d0bc86))
* DFE-4214 Include default constructor for OrderLineConstraintValidator ([98256cf](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/98256cf73c6082525a48f182edaa9c4e658e6e9b))
* DFE-4214 Use right annotation ([fdcd515](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/fdcd515211babef2f02848a0fefbb2673e02f2ca))
* DFE-4236 Map collection instructions to specific XML tags for Domino world ([64bcbb7](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/64bcbb79b453e0801aa6d40a14f308e4e3e712e8))
* DFE-4251 make buildContactInformation null safe ([f50e87b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/f50e87bc3455b87cfc232cc65b6715950828beca))
* DFE-4251 Sent contact data from consignee inside CZ prinicpal address EDI ([3ed5901](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3ed59014c318167d7a78efe62198f618ca92e425))
* DFE-4251 Sent contact data from consignee inside CZ prinicpal address EDI ([475ff8f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/475ff8f2db25ec0f4b033b8ecc9eeda060897c09))
* DFE3-2927 apply API changes ([33cb489](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/33cb489e87729d6cbbd342720b74a63d85c1e551))
* Use custom enum converter class for previousStatus to map null values in... ([3907b24](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3907b248a35a657a441beeeec03a7b6eb2c02945))


### Features

* DFE-3835 Add freight payer to be savable - Add EDI mapping for customsGoods collection order ([c3fec60](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c3fec607d902530fc6715a1156c1d8423a302839))
* DFE-3835 Correct validation for freight term - Should only get validated for forwarding orders and not collection orders ([51ed5c8](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/51ed5c8920a5622e255b7a71970b05ea48efbbb7))
* DFE-3835 Freight term is only getting validated for forwarding orders ([45266a6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/45266a68781439c820727a99c8ff869a40f7f6ef))
* DFE-3901 Implement printing for at labels ([f20440e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/f20440e6897292af9fa15cb036fade15d5167719))
* DFE-4082 Adapt calculate of sscc labels when there are packing positions ([4211738](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/42117386bf963247ce5979dd5d37d64777d26136))
* DFE-4082 Implement creating packing positions over mapper ([864fd99](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/864fd997122bc9cb21ed5f81d29f0392c477ca3e))
* DFE-4082 Implement PackingPosition for road orders ([c49af0d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c49af0d8bc5f7a58da7cb8509a7cd5173473f164))
* DFE-4082 Make order not nullable for orderlines again ([ae25a5e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/ae25a5ea6eb2a4885278508a0e46a79994ee4dab))
* DFE-4082 Reference packing position after saving ([cca97d4](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/cca97d4c9e3c6458cde3fdb87207658dbfbe42fc))
* DFE-4082 Throw a exception when user does not have setting for creating order with packing positions ([ed2e844](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/ed2e844d03480505ce11c94cf1e83bb1882dab55))
* DFE-4082 Use released api version ([0ed11b9](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/0ed11b9e1743cc8b948b95a34ca2c659623b1368))
* DFE-4091 Introduce new base class for packing position to reduce duplications ([c4efbf2](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c4efbf24f12f705c00b315360de8c57df89d4fb5))
* DFE-4091 Introduce quote to book for packing positions ([5198815](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/51988157a797f300fe94ecb6f9c2d157b1c4985e))
* DFE-4091 Make clone work with packing positions ([7a521e0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/7a521e0c9ad64d11a648b24354751417ad4f3957))
* DFE-4091 Raise book API version ([7951395](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/7951395c19a702a3c2f451cb5714c8086efe4279))
* DFE-4092 - add PackingPositions to EDI for road orders ([ab2cb5f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/ab2cb5f2b7c95abbabeeb2b533591749f531c33c))
* DFE-4139 Update type for CDCTD to CDVSD - Adjust label also ([770c31f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/770c31ffdccfc7d2a6de8e64a254b76c8b86cbb1))
* DFE-4169 Improve order overview performances at storage level ([3c05a0b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3c05a0b94770f0413618e8f50e03ff631a16c066))
* DFE-4169 rearraging scripts ([7cba659](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/7cba659bb1dce26906b69d7ddf03d8b454e22dc7))
* DFE-4214 Implement new validation annotation NullableMin - write tests ([5172700](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/5172700c41f58e6ff3b809fc37d8aa1a696585f5))
* DFE-4214 Implement totalWeight validation for Q2B ([33f3837](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/33f38376b4897ba0a413011f5707d374e2f48355))
* DFE-4214 Introduce validation for at least one weight needs to be required for Air/Sea and Road Orderlines - moved logic to own constraint validator ([2f72244](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/2f722449adb5634f372a29fd8d421920f76cda12))
* DFE-4214 Introduce validation for at least one weight needs to be required for quote orderline ([d8a69b0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/d8a69b02782013158b44e2c1efaac54301331e8c))
* DFE-4214 Use Double not Number for abstract weight ([09ef02c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/09ef02c1a85450b6ae721b8dcf655fea1810e97f))
* DFE-4252 Adapt collection option mapping and use shipper contact data for PW address - add shipment text for collection option ([7c5c0e5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/7c5c0e56acb28fc448805862f1f0aa7eed09b2d9))
* DFE3-2927 adjustments for API changes ([166349f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/166349fbde3f024b614af5c5e1bebb8f3793785b))
* DFE3-2927 move /transferlist API to BO ([3c0e9e8](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/3c0e9e86efe4bfba01e5494bca9f510ee68a0d38))
* DFE3-2927 use book app with trackandtrace extensions ([c064942](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/c0649429a49d8b2b0ef910d540ac88b578c9cae3))
* DFE3-2927 use local book API version ([6a4af36](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-backend/commit/6a4af3629022a00a6a43b9c766cc63cff1239ac0))

# [2.1.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/2.0.0...2.1.0) (2025-01-16)


### Bug Fixes

* DFE-1233 - Fill BaseEntity fields for OrderXml on creation ([76900dd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/76900ddb6860fd43a5af0b0f4cea000f7ab0ca61))
* DFE-1233 - Fix scheduled publishing for future forwarding orders ([c889363](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/c8893634360bdd4861bcca5f539d7bea9c72ffe9))
* DFE-1233 - fixed tests ([872bb97](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/872bb97c8decae6d61ff35ba6ec97b0266c406de))
* DFE-1233 - when the defined basepath is missing the application now creates it to store the xml files instead of failing the submission ([ea8781a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ea8781a6418f588d5fca8debabe299e4447daf2d))
* DFE-3961 - add missing db update script ([bffc2b0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/bffc2b041af1b280e96f0738f0608c9fa5abde36))
* DFE-4033 - Replace generic wildcards with generic types ([60dca17](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/60dca17441d94f129c34179e1e5556930b12bcf5))
* DFE-4033 Check for reference value duplication not for type ([21b20a6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/21b20a610d171cd1cec143b852798c534458aacd))
* DFE-4169 Fix order overview having an SQL field sequence inversion ([045109e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/045109e4ccfbbd5df35b2767726efa919e3aa984))


### Features

* DFE-1233 - Schedule Orders ([cff4fe5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/cff4fe52469f772e2a77bd82197f2429e74ea44c))
* DFE-3835 Add freight payer to be savable - Add EDI mapping for customsGoods collection order ([47dd7d6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/47dd7d6dd00e7c42a2fbb5ba67b8f04a39bc7454))
* DFE-3835 Correct validation for freight term - Should only get validated for forwarding orders and not collection orders ([2a673ed](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2a673eda7d8a9def93143b87e93fff13ddae0e10))
* DFE-3835 Freight term is only getting validated for forwarding orders ([dc47307](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/dc47307a41ca54e88b92208aa53348ec8bdeac1a))
* DFE-4082 Adapt calculate of sscc labels when there are packing positions ([3a56c2d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3a56c2ded91e545272c70c95520a552c721b534f))
* DFE-4082 Implement creating packing positions over mapper ([59b2406](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/59b2406ba77fba4507ebe7796edef7d515b3608b))
* DFE-4082 Implement PackingPosition for road orders ([4ae8aab](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4ae8aab1dd2ea11328a04015defb35ce1b4d4001))
* DFE-4082 Make order not nullable for orderlines again ([887e386](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/887e38601ffa44ddbcd2dadac0e93b78a10a882e))
* DFE-4082 Reference packing position after saving ([f6e8be1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f6e8be1648614ce4ba93a8ffdf0d6e52a78203f9))
* DFE-4082 Throw a exception when user does not have setting for creating order with packing positions ([640ca16](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/640ca16ff7db9cb38437116c5077bb589adc4b2c))
* DFE-4082 Use released api version ([54b27e0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/54b27e09332a829fb3276364f638d57fa177c237))
* DFE-4091 Introduce new base class for packing position to reduce duplications ([d3372bc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d3372bc506371c1255ba2af653fec955a2dfa232))
* DFE-4091 Introduce quote to book for packing positions ([5fd8ba7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5fd8ba72ae7cd9367a0ea3818a9e296e9a5f79ed))
* DFE-4091 Make clone work with packing positions ([b5fe41c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b5fe41ca70ab62e84cc1eae29818b4d344c0780a))
* DFE-4091 Raise book API version ([66dc413](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/66dc413b910fa70b1381ffe02d5b6be815d7da7c))
* DFE-4169 Improve order overview performances at storage level ([4cfec91](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4cfec91062cfa786c412edb88976181b88a7c8d2))
* DFE-4169 rearraging scripts ([348fa0a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/348fa0a74a611012abf702e14f1cbf56cc29ea75))

# [2.0.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.28.0...2.0.0) (2024-12-11)


### Bug Fixes

* DFE-3982 400 on deleted status fixed ([22d8c42](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/22d8c4229f6724a46bb41b04e25fabad6037d30b))
* DFE-4069 prevent submit on already sent order ([ae30f3f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ae30f3f435ad276d19b339fa39f982b98f279443))
* DFE-4136 - Scheduled deletion of ForwardingOrder fails with NPE. ([f486045](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f48604524c77e8dfd729372d35e7bd0e164a4130))


### Features

* DFE-2802 Should not ignore null values for port and iata to trigger validation ([eac7b10](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/eac7b1040cb8a998e143e96373feb17883e067c6))
* DFE-2915 sort documents by default ([9e3610d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/9e3610d55141db3bb5609e67371739b94569df2a))
* DFE-3237 Use right BookErrorId for routing service ([815f044](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/815f0440261f4372a83d5736345b7b98b41065d8))
* DFE-3265 differentiating sea and air for packaging favorites ([051c9e2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/051c9e2351c8bae61d61cc7ae9e5db4bbdbd9007))
* DFE-3553 develop rebase ([2e2371a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2e2371a0ca8184a807a93bfe9350d4a18222eee1))
* DFE-3553 pr fixes ([415e3c8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/415e3c8e3c8546b6b7ca872bb40f0aac712ff191))
* DFE-3553 remove deprecated q2b code ([6dd22de](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6dd22def33ea902950ad989bd6725bef9f0a38f6))
* DFE-3553 test disabled for alpha fix ([757f8f7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/757f8f75b6138d79c564864f86e4f234e20f3eb1))
* DFE-3846 sonar fixes ([bc8157a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/bc8157a261390d730af76d58aaaedcbbb27bc571))
* DFE-3846 supported filetypes by document type ([fdd309e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/fdd309e4375cbd6e5c9e963724ea7ff38ab7decc))
* DFE-3982 redefine problem responses for order delete ([307e8c3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/307e8c3920a6adcc412900d4d04d377871ade271))

# [1.28.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.27.1...1.28.0) (2024-11-13)


### Bug Fixes

* DFE-3878 - LoadingPoints using Address instead of BasicAddress to enable name2 and name3 fields ([51bdfa3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/51bdfa3ad6347ede030ba80e3e5d61cb5b21c36e))
* DFE-4017 - Adjust logging severities ([9b22ef5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/9b22ef5ce8288470724a2554f8ef71582373be35))
* DFE2-1825 - Change allowed length of name fields for QuoteAddress ([3025b36](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3025b367e61cb89714e38cffc17b96070c79d73b))


### Features

* DFE-3781 fill label timestamp on old orders ([e46ed48](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e46ed4811f1943972ea04a6d916404c1f440f056))
* DFE-3781 test fix for h2 ([e00f6bb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e00f6bb74d8d40692803d0ee38e396dfbf8890de))
* DFE-3836 Map OrderText for collection orders ([583b7df](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/583b7df313eb31ec5e9f3075aa1856ae3e1d7052))
* DFE-4044 Throw exception when sscc generation service is not available instead of returning empty list. ([f42691a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f42691a2b04d5e55018cbd3d9f717fa116d9d74a))

## [1.27.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.27.0...1.27.1) (2024-11-08)


### Bug Fixes

* DFE2-1825 - Change allowed length of name fields for QuoteAddress ([a45ed42](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a45ed42cce196feb24d5db38e12dc02f5e8e84a0))

# [1.27.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.26.1...1.27.0) (2024-10-31)


### Bug Fixes

* 4012 enable product k in whitelist ([76b6830](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/76b6830de89db1a43e02faf3cc1f1cd919fba75e))
* Code review fixes. ([d55c9c8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d55c9c8e238432759ebb353b3b42d15b49ef35ef))
* DFE-3996 Adjust labels to be translated in frontend ([5775878](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/577587841083b891cfddf9cb1f4c5ef68f10dd33))
* DFE-3996 ErrorIdExternalServiceNotAvailableException extends now from ConstraintDeclarationException ([e468cad](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e468cad096e90270bc3b7a29bce567dc9d2fec60))
* DFE-4013: Use the collection time like it comes from front end ([583a7b6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/583a7b6c1f3a391f234d8c3309c1dbe19c96e9a5))
* DFE3-2745 removed instance variables from service and refactor tests ([04aed05](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/04aed059ec0ba95adb153d1b595573dffefb260b))
* DFE3-2883 - TrackablePackingAidService exception caused due to missing auth in REST client ([29bcb94](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/29bcb94dc170c66b62fc5384f830f328e0ff9e41))
* Should not log error for null postcode. ([017509a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/017509a7030e22531677e2ce71c7052fd500011c))


### Features

* DFE-3781 mappings for status event history ([70a9b90](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/70a9b9059034c9619d8688245afb4d8e0285d408))
* DFE-3878 - Adjust loading points ([045dfc4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/045dfc46d44ce5b2dbe651d7c428140f9ea72610))


### Reverts

* Revert "fix: DFE3-2883 - TrackablePackingAidService exception caused due to missing auth in REST client" ([6869236](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6869236bfbd48c8cfdf2ed6d0c47123ecab36faf))

## [1.26.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.26.0...1.26.1) (2024-10-30)


### Bug Fixes

* DFE-4020 - Add basicAuth to missing masterdata endpoints ([120e05b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/120e05bf819de542bd56ccb9d8b21a2143ee2d2d))

# [1.26.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.25.0...1.26.0) (2024-10-22)


### Bug Fixes

* DFE-3727 - Use BigDecimal for palletLocation ([a734c13](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a734c136fa6b7edfa7a3c39bdecda8c480023c5a))
* DFE-3834 Adjust CollectionOptions to match API ([6883d18](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6883d183e56af0feda9208e1eef327e99d498e01))
* DFE-3834 Adjust interpreter to match API ([b22b981](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b22b9816a7c261d36c2a9804c862548f488dca98))
* DFE-3834 Dont set default text language for shipment text ([441e4ad](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/441e4ad501dee0289a1f5badcc815c29a6c29766))
* DFE-3845 - Remove 'Others' document types for ROAD orders ([08e264e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/08e264ee44860964792fb01c8e7ba7305bc09bcc))
* DFE-3857 Use correct naming for mapping method ([9b92aba](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/9b92aba01d1b622c2856b4296ccb93f91adea8f6))
* DFE-3857 Use more describing mapping method name and simplify code in validator ([02f71d8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/02f71d8af4b16da816d9ef00863e40cf94dd2184))
* DFE-3908 Check also for not yet deleted documents when validating for commercial invoices ([b26e738](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b26e73819e9dda3a727a50494d6735b03493b28b))
* DFE-3927 - ASL import orders missing shipper info for edi ([b5e6941](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b5e6941d8569fa3ac6e22668a1b8183a3b6735d2))
* DFE-3949 correct iso country code for Finland ([3744f11](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3744f11a533fd62f090b66fabe7ad37f7d3ba23f))
* DFE-3973 Adjust null mappings ([cf3b8a5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/cf3b8a555baf9ae17abed78a0c0693c0a986e239))
* DFE-3986 Update country codes ([4632c11](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4632c1132926f97252b55d8ac3058f432d3437b2))


### Features

* DFE-3766 backend side api generation ([cd0d5fb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/cd0d5fb94e9415d51d71cb1771f5dcbfd8d09b1b))
* DFE-3784 filtering order group endpoint, using forwarding domain ([44d16bc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/44d16bc17296b343a15522ee04ab0783d514a8b1))
* DFE-3833 Adjust Q2B side for collection option ([6f080a7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6f080a7d3aa524e8f70a8bd18e16a3ea55d305ee))
* DFE-3833 Use GoodsGroupValidator only for RoadForwarding orders ([caf1dad](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/caf1dadb3e44748178b2a827e947fbfec95901f5))
* DFE-3847 document forward only with document type ([2be8b30](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2be8b306ae2e6e7ee3d013fd90e79414ae68a512))
* DFE-3852 EDI mapping for neutralize address. ([cac4c35](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/cac4c35f0e555afb4594815274abb4d915096629))
* DFE-3857 Add collectionOption for road collecting order ([8c5da82](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8c5da827fc37dee338966762e8475f58dee92bc6))
* DFE-3857 Map differentConsigneeAddress for EDI ([38c2f74](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/38c2f74773ece717351a8702568f0e2ee7077cf2))
* DFE-3857 Map shipment text for collection options and write tests ([36f001d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/36f001d94c49868753f11cc946bc97be2da5acf5))
* DFE-3973 Enable targo on-site fix ([7c17b74](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/7c17b74ac9fafb8283a31caeafa388389da60160))
* DFE-3995 - Extended logs for bad authentication requests ([d5832c4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d5832c423561dc026929c5b7b8cf8c2007e9fb5c))

# [1.25.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.24.0...1.25.0) (2024-10-08)


### Bug Fixes

* Adjust Mapper for API version ([ebfd89a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ebfd89a75b9de8349470f7d222293f8ba95841dc))
* DFE-3567 Resolve bookBranchId for road customers ([34e9be1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/34e9be1750c46d857c18e455a2dcc57b12f24d4f))
* DFE-3567 Resolve dipOrderType enum the right way ([b39a4ce](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b39a4ce4ffd8eccd3ecc56e0324aa9a675d05599))
* DFE-3567 RoadCustomer only holds one division by now ([30fcaa6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/30fcaa60c45f7cd3678bbf88bb8c6eb3bc9d4cf8))
* DFE-3818 Catch HttpClientErrorExceptions for shipper masterdata and return empty list ([01cf8bc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/01cf8bcb28b689ec76d2fddd1239c2c29ddbfc13))
* DFE-3856 Remove FK for principal contact before dropping column ([042d3a2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/042d3a2b3f9bb9494fa9d2782c868615d84f1a93))
* DFE-3856 Remove FK for principal contact before dropping column ([bc0bfae](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/bc0bfae28e8707b00baf8a00b45c8e38052634c5))
* DFE3-2680 Use order.branchId for fetching supervising branch address ([0523657](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0523657c5e9bfc91caf09c1adf40fb068f2a7125))
* Use order customerNumber for relations webservice instead of shipper customerNumber ([1510475](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1510475b739d4a7c12032376c01c0df1c20d1ee3))


### Features

* DFE-3567 Update platform api to V4 ([82cee94](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/82cee946e5eb2ae7a86e16fb00239183ec677cf7))
* DFE-3567 Use multiple branches for ASL - Update branchId when switching order types ([5f46a16](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5f46a16006889dc3a64396d662209fd2b3be9fe9))
* DFE-3839 - Implement Forwarding Domain Webservice ([6492858](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6492858b113fc1d2ee5fd61f10175cc9f3f77d14))
* DFE-3856 Remove principal contact and add contact to shipper/consignee collectionOrder ([be6e3c1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/be6e3c1736d80373585acfb86320e3f2ab4eb1e1))
* DFE-3856 Validate road order addreses without contact ([74738c7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/74738c7bb88fa37366689660b96d0c967cc11cb4))
* DFE-3858 Adapt to updated API ([6804312](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6804312f7a842a7c1de99ccb6b90ab4eb9438c41))
* DFE-3858 Add CollectionOrderCloner ([ad96031](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ad9603170e19489fecc28514b66d53604683b4a7))
* DFE-3858 Add differentConsigneeAddress for CollectionOrder ([6f6d956](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6f6d9563a6b8d96920d218ba768fce110abc07b7))
* DFE-3858 Add missing CollectionOrderSubmitter ([4e35a43](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4e35a43df952b5588489015ddd3651dd3d816671))
* DFE-3858 Adjust mapper and introduce tests ([d29cb7e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d29cb7e8dfb9496733337ffbf887183688dc5fc3))
* DFE-3858 Disable additional addresses for RoadCollectionOrders ([5a28eb3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5a28eb3bdd664285fabd92c21d936ece421350c0))
* DFE-3858 Implement feature flag ([b6ed2c7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b6ed2c7fc225b2c9a64d97c627c8f7daa0d4052e))
* DFE-3858 Update book-api version ([fd0db7b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/fd0db7b98a8fb22a88a2229d69dc0973af76d9c8))
* DFE3-1345 add AirProductFilter and add air product code to OrderOverview ([ab4e796](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ab4e796d60f1679cb21836c11ea07a8c4e2fd8d6))
* DFE3-1345 add test for air product filter ([ae91df1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ae91df1836d6e977dde199ead1d46971b4ddca03))
* DFE3-1345 fix mock setup ([fd01075](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/fd01075fa7fe93563335a5c3b3117441f733019d))
* DFE3-1345 moved airProductService mock, so it doesn't disturb the other tests ([ffa4f83](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ffa4f839668e73a37fda416bc7e14d87d75c3521))
* DFE3-1345 moved airProductService mock, so it doesn't disturb the other tests ([df8c496](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/df8c4962543e69041f6f060cb3081fd5bd90ae93))
* DFE3-1345 use API rc ([8fc9770](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8fc97703ac8ca0a75db387b25885dbdb371e8a8d))
* DFE3-1345 use released API ([e22bfc6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e22bfc691c4b9933042e2e5ad02bb070d3be540c))

# [1.24.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.23.0...1.24.0) (2024-09-18)


### Bug Fixes

* Booking without airport possible. ([9b90acc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/9b90accf57016538de6d52a557f4ec1f6f5d744b))
* Code review fixes. ([f003ce7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f003ce788bd7e4a810a91ffb28fcb7d6d3425537))
* DFE-3777 Create order resend entry when publishing is not possible ([f0bc9ab](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f0bc9ab8191f628d854a20baf4f5ecc10cfaa1ce))
* DFE-3777 Fill sendAt attribute when order can be sent to dib ([767bd94](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/767bd94e2eb038835e48ee83981e81b61595b8d5))
* DFE-3777 Generate UUID (shipmentTransferId) the right way and add error logging ([5f655c6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5f655c65184b3e134d3625a8900f5d4dbaa27286))
* DFE-3886 Do not reset collectionTime and fixDate when cloning order ([545f665](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/545f665b5feda67738201bea484141b41aa5194d))
* DFE-3887 - Q2B Road Delivery Option not mapped properly ([4c9c64b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4c9c64b540b10d7db064a686ad1b145590eb908d))


### Features

* DFE-2472 Implement validatePostCode endpoint ([3f61473](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3f61473225405622558d99e29faf15d7a1003c9f))
* DFE-2472 Use new api structure ([6754448](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6754448426c66747d7f5d4f2e15ca8e93209b9e3))
* DFE-2472 Validate postCode structure in OrderAddressVAlidator ([b414589](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b414589dfbd886bf08bdd439c500c3e2379b5bb6))
* DFE-2472 Write tests and do some formatting ([efc19f8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/efc19f80f11d0a295a07761ce46d5ccc2cfb87fa))
* DFE-3641 - Add request_origin header to REST calls ([767c316](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/767c3168368731ccd06a4b0a8b374be3d91585cf))
* DFE-3777 Add new shedulers ([4749cb5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4749cb58738e0d052dc2dd56cf4640664fb4980e))
* DFE-3805 response bulk label print ([3a891c3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3a891c3044d8bb1072a3718398def174f36649ac))
* DFE-3805 response bulk label print ([897a81b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/897a81bd3d15d6a3202638765d26f5b8db9fded2))
* DFE-3812 collection order edi export ([39ee092](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/39ee0922619173d335e42d97602733322226e812))
* DFE-3822 sorting of orderlines now relies on number attribute ([eb72934](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/eb7293470647d68cf020565532d8e494699495f1))

# [1.23.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.22.0...1.23.0) (2024-09-05)


### Bug Fixes

* DFE-3736 Update bean usage in test ([e3a0442](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e3a0442933f90ba0086bd67314345fe380bf1934))
* DFE-3736 Update the version of the service and use new bean ([78c3fd2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/78c3fd279b8d28d4917a183be217b807e3ef05a0))
* DFE-3789 - Cannot save ePricing as Draft ([d46d486](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d46d486f0674cd58a6185b63f6c18cbb05f2bf33))
* DFE-3794 Add transaction context when updating an order ([3df3b90](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3df3b905f6e232f142690f2b3a4b1d044f2c10f6))
* DFE-3806 - Q2B AIR ([e424471](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e42447194ffa4cca3eadd85cd5e50c179ba39222))
* DFE-3806 - Q2B Air Products ([9b4c5cd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/9b4c5cddbdedba7e2bdf70a671a64e02b7ec26ca))
* DFE3-2512 Pass numberFormat parameter to PDF service ([9797b80](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/9797b804de02dd03c753ee1c765ed354a37d2ccf))
* DFE3-2512. Change "numberFormat" -> "numberLocale" in transfer list pdf service ([3166960](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/316696042b790fed29e594b968aadf516bbc453e))


### Features

* Change display data pattern for principal select. ([25bce92](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/25bce92c370a73722966ebc3d35a7b2d3ae192f2))
* DFE-2551 Limit recently used document types to 3 ([04e5e83](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/04e5e834b1b32df956d19ca469cbb8db459676f0))
* DFE-2792 Remove documents on save ([2aa3f25](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2aa3f251e5064b0b9a23ef2d564f499cc5ff48b3))
* DFE-3464 Update label ids ([e88f278](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e88f27828d2c58642cc8fe6ff545b4a8d44ff37d))
* DFE-3707 Implement new endpoint for retrieving ireland eircode town and county ([5fecaaf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5fecaaf0ec8f365407efc784aa225cb5bf0cf327))
* DFE-3707 Implement null handling of endpoint ([12785de](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/12785de7c8bc5e460ffa0fa7b41d44621832f54f))
* DFE-3712 Map order text Information for customer settings ([3a51375](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3a513754dd7d294978fce1a28367551b0e112945))
* DFE-3798 send advice on book directly ([c4d2d53](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/c4d2d530b7954d10052babe3991ffd7152978fd7))
* DFE-3798 send advice on book directly ([240e749](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/240e74976b7ea8dc82d354a0a526cf33924561b4))
* DFE-3799 - Implement Hikari config endpoint for DEV and TEST ([5da4326](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5da4326a064b8ddd7421b3e03ec682d72e1d91c6))
* DFE3-2223 Use customer_number_with_segment filter for Overview and Transferlist ([3c056f1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3c056f1d31c20081e35a46ef2c98d1ed7fed860c))
* DFE3-2512 semantically fix test ([5c3447c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5c3447c9af5c39b34cb03def16f92fa1826542ab))

# [1.22.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.21.2...1.22.0) (2024-08-08)


### Bug Fixes

* DFE-1987 - goods group mapper adjustment ([211999a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/211999a260fad73e67e173419bc2fb91928211a3))
* DFE-1987 - goods group mapper adjustment ([8bccbbd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8bccbbdfc4a2e0587ad50b24569a20d8960b6383))
* DFE-3700 - using a fallback language for road api requests that have to be mapped to dachser lang codes ([8c9ab05](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8c9ab058d4c8cf94898a7591f43892b32121074f))
* DFE-3700 - using a fallback language for road api requests that have to be mapped to dachser lang codes ([f13fbfe](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f13fbfe48a1aa9041a29bd5fcd3fa19d5a5bbc2f))
* DFE-3721 - Goods groups were not mapped properly from quote to book ([b85a712](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b85a7129957aa8ba6c47aa4c6341757a8cc0a7fd))
* DFE-3724 - Fixed GoodsGroups and OrderGroups ([d9096f6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d9096f66505d97cc2e724511d525cd85dbe897e1))
* DFE-3724 - OrderGroups are not validated correctly (Required if available for customer) ([707b3ed](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/707b3eddcf2432ed5ff90a5910b2d78f9e4b9a82))
* DFE-3724 - Refactored to adapter layer ([5df7c2b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5df7c2bce30876fb028a4fc29bb7b20441714e61))
* DFE-3724 - removed default label ([48166d6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/48166d6c099f09190569cd1e5563637e54191f21))
* DFE-3726 - Incompatible types for palletLocations in PriceRelevantRoadFieldsValidator for Q2B ([061515c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/061515c020a9900085a01bc4a23ff793862f2b11))
* DFE-3730 - remove old road 1 day in future ([98412fb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/98412fb6490ccb6a20af95e7083787964bb10f62))
* DFE-3730 - remove old road 1 day in future ([5d1f776](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5d1f776507814c3b9ad65aea8b94b98ec5b13bf3))
* DFE-3767 ssccs no longer duplicated ([2964244](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/296424410211f6ad474ea746c4a26e2200d940b5))
* DFE-3767 ssccss no longer duplicated ([d6d1077](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d6d107701e98ece4932e703cb129864dd746ea3a))


### Features

* DFE-1282 Add order number mandatory flag to validation and settings of the customer ([ef12682](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ef12682b2efe248b5573e6d70b8771c27eb338e9))
* DFE-1282 Use adapter pattern ([096754a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/096754a1eb8a9d9b8236ab284f6621603eef2d31))
* DFE-1282 Use mapper ([81121a2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/81121a284e5a4607e1e72ac951ff20a10a4276fc))
* DFE-3187 adding some tests to ensure that updates without body do not update the principal data saved before ([da2f182](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/da2f182b192ce76e2f4f9342af78f8ee0165b33f))
* DFE-3187 adding some tests to ensure that updates without body do not update the principal data saved before ([2ff3821](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2ff382168001e7a96e769527b1e2bef411177a8c))
* DFE-3670 principal can be marked as locked for an order ([cba2145](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/cba2145ea9e1c67b09b9ceb991670e8afbd2fa03))
* DFE3-2422 upgrade TnT to spec only api version and generate own stub ([48b20ba](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/48b20baa336d5aeb7a9c6a7833ec186b25676401))

## [1.21.2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.21.1...1.21.2) (2024-07-30)


### Bug Fixes

* DFE-3730 - remove old road 1 day in future ([66e955d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/66e955d42449b9947d7ab98d15acb9da39446e85))

## [1.21.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.21.0...1.21.1) (2024-07-29)


### Bug Fixes

* DFE-3725 Change to new EDN Upload function for Platform ([c644953](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/c6449530d82deed44bef779b57fc2d9a0068a708))
* DFE-3725 Use released version ([8e18b35](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8e18b35d8deef1e20ca8661b6521de505cc3eed6))
* DFE-3726 - Incompatible types for palletLocations in PriceRelevantRoadFieldsValidator for Q2B ([eadd6dc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/eadd6dcf0912417f9b3b49e3fd0601660fbf9ce5))

# [1.21.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.20.0...1.21.0) (2024-07-25)


### Bug Fixes

* DFE-1869 Only validate order number change when there is one in the database ([a34de29](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a34de2965daed6f10c0e42b2d7f298f70a35cc90))
* DFE-3585 detached entity passed to persist ([e35e9cb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e35e9cbf7c23f53ad2c04e00d5f76270e970c77f))
* DFE-3585 detached entity passed to persist error caused by objects not attached to current state ([0cf34c9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0cf34c9dd8cc7881188c120e4c21abc1c2a56ec3))
* DFE-3585 detached entity passed to persist error caused by objects not attached to current state ([15abae3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/15abae3cb78476d9e4f01d2f743c43e1ca2d176c))
* DFE-3625 - Q2B Api schema adjustments ([98512e6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/98512e64e5b3613ceafcd7f918462d3319397d03))
* DFE-3711 - Q2B: Validate each roadOrderLine for invalid input on content ([1a44c79](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1a44c79bdf43a3b3377a5b318f21ed46bb70efff))
* DFE3-1504 API change "trackablePackingAids" ([32743a1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/32743a1abc63f35b89e7c255e0173460809d5914))
* DFE3-1504 Code cleanup ([2d8ba11](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2d8ba11abc6b557987be1b0b0be14a40daf26c3d))
* DFE3-1504 fetch orderline package aid description from service ([399ea25](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/399ea2591d2bb54dfc695b3c708844ef3c78871a))
* DFE3-1504 Fix test ([8c6105a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8c6105a30d440e9ab1eafff970d8c83dbda611ad))
* DFE3-1504 Use CountriesApi for mapping iso to Dachser country codes ([16acbf1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/16acbf124af445b818bb1001153be606c33081cf))
* DFE3-1506 Fix pdf document name ([af9cd92](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/af9cd920446dbfee06f52ca7a9f354f1500bd072))
* DFE3-1837 Change references sort COLLATION -> Latin1_General_BIN ([80c6304](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/80c63043d360e547d4984c9ca71b174f8e4d87a2))
* DFE3-2278 Do no group by address_type ([9b70090](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/9b7009027396a09c2512ed19229d8dab4ee5fe41))
* DFE3-2278 Exclude orders with principal address that also have LP ([a099280](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a0992809feeb286a16492ccd7fc4549b8ee5852b))
* DFe3-2278 Exclude PRINCIPALs that have LOADING_POINTs from v_transfer_list ([a9b225e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a9b225e4f8e45b6c1a329e0b60efb135edbdbfd3))
* DFE3-2278 Replace indexed view with plain view; add table missing index ([e4cc751](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e4cc751211d2a0b53d82f78896e7b2c46ab5fd2d))
* DFE3-2370 change date formatting ([44d8c92](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/44d8c9200724a82997ab40d650e8e98366304f64))
* DFE3-2370 Update API (final) ([add5d6c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/add5d6c5651e8fb85c3661661e4a3e730e851efd))
* DFE3-2454 Change label count ([5c496bf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5c496bf0e8941d501536c74b74a0da7cac47c9e4))
* DFE3-2455 Fixes after merge ([581536e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/581536ec15277c2c0b97d4b0e64c0f893384e3e7))
* DFE3-2455 Re-add test lost during merge ([87dc98a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/87dc98a1e0d62b5b8ca7e1e2b96253a6267169fe))
* DFE3-2455 Sort pdf transfer list items by consignee name ([5831190](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5831190949bbb945c2f42d0a514bda8adbf55dfb))
* fixing errors during test execution - suppress caching is default in tests ([9395e5e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/9395e5e9216e7011d14c529f06c5a4e90cf16b09))
* fixing errors during test execution - suppress caching is default in tests ([5cca6be](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5cca6bef639407e98b12bd9182e5bb8e3997e9ea))
* fixing timing in order processor test ([a03cc0b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a03cc0b6af53e9622f2f9def01f67c6b731552be))
* fixing timing in order processor test ([3723f44](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3723f445a880a46e96b5d2ebb82518fc47eeef2f))


### Features

* DFE-1075 providing endpoint with filtered product list depending on address constellation ([6de505d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6de505d0dd55551bdc74fb43ba6b15697266aa39))
* DFE-1075 providing endpoint with filtered product list depending on address constellation ([e7e1be1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e7e1be19e0e89e9389e1ba5e4f66d6077d16dd07))
* DFE-1075 providing endpoint with filtered product list depending on address constellation ([b9e1297](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b9e1297e553f607650afa1c3e3dd9dbf0c972f36))
* DFE-1075 providing endpoint with filtered product list depending on address constellation ([1db2887](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1db28873bd33a54ada08b99f56af2b90ca32f79f))
* DFE-1638 road order validation ([3f0da5a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3f0da5aa1dabdd7b986944a27d74394386ba7f96))
* DFE-1869 Validate orders in state complete for forbidden changes ([485aa14](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/485aa14122faae93983de938a7e11e6127db17d5))
* DFE-3457 advice adjustments ([ebfea91](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ebfea917f5ef3406c7ba96420b97fb2f9a8209f0))
* DFE-3554 Adapt exception handling for handling new error api ([540d46b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/540d46bde1fed065a9370c2839ec00c8e9ae5b6f))
* DFE-3559 - Use Name1 field for principal Addresses ([ff2a78e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ff2a78e5eb6a70fc49b8f81d552efc7615ebe066))
* DFE-3586 - Add cache to order overview ([4cc6448](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4cc6448971f34dbe8a30bb11edc33a21329fa04c))
* DFE-3620 Create Exception handling for clone endpoint ([0f5f7b8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0f5f7b8955151a30b9600bfc8eff23416ce6b5c6))
* DFE-3621 - Refresh user profile cache on login ([eb47c11](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/eb47c11651eb811ce94db98a3feafeb76b50df9d))
* DFE-3653 loading meter adjustment ([9156295](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/915629584e34917110cfb1971beb703afddb6d34))
* DFE-3653 loading meter adjustment ([50749a3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/50749a3a7862bb79737acc2fb5500b495b8a6545))
* DFE-688 changes after label print ([361d3d9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/361d3d9d3fe458e070c145b8cb7ed88c707e825d))
* DFE-688 changes after label print ([49c8109](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/49c8109c3fe718df0033a15867764912c4120472))
* DFE2-1085 added endless printing based on user preferences ([9f2dc16](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/9f2dc16d5a9bf96956fef32002f925bfca66ccb5))
* DFE2-1085 removed caching of user preference to ensure that they are always up to date ([829189c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/829189ce8d5e467c5cca5485ad9f133dd8f07794))
* DFE2-1483 added endpoint and implementation to print labels for a batch of orderIds ([bd4d8b7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/bd4d8b7380cdb2826305b2aac4b2f603f775efa0))
* DFE3-1504 Fetch packaging aid description from service ([bdbe2fb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/bdbe2fbc411584fcbd536ba991caadb74172a8d0))
* DFE3-1504 Refactor TrackablePackingAidServiceNotAvailableException ([67a788d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/67a788dfe3b2733e18a15aca4695a27c1f42e11c))
* DFE3-2278 Fix test ([8a965a9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8a965a93c4d17aae377e4e9ed0355c21d179c650))


### Performance Improvements

* DFE-3591 getting rid of lazy joins to collection address and shipper address aggregating data into view ([38d1b18](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/38d1b18f3b6e59ae204e373cfd727fc9f818bb2b))


### Reverts

* Revert "fix: DFE3-2278 Exclude orders with principal address that also have LP" ([af61175](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/af61175557edffcff47cab527e0616b8c9eceaaa))

# [1.20.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.19.0...1.20.0) (2024-07-03)


### Bug Fixes

* DFE-3326 - Recovery method had wrong signature ([7ab6528](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/7ab6528e13384b4c633530cd8d0bb0875ea7796e))
* DFE-3423 Add default case for old quote to book implementation ([c3a7167](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/c3a7167e30793498d03875207651b9309e1d9bce))
* DFE-3423 Create default air order reference when creating from quote ([cd226f8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/cd226f86052ccaf69ef9b7b60e769361dc8f6a0c))
* DFE-3423 Create default air order reference when creating from quote ([cc03aff](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/cc03affd3529f3235352530f799caf25771ec2e6))
* DFE-3423 fix quote to book ([82c7803](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/82c78030f5e15806e67401b8d1e8a0172efe40f6))
* DFE-3562 Do not add contact data to principal address when road order ([11e91ae](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/11e91ae883e7446c1c2d67948cac78e130b7ce1f))
* DFE-3589 fixing status filter ([a69aaae](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a69aaae2880e8319c74af9c6d22279bbf35959a7))
* DFE-3589 fixing status filter ([5a8f926](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5a8f9267118734cda2de083431d815a41da48537))
* DFE-3619 Make mapping of consignee address not conditional ([62a44b5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/62a44b53b9c089d4bafc4ebfa866b1faa753f050))
* DFE2-1085 typo ([89b88a5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/89b88a54bf46fe8e95b576d6d246b10cbb105148))
* DFE3-1366 Only remove nanos; no time zone conversion needed ([9e1044e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/9e1044e3b15826c9263c4e9094a3a71289c56480))
* DFE3-1506 Use date formatters ([2de2f98](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2de2f98366b953e01b4a91f8e20c217647818456))
* DFE3-1633 remove Instant nanos in "lastModified" Instant and convert to Europe/Berlin time zone ([20864f9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/20864f9151efff07cc818f1caa129adea506e2cc))
* fix tests in CachingAddressTypeDescription ([0e4e6fd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0e4e6fda33f9839ee835983a3f11937f40f9fd75))


### Features

* DFE-1280 Add drop of location to order address. ([3f74834](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3f7483485256310c2b110f74e09dfb8f1e69e10c))
* DFE-1280 Adjust book api version ([f75d754](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f75d754ba88d487af17f179752936fa65160f599))
* DFE-3326 - Cleanup Advice error handling ([a88e2d1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a88e2d134777fdd158775ec66cbc74d1265e9a99))
* DFE2-1085 added endless printing based on user preferences ([f0ace28](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f0ace28513b4f32569013282b50f0265fb6f0f91))
* DFE2-1085 removed caching of user preference to ensure that they are always up to date ([a5b87f8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a5b87f8945d9de7f9885fe45821dbff1baebf083))
* DFE2-1085 rotating endless pdf to make it easier to read for the user ([0b61625](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0b61625a63f0e158df62e74ebbd09cce7b28d072))
* DFE2-1085 using try-with clause to fix sonar issue ([2f26702](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2f26702c1ca31bdae33a828d01a171fd416a521e))
* DFE2-1086 added caching to label generation ([356b59b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/356b59b8e57ca2896850ada35ca5ae3da897118e))
* DFE2-1086 added option to set label print start position ([1105d6c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1105d6c987856ba4113f9902c88a149555711cf8))
* DFE2-1086 changed InternalPrintLabelStartPosition enum into PrintLabelStartPosition ([5fe9837](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5fe98375cce8e4418bbf44422be9c268bc7e1f56))
* DFE3-1837 Alter reference value column collation to allow unicode supplementary characters for EQUALS, LIKE ([bc153cf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/bc153cf94ebbe9f867b27687448834d144fd630d))
* DFE3-1837 Need to drop/recreate indexes when changing column collation ([b2e23c4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b2e23c44a9fc327b9b270190e5461e935312a17a))


### Performance Improvements

* DFE-3587 address types api with cache ([9b4974a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/9b4974a029c97a0ac913c20d9bfc719081dd5d84))
* DFE-3589 performance improvement in sorting and filtering by status in order overview ([48a5d8e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/48a5d8e050f4554ff64d32b3fbcc7abaf247f58c))
* DFE-3595 performance in order overview query with indexes ([4313c58](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4313c580dcfda0786fcc1cc03bca5ee4d3986378))

# [1.19.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.18.2...1.19.0) (2024-06-12)


### Bug Fixes

* db mapping for mssql was missing a context attribute ([e47cfa6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e47cfa60fd4b6727e74c0c55ea0ea193df4da7f5))
* DFE-1442 - changed naming for order_road in SQL syntax to avoid interpretation conflicts ([94ca477](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/94ca477f93a1d7cda3f86f9f2b8493565a95f144))
* DFE-1442 - Fixed ClassCastException for QueryResult from SQL Query ([d089e9d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d089e9d998fcada20233b84b6e08dc339d596fa6))
* DFE-1915 - SourceOfOrigin was not set correctly ([381f65c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/381f65cb4b7f2981e3a922d146725cf9846175f4))
* DFE-2519 - Fixed SQL Migration script that causes crashbackloops on server startup ([da347ff](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/da347fffe924ae28510b334a0a23a3da293ee5a8))
* DFE-2736 Do not print relation when service not available ([93428eb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/93428eb21234ea6b562c74ad943b9b319d6d7ce3))
* DFE-2758 - Implemented Address Locking for ePricing on Q2B Road orders ([0d334ec](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0d334ec442883e4c31a37b53f9105341b37954a2))
* DFE-2758 - Only mapping Daily Price Reference when QuoteReference is not null on road orders ([7707f3c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/7707f3c7f6e74ab1498c89d97445282b2a5172a9))
* DFE-3331 Change XMLGregorianCalender date generation ([8b957a7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8b957a701bc6c2715aa3df5357ab2bd1553cb2a3))
* DFE-3397 Add filtering for addresses ([164a4d0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/164a4d08befbaa884a65730f3bbb28da7e6fcd5b))
* DFE-3397 Fix test ([d74330e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d74330ec7c3ab936d9bcab40c1ba328cdf6693cb))
* DFE-3397 Update comment ([bbb3916](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/bbb391684eaa5e7b027fd55d4105c7fc205e18fd))
* DFE-3413 - fixes gln problems from core system principal data ([b5ec7ce](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b5ec7ce93f42e6bd691dfbd79d3bd609ace5109a))
* DFE-3420 Translate commercial invoice mandatory state also for recently used document types ([b9ec485](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b9ec4854fab73a4a281efc7b2c5898fe90d4af17))
* DFE-3426 Add own change set for h2 ([e94ca55](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e94ca5523b4ecb9a1a95bc38eda183df6472d835))
* DFE-3426 Fix typo ([c060edc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/c060edca64883b161f7677bd8f57c8fe5dafb43e))
* DFE-3426 Map reduced consignor address ([4ea7320](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4ea7320cbf092a30a10d4753f3a2ba7dd594a2cc))
* DFE-3426 Move code into parent interface ([042f1f1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/042f1f1e6fba414d0647107be322ab377c1cb382))
* DFE-3426 Move SQL changelog to mssql folder as h2 does not support the syntax ([7c1c70d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/7c1c70dd5c52fcd73351edbfaef1575a708e2543))
* DFE-3426 Reduce code duplication ([3e41ddd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3e41ddd08a22b052f3431e9a85fcd1111fd26863))
* DFE-3426 Reduce duplication ([f17b599](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f17b599d0bff7d0faba09df1e2058b22f5e8f5b2))
* DFE-3426 Rename file im db changelog ([dafe6a3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/dafe6a37916dd463004380e9fab13b0c589a5d82))
* DFE-3426 Update entity constains ([0b6311c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0b6311c806a3fb3b67471fc1d29f4e985694fd33))
* DFE3-2117 Add tests ([6c5f859](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6c5f859a9d080b3141b613a6753a77a2d1a08490))
* DFE3-2117 Change retrieving + grouping of principal/loading point addresses ([01ce96c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/01ce96ce84c8ec9cac7764962ca79bea229c2ea5))
* DFE3-2117 Resolve build issues ([de9f465](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/de9f465fc96ec627cfc7dd7473769c448a2fa269))
* DFE3-2117 Update TT API version ([e88c28a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e88c28aa6c65b5c7ae51b44f5fcf8344b724ff3b))
* fixing merge error ([c302caf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/c302cafe89d8983d5d87d0ed6de5f83bc62c3f87))
* fixing merge error 2 ([f3e6f07](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f3e6f073a5a033b41ea1a493c9511dec4a1ad382))
* fixing merge error 3 ([f411cc0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f411cc0b91466a0dd2f2dd074243e5e15821a563))
* fixing merge error 4 ([6209cdc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6209cdc2ca3803ecd6089b2f99ce3a3234b7f8e6))


### Features

* DFE-1442 road order with favorites - fixing db query excluding orders with empty products ([6ed17d4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6ed17d41ef3a21dd38f41419db2d8968e459d741))
* DFE-1832 providing a possibility to interrupt backend services ([c24ec23](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/c24ec23fa12b42ad70c661b3b41c83cee2d02597))
* DFE-1915 - Added order origin via datasource to edi mappings ([a457538](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a4575381d821bc649034a4f4ad73790047989cd4))
* DFE-2519 - Expiration date required for custom road quotes ([2be913b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2be913b4d744ae04d495e201d28308e3902d3fd8))
* DFE-2736 Add book error id ([d966a43](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d966a43c816bb9de950bcc780d8fad16c4901492))
* DFE-2736 Integration relaton label webservice and implement fallback for label print ([6ea0dc2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6ea0dc271951ae23c11f77f0e801288bde20934f))
* DFE-2758 - ePricing not editable WIP ([425275c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/425275c9baedd70d1346e58d775ff4e5618a2b88))
* DFE-2758 ePricing changes should be detected whereas standard quote 2 book road should not be checked any further ([2cde7dd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2cde7dde6420e9d5109eb875216343b13c603f8f))
* DFE-2758 ePricing changes should be detected whereas standard quote 2 book road should not be checked any further ([835009c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/835009cb0e17819d3a69a3d41eeb4c2153a862bc))
* DFE-2789 - using security starter for roles and rights validation ([8fb2a7f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8fb2a7f22a92f3cd8f0cfefb4955827be8563ce2))
* DFE-3139 activate default validation on complete order validation ([1fc9399](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1fc9399f29192454a5ad28bb6584444406e0bb95))
* DFE-3163 404 instead of 403 on forbidden access to order resource ([a47d4a3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a47d4a34eb53d86c1a9b5be54a158d3bbe689ee7))
* DFE-3163 404 instead of 403 on forbidden access to order resource ([a2c0c3e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a2c0c3e7f32eb0cf4842b66ece5b4e336674b79b))
* DFE-3210 cloning an existing order ([259f4e3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/259f4e385950dc9cdfb07d51f9dd21a0aff09592))
* DFE-3368 Implement Spring retry mechanism ([b233a8f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b233a8fb1ac5398dc8e93b5d3e0111801fc6ff38))
* DFE-3397 handle Loading Point address generation ([cdb55e3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/cdb55e348a04006fcee6646f2058a461f3b0c593))
* DFE-3423 Safe unloading & loading for shippers reference even if there is no shippers reference ([e02ef6d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e02ef6d0a4096abc57f550c0cc34af8747475249))
* DFE2-1087 use cover address on labels, if there is one ([0d45b11](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0d45b1199651a29a44ab2728d95ed08fdf35f363))
* DFE3-1366 Add "lastModified" column ([d453553](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d453553cbaeb1ee6884258dec239a830316137de))
* DFE3-1366 Test "lastModified" mapping ([19b225b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/19b225b7a17bd61daa7f01b1e7f7bd4d231b2bf1))
* DFE3-1366 Use final API ([ba14615](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ba14615a9e02b5f76376dee902bc06a5c30b5ef7))
* DFE3-1757 handle order overview database error ([7e89e81](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/7e89e81f3f1ca4ca47d2a0f68f4d57d4de9bce25))
* DFE3-1757 Some code formatting ([124e5d4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/124e5d445e77954db48d739593df21154f7001f1))
* DFE3-2026 Add service and mapping for "supervising branch" ([ee02562](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ee02562596de978248519db2dece08455cd928ce))
* DFE3-2026 Add tests ([4ba630a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4ba630affedf32dbb1a798026d166643ef50ad97))
* DFE3-2026 Adjust test ([34383eb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/34383eb8eda2c7a9c1a3d55b0d7eb2b5aadd0b4d))

## [1.18.2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.18.1...1.18.2) (2024-05-13)


### Bug Fixes

* DFE-3413 - fixes gln problems from core system principal data ([2d3220b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2d3220b986854a6518de7c025af3fdcd3cccfe5b))

## [1.18.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.18.0...1.18.1) (2024-05-03)


### Bug Fixes

* DFE-3080 - Remove constraint on QuoteAddress postcode field ([7a19337](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/7a1933784128341bf67576a51638fc9b5c700407))

# [1.18.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.17.0...1.18.0) (2024-05-02)


### Bug Fixes

* DFE-3080 - Added api test for AirImport ([711b197](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/711b1974b58de35d6b0245a5b6ccce45f02258ef))
* DFE-3080 - Added seaport to mockdb to fix test ([17d4d22](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/17d4d22ccca4d4946773c6ec0dcbc8ed310f9790))
* DFE-3080 - Added UpdateValidatorTest for AirImport ([86ae39b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/86ae39b25bd39dd679be5ed7731dd5e150961e03))
* DFE-3080 - Fixed errors for orderReferences and update order ([4c087dd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4c087dd3bc840e451aa857e2f3ee083b4e36cfdf))
* DFE-3080 - Fixed some tests after refactoring ([d46e97f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d46e97f586edb4f56d3af1583e954a6dce9d1142))
* DFE-3080 - Various bug fixes ([6c8e306](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6c8e306c6022b64f4868f11dee4b7f3ca9b098ca))
* enabled AirOrderSubmitterIT again ([f919003](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f91900341165a969b783a57177109d85eea50d89))
* fix wrong content in error message ([3b92dfb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3b92dfb2750092c44d82cad09094c821eb9af9fd))
* fixed syntax error in sql file ([f01f243](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f01f2430c2ddd1fef1e01463ef0db7969378f186))
* Set file and directory permissions to free for all ([7100d59](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/7100d59878eee686df8bbf1b3831c8e3f171c56e))
* use book-api v4.3.0 ([7975606](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/797560646689296c212d055226e3081cf75049f1))


### Features

* DFE-1442 - ROAD products with favorites ([cb02523](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/cb02523c0c5004658acdef5a7d036a1d87191386))
* DFE-2909 Map Contact information for consignee for delivery contact ([57c4a57](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/57c4a5742f2e7b32d4869efeaec689fb20cae04e))
* DFE-3080 - Refactored Quote2Book Validation ([ca55c67](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ca55c6766e55b44d8b4e14ac043256e7739ee59e))
* DFE-3080 - Refactoring for Q2B ([de9e67e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/de9e67e6d88db24f674de060fd004f90db9c1992))
* DFE-3227 Adjust TransportMapper to only map filled address and only map service contact type for consignee address ([f7cf9e1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f7cf9e17417133771b6fdafbe408d02b150e7cdd))
* DFE-3227 Only map contact information for consignee address ([62c9e94](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/62c9e94ff3b4c63014969e3bdcfecd848366f5ab))
* DFE-3277 - all air products for filtering in overview ([17049ba](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/17049ba6c3a68220c22ae8971a19a529da605daf))
* DFE3-1502 Map "address.id" ([19af7b4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/19af7b4b049499d6f2df73a5ae8a68972dc40c56))
* DFE3-1506 Add "getTransferListPdf()" service ([62ef5f0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/62ef5f0f1df0e7ed27d204be49b96d7d1490e44e))
* DFE3-1506 Add PdfGenerationException ([2f37973](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2f37973ca01ffe012802c38e008ca598e207afdb))
* DFE3-1506 Add tests ([f821354](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f8213546b9334f4bcdbbc5c2734b03ae0e78ce8a))
* DFE3-1506 Add vault path for dfe-pdf-export ([8f39207](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8f392075a4a75d1332c1c3a57a9f0719583fe5f3))
* DFE3-1506 Add/generate PdfService ([975a89c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/975a89cf9f8552302d8ffe00d9df108a02e81c76))
* DFE3-1506 Align with https://bitbucket.dach041.dachser.com/projects/DFE/repos/dfe-book-backend/pull-requests/618/overview ([7c9196c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/7c9196c0693274aa5d9f698430dc08bfcea862e8))
* DFE3-1506 Change response binary->json with file as string ([0f15c0e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0f15c0e3c4c0706c780440709d14b05247d5b31b))
* DFE3-1506 Change/replace errorId: ERR_PF_01 ([b539642](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b5396428bdbcc0b0aacb0d269dcb1f4a17c52513))
* DFE3-1506 Cleanup POM ([ac5f31e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ac5f31e868f4c7581c209d0f173e49e10ae3682c))
* DFE3-1506 Code cleanup ([23ddafa](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/23ddafa00b61bb71ffa9b70356e34e6c3bac9b1f))
* DFE3-1506 Enhance JPA query ([db56c1a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/db56c1a99db69cb3996118a1efc6c3a6a2b3ca0b))
* DFE3-1506 Extract constant ([60786b9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/60786b9cc6c0c5e7f9345432c05158da0ae3d567))
* DFE3-1506 Fix API version ([564e2e8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/564e2e8e2057ed322ada8177ec88bdec17780b33))
* DFE3-1506 Fix disabled test ([6371b70](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6371b709b469c7b8269cece47d50886a2ee4006f))
* DFE3-1506 Fix pdf service DEV URLs ([7eb0c86](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/7eb0c861061af306389f3e09195a733b95eed077))
* DFE3-1506 Fix usage of base class TransferListBaseServiceImpl ([5993637](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5993637ec54a2976160a0250fa94a0c21cf6a2c7))
* DFE3-1506 Generate document file name ([3e19176](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3e191766425af090f5861bb6a5fc5795342672ba))
* DFE3-1506 Include PDF service openapi.yaml as dependency ([ff150b8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ff150b8333ccefb9c92fa7954c4261cc8f71f683))
* DFE3-1506 Map "trackablePackingAids" ([6199b32](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6199b326b310d7fbea7a160607d7d74eb7dc5ef0))
* DFE3-1506 Map "transferListDate", handle not found exception ([ff488ec](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ff488ec38e248435af6fe9bef297862675c6aa75))
* DFE3-1506 Provider mock implementation for PdfService ([e6b3160](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e6b3160a7d2afacd95f565f068a11456d444ef67))
* DFE3-1506 Refactor services ([b433bfb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b433bfb6fd98a508f4fdd66843281ca8fe389516))
* DFE3-1506 Refactor/rename RoadProductsService in tests ([8267ed0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8267ed044ea4df371e6fd36409cf6f441a711e91))
* DFE3-1506 Update TT API version ([4e6fd61](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4e6fd61f2f7d5b45f1dcc16dd3ea6b669cefa422))
* DFE3-1506 Use final PDF service API version 1.1.0 ([5ed0024](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5ed0024609a011278807f4e5b954d959924e4769))
* DFE3-1510 Add aggregation/grouping for ASL references ([8ded386](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8ded3860866f3fec0493a6340de3edbc72435850))
* DFE3-1510 Add tests ([0007b59](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0007b598a953fb4298f17a9ef9ee261d23390bab))
* DFE3-1510 Adjust changelog after rebase ([2d26417](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2d26417738824984dd542c24fbb1b5c0c25f06bb))
* DFE3-1510 Code cleanup ([f843e57](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f843e57a3dce7a8a500c0273c4e678e6ccb44165))
* DFE3-1510 Fix code smells ([1ac57e3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1ac57e3e8cbd237a9c4b2778fbbf9aa769974dd6))
* DFE3-1510 Map ORDER_NUMBER, SHIPPERS_REFERENCES as references ([4763b29](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4763b295924d6a1357e4a516e31718822b38491c))
* DFE3-1510 Remove field/column "orderNumberOrShippersReference" ([9eae376](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/9eae376c7cb65a138c2e0fadfad565a3d9b00ff9))
* DFE3-1510 Sort references, so that ORDER_NUMBER, SHIPPERS_REFERENCE are top ([f11f791](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f11f791a331afb0a6329d9a33466fad93ceeb6d8))
* DFE3-2113 Add errorId for PDF service failure ([0ed3924](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0ed3924c918743409325a13994630546641b3a04))
* DFF3-1506 Fixes after cherry-pick from alpha ([4ca7ba3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4ca7ba3f50e7c100b69225104e73248249f1a73c))

# [1.17.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.16.0...1.17.0) (2024-04-17)


### Bug Fixes

* DFE-3116 Remove code smells ([c01cdd2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/c01cdd21a88350c53a6cd6eff0879a9c9114cbb7))
* DFE-3116 transaction handling on update processing of road documents ([b8e271c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b8e271c1d72a87af18be5a6fec4390524fe7f9c2))
* DFE-3143 shippers-reference required either loading or unloading ([af2c103](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/af2c103f936aa2e9993d19b22b6e5a46b9eafb21))
* DFE-3143 shippers-reference required either loading or unloading ([1587781](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/158778136cd7e7d1f8988a0bf7a255e720728993))
* DFE-3276 Set flag import if the order type is import ([7253816](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/725381666137694543403e56b98f4b63a539762f))
* DFE3-1502 Fix transfer list changelog: ([c968af5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/c968af5b98ae54dd49b2f425383799cc3db9e24c))
* DFE3-2006 Add today date to list of dates ([9b6d7a1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/9b6d7a1cd5f6b872e9cb5f964b8bbc78320985f3))
* fixing DB Scripts for mssql and DFE-3143 ([ceac08f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ceac08f281c1543711d71dbc599083b058c849cd))


### Features

* DFE-3116 Check status of processed documents ([fb0b948](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/fb0b9485194da2bd4e735065129affb4cf727a86))
* DFE-3116 Update post save action to process documents ([384167c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/384167c34441dfe13bd65216782fd813736cee6e))
* DFE-3143 shippers-reference required either loading or unloading ([486a926](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/486a9269a43426df17f6220bea53dd22480f010b))
* DFE-3260-platform-api-v3 ([abe92b0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/abe92b05a8296872cc66b1233488276b07594fbd))
* DFE3-1502 Add "addressHash" to filter response ([cdec264](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/cdec26451c940517cb41b2aa8b7489203e19f435))
* DFE3-1502 Add "freightTerm" ([ba8a984](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ba8a9842b8b9899e9b967fec97435b1552699381))
* DFE3-1502 Add filters for date and address ([15e5a08](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/15e5a08e38ed07bdc9b554d879dd7d072c48315e))
* DFE3-1502 Add mock data and tests ([a2a1e27](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a2a1e2779f8ee8a2c5b0e790a2445a17e1b9a906))
* DFE3-1502 Add tests ([3ae0bc8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3ae0bc8517478ffe3a4a489188434b8658e2923a))
* DFE3-1502 Add where clause for pickUpDate range ([2d4dbd5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2d4dbd5dd339054309177c9beadbc48014056b5d))
* DFE3-1502 Add where clause for pickUpDate range ([df6e318](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/df6e31817b6084c91b416d8a0065ed5e34276ae7))
* DFE3-1502 Adjust changelog datestamp ([47a2881](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/47a2881834017463b8f544da8fdf5fe7e8851aaf))
* DFE3-1502 Code cleanup ([1179a4b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1179a4bb57d48705f2807276ea709748be343d2b))
* DFE3-1502 Empty filter to return empty result ([0516c41](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0516c411ce482f69f134c165d3c34d88c5b52fe5))
* DFE3-1502 Fix changelog ([be1c762](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/be1c7620097317dc3d452f43270058edb55eb563))
* DFE3-1502 Fix liquibase changelog (after rebase) ([d2ba225](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d2ba2252100db36d86ac43bb6f2d7856495f9b7c))
* DFE3-1502 Fix order_road.order_number ([67136b7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/67136b77bcee54369c907c2c504a3b0839adf018))
* DFE3-1502 Fix/use OrderFurtherAddress ([7ee937c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/7ee937cdfe0e960b5dd0a9a42d1348c7d6ad00a4))
* DFE3-1502 Initial setup ([17cc236](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/17cc2363e6df3ee18b431120361922afb1e5fea8))
* DFE3-1502 Map "address.id" ([4a488a1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4a488a1440dfa80a43352908c4675b44b0d854c0))
* DFE3-1502 Map filter address list ([091de38](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/091de38ad9eba01f65b13e34c9cb110ede16b2d6))
* DFE3-1502 Map orderline ([a43af26](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a43af2617de32446b5fdc77f9b3a98b35ba2d947))
* DFE3-1502 sort by consigneeAddress.name ([107f071](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/107f07107027fa7c9f7bdd1c18b8b2abad471a2b))
* DFE3-1502 Update API dependency ([f8d50fa](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f8d50fabf650adae25d203ed99ae70bbd91da79b))
* DFE3-1502 Use indexed view ([b4e74e6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b4e74e614ad98c990760fc03b7463e907d555a7b))
* DFE3-1502 Use join for addresses vs. flat attributes ([cbc0958](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/cbc09586d9c862e00680ba5ba9e5c48479e2df03))
* DFE3-1502 Use LocalDate (yyyy-MM-dd) in date filter ([8e98228](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8e98228e08c329419cae6eb8a287f8d50733e067))
* DFE3-1502 Use OffsetDateTime for date filter ([647dc2e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/647dc2e92019eab8782d24bc5caa3afe6607c370))
* DFE3-1503 Refactorings (PR code review) ([fba1aaa](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/fba1aaa3fd297aded9b546df2b2b278e41725c21))
* DFE3-2006 Refactor dates list in response (API 3.4.1) ([d043cb4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d043cb45c83ccb354a9baecfa08f061f3d630b2c))

# [1.16.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.15.1...1.16.0) (2024-04-04)


### Bug Fixes

* DFE-3145 Update Version of print service and update tests ([3539566](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/35395669dec410c320735b98acd509d1bcbe0507))
* DFE-3171 more tests for better coverage ([fb29563](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/fb29563226d0550f2e13746ab7980961469841db))
* DFE-3227 suppress errors in splunk produced by scheduler tasks trying to access platoform api ([0dee804](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0dee804df35d487dd59735b43e253cfeb0e4d176))


### Features

* DFE-1191 introducing portType parameter on port routing endpoint ([441f518](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/441f518c38c604d0405cf7787a2129c9a1aec9cf))
* DFE-3160 Adjust TransportMapper - Remove OrderNumber for additional references ([58a6632](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/58a6632b90bbfac69ecc1a55523b8d99ed032d60))
* DFE-3160 Move orderNumber and shipperRefernce also to reference table. ([61f377f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/61f377f5ad0afc2d05ed23a0ff5ddae9f41b6697))
* DFE-3160 Update book api to version 4.1.0 ([90d908a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/90d908aaae9920e3f2d67a96a61281939bda11e5))
* DFE-3160 Update mapping for references - remove empty mappings ([221ba48](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/221ba48f918abc2a3d7a16bbf9bf2c225185b7b3))

## [1.15.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.15.0...1.15.1) (2024-03-05)


### Bug Fixes

* DFE-3171 setting principal data to address and contact on q2b air order transfer ([0856eb3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0856eb32f0fdab9064ea1c99e8096cd863faa853))
* DFE-3179 - Q2B Air adjust collection date validation ([255cb3f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/255cb3fdaf984c29d36643a4dc35fe3b25ae7700))

# [1.15.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.14.1...1.15.0) (2024-02-28)


### Bug Fixes

* DFE-1728 spring boot update ([a983d93](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a983d934d3834b74015e84786323d94210dd0b69))
* DFE-1728 spring boot update ([2f30ef3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2f30ef34d68c9317ae9d8837481b27180b1468d9))
* DFE-1728 spring boot update ([2a25ccf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2a25ccf0f4b3990e7f8cd650757edae08a7ebf5e))
* DFE-1728 spring boot update ([1a5a80b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1a5a80ba89e132ca090895ca0202d87e0f623329))
* DFE-3107 Add DeliveryOptions no ([1a6aee5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1a6aee524b8940ed484409d24cd8976aea8be4db))
* DFE-3136 - skipping logging of input json when error is validation error, which is a valid case ([0a4d513](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0a4d5139561d20764df96343c5060eaca39f9675))
* DFE-3136 - skipping logging of input json when error is validation error, which is a valid case ([7409fe6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/7409fe6f3f556e92975d3ce99b1701a89b1cd95b))
* DFE-3137 backend validation of addresses no also prohibiting order from being marked as valid ([b66358b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b66358beaed09b192ce47eb5287ed4137f552594))
* DFE-3137 backend validation of addresses no also prohibiting order from being marked as valid ([2ee5a85](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2ee5a85688f1fdfce207e495e62fd57075a6d42c))
* DFE-3140 Add a nullpointer check for deliveryoption as this can be null if nothing was selected ([3bb8242](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3bb824213fb5056d2f3328fbdf222b7915fb068a))
* DFE-3141 - too long name fields for principal address ([06a8a19](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/06a8a1966ff5eb7530a3374e9eff8fc5b1472e7f))
* DFE3-1420 Fix reference label translation ([d8cf7c8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d8cf7c8c207bbe4f1a6f331b9aea197f5b060f39))
* fixing renaming issues ([c5d90a7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/c5d90a7c8728b8135c9b784381c884b17a898cb8))
* fixing renaming issues ([8f19391](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8f1939191f4a0a63951931303b244ae57c611968))
* fixing renaming issues ([ebe157e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ebe157e6570b71fd8a0359195a8d75e1ad776018))
* fixing renaming issues ([0182692](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0182692f7103b74d4a83e7368e4e22e5ec0b71e6))
* fixing renaming issues ([6d60ba5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6d60ba5811c1f6042fd502bd1944463cdccaafdb))
* fixing security setup for locale h2 dev ([7b379c3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/7b379c3073de738858dd4eb405c1da726f7b63b8))
* fixing tests and removing obsolete code ([d6d063a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d6d063ab24d65cac643c06f85691729b471c1cf2))
* removing duplicates ([df80eb2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/df80eb22909d1f4d85c4ac02e819443f96e1db36))
* update changes from DFE-3061 ([52bd0cc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/52bd0cc3fed7d6da48afc604c3537451c67536c2))
* update changes from DFE-3061 and fixing wrong merge result ([8137306](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8137306eb815e0b0fc8a282d82ae42cc41f3cd80))
* update version to 1.15.0-rc.2 ([51fa023](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/51fa02340127677f04fe9d613f3b51db6ce8bc09))
* using final version of parent ([ea59be6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ea59be68c8e586b8311988cfecab54a3edbf199f))


### Features

* DFE-1558 - Extend Data for GET Orders ([6d33dda](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6d33ddaa059139ef8568f845dfb0ed2b8c2c226c))
* DFE-3061 - Implement Road Masterdata Client into Book ([bbe6799](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/bbe67998874403db7fa1f9c9052e200d25843439))
* DFE-3061 - Implement Road Masterdata Client into Book ([d677e84](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d677e84159b1ffa91f81fe62e18ae3de9ca508f7))
* DFE-3061 - Implement Road Masterdata Client into Book ([560f365](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/560f365080b7bb3b53ce51bd6042c9a6d5528427))
* DFE-3090 Move xml to road subfolder and update test ([fa4df7b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/fa4df7b6693b09caf697070dbbfc0f60895d3e46))
* DFE-3090 Move xml to road subfolder and update test ([f835088](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f83508802a0b57bfbb68a054407af44586158a3d))
* upgrading to spring boot 3 ([066ef59](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/066ef59b28cc5c3e57eb7c09fe8b3d08d22f0ac8))
* upgrading to spring boot 3.1.8 ([91361b9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/91361b92ba6723f27e0eb2ae17b7c47438163bed))

## [1.14.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.14.0...1.14.1) (2024-02-08)


### Bug Fixes

* DFE-3112 - Fix mapping for platformUser to orderContact ([f485bd8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f485bd8eb1bffe46300cda3a1c6aef9558b9f83a))

# [1.14.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.13.0...1.14.0) (2024-02-07)


### Bug Fixes

* DFE-1858 Use deliveryContact for validation instead of consignee ([dbebee3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/dbebee3420bfc5d44d209b1b854c7fada9413861))
* DFE-2575 goods value can now have two decimal places ([b2e735c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/b2e735ca91c6f48f6346a7dc6527116aebbeb8b0))
* DFE-2903 - Rename Json to DTO, immutable List fix ([79af0ba](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/79af0ba5d593b7348c0a8713788cf64d2b220f44))
* DFE-3023 - Adjusting cache2k configuration to enable parallel execution ([d5ba4f5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d5ba4f54292e6f180f3a388c0a0a507c5cf437a2))
* DFE-3023 Change cache evaluation ([098bf3b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/098bf3b5dcb01a8e2facc99f9721e8e5d2a549b3))
* DFE-3023 Don't use preconfigured cache, it will try to set up cache twice ([9f20021](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/9f200216bd504455227fd502a687afbfd44aa54f))
* DFE-3023 Replace stream filtering with a map call ([470cb24](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/470cb24b9eb7b235782cd4c13bda642e01a43cb6))
* DFE-3023 Revert "replace stream filtering with a map call". Does not work as intended ([df7ee75](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/df7ee75fe37bbcad4844238640fecc91ca01ab2d))
* DFE-3039 extending log output in case of an error during order save ([573e2dd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/573e2dd5080bfca4ffd1a52b6b77bc2c72e1da67))
* DFE-3039 extending log output in case of an error during order save ([e469c11](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e469c114e497eacf6f8b3a2afb0a7c9c54d12b0a))
* DFE-3104 Update UserMapper to use name1 instead of concatenated name ([12b9dd6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/12b9dd69360dce99d0c5d39a96b9f7cae9e6723a))


### Features

* DFE-2404 - Added originalPackagingType DB changelog ([f30d72b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f30d72b0db9e1d97f9dd97e54c0dc206798e990d))
* DFE-2404 - information to customer about packaging type adjustments from Q2B ([a0d49e5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a0d49e5f1134a5e89a09769191b76ef1285e1688))
* DFE-2772 Add individualId for OrderBaseAddress. ([7305ef5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/7305ef5471bfaea4884ee8c28efff1bcac2ae81f))
* DFE-2903 - adjusted pom.xml for new api version ([fbcfd7c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/fbcfd7c9d502867c3d27fbce6db2fbcdacda5d2b))
* DFE-2903 - Fixed OrderTypes and tests after Refactoring ([f68bfe6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f68bfe61113fa24e97c846ab3b21f09b40b90ba9))
* DFE-2903 - update trackandtrace api to v3.1 ([5573c29](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5573c294c0a2ccf680d1ee0a0a8e4369febc7974))
* DFE-2996 Adjust max validation for quantity 99999 to 999 ([0f871c0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0f871c02fb12aca8bf15dfe8a19fa0bc85a1571a))
* DFE-2998 creating principal address field in API which is supposed to be read only ([3bbfd80](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/3bbfd80624e8c4d0a02e1655d1b950a4f72fb73f))

# [1.13.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.12.0...1.13.0) (2024-01-17)


### Bug Fixes

* DFE-2992 Handle filepersmission setting differently ([a7b240a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a7b240a376e6973c3847a20c6c59214847e8c1e4))


### Features

* DFE-2894 - Replace Hessian services with REST ([ba79da8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ba79da89f91aea3267ede1f399355a455596ab74))
* DFE-2894 - Switch from Hessian to Rest ([87a1edc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/87a1edc2f4e38d247a4e2a0a40cd75f47dda22f2))

## [1.12.1-rc.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.12.0...1.12.1-rc.1) (2023-12-21)

# [1.12.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.2...1.12.0) (2023-12-20)


### Features

* create new release ([673ad7a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/673ad7a1c112394e9bf945f7fa0f7e4dd76b8cee))

## [1.11.2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.1...1.11.2) (2023-12-19)

# [1.11.0-rc.7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-rc.6...1.11.0-rc.7) (2023-12-18)


### Features

* DFE-2838 Check for postcode mandatory on quote order update ([8c90a60](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8c90a60f4ff908eddeca405626c3a836ad3b2268))

# [1.11.0-rc.6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-rc.5...1.11.0-rc.6) (2023-12-15)


### Features

* DFE-2838 Save address without postcode ([de93964](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/de93964e9f524887444f45aa841ebe6941916eb3))

# [1.11.0-rc.5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-rc.4...1.11.0-rc.5) (2023-12-12)


### Bug Fixes

* fixing error handling on DFE-2921 - updating a draft rejected update with wrong error code ([a61b5d2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a61b5d27bc70ec6d1068e609c0d3242799b1d394))


### Features

* DFE-2838 Add mandatory flag for country ([26ea96c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/26ea96c8b5a1716a3fda73e6fc0f02fc55359f87))

# [1.11.0-rc.4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-rc.3...1.11.0-rc.4) (2023-12-12)


### Bug Fixes

* DFE-2918 Adjust max value for quantity from 999 to 99999 ([780d0d3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/780d0d36c2b1f645a57b6930bdcc8ae155fa2d77))

# [1.11.0-rc.3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-rc.2...1.11.0-rc.3) (2023-12-11)


### Bug Fixes

* DFE-2763 fixing errors in transfering documents on save include book ([2f185c6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2f185c6556a67748a239e32c2c5abd84d0f0df54))
* fixing tests for DFE-2763 ([5971fb9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5971fb95d4eca33b826dfee5bd6cc2c671b666b5))

# [1.11.0-rc.2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-rc.1...1.11.0-rc.2) (2023-12-07)


### Bug Fixes

* DFE-2921 changed labels for expiored messages, send already had dedicated labels ([4456440](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/4456440cedf45640fcdd7bd328eac86111567292))
* DFE-2921 changed labels for expiored messages, send already had dedicated labels ([e285747](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e28574787b896a8513890f588a13fb7dfeb36d7e))

# [1.11.0-rc.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.10.0...1.11.0-rc.1) (2023-12-05)


### Bug Fixes

* DFE-2763 - ensuring customerNumber is not changed in quote2book case ([c4ce8c9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/c4ce8c9be2940be343efe12c5ebd12f8dd0dcdc4))
* DFE-2763 - ensuring customerNumber is not changed in quote2book case ([5138d01](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5138d018ed3bfe328fca47ce87495b5085c4e558))
* DFE-2763 fixing principal switch, now including file moved as well ([6b8f96b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6b8f96b4eeece48e1e5a9a240c39fec366bda959))
* some missing changes for beta that were already on develo ([a485b48](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a485b484c17c6aa29e0d4ce2967b0b492e14328f))


### Features

* DFE-2329 generic error handling - base implementation added, further details will be implemented in subtickets ([d493f02](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d493f02b4b236209cd9d6850240411b70cadc9ad))
* DFE-2489 Add comment for the usage of collectionDate (air/sea) ([1991d5a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1991d5a2e537ec3ab2e53bea708385cd348b61f4))
* DFE-2502 Implement EDI converter/submitter for sea import order ([fc0ce07](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/fc0ce075e45feff302b4995cd9dd95c0cdd62f77))
* DFE-2502 Implement EDI converter/submitter for sea import order ([083938b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/083938b5350748005077732e36b68615cde3611c))
* DFE-2624 quote 2 book air products -> consolidating on BRANCH 2552 ([13777a9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/13777a9e0e2e792dc813dbd542249fc702ec30d9))
* DFE-2642 - errorid order submit ([c6c4b76](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/c6c4b767f6c73126fe55390e52d9ece6912fe0de))
* DFE-2642 error id in submit and print-labels ([2d1d257](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2d1d257d151e24bb0e5863dceb6f411d189bbe70))
* DFE-2714 - Air product validation ([e4cb763](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e4cb7636d5c35566b10ac9ef4058e0b086c22207))
* DFE-2733 Add reference to import and export ([1027a87](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1027a8751e05f388e2aa8aad6be8aee9d4416f94))
* DFE-2763 - principal switch of order is allowed if both source and target customerNumber is accessible ([a5ad157](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a5ad1575285d196c6880b30f252ac9fe5e7c24ef))
* DFE-2763 - principal switch of order is allowed if both source and target customerNumber is accessible ([ede8534](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ede8534def265dd18dde9475fe1e3a78b572646d))
* DFE-2803 - Adjust number of decimal digits allowed for SEA weight & volume ([0a328f5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0a328f535b4229730f3a8fdb4b8556f5642363ed))
* DFE-2832 Don´t allow update from order status LABEL_PENDING to DRAFT ([eab3d64](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/eab3d644b373731d6e4cff1666bb076e97e1da9f))
* DFE-2855 add documentation link ([f83cf32](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/f83cf323bf946bd3f1f3daf8cb039bc19e85263e))
* DFE-2855 add documentation link ([0dacee2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0dacee278fe1aae495cfde06f418b0c0cdcb383f))
* DFE-2855 Add service account name ([91bfdec](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/91bfdecd76399b99d39878f05cf8682a24ed4605))
* DFE-2855 Add service account name ([8832cf5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8832cf5489c137b70adac8b2f39a337b2a486fc5))
* DFE-2855 Move the block before database configuration and fetch database credentials ([d4f9cf6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d4f9cf67b0d0bc32395292dde21beeec94379d99))
* DFE-2855 Move the block before database configuration and fetch database credentials ([201d43a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/201d43a5313dc32227f4b8bdfcf9792229610fa8))
* DFE-2864 Adjust document type for SEA order ([d72750d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d72750d2700436ccb36c0b529cf782533fc96ad8))

# [1.11.0-develop.7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-develop.6...1.11.0-develop.7) (2023-11-30)


### Bug Fixes

* DFE-2763 - ensuring customerNumber is not changed in quote2book case ([c4ce8c9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/c4ce8c9be2940be343efe12c5ebd12f8dd0dcdc4))
* DFE-2763 - ensuring customerNumber is not changed in quote2book case ([5138d01](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/5138d018ed3bfe328fca47ce87495b5085c4e558))

# [1.11.0-develop.6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-develop.5...1.11.0-develop.6) (2023-11-30)


### Bug Fixes

* DFE-2763 fixing principal switch, now including file moved as well ([6b8f96b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/6b8f96b4eeece48e1e5a9a240c39fec366bda959))


### Features

* DFE-2763 - principal switch of order is allowed if both source and target customerNumber is accessible ([a5ad157](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a5ad1575285d196c6880b30f252ac9fe5e7c24ef))

# [1.11.0-develop.5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-develop.4...1.11.0-develop.5) (2023-11-29)


### Features

* DFE-2763 - principal switch of order is allowed if both source and target customerNumber is accessible ([ede8534](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/ede8534def265dd18dde9475fe1e3a78b572646d))
* DFE-2855 Add service account name ([8832cf5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/8832cf5489c137b70adac8b2f39a337b2a486fc5))
* DFE-2855 Move the block before database configuration and fetch database credentials ([201d43a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/201d43a5313dc32227f4b8bdfcf9792229610fa8))

# [1.11.0-develop.4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-develop.3...1.11.0-develop.4) (2023-11-28)


### Features

* DFE-2832 Don´t allow update from order status LABEL_PENDING to DRAFT ([eab3d64](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/eab3d644b373731d6e4cff1666bb076e97e1da9f))
* DFE-2855 add documentation link ([0dacee2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/0dacee278fe1aae495cfde06f418b0c0cdcb383f))

# [1.11.0-develop.3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-develop.2...1.11.0-develop.3) (2023-11-24)


### Features

* DFE-2502 Implement EDI converter/submitter for sea import order ([083938b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/083938b5350748005077732e36b68615cde3611c))

# [1.11.0-develop.2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-develop.1...1.11.0-develop.2) (2023-11-23)


### Features

* DFE-2642 - errorid order submit ([c6c4b76](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/c6c4b767f6c73126fe55390e52d9ece6912fe0de))
* DFE-2642 error id in submit and print-labels ([2d1d257](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/2d1d257d151e24bb0e5863dceb6f411d189bbe70))

# [1.11.0-develop.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.10.0...1.11.0-develop.1) (2023-11-17)


### Bug Fixes

* some missing changes for beta that were already on develo ([a485b48](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a485b484c17c6aa29e0d4ce2967b0b492e14328f))


### Features

* DFE-2329 generic error handling - base implementation added, further details will be implemented in subtickets ([d493f02](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d493f02b4b236209cd9d6850240411b70cadc9ad))
* DFE-2489 Add comment for the usage of collectionDate (air/sea) ([1991d5a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1991d5a2e537ec3ab2e53bea708385cd348b61f4))
* DFE-2624 quote 2 book air products -> consolidating on BRANCH 2552 ([13777a9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/13777a9e0e2e792dc813dbd542249fc702ec30d9))
* DFE-2714 - Air product validation ([e4cb763](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e4cb7636d5c35566b10ac9ef4058e0b086c22207))
* DFE-2733 Add reference to import and export ([1027a87](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1027a8751e05f388e2aa8aad6be8aee9d4416f94))

# [1.11.0-develop.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.10.0...1.11.0-develop.1) (2023-11-15)


### Bug Fixes

* some missing changes for beta that were already on develo ([a485b48](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a485b484c17c6aa29e0d4ce2967b0b492e14328f))


### Features

* DFE-2329 generic error handling - base implementation added, further details will be implemented in subtickets ([d493f02](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d493f02b4b236209cd9d6850240411b70cadc9ad))
* DFE-2489 Add comment for the usage of collectionDate (air/sea) ([1991d5a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1991d5a2e537ec3ab2e53bea708385cd348b61f4))
* DFE-2624 quote 2 book air products -> consolidating on BRANCH 2552 ([13777a9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/13777a9e0e2e792dc813dbd542249fc702ec30d9))
* DFE-2714 - Air product validation ([e4cb763](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e4cb7636d5c35566b10ac9ef4058e0b086c22207))
* DFE-2733 Add reference to import and export ([1027a87](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1027a8751e05f388e2aa8aad6be8aee9d4416f94))

# [1.11.0-beta.7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-beta.6...1.11.0-beta.7) (2023-11-15)


### Bug Fixes

* some missing changes for beta that were already on develo ([a485b48](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/a485b484c17c6aa29e0d4ce2967b0b492e14328f))


### Features

* DFE-2733 Add reference to import and export ([1027a87](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1027a8751e05f388e2aa8aad6be8aee9d4416f94))

# [1.11.0-beta.6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-beta.5...1.11.0-beta.6) (2023-11-15)


### Features

* DFE-2489 Add comment for the usage of collectionDate (air/sea) ([1991d5a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/1991d5a2e537ec3ab2e53bea708385cd348b61f4))

# [1.11.0-beta.5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-beta.4...1.11.0-beta.5) (2023-11-14)


### Features

* DFE-2329 generic error handling - base implementation added, further details will be implemented in subtickets ([d493f02](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/d493f02b4b236209cd9d6850240411b70cadc9ad))

# [1.11.0-beta.4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-beta.3...1.11.0-beta.4) (2023-11-14)
* DFE3-1424 add Sorting for field 'request arrangement' and pickUpDate ([DFE3-1424](https://dil-itd.atlassian.net/browse/DFE3-1424))


### Features

* DFE-2714 - Air product validation ([e4cb763](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/e4cb7636d5c35566b10ac9ef4058e0b086c22207))

# [1.11.0-beta.3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-beta.2...1.11.0-beta.3) (2023-11-13)

# [1.11.0-beta.2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.11.0-beta.1...1.11.0-beta.2) (2023-11-13)

# [1.11.0-beta.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/compare/1.10.0...1.11.0-beta.1) (2023-11-11)


### Features

* DFE-2624 quote 2 book air products -> consolidating on BRANCH 2552 ([13777a9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-backend/commit/13777a9e0e2e792dc813dbd542249fc702ec30d9))
* Countries are now sorted with a collator. ([DFE-2592](https://dil-itd.atlassian.net/browse/DFE-2592))
* Added customer number restriction for viewing air products([DFE-2753](https://dil-itd.atlassian.net/browse/DFE-2753))
* Feature: Added new endpoint for getting air products stored in database ([DFE-2552](https://dil-itd.atlassian.net/browse/DFE-2552))

### Changed
* Chore: Set reference type to not null ([DFE-2818](https://dil-itd.atlassian.net/browse/DFE-2818))
* Chore: Changed sea references to nvarchar ([DFE-2818](https://dil-itd.atlassian.net/browse/DFE-2818))

* prevent expired order from being updated and return new error type ([DFE-2520](https://dil-itd.atlassian.net/browse/DFE-2520))


<!-- Switching from manual to automatic changelog -->

## [1.10.0] - 2023-11-06

### Added

### Changed

- Chore: Integrated Platform-Api v2.0.2 ([DFE-2698](https://dil-itd.atlassian.net/browse/DFE-2698))
- Chore: Update SharedService API client version to 2.0.1  ([DFE-2446](https://dil-itd.atlassian.net/browse/DFE-2446))
- Changed configuration of hikari connection pool to 50 connections ([DFE-2705](https://dil-itd.atlassian.net/browse/DFE-2705))
- Fix: Changed cron execution time ([DFE-2760](https://dil-itd.atlassian.net/browse/DFE-2760))
- Chore: Update version of translation plugin to 1.0.2
- Added better logging for save exceptions after successful submission ([DFE-2417](https://dil-itd.atlassian.net/browse/DFE-2417))

### Fixed

### Removed

- Removed log config from application.yaml ([DFE-2701](https://dil-itd.atlassian.net/browse/DFE-2701))

## [1.9.1] - 2023-10-20

### Added
- Feature: Add reference 'shipment order number' with the shipment number ([DFE-2733](https://dil-itd.atlassian.net/browse/DFE-2733))

### Changed

- Fix: Add transactional behavior when fetching active status of incoterm([DFE-2747](https://dil-itd.atlassian.net/browse/DFE-2747))

### Fixed

### Removed

## [1.9.0] - 2023-10-17

### Added

- Feature: Add field 'request arrangement' to Overview ([DFE3-1313](https://dil-itd.atlassian.net/browse/DFE3-1313))
- Feature: Added filter for field 'request arrangement'  ([DFE3-1295](https://dil-itd.atlassian.net/browse/DFE3-1295))

### Changed

- Contact phone or mobile is no longer mandatory for order address in air / sea order ([DFE-2454](https://dil-itd.atlassian.net/browse/DFE-2454))
- Feature: Changed error results api definition for 'save' and 'update' operations ([DFE-2180](https://dil-itd.atlassian.net/browse/DFE-2180))
- Task: Split the Trial Flag into Road and AirSea ([DFE-2671](https://dil-itd.atlassian.net/browse/DFE-2671))
- Feature: Adapted incoterms to be be active or inactive ([DFE-2608](https://dil-itd.atlassian.net/browse/DFE-2608))

### Fixed

### Removed

- Chore: Removed documentID (EDI Value) from orders ([DFE-2505](https://dil-itd.atlassian.net/browse/DFE-2505))

## [1.8.0] - 2023-10-02

### Added

- Feature: Implemented 'save' and 'book' for AIR Import orders ([DFE-2501](https://dil-itd.atlassian.net/browse/DFE-2501))
- Feature: Take over information if it's an import or export order from Q2B ([DFE-2460](https://dil-itd.atlassian.net/browse/DFE-2460))
- Feature: Import Logic for SEA orders ([DFE-1192](https://dil-itd.atlassian.net/browse/DFE-1192))
- Feature: Address field check for incomplete address or contact data ([DFE-1697](https://dil-itd.atlassian.net/browse/DFE-1697))
- Feature: Data Load for Performance Test ([DFE-2394](https://dil-itd.atlassian.net/browse/DFE-2394))
- Feature: Add Sea Orders to Overview ([DFE3-1301](https://dil-itd.atlassian.net/browse/DFE3-1301))
### Changed

- Task: Simplified Q2B validation test ([DFE2-1036](https://dil-itd.atlassian.net/browse/DFE2-1036))
- Feature: Api refactoring for AIR & ROAD ([DFE-2422](https://dil-itd.atlassian.net/browse/DFE-2422))
- Feature: Using TranslationMapper for translating references texts ([DFE3-1162](https://dil-itd.atlassian.net/browse/DFE3-1162)) 
- Feature: Changed error results api definition for 'save' and 'update' operations ([DFE-2180](https://dil-itd.atlassian.net/browse/DFE-2180))

### Fixed

- Bug: AIR packaging list was not fully available ([DFE-2623](https://dil-itd.atlassian.net/browse/DFE-2623))
- Bug: Not supporting blank GLN in order address ([DFE-2549](https://dil-itd.atlassian.net/browse/DFE-2549))
- Bug: Fixed Quote price relevant change too strict for orderline volume ([DFE-2554](https://dil-itd.atlassian.net/browse/DFE-2554))
- Bug: Allow update on order with status 'complete' ([DFE-2607](https://dil-itd.atlassian.net/browse/DFE-2607))
- Bug: Use CollectionDate when CollectionDateFrom and TO are null ([DFE3-1437]) (https://dil-itd.atlassian.net/browse/DFE3-1437))

### Removed

## [1.7.2] - 2023-09-15

### Added

- This section is only a template to visualize how the changelog will look like.

### Changed

- This section is only a template to visualize how the changelog will look like.

### Fixed

- This section is only a template to visualize how the changelog will look like.

### Removed

- This section is only a template to visualize how the changelog will look like.
