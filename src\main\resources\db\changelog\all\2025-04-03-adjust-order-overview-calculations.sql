-- liquibase formatted sql

-- changeset floriank:2025-04-03-adjust-order-overview-calculations

-- adjusting the calculations for order overview columns

drop view v_order_overview;

create view v_order_overview as
select
    -- order base fields
    ob.order_id,
    ob.customer_number                                                                                          as customer_number,
    ob.customer_number_with_segment,
    ob.order_type,
    cast(ob.shipment_number as varchar)                                                                         as shipment_number,
    ob.status,
    cast(0 as bit)                                                                                              as favourite_status,
    ob.status_sort,
    ob.last_modified                                                                                            as last_modified,
    case
        when ob.collection_from is null then ob.collection_date
        else ob.collection_from
        end                                                                                                     as pick_up_date_from,
    ob.collection_to                                                                                            as pick_up_date_to,
    case
        when ob.collection_from is null then cast(1 as bit)
        else cast(0 as bit)
        end                                                                                                     as no_time_information_available,
    ob.send_at                                                                                                  as order_sent,
    ob.order_expiry_date                                                                                        as quote_expiration_time,
    ob.goods_value                                                                                              as value_of_goods,
    ob.currency                                                                                                 as goods_currency,
    -- order specific fields
    oa.request_arrangement                                                                                      as request_arrangement,
    oa.from_iata                                                                                                as port_departure,
    oa.to_iata                                                                                                  as port_destination,
    oa.inco_term                                                                                                as term_code,
    cast(oa.product_code as varchar)                                                                            as product,
    null                                                                                                        as division,
    null                                                                                                        as delivery_date,
    -- order unimplemented fields
    null                                                                                                        as principal_address,
    null                                                                                                        as flight_number_or_vessel_name,
    null                                                                                                        as transport,
    null                                                                                                        as type_of_goods,
    null                                                                                                        as project,
    null                                                                                                        as customs_goods,
    null                                                                                                        as origin_of_order,
    null                                                                                                        as branch,
    null                                                                                                        as pick_up_date_type,
    cast(0 as bit)                                                                                              as direct_delivery,
    cast(0 as bit)                                                                                              as contains_dangerous_goods,
    -- shipper fields
    shipper.name                                                                                                as shipper_name,
    shipper.name2                                                                                               as shipper_name2,
    shipper.name3                                                                                               as shipper_name3,
    shipper.city                                                                                                as shipper_city,
    shipper.postcode                                                                                            as shipper_postcode,
    shipper.country_code                                                                                        as shipper_country,
    -- consignee fields
    consignee.name                                                                                              as consignee_name,
    consignee.name2                                                                                             as consignee_name2,
    consignee.name3                                                                                             as consignee_name3,
    consignee.city                                                                                              as consignee_city,
    consignee.postcode                                                                                          as consignee_postcode,
    consignee.country_code                                                                                      as consignee_country,
    -- aggregated fields from other entities
    (select sum(ol.weight) from order_line_air ol where ol.order_id = oa.order_id)                              as weight,
    (select sum(ol.quantity) from order_line_air ol where ol.order_id = oa.order_id)                            as quantity,
    (select string_agg(ofa.address_type, ',') from order_further_address ofa where ofa.order_id = ob.order_id)  as further_codes,
    (select string_agg(d.document_type_id, ',') from document d where d.order_id = ob.order_id)                 as included_documents,
    (select string_agg(ora.reference_value, ',') from order_reference_air ora where ora.order_id = ob.order_id) as order_references
from order_air oa
         inner join order_base ob on oa.order_id = ob.order_id
         left join order_address as shipper on ob.shipper_address_id = shipper.order_address_id
         left join order_address as consignee on ob.consignee_address_id = consignee.order_address_id
union all
select
    -- order base fields
    ob.order_id,
    ob.customer_number                                                                                          as customer_number,
    ob.customer_number_with_segment,
    ob.order_type,
    cast(ob.shipment_number as varchar)                                                                         as shipment_number,
    ob.status,
    cast(0 as bit)                                                                                              as favourite_status,
    ob.status_sort,
    ob.last_modified                                                                                            as last_modified,
    case
        when ob.collection_from is null then ob.collection_date
        else ob.collection_from
        end                                                                                                     as pick_up_date_from,
    ob.collection_to                                                                                            as pick_up_date_to,
    case
        when ob.collection_from is null then cast(1 as bit)
        else cast(0 as bit)
        end                                                                                                     as no_time_information_available,
    ob.send_at                                                                                                  as order_sent,
    ob.order_expiry_date                                                                                        as quote_expiration_time,
    ob.goods_value                                                                                              as value_of_goods,
    ob.currency                                                                                                 as goods_currency,
    -- order specific fields
    os.request_arrangement                                                                                      as request_arrangement,
    os.from_port                                                                                                as port_departure,
    os.to_port                                                                                                  as port_destination,
    os.inco_term                                                                                                as term_code,
    null                                                                                                        as product,
    null                                                                                                        as division,
    null                                                                                                        as delivery_date,
    -- order unimplemented fields
    null                                                                                                        as principal_address,
    null                                                                                                        as flight_number_or_vessel_name,
    null                                                                                                        as transport,
    null                                                                                                        as type_of_goods,
    null                                                                                                        as project,
    null                                                                                                        as customs_goods,
    null                                                                                                        as origin_of_order,
    null                                                                                                        as branch,
    null                                                                                                        as pick_up_date_type,
    cast(0 as bit)                                                                                              as direct_delivery,
    cast(0 as bit)                                                                                              as contains_dangerous_goods,
    -- shipper fields
    shipper.name                                                                                                as shipper_name,
    shipper.name2                                                                                               as shipper_name2,
    shipper.name3                                                                                               as shipper_name3,
    shipper.city                                                                                                as shipper_city,
    shipper.postcode                                                                                            as shipper_postcode,
    shipper.country_code                                                                                        as shipper_country,
    -- consignee fields
    consignee.name                                                                                              as consignee_name,
    consignee.name2                                                                                             as consignee_name2,
    consignee.name3                                                                                             as consignee_name3,
    consignee.city                                                                                              as consignee_city,
    consignee.postcode                                                                                          as consignee_postcode,
    consignee.country_code                                                                                      as consignee_country,
    -- aggregated fields from other entities
    case
        when (cast(os.full_container_load as bit) = 0)
            then
            (select sum(ol.quantity) from order_line_sea ol where ol.order_id = os.order_id and ol.full_container_load_id is null)
        else
            (select sum(ol.quantity) from order_line_sea ol where ol.order_id = os.order_id and ol.full_container_load_id is not null)
        end                                                                                                     as quantity,
    case
        when (cast(os.full_container_load as bit) = 0)
            then
            (select sum(ol.weight) from order_line_sea ol where ol.order_id = os.order_id and ol.full_container_load_id is null)
        else
            (select sum(ol.weight) from order_line_sea ol where ol.order_id = os.order_id and ol.full_container_load_id is not null)
        end                                                                                                     as weight,
    (select string_agg(ofa.address_type, ',') from order_further_address ofa where ofa.order_id = ob.order_id)  as further_codes,
    (select string_agg(d.document_type_id, ',') from document d where d.order_id = ob.order_id)                 as included_documents,
    (select string_agg(ora.reference_value, ',') from order_reference_sea ora where ora.order_id = ob.order_id) as order_references
from order_sea os
         inner join order_base ob on os.order_id = ob.order_id
         left join order_address as shipper on ob.shipper_address_id = shipper.order_address_id
         left join order_address as consignee on ob.consignee_address_id = consignee.order_address_id
union all
select
    -- order base fields
    ob.order_id,
    ob.customer_number                                                                                         as customer_number,
    ob.customer_number_with_segment,
    ob.order_type,
    cast(ob.shipment_number as varchar)                                                                        as shipment_number,
    ob.status,
    cast(0 as bit)                                                                                             as favourite_status,
    ob.status_sort,
    ob.last_modified                                                                                           as last_modified,
    case
        when ob.collection_from is null then ob.collection_date
        else ob.collection_from
        end                                                                                                    as pick_up_date_from,
    ob.collection_to                                                                                           as pick_up_date_to,
    case
        when ob.collection_from is null then cast(1 as bit)
        else cast(0 as bit)
        end                                                                                                    as no_time_information_available,
    ob.send_at                                                                                                 as order_sent,
    ob.order_expiry_date                                                                                       as quote_expiration_time,
    ob.goods_value                                                                                             as value_of_goods,
    ob.currency                                                                                                as goods_currency,
    -- order specific fields
    cast(0 as bit)                                                                                             as request_arrangement,
    null                                                                                                       as port_departure,
    null                                                                                                       as port_destination,
    orr.freight_term                                                                                           as term_code,
    orr.product                                                                                                as product,
    orr.division                                                                                               as division,
    orr.fix_date                                                                                               as delivery_date,
    -- order unimplemented fields
    null                                                                                                       as principal_address,
    null                                                                                                       as flight_number_or_vessel_name,
    null                                                                                                       as transport,
    null                                                                                                       as type_of_goods,
    null                                                                                                       as project,
    null                                                                                                       as customs_goods,
    null                                                                                                       as origin_of_order,
    null                                                                                                       as branch,
    null                                                                                                       as pick_up_date_type,
    cast(0 as bit)                                                                                             as direct_delivery,
    cast(0 as bit)                                                                                             as contains_dangerous_goods,
    -- shipper fields
    shipper.name                                                                                               as shipper_name,
    shipper.name2                                                                                              as shipper_name2,
    shipper.name3                                                                                              as shipper_name3,
    shipper.city                                                                                               as shipper_city,
    shipper.postcode                                                                                           as shipper_postcode,
    shipper.country_code                                                                                       as shipper_country,
    -- consignee fields
    consignee.name                                                                                             as consignee_name,
    consignee.name2                                                                                            as consignee_name2,
    consignee.name3                                                                                            as consignee_name3,
    consignee.city                                                                                             as consignee_city,
    consignee.postcode                                                                                         as consignee_postcode,
    consignee.country_code                                                                                     as consignee_country,
    -- aggregated fields from other entities
    (select sum(ol.weight) from order_line_road ol where ol.order_id = orc.order_id)                           as weight,
    (select coalesce((select sum(ol.quantity) from order_line_road ol where ol.order_id = orc.order_id and ol.packing_position_id is null), 0)
                +
            coalesce((select sum(pp.quantity) from packing_position pp where pp.order_id = orc.order_id), 0)) as quantity,
    (select string_agg(ofa.address_type, ',') from order_further_address ofa where ofa.order_id = ob.order_id) as further_codes,
    (select string_agg(d.document_type_id, ',') from document d where d.order_id = ob.order_id)                as included_documents,
    (select string_agg(ore.reference, ',') from order_reference_road ore where ore.order_id = ob.order_id)     as order_references
from order_road_collection orc
         inner join order_base ob on orc.order_id = ob.order_id
         inner join order_road orr on orc.order_id = orr.order_id
         left join order_address as shipper on ob.shipper_address_id = shipper.order_address_id
         left join order_address as consignee on ob.consignee_address_id = consignee.order_address_id
union all
select
    -- order base fields
    ob.order_id,
    ob.customer_number                                                                                         as customer_number,
    ob.customer_number_with_segment,
    ob.order_type,
    cast(ob.shipment_number as varchar)                                                                        as shipment_number,
    ob.status,
    cast(0 as bit)                                                                                             as favourite_status,
    ob.status_sort,
    ob.last_modified                                                                                           as last_modified,
    case
        when ob.collection_from is null then ob.collection_date
        else ob.collection_from
        end                                                                                                    as pick_up_date_from,
    ob.collection_to                                                                                           as pick_up_date_to,
    case
        when ob.collection_from is null then cast(1 as bit)
        else cast(0 as bit)
        end                                                                                                    as no_time_information_available,
    ob.send_at                                                                                                 as order_sent,
    ob.order_expiry_date                                                                                       as quote_expiration_time,
    ob.goods_value                                                                                             as value_of_goods,
    ob.currency                                                                                                as goods_currency,
    -- order specific fields
    cast(0 as bit)                                                                                             as request_arrangement,
    null                                                                                                       as port_departure,
    null                                                                                                       as port_destination,
    orr.freight_term                                                                                           as term_code,
    orr.product                                                                                                as product,
    orr.division                                                                                               as division,
    orr.fix_date                                                                                               as delivery_date,
    -- order unimplemented fields
    null                                                                                                       as principal_address,
    null                                                                                                       as flight_number_or_vessel_name,
    null                                                                                                       as transport,
    null                                                                                                       as type_of_goods,
    null                                                                                                       as project,
    null                                                                                                       as customs_goods,
    null                                                                                                       as origin_of_order,
    null                                                                                                       as branch,
    null                                                                                                       as pick_up_date_type,
    cast(0 as bit)                                                                                             as direct_delivery,
    cast(0 as bit)                                                                                             as contains_dangerous_goods,
    -- shipper fields
    shipper.name                                                                                               as shipper_name,
    shipper.name2                                                                                              as shipper_name2,
    shipper.name3                                                                                              as shipper_name3,
    shipper.city                                                                                               as shipper_city,
    shipper.postcode                                                                                           as shipper_postcode,
    shipper.country_code                                                                                       as shipper_country,
    -- consignee fields
    consignee.name                                                                                             as consignee_name,
    consignee.name2                                                                                            as consignee_name2,
    consignee.name3                                                                                            as consignee_name3,
    consignee.city                                                                                             as consignee_city,
    consignee.postcode                                                                                         as consignee_postcode,
    consignee.country_code                                                                                     as consignee_country,
    -- aggregated fields from other entities
    (select sum(ol.weight) from order_line_road ol where ol.order_id = orf.order_id)                           as weight,
    case
        when orf.manual_number_sscc is not null
            then cast(orf.manual_number_sscc as int)
        else (
            coalesce((select sum(ol.quantity) from order_line_road ol where ol.order_id = orf.order_id and ol.packing_position_id is null), 0)
                +
            coalesce((select sum(pp.quantity) from packing_position pp where pp.order_id = orf.order_id), 0)
            )
        end                                                                                                    as quantity,
    (select string_agg(ofa.address_type, ',') from order_further_address ofa where ofa.order_id = ob.order_id) as further_codes,
    (select string_agg(d.document_type_id, ',') from document d where d.order_id = ob.order_id)                as included_documents,
    (select string_agg(ore.reference, ',') from order_reference_road ore where ore.order_id = ob.order_id)     as order_references
from order_road_forward orf
         inner join order_base ob on orf.order_id = ob.order_id
         inner join order_road orr on orf.order_id = orr.order_id
         left join order_address as shipper on ob.shipper_address_id = shipper.order_address_id
         left join order_address as consignee on ob.consignee_address_id = consignee.order_address_id
;