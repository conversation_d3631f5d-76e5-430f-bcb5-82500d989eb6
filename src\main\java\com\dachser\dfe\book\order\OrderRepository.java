package com.dachser.dfe.book.order;

import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.road.CollectionOrder;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.sea.SeaOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

public interface OrderRepository extends JpaRepository<Order, Long> {

	Optional<Order> findByCustomerNumberAndOrderId(String customerNumber, Long orderId);

	List<Order> findByStatus(OrderStatus status);

	List<Order> findByShipmentNumber(long shipmentNumber);

	@Query(nativeQuery = true, name = "Order.findTypeOwnerAndStatus")
	OrderTypeStatusAndOwnerPojo findTypeOwnerAndStatus(Long orderId);

	@Query("SELECT order FROM Order order WHERE order.sendAt <= :daysDifference AND order.status = 'SENT'")
	List<Order> findAllSentOrdersOlderThan(Instant daysDifference);

	@Query("SELECT order FROM Order order WHERE order.lastModified <= :daysDifference AND order.status = 'DELETED'")
	List<Order> findAllDeletedOrdersOlderThan(Instant daysDifference);

	@Query("SELECT roadOrder FROM RoadOrder roadOrder WHERE roadOrder.status IN :statusList AND roadOrder.emissionForecast IS NULL AND roadOrder.emissionForecastRetryCount <= :retryCount")
	List<RoadOrder> findAllWithEmissionForecastNullAndRetryCountBelowRetryCountRoad(List<OrderStatus> statusList, Integer retryCount);

	@Query("SELECT airOrder FROM AirOrder airOrder WHERE airOrder.status IN :statusList AND airOrder.emissionForecast IS NULL AND airOrder.emissionForecastRetryCount <= :retryCount")
	List<AirOrder> findAllWithEmissionForecastNullAndRetryCountBelowRetryCountAir(List<OrderStatus> statusList, Integer retryCount);

	@Query("SELECT seaOrder FROM SeaOrder seaOrder WHERE seaOrder.status IN :statusList AND seaOrder.emissionForecast IS NULL AND seaOrder.emissionForecastRetryCount <= :retryCount")
	List<SeaOrder> findAllWithEmissionForecastNullAndRetryCountBelowRetryCountSea(List<OrderStatus> statusList, Integer retryCount);

	// -- Expiry queries

	@Query("SELECT order FROM Order order WHERE order.status in :statusList and order.orderExpiryDate is not null and order.orderExpiryDate  <= :currentDateTime")
	List<Order> findByStatusInAndExpired(List<OrderStatus> statusList, OffsetDateTime currentDateTime);

	@Query("SELECT airOrder FROM AirOrder airOrder WHERE airOrder.status in :statusList and airOrder.orderExpiryDate is not null and airOrder.orderExpiryDate <=  :currentDateTime")
	List<AirOrder> findAllExpiredAirOrders(List<OrderStatus> statusList, OffsetDateTime currentDateTime);

	@Query("SELECT seaOrder FROM SeaOrder seaOrder WHERE seaOrder.status in :statusList and seaOrder.orderExpiryDate is not null and seaOrder.orderExpiryDate <=  :currentDateTime")
	List<SeaOrder> findAllExpiredSeaOrders(List<OrderStatus> statusList, OffsetDateTime currentDateTime);

	@Query("SELECT colOrd FROM CollectionOrder colOrd WHERE colOrd.status in :statusList and colOrd.orderExpiryDate is not null and colOrd.orderExpiryDate <=  :currentDateTime")
	List<CollectionOrder> findAllExpiredCollectionOrders(List<OrderStatus> statusList, OffsetDateTime currentDateTime);

	@Query("SELECT forOrd FROM ForwardingOrder forOrd WHERE forOrd.status in :statusList and forOrd.orderExpiryDate is not null and ((forOrd.adviceSent is not null and forOrd.orderExpiryDate <= :adviceOffsetDateTime) or (forOrd.adviceSent is null and forOrd.orderExpiryDate <=  :defaultOffsetDateTime ))")
	List<ForwardingOrder> findAllExpiredForwardingOrders(List<OrderStatus> statusList, OffsetDateTime defaultOffsetDateTime, OffsetDateTime adviceOffsetDateTime);

}
