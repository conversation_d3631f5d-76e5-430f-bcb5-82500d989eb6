package com.dachser.dfe.book.product.road;

import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ErrorIdExternalServiceNotAvailableException;
import com.dachser.dfe.book.mapper.ShipperMasterdataMapper;
import com.dachser.dfe.book.mapper.ShipperMasterdataMapperImpl;
import com.dachser.dfe.book.masterdata.road.RoadMasterDataApiWrapper;
import com.dachser.dfe.book.model.ShipperMasterData;
import com.dachser.dfe.road.masterdata.model.RMDShipperMasterdataDTO;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestClientException;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ShipperMasterdataAdapterTest {

	@Spy
	ShipperMasterdataMapper shipperMasterdataMapper = new ShipperMasterdataMapperImpl();

	@InjectMocks
	ShipperMasterdataAdapterExt shipperMasterdataAdapter;

	@Mock
	RoadMasterDataApiWrapper roadMasterDataApiWrapper;

	@Nested
	class GetShipperMasterdata {

		@Test
		void returnsShipperMasterdataWhenApiCallSucceeds() {
			Long shipperNumber = 123L;
			Integer businessDomain = 1;
			List<RMDShipperMasterdataDTO> expectedData = List.of(new RMDShipperMasterdataDTO());

			when(roadMasterDataApiWrapper.getShipperMasterdata(shipperNumber, businessDomain)).thenReturn(expectedData);

			List<ShipperMasterData> result = shipperMasterdataAdapter.getShipperMasterdata(shipperNumber, businessDomain);

			assertEquals(expectedData.size(), result.size());

		}

		@Test
		void throwsExceptionWhenApiCallFails() {
			Long shipperNumber = 123L;
			Integer businessDomain = 1;

			when(roadMasterDataApiWrapper.getShipperMasterdata(shipperNumber, businessDomain)).thenThrow(new RestClientException("Service not available"));

			ErrorIdExternalServiceNotAvailableException exception = assertThrows(ErrorIdExternalServiceNotAvailableException.class,
					() -> shipperMasterdataAdapter.getShipperMasterdata(shipperNumber, businessDomain));

			assertEquals(BookErrorId.ERR_RF_01, exception.getErrorId());
		}
	}



}