package com.dachser.dfe.book.transferlist.trackablepackingaid;

import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.masterdata.road.RoadMasterDataApiWrapper;
import com.dachser.dfe.book.transferlist.exception.TrackablePackingAidServiceNotAvailableException;
import com.dachser.dfe.book.user.BusinessDomainProvider;
import com.dachser.dfe.road.masterdata.model.RMDTrackablePackingAidRequestDTO;
import com.dachser.dfe.road.masterdata.model.RMDTrackablePackingAidResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "false", matchIfMissing = true)
public class TrackablePackingAidServiceExt implements TrackablePackingAidService {

	private final RoadMasterDataApiWrapper roadMasterdataApiWrapper;

	private final BusinessDomainProvider businessDomainProvider;

	@Override
	public RMDTrackablePackingAidResponseDTO categorizeTransferList(String shipperCountryCode, String consigneeCountryCode, Division division, LocalDate date,
			List<String> packingAidTypes) {
		try {
			return roadMasterdataApiWrapper.categorizeTransferList(createRequestDTO(shipperCountryCode, consigneeCountryCode, division, date, packingAidTypes));
		} catch (RestClientException e) {
			log.error("Exception when calling the MasterData API: ", e);
			throw new TrackablePackingAidServiceNotAvailableException("External service not available: TrackablePackingAidServiceExt");
		}

	}

	private RMDTrackablePackingAidRequestDTO createRequestDTO(String shipperCountryCode, String consigneeCountryCode, Division division, LocalDate date,
			List<String> packingAidTypes) {
		RMDTrackablePackingAidRequestDTO requestDTO = new RMDTrackablePackingAidRequestDTO();
		requestDTO.setBusinessDomain(businessDomainProvider.getBusinessDomain());
		requestDTO.setNationConsignor(shipperCountryCode);
		requestDTO.setNationConsignee(consigneeCountryCode);
		requestDTO.setDivision(division.name());
		requestDTO.setDate(dateAsInteger(date));
		requestDTO.setPackingAidTypes(packingAidTypes);
		return requestDTO;
	}

	private Integer dateAsInteger(LocalDate date) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
		String dateString = date.format(formatter);

		return Integer.parseInt(dateString);
	}
}
