package com.dachser.dfe.book.order.common.dangerousgood;

import jakarta.persistence.CascadeType;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@Entity
@Table(name = "order_lq_dangerous_good")
@EqualsAndHashCode(callSuper = true)
@DiscriminatorValue("1")
public class LQDangerousGood extends DangerousGood {

	private String nos;

	@DecimalMin("0.0")
	private BigDecimal grossMass;

	@OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
	@JoinColumn(name = "dangerous_good_data_item_id")
	private DangerousGoodDataItem dangerousGoodDataItem;

	@Override
	public DangerousGoodType getDangerousGoodType() {
		return DangerousGoodType.LQ_DANGEROUS_GOOD;
	}
}
