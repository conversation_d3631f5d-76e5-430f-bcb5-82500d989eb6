package com.dachser.dfe.book.order.road;

import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.common.dangerousgood.ADRDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.LQDangerousGood;
import com.dachser.dfe.book.order.common.orderline.OrderLine;
import com.dachser.dfe.book.order.validation.CompleteOrderValidation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.lang.NonNull;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "order_line_road")
public class RoadOrderLine extends OrderLine {

	@DecimalMin(value = "0.0")
	@DecimalMax(value = "99.9")
	private BigDecimal loadingMeter;

	@Size(max = 20)
	@NotNull(groups = CompleteOrderValidation.class, message = "{label.text.invalid_input}")
	private String content;

	@Min(1)
	@Max(99999)
	private Integer weight;

	@Size(max = 2)
	private String goodsGroup;

	@Min(value = 1, groups = CompleteOrderValidation.class, message = "{label.text.invalid_input}")
	@Max(value = 99999, groups = CompleteOrderValidation.class, message = "{label.text.invalid_input}")
	private Integer goodsGroupQuantity;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "order_id", nullable = false)
	@ToString.Exclude
	@EqualsAndHashCode.Exclude
	private Order order;

	@JoinColumn(name = "packing_position_id")
	private Long packingPositionId;

	@OneToMany(mappedBy = "roadOrderLine", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@ToString.Exclude
	@Valid
	private List<DangerousGood> dangerousGoods = new ArrayList<>();

	@Override
	public void setWeight(BigDecimal weight) {
		this.weight = weight != null ? weight.intValue() : null;
	}

	@Override
	public BigDecimal getWeight() {
		return weight != null ? BigDecimal.valueOf(weight) : null;
	}

	@NonNull
	public Integer getWeightForCount() {
		return getWeight() != null ? weight : 0;
	}

	public List<ADRDangerousGood> getAdrDangerousGoods() {
		return dangerousGoods.stream().filter(Objects::nonNull).filter(ADRDangerousGood.class::isInstance).map(ADRDangerousGood.class::cast).toList();
	}

	public List<LQDangerousGood> getLqDangerousGoods() {
		return dangerousGoods.stream().filter(Objects::nonNull).filter(LQDangerousGood.class::isInstance).map(LQDangerousGood.class::cast).toList();
	}

	public boolean isNotAssignedToPackingPosition() {
		return packingPositionId == null;
	}

	public boolean hasDangerousGoods() {
		return dangerousGoods != null && !dangerousGoods.isEmpty();
	}
}
