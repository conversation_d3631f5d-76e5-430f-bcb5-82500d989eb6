package com.dachser.dfe.book.model;

import java.util.List;

public class TestMockData {

	public static final Integer BUSINESS_DOMAIN = 1;

	public static final String CLASSICLINE = "N";

	public static final String TARGOFLEX = "Y";

	public static final String TARGOFIX = "V";

	public static final String TARGO_ONSITE = "A";

	public static final String TARGO_ONSITE_FIX = "U";

	public static final List<String> AVAILABLE_DIVISION_KEYS = List.of("T", "F");

	public static final String UIT_REFERENCE_VALUE = "1A2B3C4D5E6F7G89";

	public static final String EKAER_REFERENCE_VALUE = "E0ABCDEFG123456";

	public enum Country {
		DE("Germany"), GB("United Kingdom (Great Britain)"), FR("France"), IT("Italy"), IE("Ireland"), NO("Norway"), BE("Belgium"), NL("Netherlands"), ES("Spain"), HU("Hungary");

		final String label;

		Country(String label) {
			this.label = label;
		}

		public String getLabel() {
			return label;
		}
	}

	public enum Customer {
		//@formatter:off
		DACHSER("<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>r. 2","87439","<PERSON>ten", "00000001","DE","1234567890123", true, true),
		RAY_SONO("Ray Sono","Tumblinger Straße 32,", "80337", "München",  "00000002","DE", false),
		MUSTER<PERSON>RMA1("Musterfirma A", "Willy-Brandt-Straße 1", "10557", "Berlin", "00000003","AT", true),
		MUSTERFIRMA2("Musterfirma B","Landsberger Straße 28", "81245", "München","00000004", "HU", false),
		APP_MATICS("Appmatics","Wilhelm-Mauser-Straße 14-16","50827", "Köln", "00000005","DE", true),
		MUSTERFIRMA3("Musterfirma C","Teststraße 1", "2123","Wien", "00000006","DE", true),
		DOCUMENTTEST("Musterfirma C","Teststraße 1", "2123","Wien", "00012345","AT", true);
		//@formatter:on
		private final String name;

		private final String customerNumber;

		private final String city;

		private final String postcode;

		private final String street;

		private final String country;

		private final String gln;

		private final boolean orderOptionsEnabled;

		private final boolean hasCashOnDelivery;

		Customer(String name, String street, String postcode, String city, String customerNumber, String country, boolean orderOptionsEnabled) {
			this(name, street, postcode, city, customerNumber, country, "", orderOptionsEnabled);
		}

		Customer(String name, String street, String postcode, String city, String customerNumber, String country, String gln, boolean orderOptionsEnabled) {
			this(name, street, postcode, city, customerNumber, country, gln, orderOptionsEnabled, false);
		}

		Customer(String name, String street, String postcode, String city, String customerNumber, String country, String gln, boolean orderOptionsEnabled,
				boolean hasCashOnDelivery) {
			this.name = name;
			this.customerNumber = customerNumber;
			this.postcode = postcode;
			this.street = street;
			this.city = city;
			this.country = country;
			this.gln = gln;
			this.orderOptionsEnabled = orderOptionsEnabled;
			this.hasCashOnDelivery = hasCashOnDelivery;
		}

		public String getLabel() {
			return name;
		}

		public String getCustomerNumber() {
			return customerNumber;
		}

		public String getCity() {
			return city;
		}

		public String getPostcode() {
			return postcode;
		}

		public String getStreet() {
			return street;
		}

		public String getName() {
			return name;
		}

		public String getCountry() {
			return country;
		}

		public String getGln() {
			return gln;
		}

		public boolean isOrderOptionsEnabled() {
			return orderOptionsEnabled;
		}

		public boolean hasCashOnDelivery() {
			return hasCashOnDelivery;
		}
	}

	public enum CustomerAsl {
		//@formatter:off
		DACHSER("Dachser ASL", "Thomas-Dachser-Str. 2","87439","Kempten", "00000100","DE","1234567890123", true, false),
		RAY_SONO("Ray Sono ASL","Tumblinger Straße 32,", "80337", "München",  "00000200","DE", false),
		AIR_PRODUCTS("Air Products ASL","Tumblinger Straße 32,", "80337", "München",  "00000300", "DE", "0", false, true);
		//@formatter:on
		private final String name;

		private final String customerNumber;

		private final String city;

		private final String postcode;

		private final String street;

		private final String country;

		private final String gln;

		private final boolean orderOptionsEnabled;

		private final boolean airProducts;

		CustomerAsl(String name, String street, String postcode, String city, String customerNumber, String country, boolean orderOptionsEnabled) {
			this(name, street, postcode, city, customerNumber, country, "", orderOptionsEnabled, true);
		}

		CustomerAsl(String name, String street, String postcode, String city, String customerNumber, String country, String gln, boolean orderOptionsEnabled, boolean airProducts) {
			this.name = name;
			this.customerNumber = customerNumber;
			this.postcode = postcode;
			this.street = street;
			this.city = city;
			this.country = country;
			this.gln = gln;
			this.orderOptionsEnabled = orderOptionsEnabled;
			this.airProducts = airProducts;
		}

		public String getLabel() {
			return name;
		}

		public String getCustomerNumber() {
			return customerNumber;
		}

		public String getCity() {
			return city;
		}

		public String getPostcode() {
			return postcode;
		}

		public String getStreet() {
			return street;
		}

		public String getName() {
			return name;
		}

		public String getCountry() {
			return country;
		}

		public String getGln() {
			return gln;
		}

		public boolean isOrderOptionsEnabled() {
			return orderOptionsEnabled;
		}

		public boolean isAirProducts() {
			return airProducts;
		}
	}
}
