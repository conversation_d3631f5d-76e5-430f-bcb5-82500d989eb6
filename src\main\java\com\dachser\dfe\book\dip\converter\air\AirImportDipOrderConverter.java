package com.dachser.dfe.book.dip.converter.air;

import com.dachser.dfe.book.dip.converter.DipOrderConverter;
import com.dachser.dfe.book.dip.xml.XmlConverter;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import org.springframework.stereotype.Component;

@Component
public class AirImportDipOrderConverter extends DipOrderConverter<AirImportOrder> {

	private final AirOrderMapper mapper;

	public AirImportDipOrderConverter(XmlConverter xmlConverter, AirOrderMapper mapper) {
		super(xmlConverter);
		this.mapper = mapper;
	}

	@Override
	protected ForwardingOrder map(AirImportOrder order) {
		return mapper.map(order);
	}

}
