package com.dachser.dfe.book.dip.converter.air;

import com.dachser.dfe.book.model.jaxb.order.asl.Address;
import com.dachser.dfe.book.model.jaxb.order.asl.AddressIdentification;
import com.dachser.dfe.book.model.jaxb.order.asl.Contact;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder.AirFreightShipment;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder.AirFreightShipment.ShipmentAddress;
import com.dachser.dfe.book.model.jaxb.order.asl.Location;
import com.dachser.dfe.book.model.jaxb.order.asl.ShipmentReference;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirImportOrder;
import com.dachser.dfe.book.order.air.AirOrder;
import com.dachser.dfe.book.order.air.AirOrderReference;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderBaseAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.dip.converter.shared.AirSeaAddressMappingConfig;
import com.dachser.dfe.book.dip.converter.shared.AirSeaFreightMapper;
import com.dachser.dfe.book.dip.converter.shared.AirSeaGoodsDescriptionMapper;
import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderContactCommunicationsMapper;
import com.dachser.dfe.book.dip.converter.shared.DateMapper;
import com.dachser.dfe.book.dip.converter.shared.StringNotEmptyConditionMapper;
import com.dachser.dfe.book.dip.converter.shared.TransportMovement;
import com.dachser.dfe.book.dip.converter.shared.Types;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

import java.util.ArrayList;
import java.util.List;

@Mapper(config = AirSeaAddressMappingConfig.class, componentModel = "spring", uses = { DateMapper.class, AirPickupOrderMapper.class, AirSeaOrderContactCommunicationsMapper.class,
		AirDeliveryOrderMapper.class, AirOrderReferenceMapper.class, AirSeaGoodsDescriptionMapper.class, StringNotEmptyConditionMapper.class })
public interface AirFreightMapper extends AirSeaFreightMapper {

	@Mapping(target = "shipmentAddress", source = ".", qualifiedByName = "customShipmentAddressMapping")
	@Mapping(target = "messageDate", source = "sendAt", qualifiedByName = "mapInstantWithoutTimeZone")
	@Mapping(target = "orderDate", source = "sendAt", qualifiedByName = "mapInstantWithoutTimeZone")
	@Mapping(target = "typeOfCarriage", constant = "PreAndMain")
	@Mapping(target = "orderCategory", constant = "LCD")
	@Mapping(target = "transportMovement", source = "order", qualifiedByName = "mapTransportMovement")
	@Mapping(target = "location", source = ".")
	@Mapping(target = "deliveryOrder", source = ".")
	@Mapping(target = "pickupOrder", source = ".")
	@Mapping(target = "termOfDelivery.value", source = "incoTerm")
	@Mapping(target = "shipmentReference", source = "orderReferences")
	@Mapping(target = "function", constant = Types.Function.ORIGINAL)
	@Mapping(target = "kindOfShipment", constant = Types.KindOfShipment.FINAL)
	@Mapping(target = "customerShipmentReference", source = "shipperReference")
	@Mapping(target = "productCode", source = "productCode")
	AirFreightShipment map(AirOrder order);

	default List<AirFreightShipment> mapList(AirExportOrder order) {
		return List.of(map(order));
	}

	default List<AirFreightShipment> mapList(AirImportOrder order) {
		return List.of(map(order));
	}

	default <T extends AirOrder> List<Location> mapLocations(T order) {
		List<Location> locations = new ArrayList<>();
		if (order.getFromIATA() != null) {
			Location location = new Location();
			location.setValue(order.getFromIATA());
			location.setTypeOfLocation(Types.LocationTypes.DEPARTURE_AIRPORT);
			locations.add(location);
		}

		if (order.getToIATA() != null) {
			Location location = new Location();
			location.setValue(order.getToIATA());
			location.setTypeOfLocation(Types.LocationTypes.DESTINATION_AIRPORT);
			locations.add(location);
		}
		return locations;
	}

	default List<ShipmentReference> mapReferences(List<AirOrderReference> furtherReferences) {
		return furtherReferences.stream().filter(ref -> !ref.getReferenceType().isOrderPositionReference()).map(this::mapReference).toList();
	}

	@Mapping(target = "typeOfReference", source = "referenceType.ediReferenceType")
	@Mapping(target = "value", source = "referenceValue")
	@Mapping(target = "flagLoadUnload", source = ".")
	ShipmentReference mapReference(AirOrderReference reference);

	default String mapFlagLoadUnload(AirOrderReference reference) {
		if (reference.isLoading() && reference.isUnloading()) {
			return Types.ShipmentReferenceFlagLoadUnload.LOAD_AND_UNLOAD;
		}
		if (reference.isLoading()) {
			return Types.ShipmentReferenceFlagLoadUnload.LOAD;
		}
		if (reference.isUnloading()) {
			return Types.ShipmentReferenceFlagLoadUnload.UNLOAD;
		}
		return null;
	}

	@Named("customShipmentAddressMapping")
	default <T extends AirOrder> List<ForwardingOrder.AirFreightShipment.ShipmentAddress> customMapShipmentAddresses(T order) {
		final List<ShipmentAddress> shipmentAddresses = new ArrayList<>();
		// Add Orderer Address ID - we are not saving the address in the database, so we need to rely on customer number
		shipmentAddresses.add(buildAddressIdentification(order.getCustomerNumber(), Types.AddressTypes.ORDERER));
		shipmentAddresses.add(buildAirShipmentAddressForConsignor(mapBaseAddress(order.getShipperAddress()), mapOrderContact(order.getShipperAddress().getOrderContact()),
				order.getShipperAddress().getCustomerNumber()));
		shipmentAddresses.add(buildAddressIdentification(String.format("%03d", order.getBranchId()), Types.AddressTypes.FORWARDER));

		shipmentAddresses.add(buildShipmentAddressFromOrderAddress(order.getConsigneeAddress(), Types.AddressTypes.IMPORTER));
		if (order.getDeliveryAddress() != null) {
			shipmentAddresses.add(buildShipmentAddressFromOrderAddress(order.getDeliveryAddress(), Types.AddressTypes.ULTIMATE_CONSIGNEE));
		}
		if (order.isCollectFromAirport()) {
			shipmentAddresses.add(buildShipmentAddressFromOrderAddress(order.getConsigneeAddress(), Types.AddressTypes.CONSIGNEE));
		}
		// Note that pickup address is not mapped here, because it is mapped in the pickup order mapper

		// Add all further addresses available - Optional
		if (order.getAddresses() != null) {
			shipmentAddresses.addAll(mapFurtherAddresses(order.getAddresses()));
		}

		return shipmentAddresses;
	}

	// Workaround - We don't have any information except the branchID for the forwarder, so we use the branchId as partnerID
	default ShipmentAddress buildAddressIdentification(String id, String addressType) {
		ShipmentAddress shipmentAddress = new ShipmentAddress();
		shipmentAddress.setTypeOfAddress(addressType);
		AddressIdentification addressIdentification = new AddressIdentification();
		addressIdentification.setAddressID(id);
		shipmentAddress.setAddressIdentification(addressIdentification);
		return shipmentAddress;
	}

	default ShipmentAddress buildShipmentAddressFromOrderAddress(OrderAddress orderAddress, String addressType) {
		ShipmentAddress shipmentAddress = new ShipmentAddress();
		if (StringUtils.isNotEmpty(orderAddress.getTaxID())) {
			shipmentAddress.setGovernmentReference(orderAddress.getTaxID());
		}
		shipmentAddress.setAddress(mapBaseAddress(orderAddress));
		shipmentAddress.setContact(mapOrderContact(orderAddress.getOrderContact()));
		shipmentAddress.setTypeOfAddress(addressType);
		if (orderAddress.getCustomerNumber() != null) {
			final AddressIdentification addressIdentification = new AddressIdentification();
			addressIdentification.setAddressID(orderAddress.getCustomerNumber());
			shipmentAddress.setAddressIdentification(addressIdentification);
		}
		return shipmentAddress;
	}

	List<ShipmentAddress> mapFurtherAddresses(List<OrderFurtherAddress> addresses);

	@AfterMapping
	default void removeUnmappedAddressTypes(@MappingTarget List<ShipmentAddress> shipmentAddresses) {
		shipmentAddresses.removeIf(shipmentAddress -> shipmentAddress.getTypeOfAddress() == null);
	}

	@InheritConfiguration(name = "mapAddress")
	Address mapBaseAddress(OrderBaseAddress address);

	@InheritConfiguration(name = "mapAddress")
	Address mapFurtherAddress(OrderFurtherAddress address);

	@Mapping(target = "address", source = ".")
	@Mapping(target = "typeOfAddress", source = "addressType", qualifiedByName = "mapTypeOfAddress")
	ShipmentAddress mapFurtherAddressToShipmentAddress(OrderFurtherAddress address);


	@InheritConfiguration(name = "mapContact")
	@Mapping(target = "typeOfContact", constant = Types.ContactTypes.INFORMATION)
	Contact mapOrderContact(OrderContact orderContact);

	@Named("mapTransportMovement")
	default String mapTransportMovement(AirOrder order) {
		if (order instanceof AirImportOrder) {
			return TransportMovement.IMPORT.getDirection();
		}
		return TransportMovement.EXPORT.getDirection();
	}
}
