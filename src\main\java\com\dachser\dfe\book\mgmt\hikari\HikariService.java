package com.dachser.dfe.book.mgmt.hikari;

import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;

@Service
@Profile({ "apitest", "dev", "test" })
class HikariService {

	private final HikariDataSource dataSource;

	@Autowired
	HikariService(DataSource dataSource) {
		if (dataSource instanceof HikariDataSource hikariDataSource) {
			this.dataSource = hikariDataSource;
		} else {
			throw new IllegalArgumentException("DataSource provided is not of instance HikariDataSource");
		}
	}

	void setMinimumIdle(int minimumIdle) {
		dataSource.setMinimumIdle(minimumIdle);
	}

	int getMinimumIdle() {
		return dataSource.getMinimumIdle();
	}

	void setLeakDetectionThreshold(long threshold) {
		dataSource.setLeakDetectionThreshold(threshold);
	}

	long getLeakDetectionThreshold() {
		return dataSource.getLeakDetectionThreshold();
	}

	void setConnectionTimeout(long timeoutMs) {
		dataSource.setConnectionTimeout(timeoutMs);
	}

	long getConnectionTimeout() {
		return dataSource.getConnectionTimeout();
	}

	HikariPoolMetrics getPoolMetrics() {
		HikariPoolMXBean pool = dataSource.getHikariPoolMXBean();
		return HikariPoolMetrics.builder().totalConnections(pool.getTotalConnections()).idleConnections(pool.getIdleConnections()).activeConnections(pool.getActiveConnections()).threadsAwaitingConnection(pool.getThreadsAwaitingConnection()).build();
	}

}
