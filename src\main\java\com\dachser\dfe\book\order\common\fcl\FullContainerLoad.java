package com.dachser.dfe.book.order.common.fcl;

import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.sea.OrderLineCombiner;
import com.dachser.dfe.book.order.sea.SeaOrderLine;
import com.dachser.dfe.book.order.sea.SeaOrderLineHsCode;
import com.dachser.dfe.book.order.validation.sea.CompleteOrderValidationSea;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
@ToString
@Entity
@Table(name = "full_container_load")
public class FullContainerLoad extends BaseFullContainerLoad implements OrderLineCombiner<SeaOrderLine, SeaOrderLineHsCode> {

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "order_id", nullable = false)
	@ToString.Exclude
	@EqualsAndHashCode.Exclude
	private Order order;

	@OneToMany(mappedBy = "fullContainerLoadId", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@ToString.Exclude
	@Size(min = 1, groups = CompleteOrderValidationSea.class, message = "{label.invalid_input}")
	@OrderBy("number")
	@Valid
	private List<SeaOrderLine> orderLines;

	@Transient
	public Integer getTotalPieces() {
		if (orderLines == null) {
			return null;
		}
		return orderLines.stream().filter(orderLine -> orderLine.getQuantity() != null).mapToInt(SeaOrderLine::getQuantity).sum();
	}

	@Transient
	public Double getTotalWeight() {
		if (orderLines == null) {
			return null;
		}
		return orderLines.stream().map(SeaOrderLine::getWeight).filter(Objects::nonNull).mapToDouble(BigDecimal::doubleValue).sum();
	}

	@Transient
	public String getCombinedOrderLineGoods() {
		return combineGoods(getOrderLines(), SeaOrderLine::getHsCodes);
	}

}
