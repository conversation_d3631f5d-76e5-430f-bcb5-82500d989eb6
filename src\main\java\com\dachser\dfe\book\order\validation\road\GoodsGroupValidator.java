package com.dachser.dfe.book.order.validation.road;

import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.order.common.orderline.OrderLine;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.validation.Messages;
import com.dachser.dfe.book.order.validation.PayloadProvidingValidator;
import com.dachser.dfe.book.service.goodsgroup.GoodsGroupService;
import com.dachser.dfe.book.user.UserContextService;
import com.dachser.dfe.book.user.model.RoadCustomer;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Slf4j
public class GoodsGroupValidator implements ConstraintValidator<GoodsGroupValid, ForwardingOrder>, PayloadProvidingValidator {

	private final GoodsGroupService goodsGroupService;

	private final UserContextService userContextService;

	private final Translator translator;

	@Override
	public boolean isValid(ForwardingOrder roadOrder, ConstraintValidatorContext context) {
		final List<Integer> invalidOrderLines = new ArrayList<>();
		String customerNumber = roadOrder.getCustomerNumber();
		final Optional<RoadCustomer> roadCustomerOpt = userContextService.getCustomerInformationRoad(customerNumber);

		if (roadCustomerOpt.isPresent()) {
			context.disableDefaultConstraintViolation();
			final RoadCustomer roadCustomer = roadCustomerOpt.get();
			final Integer businessDomain = userContextService.getBusinessDomain();

			final boolean goodsGroupsAvailable = goodsGroupService.hasCustomerGoodsGroups(customerNumber, roadCustomer.getBranchId(), businessDomain);

			invalidOrderLines.addAll(goodsGroupService.findInvalidGoodsGroupInOrderLines(roadOrder.getOrderLines(), !goodsGroupsAvailable));

			final boolean validationResult = invalidOrderLines.isEmpty();
			log.debug("Goods groups available for customer {}: {}, all goods groups valid: {}", roadOrder.getCustomerNumber(), goodsGroupsAvailable, validationResult);
			addConstraintViolationIfInvalid(context, invalidOrderLines);
			return validationResult;
		} else {
			log.warn("RoadCustomer not found for customer number: {} - GoodsGroupValidation failed", customerNumber);
			return false;
		}
	}

	private void addConstraintViolationIfInvalid(ConstraintValidatorContext context, List<Integer> invalidOrderLines) {
		if (!invalidOrderLines.isEmpty()) {
			ConstraintValidatorContext.ConstraintViolationBuilder violationWithTemplate = context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.INVALID_INPUT));

			invalidOrderLines.forEach(orderLineIndex -> violationWithTemplate.addPropertyNode(String.format("orderLines[%s]", orderLineIndex.toString()))
					.addContainerElementNode("goodsGroup", OrderLine.class, 0).addConstraintViolation());
		}
	}
}
