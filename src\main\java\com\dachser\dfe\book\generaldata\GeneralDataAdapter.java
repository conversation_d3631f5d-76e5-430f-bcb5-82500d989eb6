package com.dachser.dfe.book.generaldata;

import com.dachser.dfe.book.country.Country;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.AirProductDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.generaldata.model.GDContainerTypeDto;
import com.dachser.dfe.generaldata.model.GDTermDto;
import com.dachser.dfe.generaldata.model.GDTermTypeDto;
import jakarta.annotation.Nullable;

import java.util.List;
import java.util.Map;

interface GeneralDataAdapter {

	Map<String, String> getLanguages();

	List<Country> getCountries(String language);

	List<GDTermDto> getAllActiveTerms(@Nullable GDTermTypeDto termType);

	List<GDContainerTypeDto> getContainerTypes();

	List<OptionDto> getPackagingOptionsAir(String language);

	List<OptionDto> getPackagingOptionsRoad(String language);

	List<DeliveryProductDto> getDeliveryProductsForDivision(Division division, String language);

	List<AirProductDto> getAirProducts(Boolean includeDeactivated);
}
