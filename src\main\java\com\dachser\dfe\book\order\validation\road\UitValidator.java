package com.dachser.dfe.book.order.validation.road;

import com.dachser.dfe.book.country.CountryCode;
import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.model.ValidationResultEntryDto;
import com.dachser.dfe.book.order.OrderCountryHelper;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.RoadOrderReferenceSubtype;
import com.dachser.dfe.book.order.validation.Messages;
import com.dachser.dfe.book.order.validation.PayloadProvidingValidator;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class UitValidator implements ConstraintValidator<UitValid, RoadOrder>, PayloadProvidingValidator {

	private final Translator translator;

	private final OrderCountryHelper orderCountryHelper;

	@Override
	public boolean isValid(RoadOrder roadOrder, ConstraintValidatorContext context) {
		context.disableDefaultConstraintViolation();
		if (hasUitCodes(roadOrder) && !orderCountryHelper.hasShippingForCountry(roadOrder, CountryCode.ROMANIA.getIsoCode())) {
			// @formatter:off
			createBuilderWithErrorType(context, translator.toLocale(Messages.NOT_ALLOWED_UIT), ValidationResultEntryDto.ErrorTypeEnum.CONSTRAINT_VIOLATION)
			.addPropertyNode("orderReferences")
			.addConstraintViolation();
			// @formatter:on
			return false;
		}
		return true;
	}

	private boolean hasUitCodes(RoadOrder roadOrder) {
		// @formatter:off
		return roadOrder != null
			&& roadOrder.getOrderReferences() != null
			&& roadOrder.getOrderReferences().stream()
				.filter(reference -> ReferenceType.IDENTIFICATION_CODE_TRANSPORT == reference.getReferenceType())
				.anyMatch(reference -> RoadOrderReferenceSubtype.UIT == reference.getReferenceSubtype());
		// @formatter:on
	}

}