package com.dachser.dfe.book.advice;

import com.dachser.dfe.book.country.CountryCode;
import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.OrderContact;
import com.dachser.dfe.book.order.common.OrderFurtherAddress;
import com.dachser.dfe.book.order.common.OrderText;
import com.dachser.dfe.book.order.common.dangerousgood.ADRDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.DangerousGoodDataItem;
import com.dachser.dfe.book.order.common.dangerousgood.EQDangerousGood;
import com.dachser.dfe.book.order.common.dangerousgood.LQDangerousGood;
import com.dachser.dfe.book.order.common.packingposition.PackingPosition;
import com.dachser.dfe.book.order.road.ForwardingOrder;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.legacy.advice.bean.Address;
import com.dachser.dfe.legacy.advice.bean.AdvisedOrder;
import com.dachser.dfe.legacy.advice.bean.Contact;
import com.dachser.dfe.legacy.advice.bean.GoodsGroup;
import com.dachser.dfe.legacy.advice.bean.MonetaryAmount;
import com.dachser.dfe.legacy.advice.bean.OrderLine;
import com.dachser.dfe.legacy.advice.bean.Reference;
import com.dachser.dfe.legacy.advice.bean.Text;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValueMappingStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.SubclassMapping;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
public abstract class AdvisedOrderMapper {

	@Value("${ums.dip.ediTestFlagRoad}")
	boolean ediTestFlagRoad;

	static final Integer DACHSER_TEST_BRANCH = 918;

	private static final Integer DIVISION_SCALE_CM_TO_M = 100;

	@Mapping(target = "consignmentNumber", source = "shipmentNumber")
	@Mapping(target = "consignmentReference", source = "orderNumber")
	@Mapping(target = "principalNumber", source = "customerNumber")
	@Mapping(target = "consolidatorNumber", expression = "java( java.lang.Boolean.FALSE == ediTestFlagRoad ? order.getBranchId() : DACHSER_TEST_BRANCH)")
	@Mapping(target = "consignorNumber", source = "customerNumber")
	@Mapping(target = "consignee", source = "consigneeAddress")
	@Mapping(target = "divisionCode", source = "division")
	@Mapping(target = "productCode", source = "product")
	@Mapping(target = "termsCode", source = "freightTerm")
	@Mapping(target = "date", expression = "java( java.time.LocalDate.now() )")
	@Mapping(target = "collectionDateTimeFrom", source = "collectionFrom", qualifiedByName = "mapDateTime")
	@Mapping(target = "collectionDateTimeTo", source = "collectionTo", qualifiedByName = "mapDateTime")
	@Mapping(target = "weight", expression = "java( order.getTotalOrderWeight() )")
	@Mapping(target = "ssccCount", expression = "java( order.getSsccs() == null ? 0 : order.getSsccs().size() )")
	@Mapping(target = "statusCode", ignore = true)
	@Mapping(target = "consigneeCollection", ignore = true)
	@Mapping(target = "insuranceGoodsValue", source = ".")
	@Mapping(target = "storageSpaces", ignore = true)
	@Mapping(target = "orderLines", source = "orderLines", ignore = true)
	@Mapping(target = "packingPositions", source = "packingPositions", ignore = true)
	@Mapping(target = "furtherAddresses", source = "addresses")
	@Mapping(target = "references", source = "orderReferences")
	@Mapping(target = "texts", source = "orderTexts")
	@Mapping(target = "deliveryNotification", source = ".")
	@Mapping(target = "tailLiftDelivery", expression = "java( order.getTailLiftDelivery() != null && order.getTailLiftDelivery())")
	abstract AdvisedOrder map(ForwardingOrder order);

	@Mapping(target = "value", source = "goodsValue")
	@Mapping(target = "currencyCode", source = "currency")
	abstract MonetaryAmount mapMonetaryAmount(ForwardingOrder order);

	@Mapping(target = "code", source = "referenceType.coreSystemsValue")
	@Mapping(target = "value", source = "reference")
	abstract Reference mapReference(RoadOrderReference roadOrderReference);

	@Mapping(target = "globalLocationNumber", source = "gln")
	@Mapping(target = "name1", source = "name")
	@Mapping(target = "supplement", source = "supplement", qualifiedByName = "validateAndMapSupplement")
	@Mapping(target = "countryCode", source = "countryCode", qualifiedByName = "mapDachserLanguage")
	abstract Address mapFurtherAddress(OrderFurtherAddress furtherAddress);

	@Mapping(target = "globalLocationNumber", source = "gln")
	@Mapping(target = "name1", source = "name")
	@Mapping(target = "supplement", source = "supplement", qualifiedByName = "validateAndMapSupplement")
	@Mapping(target = "countryCode", source = "countryCode", qualifiedByName = "mapDachserLanguage")
	abstract Address mapOrderAddress(OrderAddress orderAddress);

	@Mapping(target = "goodsGroup", source = ".")
	@Mapping(target = "packaging", source = "packagingType")
	@Mapping(target = "quantityOfPackages", source = "quantity")
	@Mapping(target = "length", source = "length", qualifiedByName = "mapDimensions")
	@Mapping(target = "width", source = "width", qualifiedByName = "mapDimensions")
	@Mapping(target = "height", source = "height", qualifiedByName = "mapDimensions")
	@Mapping(target = "volume", expression = "java( orderLine.getVolume() != null ? orderLine.getVolume().setScale(3, java.math.RoundingMode.HALF_UP) : null )")
	@Mapping(target = "weight", expression = "java(orderLine.getWeight() != null ? orderLine.getWeight().intValue() : 0)")
	@Mapping(target = "loadingMeter", source = "loadingMeter")
	@Mapping(target = "content", source = "content")
	@Mapping(target = "packingPositionId", expression = "java( orderLine.getPackingPositionId() != null ? orderLine.getPackingPositionId().intValue() : null )")
	abstract com.dachser.dfe.legacy.advice.bean.OrderLine mapOrderLine(RoadOrderLine orderLine);

	@SubclassMapping(target = com.dachser.dfe.legacy.advice.bean.DangerousGood.class, source = EQDangerousGood.class)
	@SubclassMapping(target = com.dachser.dfe.legacy.advice.bean.DangerousGood.class, source = LQDangerousGood.class)
	@SubclassMapping(target = com.dachser.dfe.legacy.advice.bean.DangerousGood.class, source = ADRDangerousGood.class)
	abstract com.dachser.dfe.legacy.advice.bean.DangerousGood mapDangerousGood(DangerousGood dangerousGood);

	@Mapping(target = "type", expression = "java(com.dachser.dfe.legacy.advice.bean.DangerousGoodType.EQ)")
	@Mapping(target = "id", source = "sortingPosition")
	@Mapping(target = "quantityOfPackages", source = "noOfPackages")
	@Mapping(target = "packaging", source = "packagingKey")
	abstract com.dachser.dfe.legacy.advice.bean.DangerousGood mapDangerousGoodAsEQ(EQDangerousGood dangerousGood);

	@Mapping(target = "type", expression = "java(com.dachser.dfe.legacy.advice.bean.DangerousGoodType.LQ)")
	@Mapping(target = "id", source = "sortingPosition")
	@Mapping(target = "weight", source = "grossMass")
	@Mapping(target = "customerId", source = "dangerousGoodDataItem.externalDgmId")
	@Mapping(target = "description", source = "dangerousGoodDataItem.description")
	@Mapping(target = "unNumber", source = "dangerousGoodDataItem.unNumber")
	@Mapping(target = "packingGroup", source = "dangerousGoodDataItem.packingGroup")
	@Mapping(target = "classificationCode", source = "dangerousGoodDataItem.classificationCode")
	@Mapping(target = "mainDanger", source = "dangerousGoodDataItem.mainDanger")
	@Mapping(target = "subsidiaryHazards", source = "dangerousGoodDataItem", qualifiedByName = "mapSubsidiaryHazards")
	abstract com.dachser.dfe.legacy.advice.bean.DangerousGood mapDangerousGoodAsLQ(LQDangerousGood dangerousGood);

	@Mapping(target = "type", expression = "java(com.dachser.dfe.legacy.advice.bean.DangerousGoodType.ADR)")
	@Mapping(target = "id", source = "sortingPosition")
	@Mapping(target = "weight", source = "grossMass")
	@Mapping(target = "quantityOfPackages", source = "noOfPackages")
	@Mapping(target = "packaging", source = "packagingKey")
	@Mapping(target = "customerId", source = "dangerousGoodDataItem.externalDgmId")
	@Mapping(target = "description", source = "dangerousGoodDataItem.description")
	@Mapping(target = "unNumber", source = "dangerousGoodDataItem.unNumber")
	@Mapping(target = "packingGroup", source = "dangerousGoodDataItem.packingGroup")
	@Mapping(target = "classificationCode", source = "dangerousGoodDataItem.classificationCode")
	@Mapping(target = "mainDanger", source = "dangerousGoodDataItem.mainDanger")
	@Mapping(target = "subsidiaryHazards", source = "dangerousGoodDataItem", qualifiedByName = "mapSubsidiaryHazards")
	abstract com.dachser.dfe.legacy.advice.bean.DangerousGood mapDangerousGoodAsADR(ADRDangerousGood dangerousGood);

	@Named("mapSubsidiaryHazards")
	String[] mapSubsidiaryHazards(DangerousGoodDataItem dangerousGoodDataItem) {
		if (dangerousGoodDataItem == null) {
			return null;
		}
		return Stream.of(dangerousGoodDataItem.getSubsidiaryHazardOne(), dangerousGoodDataItem.getSubsidiaryHazardTwo(), dangerousGoodDataItem.getSubsidiaryHazardThree()).filter(Objects::nonNull).toList().toArray(new String[] {});
	}

	@Mapping(target = "id", source = "id")
	@Mapping(target = "quantity", source = "quantity")
	@Mapping(target = "packaging", source = "packagingType")
	abstract com.dachser.dfe.legacy.advice.bean.PackingPosition mapPackingPosition(PackingPosition packingPosition);

	@Mapping(target = "code", source = "textType")
	@Mapping(target = "value", source = "text")
	abstract Text mapOrderText(OrderText orderText);

	@AfterMapping
	void mapRegularOrderLines(ForwardingOrder order, @MappingTarget AdvisedOrder advisedOrder) {
		if (order.getOrderLines() != null && !order.getOrderLines().isEmpty()) {
			order.getOrderLines().stream().filter(orderLine -> orderLine.getPackingPositionId() == null)
					.forEach(orderLine -> advisedOrder.getOrderLines().add(mapOrderLine(orderLine)));
		}
	}

	@AfterMapping
	void mapPackingPositionsWithIndex(ForwardingOrder order, @MappingTarget AdvisedOrder advisedOrder) {
		if (order.getPackingPositions() != null && !order.getPackingPositions().isEmpty()) {
			IntStream.range(0, order.getPackingPositions().size()).forEach(index -> {
				PackingPosition packingPosition = order.getPackingPositions().get(index);
				if (packingPosition.getOrderLines() != null && !packingPosition.getOrderLines().isEmpty()) {
					com.dachser.dfe.legacy.advice.bean.PackingPosition advisedPackingPosition = mapPackingPosition(packingPosition);
					advisedPackingPosition.setId(index + 1);
					advisedOrder.getPackingPositions().add(advisedPackingPosition);
					packingPosition.getOrderLines().forEach(orderLine -> {
						OrderLine advisedOrderLine = mapOrderLine(orderLine);
						advisedOrderLine.setPackingPositionId(index + 1);
						advisedOrder.getOrderLines().add(advisedOrderLine);
					});
				}
			});
		}
	}

	GoodsGroup mapRoadGoodsGroup(RoadOrderLine orderLine) {
		if (Objects.nonNull(orderLine.getGoodsGroup()) && Objects.nonNull(orderLine.getGoodsGroupQuantity())) {
			GoodsGroup goodsGroup = new GoodsGroup();
			goodsGroup.setCode(orderLine.getGoodsGroup());
			goodsGroup.setQuantityOfPackages(orderLine.getGoodsGroupQuantity());
			return goodsGroup;
		}
		return null;
	}

	Contact mapDeliveryOption(ForwardingOrder forwardingOrder) {
		final DeliveryOptions selectedDeliveryOption = Objects.requireNonNullElse(forwardingOrder.getDeliveryOption(), DeliveryOptions.NO);
		final Optional<OrderContact> deliveryContactOpt = Optional.ofNullable(forwardingOrder.getDeliveryContact());

		if (Objects.equals(DeliveryOptions.NO, selectedDeliveryOption) || deliveryContactOpt.isEmpty()) {
			return null;
		}

		final OrderContact orderContact = deliveryContactOpt.get();
		final Contact mappedContact = new Contact();
		mappedContact.setCode(selectedDeliveryOption.getKey());
		mappedContact.setName(orderContact.getName());
		mappedContact.setMail(orderContact.getEmail());

		//Legacy DB only allows 25 characters for phone and mobile, DFE allows 30 so we need to strip left
		mappedContact.setPhone(StringUtils.left(orderContact.getTelephone(), 25));
		mappedContact.setMobilePhone(StringUtils.left(orderContact.getMobile(), 25));
		return mappedContact;
	}

	@Named("mapDateTime")
	LocalDateTime mapDateTime(OffsetDateTime offsetDateTime) {
		if (offsetDateTime == null) {
			return null;
		}
		return offsetDateTime.toLocalDateTime();
	}

	Map<String, Address> mapFurtherAddresses(List<OrderFurtherAddress> furtherAddresses) {
		HashMap<String, Address> addressMap = new HashMap<>();
		for (OrderFurtherAddress furtherAddress : furtherAddresses) {
			if (furtherAddress.getAddressType() != null && isFurtherAddressComplete(furtherAddress))
				addressMap.put(furtherAddress.getAddressType(), mapFurtherAddress(furtherAddress));
		}
		return addressMap.isEmpty() ? null : addressMap;
	}

	private boolean isFurtherAddressComplete(OrderFurtherAddress address) {
		// Checks if all required fields for address are set
		return StringUtils.isNotBlank(address.getName()) && StringUtils.isNotBlank(address.getStreet()) && StringUtils.isNotBlank(address.getCity()) && StringUtils.isNotBlank(
				address.getCountryCode());
	}

	@Named("validateAndMapSupplement")
	String mapSupplement(String supplement) {
		// AVIS DB only allows 30 characters for supplement, DFE allows 50
		return supplement != null && supplement.length() > 30 ? supplement.substring(0, 30) : supplement;
	}

	@Named("mapDimensions")
	BigDecimal mapDimensions(Integer centimeterValue) {
		return centimeterValue != null ? BigDecimal.valueOf(centimeterValue).divide(BigDecimal.valueOf(DIVISION_SCALE_CM_TO_M), 3, RoundingMode.HALF_UP) : null;
	}

	@Named("mapDachserLanguage")
	String mapIsoCodeToDachserCode(String isoCode) {
		CountryCode countryCode = CountryCode.getFromIsoCode(isoCode).orElse(null);
		return countryCode == null ? null : countryCode.getDachserCode();
	}

	@AfterMapping
	void afterMappingNullMonetaryAmount(ForwardingOrder order, @MappingTarget AdvisedOrder advisedOrder) {
		// Remove the complete sub object if value is not available
		if (order.getGoodsValue() == null && advisedOrder.getInsuranceGoodsValue() != null) {
			advisedOrder.setInsuranceGoodsValue(null);
		}
	}

	@AfterMapping
	void setAvisIds(@MappingTarget AdvisedOrder advisedOrder) {
		int textId = 1;
		for (Text text : advisedOrder.getTexts()) {
			text.setId(textId++);
		}

		int orderLineId = 1;
		for (com.dachser.dfe.legacy.advice.bean.OrderLine orderLine : advisedOrder.getOrderLines()) {
			orderLine.setId(orderLineId++);
		}
	}
}
