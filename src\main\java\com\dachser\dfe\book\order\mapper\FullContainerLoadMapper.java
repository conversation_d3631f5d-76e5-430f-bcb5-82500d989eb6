package com.dachser.dfe.book.order.mapper;

import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.mapper.PersistentListMerger;
import com.dachser.dfe.book.model.ContainerTypeDto;
import com.dachser.dfe.book.model.FullContainerLoadDto;
import com.dachser.dfe.book.order.common.fcl.FullContainerLoad;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Mapper(componentModel = "spring", uses = OrderLineMapper.class)
public abstract class FullContainerLoadMapper {

	@Autowired
	protected GeneralDataService generalDataService;

	@Mapping(source = "containerType.code", target = "containerType")
	@Mapping(source = "lines", target = "orderLines")
	public abstract FullContainerLoad mapFullContainerLoad(FullContainerLoadDto fullContainerLoadDto);

	@Mapping(source = ".", target = "containerType.description", qualifiedByName = "mapContainerType")
	@InheritInverseConfiguration(name = "mapFullContainerLoad")
	public abstract FullContainerLoadDto mapFullContainerLoadDto(FullContainerLoad packagingPosition);

	@Named("mapContainerType")
	protected String mapContainerType(FullContainerLoad fullContainerLoad) {
		ContainerTypeDto containerTypeForKey = generalDataService.getContainerTypeForKey(fullContainerLoad.getContainerType());
		if (containerTypeForKey != null && containerTypeForKey.getDescription() != null && containerTypeForKey.getSize() != null) {
			return containerTypeForKey.getSize() + " " + containerTypeForKey.getDescription();
		}

		return null;
	}

	@InheritConfiguration(name = "mapFullContainerLoad")
	protected abstract FullContainerLoad updateFullContainerLoad(FullContainerLoadDto fullContainerLoadDto, @MappingTarget FullContainerLoad fullContainerLoad);

	protected void updateFullContainerLoads(List<FullContainerLoadDto> packingPositionJsons, @MappingTarget List<FullContainerLoad> fullContainerLoads) {
		final PersistentListMerger<FullContainerLoad, FullContainerLoadDto> listMapper = this::updateFullContainerLoad;
		listMapper.updateList(packingPositionJsons, fullContainerLoads, FullContainerLoad::getId, FullContainerLoadDto::getId, FullContainerLoad::new);
	}

	public abstract List<FullContainerLoadDto> mapFullContainerLoadDtoList(List<FullContainerLoad> fullContainerLoads);

	public abstract List<FullContainerLoad> mapFullContainerLoadList(List<FullContainerLoadDto> fullContainerLoads);

}
