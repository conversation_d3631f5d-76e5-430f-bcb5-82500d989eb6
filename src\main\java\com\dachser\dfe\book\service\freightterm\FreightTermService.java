package com.dachser.dfe.book.service.freightterm;

import com.dachser.dfe.book.exception.ErrorIdExternalServiceNotAvailableException;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.model.FreightTermDto;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.service.forwardingdomain.ForwardingDomainService;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Predicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class FreightTermService {

	private final GeneralDataService generalDataService;

	private final FreightTermAdapter freightTermAdapter;

	private final ForwardingDomainService forwardingDomainService;

	public List<FreightTermDto> getAllValidFreightTermOptions() {
		return generalDataService.getFreightTerms();
	}

	@Nullable
	public FreightTermDto getFreightTermByDachserKey(String dachserKey) {
		return generalDataService.getFreightTerms().stream().filter(filterByDachserCode(dachserKey)).findFirst().orElse(null);
	}

	public boolean validateFreightTermForOrder(final RoadOrder order, final Integer businessDomain) {
		final String forwardingDomain = getForwardingDomainForOrder(order, businessDomain);
		final String freightTerm = order.getFreightTerm();
		try {
			return forwardingDomain != null && freightTerm != null && freightTermAdapter.validateFreightTerm(businessDomain, freightTerm, forwardingDomain);
		} catch (ErrorIdExternalServiceNotAvailableException e) {
			log.error("Exception when calling the MasterData API: ", e);
			return false;
		}
	}

	private String getForwardingDomainForOrder(final RoadOrder order, final Integer businessDomain) {
		final OrderAddress shipperAddress = order.getShipperAddress();
		final OrderAddress consigneeAddress = order.getConsigneeAddress();

		if (shipperAddress == null || consigneeAddress == null) {
			log.warn("Either one or both of the addresses are null for - FreightTermValidation failed");
		} else {
			try {
				return forwardingDomainService.getForwardingDomain(businessDomain, order.getBranchId(), consigneeAddress.getCountryCode(), shipperAddress.getCountryCode());
			} catch (ErrorIdExternalServiceNotAvailableException e) {
				log.error("Exception when calling the MasterData API: ", e);
			}
		}
		return null;
	}

	private Predicate<FreightTermDto> filterByDachserCode(String dachserCode) {
		return term -> term.getDachserTermKey() != null && term.getDachserTermKey().equals(dachserCode);
	}

}
