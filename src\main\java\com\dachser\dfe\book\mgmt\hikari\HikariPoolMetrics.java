package com.dachser.dfe.book.mgmt.hikari;

import com.zaxxer.hikari.HikariPoolMXBean;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter(onMethod_ = {@Override})
public class HikariPoolMetrics implements HikariPoolMXBean {

	private static final String UNSUPPORTED_OPERATION = "Metrics data are not made for triggering any backend change !";

	private int idleConnections;

	private int activeConnections;

	private int totalConnections;

	private int threadsAwaitingConnection;

	@Override
	public void softEvictConnections() {
		throw new UnsupportedOperationException(UNSUPPORTED_OPERATION);
	}

	@Override
	public void suspendPool() {
		throw new UnsupportedOperationException(UNSUPPORTED_OPERATION);
	}

	@Override
	public void resumePool() {
		throw new UnsupportedOperationException(UNSUPPORTED_OPERATION);
	}

}
