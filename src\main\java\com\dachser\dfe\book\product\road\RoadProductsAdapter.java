package com.dachser.dfe.book.product.road;

import com.dachser.dfe.book.exception.ExtDeliveryProductServiceNotAvailable;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.order.road.RoadOrder;

import java.util.List;

public interface RoadProductsAdapter {

	List<DeliveryProductDto> getOnlyValidDeliveryProducts(Division division, int businessDomain, List<String> whiteList, String shipperCountry, String shipperPostcode,
			String consigneeCountry, String consigneePostcode) throws ExtDeliveryProductServiceNotAvailable;

	boolean validateProduct(RoadOrder roadOrder, int businessDomain);

}
