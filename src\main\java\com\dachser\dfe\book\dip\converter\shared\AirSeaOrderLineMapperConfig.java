package com.dachser.dfe.book.dip.converter.shared;

import com.dachser.dfe.book.model.jaxb.order.asl.OrderPosition;
import com.dachser.dfe.book.order.common.orderline.OrderLine;
import org.mapstruct.MapperConfig;
import org.mapstruct.Mapping;

@MapperConfig
public interface AirSeaOrderLineMapperConfig {
	@Mapping(target = "pieces", source = "quantity", conditionExpression = "java(orderLine.getQuantity() > 0)")
	@Mapping(target = "volume", source = "volume")
	@Mapping(target = "packageType", source = "packagingType")
	@Mapping(target = "dimension", source = ".")
	@Mapping(target = "HSCode", source = "hsCodes")
	OrderPosition mapOrderPosition(OrderLine orderLine);

}
