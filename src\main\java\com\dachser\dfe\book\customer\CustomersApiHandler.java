package com.dachser.dfe.book.customer;

import com.dachser.dfe.book.api.CustomersApiDelegate;
import com.dachser.dfe.book.country.CountryService;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.jpa.entity.Division;
import com.dachser.dfe.book.mapper.TimeFrameMapper;
import com.dachser.dfe.book.model.AddressDto;
import com.dachser.dfe.book.model.CollectionTimeSlotDto;
import com.dachser.dfe.book.model.ContactDataDto;
import com.dachser.dfe.book.model.ContainerTypeDto;
import com.dachser.dfe.book.model.ContainerTypesWithLastUsedDto;
import com.dachser.dfe.book.model.CustomerSettingsDto;
import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.book.model.DeliveryProductsWithFavoritesDto;
import com.dachser.dfe.book.model.FavoriteCountriesDto;
import com.dachser.dfe.book.model.GoodsGroupResponseItemDto;
import com.dachser.dfe.book.model.MeasurementDto;
import com.dachser.dfe.book.model.MeasurementProposalsDto;
import com.dachser.dfe.book.model.OptionDto;
import com.dachser.dfe.book.model.OrderTypeDto;
import com.dachser.dfe.book.model.PackagingOptionsWithFavoritesDto;
import com.dachser.dfe.book.model.Segment;
import com.dachser.dfe.book.model.SegmentDto;
import com.dachser.dfe.book.model.TimeFrameContainer;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.order.mapper.OrderTypeMapper;
import com.dachser.dfe.book.packaging.PackagingOptionsService;
import com.dachser.dfe.book.product.air.AirProductService;
import com.dachser.dfe.book.product.road.RoadProductsService;
import com.dachser.dfe.book.service.FurtherAddressTypeService;
import com.dachser.dfe.book.service.ext.OpeningHoursServiceInterface;
import com.dachser.dfe.book.service.goodsgroup.GoodsGroupService;
import com.dachser.dfe.book.service.loadingpoint.LoadingPoint;
import com.dachser.dfe.book.service.loadingpoint.LoadingPointMapper;
import com.dachser.dfe.book.service.loadingpoint.LoadingPointService;
import com.dachser.dfe.book.service.ordergroup.OrderGroupService;
import com.dachser.dfe.book.user.BusinessDomainProvider;
import com.dachser.dfe.book.user.UserContextService;
import com.dachser.dfe.book.user.UserMapper;
import com.dachser.dfe.book.user.model.Address;
import com.dachser.dfe.book.user.model.AslCustomer;
import com.dachser.dfe.book.user.model.RoadCustomer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@CustomerNumberValid
@Slf4j
public class CustomersApiHandler implements CustomersApiDelegate {

	private static final Map<String, MeasurementDto> MEASUREMENTS = standardMeasurements();

	private final UserContextService userContextService;

	private final UserMapper userMapper;

	private final SegmentMapper segmentMapper;

	private final OrderGroupService orderGroupService;

	private final OrderTypeMapper orderTypeMapper;

	private final FurtherAddressTypeService furtherAddressTypeService;

	private final PackagingOptionsService packagingOptionsService;

	private final LoadingPointService loadingPointService;

	private final LoadingPointMapper loadingPointMapper;

	private final OpeningHoursServiceInterface openingHoursService;

	private final TimeFrameMapper timeFrameMapper;

	private final GoodsGroupService goodsGroupService;

	private final CountryService countryService;

	private final AirProductService airProductService;

	private final RoadProductsService roadProductsService;

	private final GeneralDataService generalDataService;

	private final BusinessDomainProvider businessDomainProvider;

	private static Map<String, MeasurementDto> standardMeasurements() {
		final Map<String, MeasurementDto> map = new HashMap<>();
		map.put("EU", new MeasurementDto().length(120).width(80));
		map.put("HE", new MeasurementDto().length(80).width(60));
		map.put("GB", new MeasurementDto().length(124).width(84));
		map.put("DD", new MeasurementDto().length(80).width(60));
		map.put("H1", new MeasurementDto().length(120).width(80));
		map.put("C0", new MeasurementDto().length(120).width(100));
		map.put("C1", new MeasurementDto().length(120).width(80));
		map.put("C2", new MeasurementDto().length(80).width(60));
		map.put("C4", new MeasurementDto().length(60).width(40));
		map.put("IP", new MeasurementDto().length(120).width(100));
		map.put("E2", new MeasurementDto().length(60).width(40));
		map.put("PE", new MeasurementDto().length(120).width(80));
		return map;
	}

	@Override
	@Deprecated
	public ResponseEntity<List<OptionDto>> getOrderGroups(final String customerNumber, final OrderTypeDto orderType) {
		final OrderType currentOrderType = orderTypeMapper.map(orderType);
		if (Segment.ROAD.equals(currentOrderType.getSegment())) {
			final Division division = userContextService.getFirstFoundDivision(customerNumber, currentOrderType.getSegment());
			final Optional<RoadCustomer> roadCustomer = userContextService.getCustomerInformationRoad(customerNumber);

			return roadCustomer.map(
							customer -> ResponseEntity.ok(orderGroupService.getOrderGroupsForCustomer(customer, division, businessDomainProvider.getBusinessDomain(), null, null, null)))
					.orElse(ResponseEntity.ok(new ArrayList<>()));
		}
		return ResponseEntity.ok(new ArrayList<>());
	}

	@Override
	public ResponseEntity<List<OptionDto>> getOrderGroupsFiltered(final String customerNumber, final OrderTypeDto orderType, final String shipperCountryCode,
			final String consigneeCountryCode) {
		final OrderType currentOrderType = orderTypeMapper.map(orderType);
		if (Segment.ROAD.equals(currentOrderType.getSegment())) {
			final Division division = userContextService.getFirstFoundDivision(customerNumber, currentOrderType.getSegment());
			final Optional<RoadCustomer> roadCustomer = userContextService.getCustomerInformationRoad(customerNumber);

			return roadCustomer.map(customer -> ResponseEntity.ok(
					orderGroupService.getOrderGroupsForCustomer(customer, division, businessDomainProvider.getBusinessDomain(), shipperCountryCode, consigneeCountryCode,
							orderTypeMapper.map(orderType)))).orElse(ResponseEntity.ok(new ArrayList<>()));
		}
		return ResponseEntity.ok(new ArrayList<>());
	}

	@Override
	public ResponseEntity<List<OptionDto>> getFurtherAddressTypes(final String customerNumber, final SegmentDto customerSegment) {
		List<String> keysToFilter = null;
		final Segment segment = segmentMapper.map(customerSegment);
		if (Segment.ROAD.equals(segment)) {
			keysToFilter = AddressTypesFilterUtil.getFilterForRoadCustomer(userContextService.getCustomerInformationRoad(customerNumber));
		}
		final List<OptionDto> addressTypeDtos = furtherAddressTypeService.getFurtherAddressTypes(businessDomainProvider.getBusinessDomain(), keysToFilter);
		return ResponseEntity.ok(addressTypeDtos);
	}

	@Override
	public ResponseEntity<PackagingOptionsWithFavoritesDto> getCustomerPackagingOptions(final String customerNumber, final SegmentDto customerSegment) {
		final List<OptionDto> packagingOptions = new ArrayList<>(
				packagingOptionsService.getPackagingOptions(segmentMapper.map(customerSegment)));

		final List<OptionDto> favorites = packagingOptionsService.getPackagingOptionsFavoritesForCustomer(customerNumber, segmentMapper.map(customerSegment));
		packagingOptions.removeAll(favorites);
		return ResponseEntity.ok(new PackagingOptionsWithFavoritesDto().packagingOptions(packagingOptions).favorites(favorites));
	}

	@Override
	public ResponseEntity<ContainerTypesWithLastUsedDto> getContainerTypes(final String customerNumber, final SegmentDto customerSegment) {
		final List<ContainerTypeDto> containerTypes = new ArrayList<>(generalDataService.getAllWhitelistedContainerTypes());
		final List<ContainerTypeDto> lastUsed = generalDataService.getMostFrequentlyUsedContainerTypesForCustomer(customerNumber);
		containerTypes.removeAll(lastUsed);

		return ResponseEntity.ok(new ContainerTypesWithLastUsedDto().containerTypes(containerTypes).lastUsed(lastUsed));
	}

	@Override
	public ResponseEntity<FavoriteCountriesDto> getFavoriteCountries(final String customerNumber, final SegmentDto customerSegment) {
		final FavoriteCountriesDto countries = countryService.getCountriesForCustomer(LocaleContextHolder.getLocale().getLanguage(),
				Optional.ofNullable(customerSegment).map(segmentMapper::map).orElse(Segment.ROAD), customerNumber);

		return ResponseEntity.ok(countries);
	}

	@Override
	public ResponseEntity<CustomerSettingsDto> getCustomerSettings(final String customerNumber, final SegmentDto customerSegment) {
		if (customerSegment != SegmentDto.ROAD) {
			final Optional<AslCustomer> customerInformation = userContextService.getCustomerInformationAsl(customerNumber);
			return customerInformation.map(customer -> ResponseEntity.ok(userMapper.mapCustomerOptions(customer))).orElse(ResponseEntity.notFound().build());
		}

		final Optional<RoadCustomer> customerInformation = userContextService.getCustomerInformationRoad(customerNumber);
		return customerInformation.map(customer -> ResponseEntity.ok(userMapper.mapCustomerOptions(customer.getOrderOptions()))).orElse(ResponseEntity.notFound().build());
	}

	@Override
	public ResponseEntity<List<AddressDto>> getCustomerLoadingPoints(final String customerNumber, final SegmentDto customerSegment) {
		final List<LoadingPoint> loadingPoints = loadingPointService.getLoadingPoints(customerNumber, LocaleContextHolder.getLocale().getLanguage());
		// Setting an id for the frontend because selector requires it, need to be reviewed during DFE-1433
		return ResponseEntity.ok(loadingPointMapper.mapList(loadingPoints));
	}

	@Override
	public ResponseEntity<AddressDto> getCustomerAddress(final String customerNumber, final SegmentDto customerSegment) {
		final Optional<Address> customerAddress = userContextService.getCustomerAddress(customerNumber, segmentMapper.map(customerSegment));
		return customerAddress.map(address -> {
			AddressDto dto = userMapper.mapCustomerAddress(address);
			dto.setCustomerNumber(customerNumber);
			return ResponseEntity.ok(dto);
		}).orElse(ResponseEntity.noContent().build());
	}

	@Override
	public ResponseEntity<List<CollectionTimeSlotDto>> getCustomerCollectionTimeSlots(final LocalDate collectionDate, final String customerNumber, final SegmentDto customerSegment,
			final OrderTypeDto orderType) {
		final Division division = userContextService.getFirstFoundDivision(customerNumber, segmentMapper.map(customerSegment));
		final Integer branchId = userContextService.getBranchId(customerNumber, orderTypeMapper.map(orderType));

		final Optional<List<TimeFrameContainer>> openingHours = openingHoursService.getOpeningHours(collectionDate, customerNumber, division, branchId);

		return openingHours.map(timeFrameContainers -> ResponseEntity.ok(buildTimeSlots(timeFrameContainers, collectionDate))).orElseGet(() -> ResponseEntity.noContent().build());
	}

	@Override
	public ResponseEntity<MeasurementProposalsDto> getPackagingMeasurementsProposals(final String packagingCode, final String customerNumber, final SegmentDto customerSegment) {
		final MeasurementProposalsDto measurementProposals = new MeasurementProposalsDto().standard(MEASUREMENTS.get(packagingCode));

		// TODO: add frequently used measurements
		measurementProposals.frequentlyUsed(List.of());
		return ResponseEntity.ok(measurementProposals);
	}

	@Override
	public ResponseEntity<List<GoodsGroupResponseItemDto>> getCustomerGoodsGroups(final String customerNumber, final SegmentDto customerSegment, final OrderTypeDto orderType) {
		final int branchId = userContextService.getBranchId(customerNumber, orderTypeMapper.map(orderType));
		final List<GoodsGroupResponseItemDto> results = goodsGroupService.getGoodsGroupsForCustomer(customerNumber, branchId, businessDomainProvider.getBusinessDomain());
		return ResponseEntity.ok(results);
	}

	@Override
	public ResponseEntity<List<OptionDto>> getCustomerTransports(final String customerNumber, final SegmentDto customerSegment) {
		final List<OptionDto> list = new ArrayList<>();

		return ResponseEntity.ok(list);
	}

	@Override
	public ResponseEntity<ContactDataDto> getCustomerContactData(final String customerNumber, final SegmentDto customerSegment) {
		// TODO: send real data
		return ResponseEntity.ok(null);
	}

	@Override
	public ResponseEntity<List<DeliveryProductDto>> getAirDeliveryProducts(final String customerNumber, final SegmentDto customerSegment) {
		final boolean viewAirProducts = userContextService.isCustomerAllowedToViewAirProducts(customerNumber, segmentMapper.map(customerSegment));
		return ResponseEntity.ok(viewAirProducts ? airProductService.getAirDeliveryProducts() : new ArrayList<>());
	}

	@Override
	public ResponseEntity<DeliveryProductsWithFavoritesDto> getApplicableRoadDeliveryProducts(final String customerNumber, final SegmentDto customerSegment,
			final String consigneeCountryCode, final String consigneePostalCode, final String shipperCountryCode, final String shipperPostalCode) {
		final Division division = userContextService.getFirstFoundDivision(customerNumber, segmentMapper.map(customerSegment));
		final DeliveryProductsWithFavoritesDto productsWithFavorites = roadProductsService.getOnlyApplicableDeliveryProductsFavoritesForCustomer(customerNumber, division,
				businessDomainProvider.getBusinessDomain(), shipperCountryCode, shipperPostalCode, consigneeCountryCode, consigneePostalCode);
		return ResponseEntity.ok(productsWithFavorites);
	}

	private List<CollectionTimeSlotDto> buildTimeSlots(final List<TimeFrameContainer> timeFrames, final LocalDate collectionDate) {
		return timeFrameMapper.mapFramesToTimeSlots(timeFrames.stream().map(TimeFrameContainer::getCollectionTimeFrame).toList(), collectionDate);
	}
}
