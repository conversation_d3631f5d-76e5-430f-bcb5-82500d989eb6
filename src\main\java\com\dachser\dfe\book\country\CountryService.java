package com.dachser.dfe.book.country;

import com.dachser.dfe.book.config.WhiteListFilterConfiguration;
import com.dachser.dfe.book.exception.DachserCodeNotMappedException;
import com.dachser.dfe.book.generaldata.GeneralDataService;
import com.dachser.dfe.book.jpa.query.QueryService;
import com.dachser.dfe.book.model.CountryDto;
import com.dachser.dfe.book.model.FavoriteCountriesDto;
import com.dachser.dfe.book.model.Segment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.Collator;
import java.time.OffsetDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

import static java.util.Comparator.naturalOrder;

@Service
@RequiredArgsConstructor
@Slf4j
public class CountryService {

	private final WhiteListFilterConfiguration whiteListFilterConfiguration;

	private final GeneralDataService generalDataService;

	private final QueryService queryService;

	public List<CountryDto> getSortedAndFilteredCountries(String language, Segment segment) {
		List<Country> countries = generalDataService.getCountries(language);
		Stream<Country> countriesStream = countries.stream();

		if (Segment.ROAD == segment) {
			countriesStream = countriesStream.filter(country -> whiteListFilterConfiguration.getCountries().getRoad().contains(country.getCountryCode()));
		}

		Collator collator = Collator.getInstance(Locale.forLanguageTag(language));

		List<Country> sortedCountries = countriesStream.sorted(Comparator.comparing(Country::getCountryName, Comparator.nullsLast(naturalOrder())))
				.sorted(Comparator.comparing(Country::getCountryName, Comparator.nullsLast(collator))).toList();

		return mapToCountryDtos(sortedCountries);
	}

	public FavoriteCountriesDto getCountriesForCustomer(String language, Segment segment, String customerNumber) {
		List<CountryDto> countriesForSegment = getSortedAndFilteredCountries(language, segment).stream().filter(country -> StringUtils.isNotBlank(country.getLabel())).toList();

		List<CountryDto> shipperCountries = getFavoriteCountriesForCustomer(true, customerNumber, countriesForSegment);

		List<CountryDto> consigneeCountries = getFavoriteCountriesForCustomer(false, customerNumber, countriesForSegment);

		return new FavoriteCountriesDto().shipperCountries(shipperCountries).consigneeCountries(consigneeCountries);
	}

	private List<CountryDto> getFavoriteCountriesForCustomer(boolean isShipper, String customerNumber, List<CountryDto> availableCountries) {

		final String dynamicQueryPart = isShipper ? "ob.shipper_address_id" : "ob.consignee_address_id";

		// @formatter:off
		final String query = (
				"""
				SELECT country FROM \
				(SELECT TOP 250 oa.country_code AS country \
				FROM order_base ob INNER JOIN order_address oa \
				ON %s = oa.order_address_id \
				WHERE ob.customer_number = '%s' \
				AND ob.last_modified > '%s') AS T \
				GROUP BY country \
				ORDER BY COUNT(*) DESC\
				""").formatted(dynamicQueryPart, customerNumber, OffsetDateTime.now().minusDays(30));
		// @formatter:on

		final List<Object> queryResult = queryService.performNativeQuery(query);

		final List<String> favoriteCountryCodes = queryResult.stream().map(String.class::cast).map(CountryCode::getFromIsoCode).flatMap(Optional::stream)
				.map(CountryCode::getIsoCode).limit(3).toList();

		return favoriteCountryCodes.stream().map(country -> availableCountries.stream().filter(entry -> entry.getCountryCode().equals(country)).findFirst())
				.flatMap(Optional::stream).toList();
	}

	private List<CountryDto> mapToCountryDtos(List<Country> countries) {
		return countries.stream()
				.map(country -> new CountryDto().countryCode(country.getCountryCode()).label(country.getCountryName()).isPostCodeMandatory(country.isPostcodeMandatory()))
				.toList();
	}

	public String mapToDachserCountryCode(String countryCode) {
		if (countryCode == null) {
			return null;
		}
		return generalDataService.getCountries("en").stream().filter(country -> country.getCountryCode().equals(countryCode)).map(Country::getDachserCountryCode)
				.filter(Objects::nonNull).findFirst().orElseThrow(() -> new DachserCodeNotMappedException("Country code " + countryCode + " not mapped to Dachser code"));
	}

	public boolean isPostCodeMandatory(String countryCode) {
		return generalDataService.getCountries("en").stream().filter(country -> country.getCountryCode().equals(countryCode)).map(Country::isPostcodeMandatory)
				.findFirst().orElse(false);
	}
}
