package com.dachser.dfe.book.dip.repository;

import com.dachser.dfe.book.dip.xml.XmlPublishStatus;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface OrderXmlRepository extends JpaRepository<OrderXml, Long> {
	List<OrderXml> findByStatus(XmlPublishStatus status);

	Optional<OrderXml> findByStatusAndCustomerNumberAndShipmentNumberAndOrderId(XmlPublishStatus status, String customerNumber, Long shipmentNumber, Long orderId);
}
