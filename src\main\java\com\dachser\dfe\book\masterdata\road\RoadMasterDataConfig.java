package com.dachser.dfe.book.masterdata.road;

import com.dachser.dfe.book.cache.Cache2KConfiguration;
import com.dachser.dfe.book.masterdata.road.cache.CachingAdressTypeDescriptionControllerApi;
import com.dachser.dfe.book.masterdata.road.cache.CachingBranchDataControllerApi;
import com.dachser.dfe.book.masterdata.road.cache.CachingCustomerOrderGroupControllerApi;
import com.dachser.dfe.book.masterdata.road.cache.CachingDeliveryTermControllerApi;
import com.dachser.dfe.book.masterdata.road.cache.CachingForwardingDomainControllerApi;
import com.dachser.dfe.book.masterdata.road.cache.CachingPackagingNameServiceApi;
import com.dachser.dfe.book.masterdata.road.cache.CachingProductGroupControllerApi;
import com.dachser.dfe.book.masterdata.road.cache.CachingProductNameControllerApi;
import com.dachser.dfe.book.masterdata.road.cache.CachingServiceTypeControllerApi;
import com.dachser.dfe.book.masterdata.road.cache.CachingShipperMasterdataControllerApi;
import com.dachser.dfe.book.masterdata.road.cache.CachingTermControllerApi;
import com.dachser.dfe.road.masterdata.ApiClient;
import com.dachser.dfe.road.masterdata.api.AdressTypeDescriptionControllerApi;
import com.dachser.dfe.road.masterdata.api.BranchDataControllerApi;
import com.dachser.dfe.road.masterdata.api.ConvertTermIncotermControllerApi;
import com.dachser.dfe.road.masterdata.api.CountryControllerApi;
import com.dachser.dfe.road.masterdata.api.CustomerOrderGroupControllerApi;
import com.dachser.dfe.road.masterdata.api.DeliveryTermControllerApi;
import com.dachser.dfe.road.masterdata.api.ForwardingDomainControllerApi;
import com.dachser.dfe.road.masterdata.api.PackagingNameServiceApi;
import com.dachser.dfe.road.masterdata.api.PingServiceApi;
import com.dachser.dfe.road.masterdata.api.ProductGroupControllerApi;
import com.dachser.dfe.road.masterdata.api.ProductNameControllerApi;
import com.dachser.dfe.road.masterdata.api.ProductPilotControllerApi;
import com.dachser.dfe.road.masterdata.api.RelationLabelprintingControllerApi;
import com.dachser.dfe.road.masterdata.api.ServiceTypeControllerApi;
import com.dachser.dfe.road.masterdata.api.ShipperMasterdataControllerApi;
import com.dachser.dfe.road.masterdata.api.TermControllerApi;
import com.dachser.dfe.road.masterdata.api.TextTypeControllerApi;
import com.dachser.dfe.road.masterdata.api.TrackablePackingAidControllerApi;
import com.dachser.dfe.road.masterdata.api.ValidateFixedDateControllerApi;
import com.dachser.dfe.road.masterdata.api.ValidateProductControllerApi;
import com.dachser.dfe.road.masterdata.api.ValidateTermControllerApi;
import com.dachser.dfe.road.masterdata.api.ValidateThirdCountryControllerApi;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.client.RestTemplate;

@Configuration
@ConfigurationProperties(prefix = "dfe.road.masterdata")
@Getter
@Setter
@Import(Cache2KConfiguration.class)
class RoadMasterDataConfig {

	private String baseURL;

	private String username;

	private String password;

	private final RestTemplate bookRestTemplate;

	private boolean debugging = false;

	private boolean useCache = true;

	@Autowired
	RoadMasterDataConfig(@Qualifier("bookRestTemplateNoAuth") RestTemplate restTemplate) {
		this.bookRestTemplate = restTemplate;
	}

	private ApiClient roadMasterDataApiClient() {
		final ApiClient apiClient = new ApiClient(bookRestTemplate);
		apiClient.setBasePath(baseURL);
		apiClient.setPassword(password);
		apiClient.setUsername(username);
		apiClient.setDebugging(debugging);
		return apiClient;
	}

	@Bean
	PingServiceApi pingServiceApi() {
		return new PingServiceApi(roadMasterDataApiClient());
	}

	@Bean
	DeliveryTermControllerApi deliveryTermApi() {
		if (useCache) {
			return new CachingDeliveryTermControllerApi(roadMasterDataApiClient());
		} else {
			return new DeliveryTermControllerApi(roadMasterDataApiClient());
		}
	}

	@Bean
	PackagingNameServiceApi packagingNameServiceApi() {
		if (useCache) {
			return new CachingPackagingNameServiceApi(roadMasterDataApiClient());
		} else {
			return new PackagingNameServiceApi(roadMasterDataApiClient());
		}
	}

	@Bean
	ProductNameControllerApi productNameControllerApi() {
		if (useCache) {
			return new CachingProductNameControllerApi(roadMasterDataApiClient());
		} else {
			return new ProductNameControllerApi(roadMasterDataApiClient());
		}
	}

	@Bean
	ConvertTermIncotermControllerApi convertTermIncotermControllerApi() {
		return new ConvertTermIncotermControllerApi(roadMasterDataApiClient());
	}

	@Bean
	TermControllerApi termControllerApi() {
		if (useCache) {
			return new CachingTermControllerApi(roadMasterDataApiClient());
		} else {
			return new TermControllerApi(roadMasterDataApiClient());
		}
	}

	@Bean
	TextTypeControllerApi textTypeControllerApi() {
		return new TextTypeControllerApi(roadMasterDataApiClient());
	}

	@Bean
	AdressTypeDescriptionControllerApi adressTypeDescriptionControllerApi() {
		if (useCache) {
			return new CachingAdressTypeDescriptionControllerApi(roadMasterDataApiClient());
		} else {
			return new AdressTypeDescriptionControllerApi(roadMasterDataApiClient());
		}
	}

	@Bean
	CustomerOrderGroupControllerApi customerOrderGroupControllerApi() {
		if (useCache) {
			return new CachingCustomerOrderGroupControllerApi(roadMasterDataApiClient());
		} else {
			return new CustomerOrderGroupControllerApi(roadMasterDataApiClient());
		}
	}

	@Bean
	ProductGroupControllerApi productGroupControllerApi() {
		if (useCache) {
			return new CachingProductGroupControllerApi(roadMasterDataApiClient());
		} else {
			return new ProductGroupControllerApi(roadMasterDataApiClient());
		}
	}

	@Bean
	ValidateProductControllerApi validateProductControllerApi() {
		return new ValidateProductControllerApi(roadMasterDataApiClient());
	}

	@Bean
	ValidateTermControllerApi validateTermControllerApi() {
		return new ValidateTermControllerApi(roadMasterDataApiClient());
	}

	@Bean
	ValidateFixedDateControllerApi validateFixedDateControllerApi() {
		return new ValidateFixedDateControllerApi(roadMasterDataApiClient());
	}

	@Bean
	ValidateThirdCountryControllerApi validateThirdCountryControllerApi() {
		return new ValidateThirdCountryControllerApi(roadMasterDataApiClient());
	}

	@Bean
	ShipperMasterdataControllerApi shipperMasterdataControllerApi() {
		ShipperMasterdataControllerApi controllerApi = new ShipperMasterdataControllerApi(roadMasterDataApiClient());
		if (useCache) {
			return new CachingShipperMasterdataControllerApi(controllerApi);
		}
		return controllerApi;
	}

	@Bean
	ServiceTypeControllerApi serviceTypeControllerApi() {
		if (useCache) {
			return new CachingServiceTypeControllerApi(roadMasterDataApiClient());
		} else {
			return new ServiceTypeControllerApi(roadMasterDataApiClient());
		}
	}

	@Bean
	BranchDataControllerApi branchDataControllerApi() {
		if (useCache) {
			return new CachingBranchDataControllerApi(roadMasterDataApiClient());
		} else {
			return new BranchDataControllerApi(roadMasterDataApiClient());
		}
	}

	@Bean
	RelationLabelprintingControllerApi relationLabelprintingControllerApi() {
		return new RelationLabelprintingControllerApi(roadMasterDataApiClient());
	}

	@Bean
	TrackablePackingAidControllerApi trackablePackingAidControllerApi() {
		return new TrackablePackingAidControllerApi(roadMasterDataApiClient());
	}

	@Bean
	ProductPilotControllerApi productPilotControllerApi() {
		return new ProductPilotControllerApi(roadMasterDataApiClient());
	}

	@Bean
	ForwardingDomainControllerApi forwardingDomainControllerApi() {
		if (useCache) {
			return new CachingForwardingDomainControllerApi(roadMasterDataApiClient());
		} else {
			return new ForwardingDomainControllerApi(roadMasterDataApiClient());
		}
	}

	@Bean
	CountryControllerApi countryControllerApi() {
		return new CountryControllerApi(roadMasterDataApiClient());
	}
}
