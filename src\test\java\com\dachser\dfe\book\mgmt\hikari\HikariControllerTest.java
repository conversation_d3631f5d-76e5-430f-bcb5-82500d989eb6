package com.dachser.dfe.book.mgmt.hikari;

import com.dachser.dfe.book.BaseOpenApiTest;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.HttpStatus;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
class HikariControllerTest extends BaseOpenApiTest {

	@SpyBean
	private HikariService hikariService;

	@Nested
	class Get {

		@Test
		void shouldReturn200MinimumIdle() {
			int minimumIdle = hikariService.getMinimumIdle();

			final MockMvcResponse response = givenRequest().get(buildUrl("/mgmt/hikari/minimum-idle"));
			assertEquals(200, response.statusCode());
			final String responseContent = response.getBody().print();
			assertEquals("Minimum idle is " + minimumIdle, responseContent);
		}

		@Test
		void shouldReturn200LeakThreshold() {
			long leakDetectionThreshold = hikariService.getLeakDetectionThreshold();

			final MockMvcResponse response = givenRequest().get(buildUrl("/mgmt/hikari/leak-detection-threshold"));
			assertEquals(200, response.statusCode());
			final String responseContent = response.getBody().print();
			assertEquals("Leak detection threshold is " + leakDetectionThreshold + "ms", responseContent);
		}

		@Test
		void shouldReturn200ResetLeakThreshold() {
			final MockMvcResponse response = givenRequest().get(buildUrl("/mgmt/hikari/reset-leak-detection-threshold"));
			assertEquals(200, response.statusCode());
			final String responseContent = response.getBody().print();
			assertEquals("Leak detection threshold turned off", responseContent);
			assertEquals(0, hikariService.getLeakDetectionThreshold());
		}

		@Test
		void shouldReturn200ConnectionTimeout() {
			long connectionTimeout = hikariService.getConnectionTimeout();

			final MockMvcResponse response = givenRequest().get(buildUrl("/mgmt/hikari/connection-timeout"));
			assertEquals(200, response.statusCode());
			final String responseContent = response.getBody().print();
			assertEquals("Connection timeout is " + connectionTimeout + "ms", responseContent);
		}

		@Test
		void shouldReturn200ResetConnectionTimeout() {
			final MockMvcResponse response = givenRequest().get(buildUrl("/mgmt/hikari/reset-connection-timeout"));
			assertEquals(200, response.statusCode());
			final String responseContent = response.getBody().print();
			assertEquals("Connection timeout reset to default (30000ms)", responseContent);
			assertEquals(30000, hikariService.getConnectionTimeout());
		}

		@Test
		void shouldReturn200PoolMetrics() {
			final MockMvcResponse response = givenRequest().get(buildUrl("/mgmt/hikari/metrics/pool"));
			assertNotNull(response);
			assertEquals(HttpStatus.OK.value(), response.statusCode());
			assertTrue(StringUtils.isNotBlank(response.getBody().print()));
			log.debug("Hikari pool metrics = {}", response.getBody().print());
		}

	}

	@Nested
	class Post {

		@Test
		void shouldSetMinimumIdle() {
			final MockMvcResponse response = givenRequest().post(buildUrl("/mgmt/hikari/minimum-idle?minimumIdle=10"));
			assertEquals(200, response.statusCode());
			final String responseContent = response.getBody().print();
			assertEquals("Minimum idle set to 10", responseContent);
			assertEquals(10, hikariService.getMinimumIdle());
		}

		@Test
		void shouldSetLeakDetectionThreshold() {
			final MockMvcResponse response = givenRequest().post(buildUrl("/mgmt/hikari/leak-detection-threshold?thresholdMs=50000"));
			assertEquals(200, response.statusCode());
			final String responseContent = response.getBody().print();
			assertEquals("Leak detection threshold set to 50000ms", responseContent);
			assertEquals(50000, hikariService.getLeakDetectionThreshold());
		}

		@Test
		void shouldSetConnectionTimeout() {
			final MockMvcResponse response = givenRequest().post(buildUrl("/mgmt/hikari/connection-timeout?timeoutMs=50000"));
			assertEquals(200, response.statusCode());
			final String responseContent = response.getBody().print();
			assertEquals("Connection timeout set to 50000ms", responseContent);
			assertEquals(50000, hikariService.getConnectionTimeout());
		}
	}
}
