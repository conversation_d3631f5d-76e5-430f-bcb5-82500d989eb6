package com.dachser.dfe.book.order.validation.road.delivery;

import com.dachser.dfe.book.i18n.Translator;
import com.dachser.dfe.book.model.options.DeliveryOptions;
import com.dachser.dfe.book.order.road.RoadOrder;
import com.dachser.dfe.book.order.road.TargoOnSiteProducts;
import com.dachser.dfe.book.order.validation.Messages;
import com.dachser.dfe.book.order.validation.PayloadProvidingValidator;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
public class DeliveryOptionValidator implements ConstraintValidator<DeliveryOptionValid, RoadOrder>, PayloadProvidingValidator {

	private final Translator translator;

	// @formatter:off
	private static final List<DeliveryOptionsRule> RULES = List.of(
		new DeliveryOptionsRule(TargoOnSiteProducts.TARGO_ON_SITE.getCoreSystemValue(), true, List.of(DeliveryOptions.AP, DeliveryOptions.AT)),
		new DeliveryOptionsRule(TargoOnSiteProducts.TARGO_ON_SITE_PLUS.getCoreSystemValue(), true, List.of(DeliveryOptions.AP, DeliveryOptions.AT)),
		new DeliveryOptionsRule(TargoOnSiteProducts.TARGO_ON_SITE_PREMIUM.getCoreSystemValue(), true, List.of(DeliveryOptions.AP, DeliveryOptions.AT)),
		new DeliveryOptionsRule(TargoOnSiteProducts.TARGO_ON_SITE_FIX.getCoreSystemValue(), true, List.of(DeliveryOptions.AP))
	);
	// @formatter:on

	@AllArgsConstructor
	private static class DeliveryOptionsRule {

		private String product;

		private boolean required;

		private List<DeliveryOptions> allowedDeliveryOptions;

	}

	@Override
	public boolean isValid(RoadOrder order, ConstraintValidatorContext context) {
		if (order == null) {
			return true;
		}
		Optional<DeliveryOptionsRule> deliveryOptionRule = RULES.stream().filter(rule -> rule.product.equalsIgnoreCase(order.getProduct())).findFirst();
		if (deliveryOptionRule.isPresent()) {
			if (deliveryOptionRule.get().required && !hasDeliveryOption(order)) {
				context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.MISSING_DELIVERY_OPTION)).addPropertyNode("deliveryOption").addConstraintViolation();
				return false;
			}
			if (!deliveryOptionRule.get().allowedDeliveryOptions.contains(order.getDeliveryOption())) {
				context.buildConstraintViolationWithTemplate(translator.toLocale(Messages.NOT_ALLOWED_DELIVERY_OPTION, deliveryOptionRule.get().allowedDeliveryOptions))
						.addPropertyNode("deliveryOption").addConstraintViolation();
				return false;
			}
		}
		return true;
	}

	private boolean hasDeliveryOption(RoadOrder order) {
		return order.getDeliveryOption() != null && order.getDeliveryOption() != DeliveryOptions.NO;
	}

}