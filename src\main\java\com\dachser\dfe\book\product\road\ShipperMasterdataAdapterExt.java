package com.dachser.dfe.book.product.road;

import com.dachser.dfe.book.exception.BookErrorId;
import com.dachser.dfe.book.exception.ErrorIdExternalServiceNotAvailableException;
import com.dachser.dfe.book.mapper.ShipperMasterdataMapper;
import com.dachser.dfe.book.masterdata.road.RoadMasterDataApiWrapper;
import com.dachser.dfe.book.model.ShipperMasterData;
import com.dachser.dfe.book.service.ext.RoadMasterDataServiceMarker;
import com.dachser.dfe.road.masterdata.model.RMDShipperMasterdataDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "dfe.book.mock", havingValue = "false", matchIfMissing = true)
public class ShipperMasterdataAdapterExt implements ShipperMasterdataAdapter, RoadMasterDataServiceMarker {

	private final RoadMasterDataApiWrapper roadMasterdataApiWrapper;

	private final ShipperMasterdataMapper shipperMasterdataMapper;

	@Override
	public List<ShipperMasterData> getShipperMasterdata(Long shipperNumber, Integer businessDomain) {
		try {
			List<RMDShipperMasterdataDTO> shipperMasterdata = roadMasterdataApiWrapper.getShipperMasterdata(shipperNumber, businessDomain);

			return shipperMasterdataMapper.map(shipperMasterdata);
		} catch (final RestClientException ex) {
			log.error("Shipper masterdata service not available with message: {}", ex.getMessage());
			throw new ErrorIdExternalServiceNotAvailableException(BookErrorId.ERR_RF_01, ex.getMessage());
		}
	}
}
