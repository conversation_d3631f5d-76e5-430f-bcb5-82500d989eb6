package com.dachser.dfe.book.mapper;

import com.dachser.dfe.book.model.DeliveryProductDto;
import com.dachser.dfe.road.masterdata.model.RMDProductNameDTO;
import com.dachser.dfe.road.masterdata.model.RMDProductPilotResponsePositionDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ProductNameMapper {

	String FIX = "fix";

	@Mapping(source = RMDProductPilotResponsePositionDTO.JSON_PROPERTY_PRODUCT, target = "code")
	@Mapping(source = RMDProductPilotResponsePositionDTO.JSON_PROPERTY_PRODUCT_DESCRIPTION, target = "description")
	@Mapping(source = ".", target = "fixedDeliveryDate")
	DeliveryProductDto map(RMDProductPilotResponsePositionDTO masterdataDto);

	List<DeliveryProductDto> mapToDtos(List<RMDProductPilotResponsePositionDTO> masterdataDtos);

	default Boolean mapFixDate(RMDProductNameDTO masterdataDto) {
		final String productName = masterdataDto.getProductName();
		return productName != null && productName.toLowerCase().contains(FIX);
	}

	default Boolean mapFixDate(RMDProductPilotResponsePositionDTO masterdataDto) {
		final String productName = masterdataDto.getProductDescription();
		return productName != null && productName.toLowerCase().contains(FIX);
	}

	default String trimMap(String input) {
		if (input == null) {
			return null;
		}
		return input.trim();
	}
}
