package com.dachser.dfe.book.order.common.packingposition;

import com.dachser.dfe.book.order.Order;
import com.dachser.dfe.book.order.road.RoadOrderLine;
import com.dachser.dfe.book.order.validation.CompleteOrderValidation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@Entity
@Table(name = "packing_position")
public class PackingPosition extends BasePackingPosition {

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "order_id", nullable = false)
	@ToString.Exclude
	@EqualsAndHashCode.Exclude
	private Order order;

	@OneToMany(mappedBy = "packingPositionId", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@ToString.Exclude
	@Size(min = 1, groups = CompleteOrderValidation.class, message = "{label.text.invalid_input}")
	@Valid
	private List<RoadOrderLine> orderLines;
}
