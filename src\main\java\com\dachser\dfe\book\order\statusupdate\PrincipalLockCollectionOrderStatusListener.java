package com.dachser.dfe.book.order.statusupdate;

import com.dachser.dfe.book.model.OrderStatus;
import com.dachser.dfe.book.order.road.CollectionOrder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class PrincipalLockCollectionOrderStatusListener implements TypedOrderStatusListener<CollectionOrder> {

	@Override
	public void notifyOfStatusUpdate(CollectionOrder order, OrderStatus oldStatus, OrderStatus newStatus) {
			log.debug("Setting principal lock for order {}", order.getOrderId());
			order.setPrincipalLocked(true);
	}
}
