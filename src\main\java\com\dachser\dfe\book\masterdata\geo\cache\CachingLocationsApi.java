package com.dachser.dfe.book.masterdata.geo.cache;

import com.dachser.dfe.book.cache.CacheNames;
import com.dachser.dfe.masterdata.geo.ApiClient;
import com.dachser.dfe.masterdata.geo.api.LocationsApi;
import com.dachser.dfe.masterdata.geo.model.GMDDachserPostalCode;
import com.dachser.dfe.masterdata.geo.model.GMDLocation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.client.RestClientException;

import java.util.List;

@Slf4j
public class CachingLocationsApi extends LocationsApi {

	public CachingLocationsApi(ApiClient apiClient) {
		super(apiClient);
	}

	@Override
	@Cacheable(cacheNames = CacheNames.MASTERDATA_LOCATIONS)
	public List<GMDLocation> lookupPostalCode(String isoCountryAlpha2Code, String zipCode, String isoLanguageCode, Integer numberOfResults) throws RestClientException {
		log.debug("Requesting lookupZipCode({}, {}, {}, {})", isoCountryAlpha2Code, zipCode, isoLanguageCode, numberOfResults);
		return super.lookupPostalCode(isoCountryAlpha2Code, zipCode, isoLanguageCode, numberOfResults);
	}

	@Override
	@Cacheable(cacheNames = CacheNames.MASTERDATA_LOCATIONS)
	public List<GMDDachserPostalCode> findDachserPostalcodes(String eircode) throws RestClientException {
		log.debug("Requesting findDachserPostalcodes({})", eircode);
		return super.findDachserPostalcodes(eircode);
	}

}
