databaseChangeLog:
  - changeSet:
      id: order-road-mock-data
      author: clisson
      changes:
        # 1 - road forwarding order
        - insert:
            tableName: order_road
            columns:
              - column:
                  name: order_id
                  value: 1
              - column:
                  name: division
                  value: 'T' # European Logistics
              - column:
                  name: product
                  value: 'Y' # Targoflex
              - column:
                  name: freight_term
                  value: '031' # free delivered
        - insert:
            tableName: order_road_forward
            columns:
              - column:
                  name: order_id
                  value: 1
        # 2 - road forwarding order having references of type 077 (booking reference)
        - insert:
            tableName: order_road
            columns:
              - column:
                  name: order_id
                  value: 2
              - column:
                  name: division
                  value: 'T' # European Logistics
              - column:
                  name: product
                  value: 'Y' # Targoflex
              - column:
                  name: freight_term
                  value: '031' # free delivered
        - insert:
            tableName: order_road_forward
            columns:
              - column:
                  name: order_id
                  value: 2
        # 3 - road forwarding order having references of type 337 (identification code transport)
        - insert:
            tableName: order_road
            columns:
              - column:
                  name: order_id
                  value: 3
              - column:
                  name: division
                  value: 'T' # European Logistics
              - column:
                  name: product
                  value: 'Y' # Targoflex
              - column:
                  name: freight_term
                  value: '031' # free delivered
        - insert:
            tableName: order_road_forward
            columns:
              - column:
                  name: order_id
                  value: 3
