package com.dachser.dfe.book.dip.converter.sea;

import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.order.sea.SeaOrder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SeaOrderCategoryMapper {

	@Named("mapOrderCategory")
	default String mapOrderCategory(SeaOrder order) {
		if (order.isFullContainerLoad()) {
			return Types.SeaOrderCategory.FCL;
		}
		return Types.SeaOrderCategory.LCL;
	}

}
