package com.dachser.dfe.book.order;

import com.dachser.dfe.book.api.OrdersApiDelegate;
import com.dachser.dfe.book.document.DeleteOnCloseFileInputStream;
import com.dachser.dfe.book.document.DocumentDownloadZipHolder;
import com.dachser.dfe.book.exception.generic.BadRequestException;
import com.dachser.dfe.book.model.BasicOrderDto;
import com.dachser.dfe.book.model.BasicQuoteInformationDto;
import com.dachser.dfe.book.model.DocumentDownloadDto;
import com.dachser.dfe.book.model.OrderActionWithoutValidationDto;
import com.dachser.dfe.book.model.OrderProcessResultDto;
import com.dachser.dfe.book.model.OrderRequestBodyDto;
import com.dachser.dfe.book.model.OrderResponseBodyDto;
import com.dachser.dfe.book.model.OrderSaveActionDto;
import com.dachser.dfe.book.model.PrintLabelStartPositionDto;
import com.dachser.dfe.book.order.exception.DocumentZipDownloadException;
import com.dachser.dfe.book.order.mapper.OrderMapper;
import com.dachser.dfe.book.quote.QuoteService;
import jakarta.xml.bind.DatatypeConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
@OrderAccessValid
public class OrderApiHandler implements OrdersApiDelegate {
	private final OrderMapper orderMapper;

	private final OrderService orderService;

	private final OrderRepositoryFacade orderRepositoryFacade;

	private final QuoteService quoteService;

	@Override
	public ResponseEntity<OrderProcessResultDto> saveOrderV2(OrderSaveActionDto action, OrderRequestBodyDto orderRequestBodyDto, PrintLabelStartPositionDto startPosition) {
		BasicOrderDto basicOrder = (BasicOrderDto) orderRequestBodyDto;

		orderService.checkIfUserIsAllowedToUsePackingPositions(basicOrder);

		if (basicOrder.getOrderId() != null) {
			return ResponseEntity.ok(orderService.updateOrder(basicOrder, action, startPosition));
		} else {
			return ResponseEntity.ok(orderService.createNewOrder(basicOrder, action, startPosition));
		}
	}

	@Override
	public ResponseEntity<OrderProcessResultDto> saveDraftOrderV2(OrderRequestBodyDto orderRequestBodyDto) {
		BasicOrderDto basicOrder = (BasicOrderDto) orderRequestBodyDto;

		orderService.checkIfUserIsAllowedToUsePackingPositions(basicOrder);

		if (basicOrder.getOrderId() != null) {
			return ResponseEntity.ok(orderService.updateDraftOrderWithOrderTypeSwitch(OrderType.getByName(orderRequestBodyDto.getOrderType()), basicOrder));
		} else {
			return ResponseEntity.ok(new OrderProcessResultDto().order(orderService.createNewDraftOrder(basicOrder)));
		}
	}

	@Override
	@Deprecated
	public ResponseEntity<OrderProcessResultDto> saveOrder(OrderSaveActionDto action, OrderRequestBodyDto orderRequestBodyDto, PrintLabelStartPositionDto startPosition) {

		BasicOrderDto basicOrder = (BasicOrderDto) orderRequestBodyDto;

		orderService.checkIfUserIsAllowedToUsePackingPositions(basicOrder);

		if (basicOrder.getOrderId() != null) {

			return ResponseEntity.ok(orderService.updateOrder(basicOrder, action, startPosition));
		} else {
			return ResponseEntity.ok(orderService.createNewOrder(basicOrder, action, startPosition));
		}
	}

	@Override
	public ResponseEntity deleteOrder(Long orderId) {
		return ResponseEntity.ok(orderService.deleteOrder(orderId));
	}

	@Override
	public ResponseEntity<OrderResponseBodyDto> getOrderDetails(Long orderId) {
		final Order order = orderRepositoryFacade.loadOrderById(orderId);
		return ResponseEntity.ok(orderMapper.map(order));
	}

	@Override
	@Deprecated
	public ResponseEntity<OrderProcessResultDto> saveDraftOrder(OrderRequestBodyDto orderRequestBodyDto) {

		BasicOrderDto basicOrder = (BasicOrderDto) orderRequestBodyDto;

		orderService.checkIfUserIsAllowedToUsePackingPositions(basicOrder);

		if (basicOrder.getOrderId() != null) {
			return ResponseEntity.ok(new OrderProcessResultDto().order((OrderResponseBodyDto) orderService.updateDraftOrder(basicOrder.getOrderId(), basicOrder)));
		} else {
			return ResponseEntity.ok(new OrderProcessResultDto().order(orderService.createNewDraftOrder(basicOrder)));
		}
	}

	@Override
	public ResponseEntity<OrderProcessResultDto> processActionWithoutBody(Long orderId, OrderActionWithoutValidationDto action, PrintLabelStartPositionDto startPosition) {
		return ResponseEntity.ok(orderService.processOrderWithoutOrderBody(orderId, action, startPosition));
	}

	@Override
	public ResponseEntity<DocumentDownloadDto> downloadDocuments(Long orderId) {
		Optional<DocumentDownloadZipHolder> documentDownload;
		try {
			documentDownload = orderService.downloadDocumentsZippedPerOrderId(orderId);
			if (documentDownload.isEmpty()) {
				return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(new DocumentDownloadDto());
			}
			return finishResultActionForZipDownload(documentDownload.get(), orderId);
		} catch (FileNotFoundException e) {
			throw new DocumentZipDownloadException("An error occurred during Download for order %d with message: %s".formatted(orderId, e.getMessage()));
		}
	}

	@Override
	public ResponseEntity<OrderResponseBodyDto> createDraftOrderFromQuote(BasicQuoteInformationDto quoteInformationDto) {
		final OrderResponseBodyDto createdOrder = quoteService.createDraftOrderFromQuote(quoteInformationDto);
		return ResponseEntity.ok(createdOrder);
	}

	@Override
	public ResponseEntity<OrderProcessResultDto> cloneOrder(Long orderId) {
		OrderProcessResultDto processResult = orderService.cloneOrder(orderId);
		return ResponseEntity.ok(processResult);
	}

	@Override
	public ResponseEntity<String> printLabelsForOrders(List<Long> orderIds, PrintLabelStartPositionDto startPosition) {
		if (CollectionUtils.isEmpty(orderIds) || orderIds.size() > 50) {
			throw new BadRequestException("At least 1 and at most 50 orderIds must be provided for batch printing labels.");
		}
		if (orderIds.size() != orderIds.stream().distinct().count()) {
			throw new BadRequestException("Duplicate orderIds provided for batch printing labels.");
		}
		return ResponseEntity.ok(orderService.printLabelsForOrders(orderIds, startPosition));
	}

	private ResponseEntity<DocumentDownloadDto> finishResultActionForZipDownload(DocumentDownloadZipHolder documentDownload, Long orderId) {
		try (DeleteOnCloseFileInputStream deleteOnCloseFileInputStream = documentDownload.getZipFileInputStream()) {
			log.info("Start adding zip file to response for order {}.", orderId);
			DocumentDownloadDto documentDownloadDto = documentDownload.getDocumentDownload();
			documentDownloadDto.setContent(DatatypeConverter.printBase64Binary(deleteOnCloseFileInputStream.readAllBytes()));
			log.info("Successfully added zip file to response for order {}.", orderId);
			return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(documentDownloadDto);

		} catch (IOException exc) {
			throw new DocumentZipDownloadException("Reading from Zip File Input Stream failed for order %d with message: %s.".formatted(orderId, exc.getMessage()));
		}
	}

}
