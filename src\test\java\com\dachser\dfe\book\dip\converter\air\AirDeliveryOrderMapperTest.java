package com.dachser.dfe.book.dip.converter.air;

import com.dachser.dfe.book.TestUtil;
import com.dachser.dfe.book.dip.converter.shared.AirSeaGoodsDescriptionMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.AirSeaOrderContactCommunicationsMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.DateMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.StringNotEmptyConditionMapperImpl;
import com.dachser.dfe.book.dip.converter.shared.Types;
import com.dachser.dfe.book.model.jaxb.order.asl.ForwardingOrder;
import com.dachser.dfe.book.order.air.AirExportOrder;
import com.dachser.dfe.book.order.air.AirOrderLine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
@SpringBootTest(classes = { AirFreightMapperImpl.class, AirPickupOrderMapperImpl.class, AirDeliveryOrderMapperImpl.class, DateMapperImpl.class,
		AirSeaOrderContactCommunicationsMapperImpl.class, AirOrderLineMapperImpl.class, AirOrderReferenceMapperImpl.class, AirSeaGoodsDescriptionMapperImpl.class,
		StringNotEmptyConditionMapperImpl.class })
class AirDeliveryOrderMapperTest {

	private AirExportOrder airExportOrder;

	@Autowired
	AirDeliveryOrderMapper airDeliveryOrderMapper;

	private final TestUtil testUtil = TestUtil.createInstanceNonSpring();

	@BeforeEach
	void setUp() {
		airExportOrder = testUtil.generateAirExportOrder();
	}

	@Test
	void shouldMapToDeliveryOrderIfDeliverToIATASet() {
		airExportOrder.setDeliverToAirport(true);
		final List<ForwardingOrder.AirFreightShipment.DeliveryOrder> deliveryOrders = airDeliveryOrderMapper.mapDeliveryOrders(airExportOrder);
		assertNotNull(deliveryOrders);
		assertEquals(1, deliveryOrders.size());
		assertNotNull(deliveryOrders.get(0).getOrderAddress());
	}

	@Test
	void shouldNotMapToDeliveryOrderIfDeliverToIATASet() {
		final List<ForwardingOrder.AirFreightShipment.DeliveryOrder> deliveryOrders = airDeliveryOrderMapper.mapDeliveryOrders(airExportOrder);
		assertNotNull(deliveryOrders);
		assertEquals(0, deliveryOrders.size());
	}

	@Test
	void shouldMapShipperToDeliveryOrder() {
		airExportOrder.setDeliverToAirport(true);
		final List<ForwardingOrder.AirFreightShipment.DeliveryOrder> deliveryOrders = airDeliveryOrderMapper.mapDeliveryOrders(airExportOrder);
		assertNotNull(deliveryOrders);
		assertEquals(1, deliveryOrders.size());
		final ForwardingOrder.AirFreightShipment.DeliveryOrder.OrderAddress orderAddress = deliveryOrders.get(0).getOrderAddress();
		assertEquals(Types.AddressTypes.DELIVERER, orderAddress.getTypeOfAddress());
		assertEquals("Example GmbH _shipper", orderAddress.getAddress().getName1());
	}

	@Test
	void shouldMapGoodsDescription() {
		airExportOrder.setDeliverToAirport(true);
		final List<ForwardingOrder.AirFreightShipment.DeliveryOrder> deliveryOrders = airDeliveryOrderMapper.mapDeliveryOrders(airExportOrder);
		assertNotNull(deliveryOrders);
		final List<String> goodsDescription = deliveryOrders.get(0).getGoodsDescription();
		assertEquals(1, goodsDescription.size());
		assertEquals("goods1 | goods2 | goods3", goodsDescription.get(0));

	}

	@Test
	void shouldMapAdditionalOrderInformation() {
		airExportOrder.setDeliverToAirport(true);
		AirOrderLine orderLine = testUtil.generateAirOrderLine();
		orderLine.setMarkAndNumbers("Another marks and numbers string");
		airExportOrder.addOrderLine(orderLine);
		ForwardingOrder.AirFreightShipment.DeliveryOrder deliveryOrder = airDeliveryOrderMapper.mapDeliveryOrders(airExportOrder).getFirst();
		assertEquals("markAndNumbers - text - content", deliveryOrder.getAdditionalOrderInformation().getFirst().getValue());
		assertEquals("Another marks and numbers string", deliveryOrder.getAdditionalOrderInformation().get(1).getValue());

	}

	@Test
	void shouldMapMarksAndNumbers() {
		airExportOrder.setDeliverToAirport(true);
		List<ForwardingOrder.AirFreightShipment.DeliveryOrder> deliveryOrders = airDeliveryOrderMapper.mapDeliveryOrders(airExportOrder);
		assertNotNull(deliveryOrders);
		assertEquals(1, deliveryOrders.size());
		assertEquals(1, deliveryOrders.get(0).getAdditionalOrderInformation().size());
		assertEquals(Types.AdditionalOrderInformation.MARKS_AND_NUMBERS, deliveryOrders.get(0).getAdditionalOrderInformation().get(0).getTypeOfInformation());
		assertEquals("markAndNumbers - text - content", deliveryOrders.get(0).getAdditionalOrderInformation().get(0).getValue());
	}

}