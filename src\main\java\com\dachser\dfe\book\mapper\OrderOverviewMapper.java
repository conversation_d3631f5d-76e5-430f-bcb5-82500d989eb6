package com.dachser.dfe.book.mapper;

import com.dachser.dfe.book.jpa.entity.VOrderOverview;
import com.dachser.dfe.book.order.OrderReferenceHelper;
import com.dachser.dfe.book.order.OrderType;
import com.dachser.dfe.book.order.air.AirOrderReference;
import com.dachser.dfe.book.order.common.OrderAddress;
import com.dachser.dfe.book.order.common.ReferenceType;
import com.dachser.dfe.book.order.mapper.OrderOverviewMapperConfig;
import com.dachser.dfe.book.order.road.RoadOrderReference;
import com.dachser.dfe.book.order.sea.SeaOrderReference;
import com.dachser.dfe.book.overview.TrackAndTraceApiImplementation;
import com.dachser.dfe.trackandtrace.model.TTASLOrder;
import com.dachser.dfe.trackandtrace.model.TTAddress;
import com.dachser.dfe.trackandtrace.model.TTOption;
import com.dachser.dfe.trackandtrace.model.TTOrderReference;
import com.dachser.dfe.trackandtrace.model.TTOrderType;
import com.dachser.dfe.trackandtrace.model.TTPortInfo;
import com.dachser.dfe.trackandtrace.model.TTRoadCollectionOrder;
import com.dachser.dfe.trackandtrace.model.TTRoadForwardingOrder;
import com.dachser.dfe.trackandtrace.model.TTRoadOrder;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.BeforeMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.SubclassExhaustiveStrategy;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Mapper(config = OrderOverviewMapperConfig.class, componentModel = "spring", subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION)
@Named("OrderOverviewMapper")
@TrackAndTraceApiImplementation
public interface OrderOverviewMapper {

    List<ReferenceType> hiddenReferences = List.of(ReferenceType.DAILY_PRICE_REFERENCE);

    OffsetDateTime DEFAULT_DATE = OffsetDateTime.of(1970, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC);

    @Mapping(target = "goodsValue", source = "valueOfGoods")
    @Mapping(target = "references", source = ".", qualifiedByName = "mapReferencesASL")
    @Mapping(target = "portInfo", source = ".", qualifiedByName = {"mapPortAirPortRouting"})
    @Mapping(target = "pickupDateTime.requestArrangement", source = "requestArrangement")
    @Mapping(target = "expirationTime", source = "quoteExpirationTime")
    TTASLOrder mapASL(VOrderOverview aslOrder);

    @Mapping(target = "goodsValue", source = "valueOfGoods")
    @Mapping(target = "references", source = "allReferencesRoad", qualifiedByName = "mapReferencesRoad")
    @Mapping(target = "expirationTime", source = "quoteExpirationTime")
	TTRoadForwardingOrder mapRoadForward(VOrderOverview roadOrder);

    @Mapping(target = "goodsValue", source = "valueOfGoods")
    @Mapping(target = "references", source = "allReferencesRoad", qualifiedByName = "mapReferencesRoad")
    @Mapping(target = "expirationTime", source = "quoteExpirationTime")
	TTRoadCollectionOrder mapRoadCollection(VOrderOverview roadOrder);

    @Mapping(source = "orderAddressId", target = "id")
    TTAddress mapAddress(OrderAddress orderAddress);

    @Mapping(source = "pickUpDateType", target = "pickupDateTime.type")
    default TTOption mapPickUpDateType(final String type) {
        final TTOption option = new TTOption();
        option.setCode(type);
        option.setDescription(type);
        return option;
    }

    @Mapping(target = "id", source = "value")
    TTAddress map(Long value);

    @Mapping(source = "orderType", target = "orderType")
    default TTOrderType mapOrderType(final Integer orderTypeId) {
        final String orderTypeSegment = Stream.ofNullable(orderTypeId).map(OrderType::getById).map(OrderType::getSegment).map(Enum::name).findFirst().orElse(null);
        return Arrays.stream(TTOrderType.values()).filter(category -> (Objects.equals(category.toString(), orderTypeSegment))).findFirst().orElse(null);
    }

    default TTOrderReference mapReferenceRoad(final RoadOrderReference roadOrderReference) {
        if (roadOrderReference == null) {
            return null;
        }

        // daily price reference shouldn't be displayed to the user
        if (!hiddenReferences.contains(roadOrderReference.getReferenceType())) {
            TTOrderReference orderReferenceDto = new TTOrderReference();

            String referenceType = roadOrderReference.getReferenceType().name();
            if (referenceType.equalsIgnoreCase(ReferenceType.BOOKING_REFERENCE.name())) {
                referenceType = ReferenceType.BOOKING_REFERENCE_LABEL.name();
            }

            orderReferenceDto.setReferenceCode(com.dachser.dfe.trackandtrace.model.TTOrderReference.ReferenceCodeEnum.valueOf(referenceType));
            orderReferenceDto.setValue(prefixRoadReferenceValueWithSubType(roadOrderReference));

            return orderReferenceDto;
        }

        return null;
    }

    private List<String> prefixRoadReferenceValueWithSubType(RoadOrderReference roadOrderReference) {
        if (roadOrderReference == null) {
            return List.of();
        }

        List<String> values = List.of(roadOrderReference.getReference());
        if (roadOrderReference.getReferenceSubtype() == null) {
            return values;
        }

        String prefix = OrderReferenceHelper.prefix(roadOrderReference.getReferenceSubtype());
        return values.stream().map(value -> prefix.concat(" ").concat(StringUtils.defaultString(value))).toList();
    }

    @Named("mapReferencesRoad")
    List<TTOrderReference> mapReferencesRoad(List<RoadOrderReference> references);

    @Mapping(source = "referenceType", target = "referenceCodeASL")
    @Mapping(source = "referenceValue", target = "value")
    TTOrderReference mapReferenceAir(AirOrderReference airOrderReference);

    @Named("mapReferencesASL")
    default List<TTOrderReference> mapReferencesASL(VOrderOverview overview) {
        if(OrderType.isAirOrderType(OrderType.getById(overview.getOrderType()))){
            return mapReferencesAir(overview.getAllReferencesAir());
        }
        return mapReferencesSea(overview.getAllReferencesSea());
    }

    List<TTOrderReference> mapReferencesAir(List<AirOrderReference> references);

    @Mapping(source = "referenceType", target = "referenceCodeASL")
    @Mapping(source = "referenceValue", target = "value")
    TTOrderReference mapReferenceSea(SeaOrderReference airOrderReference);

    List<TTOrderReference> mapReferencesSea(List<SeaOrderReference> references);

    @Mapping(source = "includedDocuments", target = "includedDocuments")
    default List<String> mapStringToList(final String text) {
        return Stream.of(Objects.requireNonNullElse(text, "").split(",")).filter(ref -> (StringUtils.isNotEmpty(ref) && (StringUtils.isNotBlank(ref)))).toList();
    }

    @Named("mapPortAirPortRouting")
    @Mapping(target = "from.code", source = "portDeparture")
    @Mapping(target = "to.code", source = "portDestination")
    @Mapping(target = "dateFrom", ignore = true)
    @Mapping(target = "dateTo", ignore = true)
    TTPortInfo mapPortOrAirportRouting(VOrderOverview overview);

    private List<TTOrderReference> aggregateReferencesRoad(List<TTOrderReference> references) {
        if (references != null) {
            EnumMap<TTOrderReference.ReferenceCodeEnum, List<TTOrderReference>> groupedReferences = references.stream().filter(ref -> ref != null && ref.getReferenceCode() != null)
                    .collect(Collectors.groupingBy(TTOrderReference::getReferenceCode, () -> new EnumMap<>(TTOrderReference.ReferenceCodeEnum.class), Collectors.toList()));
            return sortReferences(aggregateReferenceValuesRoad(groupedReferences), TTOrderType.ROAD);
        }
        return List.of();
    }

    private List<TTOrderReference> aggregateReferencesAsl(List<TTOrderReference> references) {
        if (references != null) {
            EnumMap<TTOrderReference.ReferenceCodeASLEnum, List<TTOrderReference>> groupedReferences = references.stream()
                    .filter(ref -> ref != null && ref.getReferenceCodeASL() != null)
                    .collect(Collectors.groupingBy(TTOrderReference::getReferenceCodeASL, () -> new EnumMap<>(TTOrderReference.ReferenceCodeASLEnum.class), Collectors.toList()));
            return sortReferences(aggregateReferenceValuesAsl(groupedReferences), TTOrderType.AIR);
        }
        return List.of();
    }

    Comparator<TTOrderReference> roadReferenceComparator = (o1, o2) -> {
        // ReferenceCodeEnum.ORDER_NUMBER needs to be displayed first
        if (o1.getReferenceCode() == TTOrderReference.ReferenceCodeEnum.ORDER_NUMBER) {
			return -1;
        } else if (o2.getReferenceCode() == TTOrderReference.ReferenceCodeEnum.ORDER_NUMBER) {
			return 1;
		}
		return 0;
	};

    Comparator<TTOrderReference> aslReferenceComparator = (o1, o2) -> {
        // ReferenceCodeASLEnum.SHIPPERS_REFERENCE needs to be displayed first
        if (o1.getReferenceCodeASL() == TTOrderReference.ReferenceCodeASLEnum.SHIPPERS_REFERENCE) {
			return -1;
        } else if (o2.getReferenceCodeASL() == TTOrderReference.ReferenceCodeASLEnum.SHIPPERS_REFERENCE) {
			return 1;
		}
		return 0;
	};

    private List<TTOrderReference> sortReferences(List<TTOrderReference> references, TTOrderType orderType) {
		return switch (orderType) {
			case ROAD -> references.stream().sorted(roadReferenceComparator).toList();
			case AIR, SEA -> references.stream().sorted(aslReferenceComparator).toList();
		};
	}

    @Named("mapLastModified")
    default OffsetDateTime mapLastModified(OffsetDateTime timestamp) {
        if (timestamp == null) {
            return null;
        }
        return timestamp.withNano(0);
    }

    @AfterMapping()
    default void aggregateReferences(@MappingTarget TTRoadOrder order) {
        order.setReferences(aggregateReferencesRoad(order.getReferences()));
    }

    @AfterMapping()
    default void aggregateReferences(@MappingTarget TTASLOrder order) {
        order.setReferences(aggregateReferencesAsl(order.getReferences()));
    }

    private List<TTOrderReference> aggregateReferenceValuesRoad(final Map<TTOrderReference.ReferenceCodeEnum, List<TTOrderReference>> groupedReferences) {
        return groupedReferences.entrySet().stream().map(entry -> {
            final TTOrderReference reference = new TTOrderReference();
            reference.setReferenceCode(entry.getKey());
            reference.setValue(aggregateValues(entry.getValue()));
            return reference;
        }).toList();
    }

    private List<TTOrderReference> aggregateReferenceValuesAsl(final Map<TTOrderReference.ReferenceCodeASLEnum, List<TTOrderReference>> groupedReferences) {
        return groupedReferences.entrySet().stream().map(entry -> {
            final TTOrderReference reference = new TTOrderReference();
            reference.setReferenceCodeASL(entry.getKey());
            reference.setValue(aggregateValues(entry.getValue()));
            return reference;
        }).toList();
    }

    private List<String> aggregateValues(final List<TTOrderReference> group) {
        return group.stream().map(TTOrderReference::getValue).flatMap(Collection::stream).sorted().toList();
    }

    @AfterMapping()
    default void updateNotImplementedFieldsForRoadOrder(@MappingTarget final TTRoadOrder order) {
        if (order.getFurtherAddresses() == null) {
            order.setFurtherAddresses(new ArrayList<>());
        }
    }

    @AfterMapping()
    default void updateNotImplementedFieldsForASLOrder(@MappingTarget final TTASLOrder order) {

        if (order.getHouseAWBOrBLNumber() == null) {
            order.setHouseAWBOrBLNumber("0");
        }
        if (order.getFlightNumberOrVesselTerm() == null) {
            order.setFlightNumberOrVesselTerm("");
        }
        if (order.getDeliveryTime() == null) {
            order.setDeliveryTime(DEFAULT_DATE);
        }
    }

    @BeforeMapping
    default void fillNullCollections(final VOrderOverview orderOverview) {
        if (orderOverview == null) {
            return;
        }
        addDefaultsIfNull(orderOverview);
    }

    private void addDefaultsIfNull(final VOrderOverview orderOverview) {
        orderOverview.setOrderReferences(Objects.requireNonNullElse(orderOverview.getOrderReferences(), ""));
        orderOverview.setWeight(Objects.requireNonNullElse(orderOverview.getWeight(), 0.0));
        orderOverview.setQuantity(Objects.requireNonNullElse(orderOverview.getQuantity(), 0L));
        orderOverview.setIncludedDocuments(Objects.requireNonNullElse(orderOverview.getIncludedDocuments(), ""));
        orderOverview.setPortDeparture(Objects.requireNonNullElse(orderOverview.getPortDeparture(), ""));
        orderOverview.setPortDestination(Objects.requireNonNullElse(orderOverview.getPortDestination(), ""));
        orderOverview.setFlightNumberOrVesselName(Objects.requireNonNullElse(orderOverview.getFlightNumberOrVesselName(), ""));
        orderOverview.setTermCode(Objects.requireNonNullElse(orderOverview.getTermCode(), ""));
        orderOverview.setProduct(Objects.requireNonNullElse(orderOverview.getProduct(), ""));
        orderOverview.setValueOfGoods(Objects.requireNonNullElse(orderOverview.getValueOfGoods(), 0.0));
        orderOverview.setPrincipalAddress(Objects.requireNonNullElse(orderOverview.getPrincipalAddress(), Integer.parseInt(orderOverview.getCustomerNumber())));
        orderOverview.setTransport(Objects.requireNonNullElse(orderOverview.getTransport(), ""));
        orderOverview.setDeliveryDate(Objects.requireNonNullElse(orderOverview.getDeliveryDate(), DEFAULT_DATE));
        orderOverview.setOrderSent(Objects.requireNonNullElse(orderOverview.getOrderSent(), DEFAULT_DATE));
        orderOverview.setPickUpDateTo(Objects.requireNonNullElse(orderOverview.getPickUpDateTo(), DEFAULT_DATE));
        orderOverview.setPickUpDateFrom(Objects.requireNonNullElse(orderOverview.getPickUpDateFrom(), DEFAULT_DATE));
        orderOverview.setTypeOfGoods(Objects.requireNonNullElse(orderOverview.getTypeOfGoods(), ""));
        orderOverview.setBranch(Objects.requireNonNullElse(orderOverview.getBranch(), ""));
        orderOverview.setOriginOfOrder(Objects.requireNonNullElse(orderOverview.getOriginOfOrder(), ""));
        orderOverview.setProject(Objects.requireNonNullElse(orderOverview.getProject(), ""));
        orderOverview.setCustomsGoods(Objects.requireNonNullElse(orderOverview.getCustomsGoods(), ""));
        orderOverview.setPickUpDateType(Objects.requireNonNullElse(orderOverview.getPickUpDateType(), ""));
        orderOverview.setGoodsCurrency(Objects.requireNonNullElse(orderOverview.getGoodsCurrency(), "EUR"));

    }
}
